package dao_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"gorm.io/gorm"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"google.golang.org/protobuf/testing/protocmp"

	modelPb "github.com/epifi/gamma/api/insights/model"
	"github.com/epifi/gamma/insights/config"
	"github.com/epifi/gamma/insights/dao"
	"github.com/epifi/gamma/insights/dao/model"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

type InsightFrameworkDaoTestSuite struct {
	db   *gorm.DB
	dao  dao.InsightFrameworkDao
	conf *config.Config
}

var (
	frameworkDTS *InsightFrameworkDaoTestSuite

	insightFrameworkTables = []string{"insight_frameworks", "actor_insight_generation_logs", "story_group_engagements"}
	framework1             = &modelPb.InsightFramework{
		FrameworkName: "INS_MERCH04",
		FrameworkVariables: []modelPb.InsightVariable{
			modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
			modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
		},
		SegmentVariables: []modelPb.InsightVariable{
			modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
		},
		ContentType:      modelPb.ContentType_CONTENT_TYPE_PERSONAL,
		TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
		InsightState:     modelPb.InsightState_INSIGHT_STATE_ACTIVE,
		Deeplink:         "deeplink",
		ExpiryConfig: &modelPb.ExpiryConfig{
			Method: modelPb.ExpiryMethod_EXPIRY_METHOD_END_OF_CALENDER_UNIT_FROM_GENERATION,
			ExpiryValue: &modelPb.ExpiryConfig_EndOfCalenderUnitParams{
				EndOfCalenderUnitParams: &modelPb.EndOfCalenderUnitParams{
					Unit:  modelPb.TimeUnit_TIME_UNIT_WEEK,
					Count: 1,
				},
			},
		},
		ValidationExpression: "{#INSIGHT_VARIABLE_AMOUNT#} > 1000",
	}
	frameworkFixtureId1 = "f9a8649a-6cfc-4cba-8d80-848910d837fc"
	frameworkFixture1   = &modelPb.InsightFramework{
		Id:            frameworkFixtureId1,
		FrameworkName: "INS_MERCH01",
		FrameworkVariables: []modelPb.InsightVariable{
			modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
			modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
		},
		SegmentVariables: []modelPb.InsightVariable{
			modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
		},
		ContentType:      modelPb.ContentType_CONTENT_TYPE_PERSONAL,
		TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
		InsightState:     modelPb.InsightState_INSIGHT_STATE_ACTIVE,
		Deeplink:         "{}",
		ExpiryConfig:     &modelPb.ExpiryConfig{},
	}

	frameworkFixtureId4 = "f9a8649a-6cfc-4cba-8d80-848910d837f3"
	frameworkFixture4   = &modelPb.InsightFramework{
		Id:            frameworkFixtureId4,
		FrameworkName: "INS_MERCH05",
		ReleaseGroup:  commontypes.UserGroup_INTERNAL,
		FrameworkVariables: []modelPb.InsightVariable{
			modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
		},
		ContentType:      modelPb.ContentType_CONTENT_TYPE_PERSONAL,
		TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
		InsightState:     modelPb.InsightState_INSIGHT_STATE_ACTIVE,
		Deeplink:         "{}",
		ExpiryConfig:     &modelPb.ExpiryConfig{},
	}

	frameworkFixtureId5 = "f9a8649a-6cfc-4cba-8d80-848910d837f4"
	frameworkFixture5   = &modelPb.InsightFramework{
		Id:            frameworkFixtureId5,
		FrameworkName: "INS_MERCH06",
		ReleaseGroup:  commontypes.UserGroup_FNF,
		FrameworkVariables: []modelPb.InsightVariable{
			modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
		},
		ContentType:      modelPb.ContentType_CONTENT_TYPE_PERSONAL,
		TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
		InsightState:     modelPb.InsightState_INSIGHT_STATE_INACTIVE,
		Deeplink:         "{}",
		ExpiryConfig:     &modelPb.ExpiryConfig{},
	}
)

func TestInsightFrameworkDaoPgdb_Create(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, frameworkDTS.db, frameworkDTS.conf.ActorInsightsDb.GetName(), insightFrameworkTables)
	ctx := context.Background()
	type args struct {
		ctx       context.Context
		framework *modelPb.InsightFramework
	}
	tests := []struct {
		name    string
		args    args
		want    *modelPb.InsightFramework
		wantErr error
	}{
		{
			name: "insight framework is nil",
			args: args{
				ctx:       ctx,
				framework: nil,
			},
			wantErr: dao.EmptyFrameworkErr,
		},
		{
			name: "framework entry successfully created",
			args: args{
				ctx:       ctx,
				framework: framework1,
			},
			want: framework1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := frameworkDTS.dao.Create(tt.args.ctx, tt.args.framework)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualInsightFramework(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInsightFrameworkDaoPgdb_GetById(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, frameworkDTS.db, frameworkDTS.conf.ActorInsightsDb.GetName(), insightFrameworkTables)
	ctx := context.Background()
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *modelPb.InsightFramework
		wantErr error
	}{
		{
			name: "id is empty",
			args: args{
				ctx: ctx,
				id:  "",
			},
			wantErr: dao.IdUnspecifiedError,
		},
		{
			name: "entry not found",
			args: args{
				ctx: ctx,
				id:  uuid.New().String(),
			},
			wantErr: gorm.ErrRecordNotFound,
		},
		{
			name: "entry found",
			args: args{
				ctx: ctx,
				id:  frameworkFixture1.Id,
			},
			want: frameworkFixture1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := frameworkDTS.dao.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualInsightFramework(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInsightFrameworkDaoPgdb_GetByName(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, frameworkDTS.db, frameworkDTS.conf.ActorInsightsDb.GetName(), insightFrameworkTables)
	ctx := context.Background()
	type args struct {
		ctx           context.Context
		frameworkName string
	}
	tests := []struct {
		name    string
		args    args
		want    *modelPb.InsightFramework
		wantErr error
	}{
		{
			name: "frameworkName is empty",
			args: args{
				ctx:           ctx,
				frameworkName: "",
			},
			wantErr: dao.FrameworkNameUnspecifiedError,
		},
		{
			name: "entry not found",
			args: args{
				ctx:           ctx,
				frameworkName: "random-name-xyz",
			},
			wantErr: gorm.ErrRecordNotFound,
		},
		{
			name: "entry found",
			args: args{
				ctx:           ctx,
				frameworkName: frameworkFixture1.GetFrameworkName(),
			},
			want: frameworkFixture1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := frameworkDTS.dao.GetByName(tt.args.ctx, tt.args.frameworkName)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("GetByName() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByName() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualInsightFramework(got, tt.want) {
				t.Errorf("GetByName() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInsightFrameworkDaoPgdb_GetByFrameworkNames(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, frameworkDTS.db, frameworkDTS.conf.ActorInsightsDb.GetName(), insightFrameworkTables)
	ctx := context.Background()
	type args struct {
		ctx            context.Context
		frameworkNames []string
		filterOptions  []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*modelPb.InsightFramework
		wantErr error
	}{
		{
			name: "no framework name in array, failure",
			args: args{
				ctx:            ctx,
				frameworkNames: nil,
			},
			wantErr: dao.FrameworkNamesUnspecifiedErr,
		},
		{
			name: "no record found, success",
			args: args{
				ctx:            ctx,
				frameworkNames: []string{"INS_MERCH555", "INS_MERCH101"},
			},
			want: []*modelPb.InsightFramework{},
		},
		{
			name: "number of entries found is different from requested, success",
			args: args{
				ctx:            ctx,
				frameworkNames: []string{"INS_MERCH01", "INS_MERCH101", "INS_MERCH05"},
			},
			want: []*modelPb.InsightFramework{
				frameworkFixture1,
				frameworkFixture4,
			},
		},
		{
			name: "all entries found, success",
			args: args{
				ctx:            ctx,
				frameworkNames: []string{"INS_MERCH06", "INS_MERCH01", "INS_MERCH05"},
			},
			want: []*modelPb.InsightFramework{
				frameworkFixture1,
				frameworkFixture4,
				frameworkFixture5,
			},
		},
		{
			name: "1 entry found based on insight state filter filter, success",
			args: args{
				ctx:            ctx,
				frameworkNames: []string{"INS_MERCH06", "INS_MERCH01", "INS_MERCH05"},
				filterOptions: []storagev2.FilterOption{
					dao.FilterInsightState(modelPb.InsightState_INSIGHT_STATE_INACTIVE),
				},
			},
			want: []*modelPb.InsightFramework{
				frameworkFixture5,
			},
		},
		{
			name: "2 entry found based on insight state filter filter, success",
			args: args{
				ctx:            ctx,
				frameworkNames: []string{"INS_MERCH06", "INS_MERCH01", "INS_MERCH05"},
				filterOptions: []storagev2.FilterOption{
					dao.FilterInsightState(modelPb.InsightState_INSIGHT_STATE_ACTIVE),
				},
			},
			want: []*modelPb.InsightFramework{
				frameworkFixture1,
				frameworkFixture4,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := frameworkDTS.dao.GetByFrameworkNames(tt.args.ctx, tt.args.frameworkNames, tt.args.filterOptions...)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("GetByFrameworkNames() error = %v, wantErr %v", err, tt.wantErr)
				return
			} else if err != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("GetByFrameworkNames() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualInsightFrameworkArr(got, tt.want) {
				t.Errorf("GetByFrameworkNames() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInsightFrameworkDaoPgdb_Update(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, frameworkDTS.db, frameworkDTS.conf.ActorInsightsDb.GetName(), insightFrameworkTables)
	type args struct {
		ctx        context.Context
		framework  *modelPb.InsightFramework
		updateMask []modelPb.InsightFrameworkFieldMask
	}
	ctx := context.Background()
	framework1.Id = frameworkFixture1.Id
	tests := []struct {
		name    string
		args    args
		wantErr error
		want    *modelPb.InsightFramework
	}{
		{
			name: "insight framework object is nil",
			args: args{
				ctx:       ctx,
				framework: nil,
				updateMask: []modelPb.InsightFrameworkFieldMask{
					modelPb.InsightFrameworkFieldMask_INSIGHT_FRAMEWORK_FIELD_MASK_DEEPLINK,
					modelPb.InsightFrameworkFieldMask_INSIGHT_FRAMEWORK_FIELD_MASK_EXPIRY_CONFIG,
				},
			},
			wantErr: dao.IdUnspecifiedError,
		},
		{
			name: "framework_id not specified",
			args: args{
				ctx: ctx,
				framework: &modelPb.InsightFramework{
					Deeplink:     "deeplink",
					InsightState: modelPb.InsightState_INSIGHT_STATE_ACTIVE,
				},
				updateMask: []modelPb.InsightFrameworkFieldMask{
					modelPb.InsightFrameworkFieldMask_INSIGHT_FRAMEWORK_FIELD_MASK_DEEPLINK,
					modelPb.InsightFrameworkFieldMask_INSIGHT_FRAMEWORK_FIELD_MASK_EXPIRY_CONFIG,
				},
			},
			wantErr: dao.IdUnspecifiedError,
		},
		{
			name: "update mask is empty",
			args: args{
				ctx:        ctx,
				framework:  framework1,
				updateMask: []modelPb.InsightFrameworkFieldMask{},
			},
			wantErr: dao.UpdateMaskEmptyErr,
		},
		{
			name: "successfully update framework",
			args: args{
				ctx:       ctx,
				framework: framework1,
				updateMask: []modelPb.InsightFrameworkFieldMask{
					modelPb.InsightFrameworkFieldMask_INSIGHT_FRAMEWORK_FIELD_MASK_FRAMEWORK_NAME,
					modelPb.InsightFrameworkFieldMask_INSIGHT_FRAMEWORK_FIELD_MASK_DEEPLINK,
					modelPb.InsightFrameworkFieldMask_INSIGHT_FRAMEWORK_FIELD_MASK_EXPIRY_CONFIG,
				},
			},
			want: &modelPb.InsightFramework{
				Id:            "f9a8649a-6cfc-4cba-8d80-848910d837fc",
				FrameworkName: "INS_MERCH04",
				FrameworkVariables: []modelPb.InsightVariable{
					modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
					modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
				},
				SegmentVariables: []modelPb.InsightVariable{
					modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
				},
				ContentType:      modelPb.ContentType_CONTENT_TYPE_PERSONAL,
				TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
				InsightState:     modelPb.InsightState_INSIGHT_STATE_ACTIVE,
				Deeplink:         "deeplink",
				ExpiryConfig: &modelPb.ExpiryConfig{
					Method: modelPb.ExpiryMethod_EXPIRY_METHOD_END_OF_CALENDER_UNIT_FROM_GENERATION,
					ExpiryValue: &modelPb.ExpiryConfig_EndOfCalenderUnitParams{
						EndOfCalenderUnitParams: &modelPb.EndOfCalenderUnitParams{
							Unit:  modelPb.TimeUnit_TIME_UNIT_WEEK,
							Count: 1,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := frameworkDTS.dao.Update(tt.args.ctx, tt.args.framework, tt.args.updateMask)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			got := getFrameworkById(tt.args.framework.Id)
			if !isDeepEqualInsightFramework(got, tt.want) {
				t.Errorf("Update() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInsightSegmentDaoPgdb_GetInsightFrameworksInReleaseGroups(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, segmentDTS.db, segmentDTS.conf.ActorInsightsDb.GetName(), insightFrameworkTables)
	ctx := context.Background()
	type args struct {
		ctx           context.Context
		releaseGroups []commontypes.UserGroup
	}
	tests := []struct {
		name    string
		args    args
		want    []*modelPb.InsightFramework
		wantErr error
	}{
		{
			name: "release group arr empty",
			args: args{
				ctx:           ctx,
				releaseGroups: []commontypes.UserGroup{},
			},
			wantErr: dao.ReleaseGroupArrEmptyErr,
		},
		{
			name: "only unspecified enum present in array",
			args: args{
				ctx:           ctx,
				releaseGroups: []commontypes.UserGroup{commontypes.UserGroup_USER_GROUP_UNSPECIFIED},
			},
			wantErr: dao.ReleaseGroupArrEmptyErr,
		},
		{
			name: "no records found",
			args: args{
				ctx:           ctx,
				releaseGroups: []commontypes.UserGroup{commontypes.UserGroup_REQUEST_NEW_CARD_FNF},
			},
			want: []*modelPb.InsightFramework{},
		},
		{
			name: "All FnF segments",
			args: args{
				ctx:           ctx,
				releaseGroups: []commontypes.UserGroup{commontypes.UserGroup_FNF},
			},
			want: []*modelPb.InsightFramework{
				frameworkFixture5,
			},
		},
		{
			name: "All FnF and Internal segments",
			args: args{
				ctx: ctx,
				releaseGroups: []commontypes.UserGroup{
					commontypes.UserGroup_FNF,
					commontypes.UserGroup_INTERNAL,
				},
			},
			want: []*modelPb.InsightFramework{
				frameworkFixture4,
				frameworkFixture5,
			},
		},
		{
			name: "unspecified enum filtered out from input release group array",
			args: args{
				ctx: ctx,
				releaseGroups: []commontypes.UserGroup{
					commontypes.UserGroup_FNF,
					commontypes.UserGroup_INTERNAL,
					commontypes.UserGroup_USER_GROUP_UNSPECIFIED,
				},
			},
			want: []*modelPb.InsightFramework{
				frameworkFixture4,
				frameworkFixture5,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := frameworkDTS.dao.GetFrameworksInReleaseGroups(tt.args.ctx, tt.args.releaseGroups)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("GetFrameworksInReleaseGroups() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetFrameworksInReleaseGroups() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualInsightFrameworkArr(got, tt.want) {
				t.Errorf("GetFrameworksInReleaseGroups() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func getFrameworkById(id string) *modelPb.InsightFramework {
	var data model.InsightFramework
	frameworkDTS.db.Where(&model.InsightFramework{
		Id: id,
	}).First(&data)
	return data.ToProto()
}

func isDeepEqualInsightFramework(actual, expected *modelPb.InsightFramework) bool {
	if expected != nil && actual != nil {
		expected.Id = actual.Id
		expected.UpdatedAt = actual.UpdatedAt
		expected.CreatedAt = actual.CreatedAt
		expected.DeletedAt = actual.DeletedAt
	}
	diff := cmp.Diff(actual, expected, protocmp.Transform())
	return diff == ""
}

func isDeepEqualInsightFrameworkArr(actual, expected []*modelPb.InsightFramework) bool {
	if len(actual) != len(expected) {
		return false
	}
	for _, expectedEntry := range expected {
		found := false
		for _, actualEntry := range actual {
			if expectedEntry.Id == actualEntry.Id {
				found = true
				if !isDeepEqualInsightFramework(actualEntry, expectedEntry) {
					return false
				}
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}
