package impl_test

import (
	"context"
	"testing"

	moneyPb "github.com/epifi/be-common/pkg/money"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	gormv2 "gorm.io/gorm"

	userDeclarationModelPb "github.com/epifi/gamma/api/insights/user_declaration/model"
	"github.com/epifi/gamma/insights/config"
	"github.com/epifi/gamma/insights/test"
	"github.com/epifi/gamma/insights/user_declaration/dao"
)

var (
	sampleCreatedUserDeclaration = &userDeclarationModelPb.UserDeclaration{
		ExternalId:      "external-id-2",
		ActorId:         "actor-1",
		DeclarationType: userDeclarationModelPb.UserDeclarationType_USER_DECLARATION_TYPE_MONTHLY_INCOME,
		UserDeclaration: &userDeclarationModelPb.Declaration{
			Declaration: &userDeclarationModelPb.Declaration_MonthlyIncomeDeclaration{MonthlyIncomeDeclaration: &userDeclarationModelPb.MonthlyIncomeDeclaration{MonthlyIncome: moneyPb.AmountINR(100).GetPb()}},
		},
		Id: "b59fbb87-5f22-48f4-9611-767d58c2bda2",
	}
)

type userDeclarationTestSuite struct {
	db   *gormv2.DB
	dao  dao.UserDeclarationDao
	conf *config.Config
}

func newUserDeclarationTestSuite(db *gormv2.DB, dao dao.UserDeclarationDao, conf *config.Config) *userDeclarationTestSuite {
	return &userDeclarationTestSuite{
		db:   db,
		dao:  dao,
		conf: conf,
	}
}

var (
	userDeclarationTestSuiteObj *userDeclarationTestSuite
)

func TestUserDeclarationImpl_Create(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, userDeclarationTestSuiteObj.db, userDeclarationTestSuiteObj.conf.InsightsDb.GetName(), test.InsightsTables)
	type args struct {
		ctx             context.Context
		userDeclaration *userDeclarationModelPb.UserDeclaration
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successful create",
			args: args{
				ctx: context.Background(),
				userDeclaration: &userDeclarationModelPb.UserDeclaration{
					ExternalId:      "test-external-id",
					ActorId:         "some-actor-id",
					DeclarationType: userDeclarationModelPb.UserDeclarationType_USER_DECLARATION_TYPE_MONTHLY_INCOME,
					UserDeclaration: &userDeclarationModelPb.Declaration{
						Declaration: &userDeclarationModelPb.Declaration_MonthlyIncomeDeclaration{MonthlyIncomeDeclaration: &userDeclarationModelPb.MonthlyIncomeDeclaration{MonthlyIncome: moneyPb.AmountINR(1000).GetPb()}},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "duplicate entry",
			args: args{
				ctx: context.Background(),
				userDeclaration: &userDeclarationModelPb.UserDeclaration{
					ExternalId:      "duplicate-external-id",
					ActorId:         sampleCreatedUserDeclaration.GetActorId(),
					DeclarationType: userDeclarationModelPb.UserDeclarationType_USER_DECLARATION_TYPE_MONTHLY_INCOME,
					UserDeclaration: &userDeclarationModelPb.Declaration{
						Declaration: &userDeclarationModelPb.Declaration_MonthlyIncomeDeclaration{MonthlyIncomeDeclaration: &userDeclarationModelPb.MonthlyIncomeDeclaration{MonthlyIncome: moneyPb.AmountINR(1000).GetPb()}},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := userDeclarationTestSuiteObj.dao.Create(tt.args.ctx, tt.args.userDeclaration)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantErr == true {
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&userDeclarationModelPb.UserDeclaration{}, "created_at", "updated_at", "deleted_at", "id"),
			}
			if diff := cmp.Diff(tt.args.userDeclaration, got, opts...); diff != "" {
				t.Errorf("Create() mismatch (-want +got):\n%s", diff)

			}
		})
	}
}

func TestUserDeclarationImpl_GetByFilters(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, userDeclarationTestSuiteObj.db, userDeclarationTestSuiteObj.conf.InsightsDb.GetName(), test.InsightsTables)
	type args struct {
		ctx     context.Context
		filters []storageV2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*userDeclarationModelPb.UserDeclaration
		wantErr bool
	}{
		{
			name: "successful get by filters",
			args: args{
				ctx: context.Background(),
				filters: []storageV2.FilterOption{
					dao.ActorIdAndDeclarationTypeFilter(sampleCreatedUserDeclaration.GetActorId(), sampleCreatedUserDeclaration.GetDeclarationType()),
				},
			},
			want:    []*userDeclarationModelPb.UserDeclaration{sampleCreatedUserDeclaration},
			wantErr: false,
		},
		{
			name: "successful get by filters(external id)",
			args: args{
				ctx: context.Background(),
				filters: []storageV2.FilterOption{
					dao.ExternalIdFilter(sampleCreatedUserDeclaration.GetExternalId()),
				},
			},
			want:    []*userDeclarationModelPb.UserDeclaration{sampleCreatedUserDeclaration},
			wantErr: false,
		},
		{
			name: "record not found",
			args: args{
				ctx:     context.Background(),
				filters: []storageV2.FilterOption{dao.ExternalIdFilter("non-existent-id")},
			},
			want:    []*userDeclarationModelPb.UserDeclaration{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := userDeclarationTestSuiteObj.dao.GetByFilters(tt.args.ctx, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(got) != len(tt.want) {
				t.Errorf("expected %d objects, got %d", len(tt.want), len(got))
				return
			}
			for i := 0; i < len(got); i++ {
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&userDeclarationModelPb.UserDeclaration{}, "created_at", "updated_at", "deleted_at"),
				}
				if diff := cmp.Diff(tt.want[i], got[i], opts...); diff != "" {
					t.Errorf("GetByFilters() mismatch (-want +got):\n%s", diff)

				}
			}
		})
	}
}

func TestUserDeclarationImpl_UpdateByExternalId(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, userDeclarationTestSuiteObj.db, userDeclarationTestSuiteObj.conf.InsightsDb.GetName(), test.InsightsTables)
	type args struct {
		ctx         context.Context
		declaration *userDeclarationModelPb.UserDeclaration
		fieldMasks  []userDeclarationModelPb.UserDeclarationFieldMask
	}
	sampleObj := &userDeclarationModelPb.UserDeclaration{
		ExternalId:      "test-external-id",
		ActorId:         "some-actor-id",
		DeclarationType: userDeclarationModelPb.UserDeclarationType_USER_DECLARATION_TYPE_MONTHLY_INCOME,
		UserDeclaration: &userDeclarationModelPb.Declaration{
			Declaration: &userDeclarationModelPb.Declaration_MonthlyIncomeDeclaration{MonthlyIncomeDeclaration: &userDeclarationModelPb.MonthlyIncomeDeclaration{MonthlyIncome: moneyPb.AmountINR(7474).GetPb()}},
		},
	}
	createdObj, err := userDeclarationTestSuiteObj.dao.Create(context.Background(), sampleObj)
	if err != nil {
		t.Errorf("unable to create sample object")
		return
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successful update",
			args: args{
				ctx: context.Background(),
				declaration: &userDeclarationModelPb.UserDeclaration{
					ExternalId:      createdObj.GetExternalId(),
					ActorId:         "some-actor-id",
					DeclarationType: userDeclarationModelPb.UserDeclarationType_USER_DECLARATION_TYPE_MONTHLY_INCOME,
					UserDeclaration: &userDeclarationModelPb.Declaration{
						Declaration: &userDeclarationModelPb.Declaration_MonthlyIncomeDeclaration{MonthlyIncomeDeclaration: &userDeclarationModelPb.MonthlyIncomeDeclaration{MonthlyIncome: moneyPb.AmountINR(1234).GetPb()}},
					},
					Id:        createdObj.GetId(),
					CreatedAt: createdObj.GetCreatedAt(),
				},
				fieldMasks: []userDeclarationModelPb.UserDeclarationFieldMask{
					userDeclarationModelPb.UserDeclarationFieldMask_USER_DECLARATION_FIELD_MASK_DECLARATION,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := userDeclarationTestSuiteObj.dao.UpdateByExternalId(tt.args.ctx, tt.args.declaration, tt.args.fieldMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateByExternalId() error = %v, wantErr %v", err, tt.wantErr)
			}
			getObj, err := userDeclarationTestSuiteObj.dao.GetByFilters(context.Background(), dao.ExternalIdFilter(tt.args.declaration.GetExternalId()))
			if err != nil {
				t.Errorf("unable to fetch updated object")
				return
			}
			if len(getObj) != 1 {
				t.Errorf("expected 1 object, got %d", len(getObj))
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&userDeclarationModelPb.UserDeclaration{}, "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(tt.args.declaration, getObj[0], opts...); diff != "" {
				t.Errorf("GetByFilters() mismatch (-want +got):\n%s", diff)
			}

		})
	}
}
