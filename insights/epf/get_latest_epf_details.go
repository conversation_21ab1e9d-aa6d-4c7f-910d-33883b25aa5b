package epf

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	insightsPb "github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/pkg/dmf/util"
)

const (
	layout                      = "02-01-2006"
	passbookTxnParticularPrefix = "cont"
)

func (s *Service) GetLatestEpfDetails(ctx context.Context, req *insightsPb.GetLatestEpfDetailsRequest) (*insightsPb.GetLatestEpfDetailsResponse, error) {
	actorId := req.GetActorId()
	if actorId == "" {
		return &insightsPb.GetLatestEpfDetailsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id cannot be empty"),
		}, nil
	}

	// fetching UAN accounts data received from Karza
	uanAccountsResp, err := s.GetUANAccounts(ctx, &insightsPb.GetUANAccountsRequest{
		ActorId: actorId,
	})

	if rpcErr := epifigrpc.RPCError(uanAccountsResp, err); rpcErr != nil && !uanAccountsResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to get UAN accounts", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &insightsPb.GetLatestEpfDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	latestEpfDetailsFromUAN := make([]*insightsPb.LatestEpfDetails, 0, len(uanAccountsResp.GetUanAccounts()))
	for _, uanAccount := range uanAccountsResp.GetUanAccounts() {
		for _, eachEstablishment := range uanAccount.GetRawDetails().GetEstDetails() {
			detail := &insightsPb.LatestEpfDetails{
				EpfSource:      insightsPb.EpfSource_EPF_SOURCE_UAN,
				UanNumber:      uanAccount.GetUanNumber(),
				PassbookNumber: eachEstablishment.GetMemberId(),
				CompanyName:    eachEstablishment.GetEstName(),
				EpfBalance:     money.AmountINR(eachEstablishment.GetPfBalance().GetNetBalance()).GetPb(),
			}
			lastTxnOfPassbook := datetime.TIME_MIN
			for _, eachPassbookDetail := range eachEstablishment.GetPassbook() {
				if strings.HasPrefix(strings.ToLower(eachPassbookDetail.GetParticular()), passbookTxnParticularPrefix) {
					parsedTrDateMy, err1 := time.Parse(layout, eachPassbookDetail.GetTrDateMy())
					if err1 != nil {
						return nil, fmt.Errorf("error while parsing TrDateMy: %w", err)
					}
					if parsedTrDateMy.After(lastTxnOfPassbook) {
						lastTxnOfPassbook = parsedTrDateMy
					}
				}
			}
			if lastTxnOfPassbook.Equal(datetime.TIME_MIN) {
				continue
			}
			creditMonth := datetime.TimeToDateInLoc(lastTxnOfPassbook, time.UTC)
			detail.CreditMonth = creditMonth
			latestEpfDetailsFromUAN = append(latestEpfDetailsFromUAN, detail)
		}
	}

	// fetching epf sms data from database
	epfSmsData, err := s.epfSmsDataDao.GetAllPassbooksLatestDataByActorId(ctx, actorId)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to get EPF SMS data from DB", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return &insightsPb.GetLatestEpfDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	if len(uanAccountsResp.GetUanAccounts()) == 0 && len(epfSmsData) == 0 {
		return &insightsPb.GetLatestEpfDetailsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	latestEpfDetailsFromSMS := make([]*insightsPb.LatestEpfDetails, 0, len(epfSmsData))
	for _, smsData := range epfSmsData {
		detail := &insightsPb.LatestEpfDetails{
			EpfSource:      insightsPb.EpfSource_EPF_SOURCE_SMS,
			UanNumber:      smsData.GetMaskedUanNumber(),
			CompanyName:    "",
			PassbookNumber: smsData.GetPassbookNumber(),
			EpfBalance:     smsData.GetPassbookBalance(),
			CreditMonth:    smsData.GetCreditMonth(),
		}
		latestEpfDetailsFromSMS = append(latestEpfDetailsFromSMS, detail)
	}

	latestEpfDetailsByPassbook := make(map[string]*insightsPb.LatestEpfDetails)
	for _, epfDetail := range latestEpfDetailsFromUAN {
		passbookNumber := epfDetail.GetPassbookNumber()
		_, exists := latestEpfDetailsByPassbook[passbookNumber]
		if !exists {
			latestEpfDetailsByPassbook[passbookNumber] = epfDetail
		}
	}

	// Process SMS details
	for _, epfDetail := range latestEpfDetailsFromSMS {
		passbookNumber := epfDetail.GetPassbookNumber()
		existing, exists := latestEpfDetailsByPassbook[passbookNumber]

		// If this passbook doesn't exist in the map yet, or if this entry is newer than the existing one
		if !exists {
			latestEpfDetailsByPassbook[passbookNumber] = epfDetail
		} else if epfDetail.GetCreditMonth() != nil && existing.GetCreditMonth() != nil && util.CompareDate(epfDetail.GetCreditMonth(), existing.GetCreditMonth()) > 0 {
			// only updating the below params for the case where data is available in both UAN and SMS
			// reason: we get extra params like complete UAN and company name from Karza
			latestEpfDetailsByPassbook[passbookNumber].EpfSource = epfDetail.GetEpfSource()
			latestEpfDetailsByPassbook[passbookNumber].EpfBalance = epfDetail.GetEpfBalance()
			latestEpfDetailsByPassbook[passbookNumber].CreditMonth = epfDetail.GetCreditMonth()
		}
	}

	allEpfDetails := make([]*insightsPb.LatestEpfDetails, 0, len(latestEpfDetailsByPassbook))
	for _, detail := range latestEpfDetailsByPassbook {
		allEpfDetails = append(allEpfDetails, detail)
	}

	return &insightsPb.GetLatestEpfDetailsResponse{
		Status:           rpc.StatusOk(),
		LatestEpfDetails: allEpfDetails,
	}, nil
}
