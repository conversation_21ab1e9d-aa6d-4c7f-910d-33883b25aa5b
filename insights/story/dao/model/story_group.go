package model

import (
	"github.com/epifi/gamma/api/insights/story/model"
	"github.com/epifi/be-common/pkg/nulltypes"
)

type StoryGroup struct {
	Id             string
	Name           model.StoryGroupName
	ValidFrom      nulltypes.NullTime
	ValidTill      nulltypes.NullTime
	DisplayDetails *model.StoryGroupDisplayDetails
	ShareDetails   *model.ShareDetails
}

func NewStoryGroup(proto *model.StoryGroup) *StoryGroup {
	sg := &StoryGroup{
		Id:             proto.GetId(),
		Name:           proto.GetName(),
		DisplayDetails: proto.GetDisplayDetails(),
		ShareDetails:   proto.GetShareDetails(),
	}
	if proto.GetValidFrom() != nil {
		sg.ValidFrom = nulltypes.NewNullTime(proto.GetValidFrom().AsTime())
	}
	if proto.GetValidTill() != nil {
		sg.ValidTill = nulltypes.NewNullTime(proto.GetValidTill().AsTime())
	}
	return sg
}

func (s *StoryGroup) Proto() *model.StoryGroup {
	return &model.StoryGroup{
		Id:             s.Id,
		Name:           s.Name,
		DisplayDetails: s.DisplayDetails,
		ValidFrom:      s.ValidFrom.GetProto(),
		ValidTill:      s.ValidTill.GetProto(),
		ShareDetails:   s.ShareDetails,
	}
}
