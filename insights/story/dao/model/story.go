package model

import (
	"github.com/epifi/gamma/api/insights/story/model"
	"github.com/epifi/gamma/api/webfe/story"
)

type Story struct {
	Id             string
	Name           string
	TemplateId     story.StoryTemplate
	Generator      model.StoryValueGenerator
	Values         map[string]string
	ValidationExpr string
}

func NewStory(proto *model.Story) *Story {
	return &Story{
		Id:             proto.GetId(),
		Name:           proto.GetName(),
		TemplateId:     proto.GetTemplateId(),
		Generator:      proto.GetGenerator(),
		Values:         proto.GetValues(),
		ValidationExpr: proto.GetValidationExpr(),
	}
}

func (s *Story) Proto() *model.Story {
	return &model.Story{
		Id:             s.Id,
		Name:           s.Name,
		TemplateId:     s.TemplateId,
		Generator:      s.Generator,
		Values:         s.Values,
		ValidationExpr: s.ValidationExpr,
	}
}
