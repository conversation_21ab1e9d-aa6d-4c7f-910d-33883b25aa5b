// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	epan "github.com/epifi/gamma/api/pan/epan"
	panupdate "github.com/epifi/gamma/api/pan/panupdate"
	scannedpan "github.com/epifi/gamma/api/pan/scannedpan"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	gomock "github.com/golang/mock/gomock"
)

// MockEPANAttemptsDao is a mock of EPANAttemptsDao interface.
type MockEPANAttemptsDao struct {
	ctrl     *gomock.Controller
	recorder *MockEPANAttemptsDaoMockRecorder
}

// MockEPANAttemptsDaoMockRecorder is the mock recorder for MockEPANAttemptsDao.
type MockEPANAttemptsDaoMockRecorder struct {
	mock *MockEPANAttemptsDao
}

// NewMockEPANAttemptsDao creates a new mock instance.
func NewMockEPANAttemptsDao(ctrl *gomock.Controller) *MockEPANAttemptsDao {
	mock := &MockEPANAttemptsDao{ctrl: ctrl}
	mock.recorder = &MockEPANAttemptsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEPANAttemptsDao) EXPECT() *MockEPANAttemptsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEPANAttemptsDao) Create(ctx context.Context, epanAttempt *epan.EPANAttempt) (*epan.EPANAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, epanAttempt)
	ret0, _ := ret[0].(*epan.EPANAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEPANAttemptsDaoMockRecorder) Create(ctx, epanAttempt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEPANAttemptsDao)(nil).Create), ctx, epanAttempt)
}

// DeletePayload mocks base method.
func (m *MockEPANAttemptsDao) DeletePayload(ctx context.Context, clientRequestId []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePayload", ctx, clientRequestId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePayload indicates an expected call of DeletePayload.
func (mr *MockEPANAttemptsDaoMockRecorder) DeletePayload(ctx, clientRequestId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePayload", reflect.TypeOf((*MockEPANAttemptsDao)(nil).DeletePayload), ctx, clientRequestId)
}

// GetAllAttemptsByActorId mocks base method.
func (m *MockEPANAttemptsDao) GetAllAttemptsByActorId(ctx context.Context, actorId string, limit int) ([]*epan.EPANAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllAttemptsByActorId", ctx, actorId, limit)
	ret0, _ := ret[0].([]*epan.EPANAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllAttemptsByActorId indicates an expected call of GetAllAttemptsByActorId.
func (mr *MockEPANAttemptsDaoMockRecorder) GetAllAttemptsByActorId(ctx, actorId, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAttemptsByActorId", reflect.TypeOf((*MockEPANAttemptsDao)(nil).GetAllAttemptsByActorId), ctx, actorId, limit)
}

// GetAttemptByActorId mocks base method.
func (m *MockEPANAttemptsDao) GetAttemptByActorId(ctx context.Context, actorId string, options ...storagev2.FilterOption) (*epan.EPANAttempt, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAttemptByActorId", varargs...)
	ret0, _ := ret[0].(*epan.EPANAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttemptByActorId indicates an expected call of GetAttemptByActorId.
func (mr *MockEPANAttemptsDaoMockRecorder) GetAttemptByActorId(ctx, actorId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttemptByActorId", reflect.TypeOf((*MockEPANAttemptsDao)(nil).GetAttemptByActorId), varargs...)
}

// GetAttemptByClientReqId mocks base method.
func (m *MockEPANAttemptsDao) GetAttemptByClientReqId(ctx context.Context, clientReqId string) (*epan.EPANAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAttemptByClientReqId", ctx, clientReqId)
	ret0, _ := ret[0].(*epan.EPANAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttemptByClientReqId indicates an expected call of GetAttemptByClientReqId.
func (mr *MockEPANAttemptsDaoMockRecorder) GetAttemptByClientReqId(ctx, clientReqId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttemptByClientReqId", reflect.TypeOf((*MockEPANAttemptsDao)(nil).GetAttemptByClientReqId), ctx, clientReqId)
}

// GetAttemptsByExpiry mocks base method.
func (m *MockEPANAttemptsDao) GetAttemptsByExpiry(ctx context.Context, expiry time.Time, limit int) ([]*epan.EPANAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAttemptsByExpiry", ctx, expiry, limit)
	ret0, _ := ret[0].([]*epan.EPANAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttemptsByExpiry indicates an expected call of GetAttemptsByExpiry.
func (mr *MockEPANAttemptsDaoMockRecorder) GetAttemptsByExpiry(ctx, expiry, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttemptsByExpiry", reflect.TypeOf((*MockEPANAttemptsDao)(nil).GetAttemptsByExpiry), ctx, expiry, limit)
}

// Update mocks base method.
func (m *MockEPANAttemptsDao) Update(ctx context.Context, epanAttempt *epan.EPANAttempt, fieldMask []epan.EPANAttemptFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, epanAttempt, fieldMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockEPANAttemptsDaoMockRecorder) Update(ctx, epanAttempt, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockEPANAttemptsDao)(nil).Update), ctx, epanAttempt, fieldMask)
}

// MockEPANAttemptEventsDao is a mock of EPANAttemptEventsDao interface.
type MockEPANAttemptEventsDao struct {
	ctrl     *gomock.Controller
	recorder *MockEPANAttemptEventsDaoMockRecorder
}

// MockEPANAttemptEventsDaoMockRecorder is the mock recorder for MockEPANAttemptEventsDao.
type MockEPANAttemptEventsDaoMockRecorder struct {
	mock *MockEPANAttemptEventsDao
}

// NewMockEPANAttemptEventsDao creates a new mock instance.
func NewMockEPANAttemptEventsDao(ctrl *gomock.Controller) *MockEPANAttemptEventsDao {
	mock := &MockEPANAttemptEventsDao{ctrl: ctrl}
	mock.recorder = &MockEPANAttemptEventsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEPANAttemptEventsDao) EXPECT() *MockEPANAttemptEventsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEPANAttemptEventsDao) Create(ctx context.Context, epanAttemptEvent *epan.EPANAttemptEvent) (*epan.EPANAttemptEvent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, epanAttemptEvent)
	ret0, _ := ret[0].(*epan.EPANAttemptEvent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEPANAttemptEventsDaoMockRecorder) Create(ctx, epanAttemptEvent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEPANAttemptEventsDao)(nil).Create), ctx, epanAttemptEvent)
}

// GetEventsByClientReqId mocks base method.
func (m *MockEPANAttemptEventsDao) GetEventsByClientReqId(ctx context.Context, clientReqId string) ([]*epan.EPANAttemptEvent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEventsByClientReqId", ctx, clientReqId)
	ret0, _ := ret[0].([]*epan.EPANAttemptEvent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEventsByClientReqId indicates an expected call of GetEventsByClientReqId.
func (mr *MockEPANAttemptEventsDaoMockRecorder) GetEventsByClientReqId(ctx, clientReqId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEventsByClientReqId", reflect.TypeOf((*MockEPANAttemptEventsDao)(nil).GetEventsByClientReqId), ctx, clientReqId)
}

// MockScannedPanAttemptsDAO is a mock of ScannedPanAttemptsDAO interface.
type MockScannedPanAttemptsDAO struct {
	ctrl     *gomock.Controller
	recorder *MockScannedPanAttemptsDAOMockRecorder
}

// MockScannedPanAttemptsDAOMockRecorder is the mock recorder for MockScannedPanAttemptsDAO.
type MockScannedPanAttemptsDAOMockRecorder struct {
	mock *MockScannedPanAttemptsDAO
}

// NewMockScannedPanAttemptsDAO creates a new mock instance.
func NewMockScannedPanAttemptsDAO(ctrl *gomock.Controller) *MockScannedPanAttemptsDAO {
	mock := &MockScannedPanAttemptsDAO{ctrl: ctrl}
	mock.recorder = &MockScannedPanAttemptsDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScannedPanAttemptsDAO) EXPECT() *MockScannedPanAttemptsDAOMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockScannedPanAttemptsDAO) Create(ctx context.Context, scannedPanAttempt *scannedpan.ScannedPanAttempt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, scannedPanAttempt)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockScannedPanAttemptsDAOMockRecorder) Create(ctx, scannedPanAttempt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockScannedPanAttemptsDAO)(nil).Create), ctx, scannedPanAttempt)
}

// GetByActorId mocks base method.
func (m *MockScannedPanAttemptsDAO) GetByActorId(ctx context.Context, actorId string, limit int) ([]*scannedpan.ScannedPanAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId, limit)
	ret0, _ := ret[0].([]*scannedpan.ScannedPanAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockScannedPanAttemptsDAOMockRecorder) GetByActorId(ctx, actorId, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockScannedPanAttemptsDAO)(nil).GetByActorId), ctx, actorId, limit)
}

// GetByClientRequestId mocks base method.
func (m *MockScannedPanAttemptsDAO) GetByClientRequestId(ctx context.Context, requestId string) (*scannedpan.ScannedPanAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientRequestId", ctx, requestId)
	ret0, _ := ret[0].(*scannedpan.ScannedPanAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientRequestId indicates an expected call of GetByClientRequestId.
func (mr *MockScannedPanAttemptsDAOMockRecorder) GetByClientRequestId(ctx, requestId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientRequestId", reflect.TypeOf((*MockScannedPanAttemptsDAO)(nil).GetByClientRequestId), ctx, requestId)
}

// MockPanUpdateAttemptsDao is a mock of PanUpdateAttemptsDao interface.
type MockPanUpdateAttemptsDao struct {
	ctrl     *gomock.Controller
	recorder *MockPanUpdateAttemptsDaoMockRecorder
}

// MockPanUpdateAttemptsDaoMockRecorder is the mock recorder for MockPanUpdateAttemptsDao.
type MockPanUpdateAttemptsDaoMockRecorder struct {
	mock *MockPanUpdateAttemptsDao
}

// NewMockPanUpdateAttemptsDao creates a new mock instance.
func NewMockPanUpdateAttemptsDao(ctrl *gomock.Controller) *MockPanUpdateAttemptsDao {
	mock := &MockPanUpdateAttemptsDao{ctrl: ctrl}
	mock.recorder = &MockPanUpdateAttemptsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPanUpdateAttemptsDao) EXPECT() *MockPanUpdateAttemptsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockPanUpdateAttemptsDao) Create(ctx context.Context, panUpdateAttempt *panupdate.PanUpdateAttempt) (*panupdate.PanUpdateAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, panUpdateAttempt)
	ret0, _ := ret[0].(*panupdate.PanUpdateAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockPanUpdateAttemptsDaoMockRecorder) Create(ctx, panUpdateAttempt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockPanUpdateAttemptsDao)(nil).Create), ctx, panUpdateAttempt)
}

// Delete mocks base method.
func (m *MockPanUpdateAttemptsDao) Delete(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockPanUpdateAttemptsDaoMockRecorder) Delete(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockPanUpdateAttemptsDao)(nil).Delete), ctx, id)
}

// Get mocks base method.
func (m *MockPanUpdateAttemptsDao) Get(ctx context.Context, options ...storagev2.FilterOption) (*panupdate.PanUpdateAttempt, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(*panupdate.PanUpdateAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockPanUpdateAttemptsDaoMockRecorder) Get(ctx interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockPanUpdateAttemptsDao)(nil).Get), varargs...)
}

// Update mocks base method.
func (m *MockPanUpdateAttemptsDao) Update(ctx context.Context, panUpdateAttempt *panupdate.PanUpdateAttempt, fieldMask []panupdate.PanUpdateAttemptFieldMask) (*panupdate.PanUpdateAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, panUpdateAttempt, fieldMask)
	ret0, _ := ret[0].(*panupdate.PanUpdateAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockPanUpdateAttemptsDaoMockRecorder) Update(ctx, panUpdateAttempt, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockPanUpdateAttemptsDao)(nil).Update), ctx, panUpdateAttempt, fieldMask)
}
