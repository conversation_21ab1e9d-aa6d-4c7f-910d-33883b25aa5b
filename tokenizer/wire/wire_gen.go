// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/kms"
	"github.com/aws/aws-sdk-go-v2/service/sts"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/gamma/tokenizer/config"
	"github.com/epifi/gamma/tokenizer/crypto"
	"github.com/epifi/gamma/tokenizer/dao"
	"github.com/epifi/gamma/tokenizer/processor"
	"github.com/epifi/gamma/tokenizer/service"
)

// Injectors from wire.go:

// config: {"tokenizerS3Client": "Buckets.BucketName"}
func InitializeService(ctx context.Context, conf *config.Config, redisClient types.TokenizerRedisStore, awsConf aws.Config, tokenizerS3Client service.TokenizerS3Client) (*service.TokenizerService, error) {
	perpetualProcessor, err := getNewPerpetualProcessor(conf, awsConf)
	if err != nil {
		return nil, err
	}
	dynamodbTokenStore := getFederalDynamodbTokenStore(awsConf, conf)
	kmsSymmetricCryptoV2 := getFederalKmsSymmetricCrypto(awsConf, conf)
	perpetualFederalProcessor := getPerpetualFederalProcessor(dynamodbTokenStore, kmsSymmetricCryptoV2, conf)
	client := types.TokenizerRedisStoreRedisClientProvider(redisClient)
	redisTokenStore := dao.NewRedisTokenStore(client)
	transientFederalProcessor := getTransientFederalProcessor(redisTokenStore, kmsSymmetricCryptoV2, conf)
	factory := processor.NewProcessorFactory(perpetualProcessor, perpetualFederalProcessor, transientFederalProcessor)
	tokenizerService := service.NewTokenizerService(factory, conf, tokenizerS3Client)
	return tokenizerService, nil
}

// wire.go:

func getNewPerpetualProcessor(conf *config.Config, awsConf aws.Config) (*processor.PerpetualProcessor, error) {
	dynamodbTokenStore := getDynamoTokenStore(conf, awsConf)
	kmsSymmetricCrypto, err := getKmsSymmetryCrypto(conf, awsConf)
	if err != nil {
		return nil, err
	}
	return processor.NewPerpetualProcessor(dynamodbTokenStore, kmsSymmetricCrypto, conf), nil
}

func getPerpetualFederalProcessor(federalDynamodbTokenStore *dao.DynamodbTokenStore, federalKmsSymmetricCrypto *crypto.KmsSymmetricCryptoV2, conf *config.Config) processor.PerpetualFederalProcessor {
	return processor.NewPerpetualProcessor(federalDynamodbTokenStore, federalKmsSymmetricCrypto, conf)
}

func getTransientFederalProcessor(federalRedisTokenStore *dao.RedisTokenStore, federalKmsSymmetricCrypto *crypto.KmsSymmetricCryptoV2, conf *config.Config) processor.TransientFederalProcessor {
	return processor.NewTransientProcessor(federalRedisTokenStore, federalKmsSymmetricCrypto, conf)
}

func getDynamoTokenStore(conf *config.Config, awsConf aws.Config) *dao.DynamodbTokenStore {
	dynamodbHandle := dynamodb.NewFromConfig(awsConf)
	return dao.NewDynamodbTokenStore(dynamodbHandle, conf.DynamodbTables)
}

func getKmsSymmetryCrypto(conf *config.Config, awsConf aws.Config) (*crypto.KMSSymmetricCrypto, error) {
	kmsHandle := kms.NewFromConfig(awsConf)
	return crypto.NewKMSSymmetricCrypto(kmsHandle, conf.KMSKeysIds, conf.HMACSHA256KeyId)
}

func getFederalDynamodbTokenStore(awsConf aws.Config, conf *config.Config) *dao.DynamodbTokenStore {
	if !cfg.IsTeamSpaceTenant() {
		awsV2Client := sts.NewFromConfig(awsConf)
		assumeRoleCredentials := stscreds.NewAssumeRoleProvider(awsV2Client, conf.FederalCrossAccountRoleArn)
		awsConf.Credentials = assumeRoleCredentials
	}

	federalDynamoDbHandle := dynamodb.NewFromConfig(awsConf)
	federalDynamodbTokenStore := dao.NewDynamodbTokenStore(federalDynamoDbHandle, conf.FederalDynamodbTables)
	return federalDynamodbTokenStore
}

func getFederalKmsSymmetricCrypto(awsConf aws.Config, conf *config.Config) *crypto.KmsSymmetricCryptoV2 {
	if !cfg.IsTeamSpaceTenant() {
		awsV2Client := sts.NewFromConfig(awsConf)
		assumeRoleCredentials := stscreds.NewAssumeRoleProvider(awsV2Client, conf.FederalCrossAccountRoleArn)
		awsConf.Credentials = assumeRoleCredentials
	}

	federalKmsHandle := kms.NewFromConfig(awsConf)
	federalKmsSymmetricCrypto, _ := crypto.NewKmsSymmetricCryptoV2(federalKmsHandle, conf.FederalKMSKeysIds, conf.FederalHMACSHA256KeyId)

	return federalKmsSymmetricCrypto
}
