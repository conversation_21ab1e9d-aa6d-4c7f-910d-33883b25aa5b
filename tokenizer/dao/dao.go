//go:generate mockgen -source=dao.go -destination=$PWD/../test/mocks/dao/dao.go -package=mock_dao
package dao

import (
	"context"
	"time"

	"github.com/google/wire"

	"github.com/epifi/gamma/tokenizer/model"
)

//go:generate dao_metrics_gen .
var PerpetualStoreWireSet = wire.NewSet(NewDynamodbTokenStore, wire.Bind(new(PerpetualStore), new(*DynamodbTokenStore)))
var TransientStoreWireSet = wire.NewSet(NewRedisTokenStore, wire.Bind(new(TransientStore), new(*RedisTokenStore)))

type PerpetualStore interface {
	Persist(ctx context.Context, data *model.StoreData, mappingData *model.StoreMappingData) error
	FetchData(ctx context.Context, token string) (*model.StoreData, error)
	FetchMappingData(ctx context.Context, hashToken string) (*model.StoreMappingData, error)
	DeleteDataAndMappingData(ctx context.Context, token, hashToken string) error
	GetPaginatedTokens(ctx context.Context, pageSize int32, exclusiveStartToken string, scopeFilter string) ([]string, string, error)
}

type TransientStore interface {
	Persist(ctx context.Context, data *model.StoreData, ttl time.Duration) error
	Fetch(ctx context.Context, token string) (*model.StoreData, error)
	Delete(ctx context.Context, token string) error
}
