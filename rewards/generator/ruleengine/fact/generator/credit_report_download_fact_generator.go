package generator

import (
	"context"

	"github.com/epifi/gamma/api/rewards/datacollector"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/creditreport"
	"github.com/epifi/gamma/rewards/helper"
)

type CreditReportDownloadFactGenerator struct {
	userHelperSvc helper.IUserHelperService
}

func NewCreditReportDownloadFactGenerator(userHelperSvc helper.IUserHelperService) *CreditReportDownloadFactGenerator {
	return &CreditReportDownloadFactGenerator{
		userHelperSvc: userHelperSvc,
	}
}

// nolint: funlen
func (c *CreditReportDownloadFactGenerator) GenerateFacts(ctx context.Context, collectedData *datacollector.CollectedData, rewardOffer *rewardOffersPb.RewardOffer) ([]common.IFact, error) {
	commonFact := &common.CommonFact{
		Ctx:                  ctx,
		RewardOffer:          rewardOffer,
		ActorId:              collectedData.GetCreditReportDownloadEvent().GetActorId(),
		RefId:                collectedData.GetId(),
		ActionType:           collectedData.GetDataType(),
		ActionTime:           collectedData.GetActionTime(),
		ActionCollectionTime: collectedData.GetCreationTime(),
		UserHelperService:    c.userHelperSvc,
	}
	generatedFact := creditreport.NewCreditReportDownloadFact(commonFact, collectedData.GetCreditReportDownloadEvent())
	return []common.IFact{generatedFact}, nil
}
