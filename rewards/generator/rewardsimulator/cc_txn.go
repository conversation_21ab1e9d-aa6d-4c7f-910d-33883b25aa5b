// nolint:dupl,funlen
package rewardsimulator

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	fireflyPb "github.com/epifi/gamma/api/firefly/accounting"
	ffConsumer "github.com/epifi/gamma/api/firefly/accounting/consumer"
	dataCollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	generatorPb "github.com/epifi/gamma/api/rewards/generator"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/rewards/datacollector/datacollectionerrors"
	"github.com/epifi/gamma/rewards/datacollector/model"
)

func (rss *GenerateRewardSimulatorService) getCollectedDataForCcTxn(ctx context.Context, req *generatorPb.SimulateRewardGenerationRequest) (*dataCollectorPb.CollectedData, error) {
	var (
		getTxnsWithAdditionalInfosRequest *fireflyPb.GetTxnsWithAdditionalInfosRequest
		txnId                             string
	)

	switch req.GetCcTxnId().GetId().(type) {
	case *generatorPb.CcTxnId_TxnId:
		getTxnsWithAdditionalInfosRequest = &fireflyPb.GetTxnsWithAdditionalInfosRequest{
			GetBy: &fireflyPb.GetTxnsWithAdditionalInfosRequest_BatchTxnIds{
				BatchTxnIds: &fireflyPb.BatchTxnIds{
					TxnIds: []string{req.GetCcTxnId().GetTxnId()},
				},
			},
		}
		txnId = req.GetCcTxnId().GetTxnId()
	case *generatorPb.CcTxnId_ExternalId:
		getTxnsWithAdditionalInfosRequest = &fireflyPb.GetTxnsWithAdditionalInfosRequest{
			GetBy: &fireflyPb.GetTxnsWithAdditionalInfosRequest_BatchExternalTxnIds{
				BatchExternalTxnIds: &fireflyPb.BatchExternalTxnIds{
					ExternalTxnIds: []string{req.GetCcTxnId().GetExternalId()},
				},
			},
		}
		txnId = req.GetCcTxnId().GetExternalId()
	default:
		return nil, fmt.Errorf("unsupported cc txn identifier type")
	}

	// get CreditCard txn details and additional info in bulk
	getTxnsWithAdditionalInfosResponse, err := rss.ffAccClient.GetTxnsWithAdditionalInfos(ctx, getTxnsWithAdditionalInfosRequest)
	if err != nil || !getTxnsWithAdditionalInfosResponse.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching credit card txns using given event id", zap.String("txnId", txnId), zap.Any(logger.RPC_STATUS, getTxnsWithAdditionalInfosResponse.GetStatus()), zap.Error(err))
		return nil, fmt.Errorf("error fetching Credit Card Txn in bulk")
	}

	if len(getTxnsWithAdditionalInfosResponse.GetCardTransactionWithAdditionalInfos()) == 0 {
		return nil, fmt.Errorf("txns details not returned")
	}

	// fetch credit account info
	getAccountRes, err := rss.ffAccClient.GetAccount(ctx, &fireflyPb.GetAccountRequest{GetBy: &fireflyPb.GetAccountRequest_AccountId{AccountId: getTxnsWithAdditionalInfosResponse.GetCardTransactionWithAdditionalInfos()[0].GetTransaction().GetAccountId()}})
	if rpcErr := epifigrpc.RPCError(getAccountRes, err); rpcErr != nil {
		return nil, fmt.Errorf("error while fetching account info, err: %w", rpcErr)
	}

	ccTxnEvent := getCcTxnCollectedDataEvent(getTxnsWithAdditionalInfosResponse.GetCardTransactionWithAdditionalInfos()[0], getAccountRes.GetAccount())
	actorToTxnCategoryMap, err := rss.getTxnCategoriesForActors(ctx, ccTxnEvent.GetTransactionEventPayload().GetActorFrom(), ccTxnEvent.GetTransactionEventPayload().GetActorTo(), ccTxnEvent.GetTransactionEventPayload().GetId())
	if err != nil {
		return nil, fmt.Errorf("error while fetching txn categories, err: %w", err)
	}

	ccTxnModel := &model.CreditCardTransactionCollectedData{
		Ctx:                                ctx,
		CreditCardTxnEvent:                 ccTxnEvent,
		ActorIdToTxnCategoryOntologyIdsMap: actorToTxnCategoryMap,
	}
	collectedData, err := ccTxnModel.GetProtoCollectedData()
	if err != nil {
		return nil, fmt.Errorf("error while getting proto collected data, err: %w", err)
	}

	return collectedData, nil
}

func getCcTxnCollectedDataEvent(txnWithAdditonalInfo *fireflyPb.CardTransactionWithAdditionalInfo, creditAccount *fireflyPb.CreditAccount) *ffConsumer.CreditCardTransactionEvent {
	cardTransaction := txnWithAdditonalInfo.GetTransaction()
	transactionAdditionalInfo := txnWithAdditonalInfo.GetAdditionalInfo()
	return &ffConsumer.CreditCardTransactionEvent{
		CardTransaction:           cardTransaction,
		TransactionAdditionalInfo: transactionAdditionalInfo,
		TransactionEventPayload: &ffConsumer.TransactionEventPayload{
			Id:                      cardTransaction.GetId(),
			AccountId:               cardTransaction.GetAccountId(),
			CardId:                  cardTransaction.GetCardId(),
			Amount:                  cardTransaction.GetAmount(),
			TxnTime:                 cardTransaction.GetTxnTime(),
			TxnStatus:               cardTransaction.GetTxnStatus(),
			TxnCategory:             cardTransaction.GetTxnCategory(),
			TxnOrigin:               cardTransaction.GetTxnOrigin(),
			TxnType:                 cardTransaction.GetTxnType(),
			BeneficiaryInfo:         cardTransaction.GetBeneficiaryInfo(),
			Description:             cardTransaction.GetDescription(),
			ExternalTxnId:           cardTransaction.GetExternalTxnId(),
			RetrievalReferenceNo:    cardTransaction.GetRetrievalReferenceNo(),
			ParentTransactionId:     cardTransaction.GetParentTransactionId(),
			ChildTransactionIds:     cardTransaction.GetChildTransactionIds(),
			PiTo:                    transactionAdditionalInfo.GetPiTo(),
			PiFrom:                  transactionAdditionalInfo.GetPiFrom(),
			ActorFrom:               transactionAdditionalInfo.GetActorFrom(),
			ActorTo:                 transactionAdditionalInfo.GetActorTo(),
			EnrichedBeneficiaryInfo: transactionAdditionalInfo.GetEnrichedBeneficiaryInfo(),
			OriginalCurrencyCode:    cardTransaction.GetConversionInfo().GetOriginalTxnCurrency(),
			CardProgram:             creditAccount.GetCardProgram(),
		},
	}
}

func (rss *GenerateRewardSimulatorService) getTxnCategoriesForActors(ctx context.Context, fromActor, toActor, txnId string) (map[string]*dataCollectorPb.CategoryOntologyIds, error) {
	actorsDetailsRes, err := rss.actorClient.GetEntityDetails(ctx, &actorPb.GetEntityDetailsRequest{ActorIds: []string{fromActor, toActor}})
	if rpcErr := epifigrpc.RPCError(actorsDetailsRes, err); rpcErr != nil {
		return nil, fmt.Errorf("error while fetching actor details, err : %w", rpcErr)
	}
	actorIdToActorTypeMap := make(map[string]types.ActorType)
	for _, actorDetails := range actorsDetailsRes.GetEntityDetails() {
		actorIdToActorTypeMap[actorDetails.GetActorId()] = actorDetails.GetEntityType()
	}

	fromActorType, isPresent := actorIdToActorTypeMap[fromActor]
	if !isPresent {
		return nil, fmt.Errorf("fromActor info not present in actorIdToEntityTypeMap, fromActor: %s", fromActor)
	}
	toActorType, isPresent := actorIdToActorTypeMap[toActor]
	if !isPresent {
		return nil, fmt.Errorf("toActor info not present in actorIdToEntityTypeMap, toActor: %s", toActor)
	}

	actorIdToTxnCategoryOntologyIds := map[string]*dataCollectorPb.CategoryOntologyIds{}

	// fetch txn categories from fromActor's perspective if fromActor is a fi user
	// Note : txn categories are persisted only w.r.t to fi users, there this actorType check is required
	if fromActorType == types.ActorType_USER {
		txnCategoriesRes, err := rss.txnCatClient.GetTxnCategoryDetails(ctx, &categorizerPb.GetTxnCategoryDetailsRequest{
			ActorId:      fromActor,
			Provenance:   categorizerPb.Provenance_DS,
			DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
			ActivityId:   &categorizerPb.ActivityId{Id: &categorizerPb.ActivityId_FiCardTxnId{FiCardTxnId: txnId}},
		})
		if rpcErr := epifigrpc.RPCError(txnCategoriesRes, err); rpcErr != nil {
			return nil, fmt.Errorf("txnCatClient.GetTxnCategoryDetails rpc call failed for fromActor for credit card txn. err: %s, priority: %w", rpcErr.Error(), datacollectionerrors.P1RewardDataCollectorError)
		}

		var ontologyIds []string
		lo.ForEach[*categorizerPb.OntologyDetails](txnCategoriesRes.GetTxnCategories().GetOntologies(), func(ontologyDetails *categorizerPb.OntologyDetails, _ int) {
			ontologyIds = append(ontologyIds, ontologyDetails.GetOntologyId())
		})
		actorIdToTxnCategoryOntologyIds[fromActor] = &dataCollectorPb.CategoryOntologyIds{OntologyIds: ontologyIds}
	}

	// fetch txn categories from toActor's perspective if fromActor is a fi user
	// Note : txn categories are persisted only w.r.t to fi users, there this actorType check is required
	if toActorType == types.ActorType_USER {
		txnCategoriesRes, err := rss.txnCatClient.GetTxnCategoryDetails(ctx, &categorizerPb.GetTxnCategoryDetailsRequest{
			ActorId:      toActor,
			Provenance:   categorizerPb.Provenance_DS,
			DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
			ActivityId:   &categorizerPb.ActivityId{Id: &categorizerPb.ActivityId_FiCardTxnId{FiCardTxnId: txnId}},
		})
		if rpcErr := epifigrpc.RPCError(txnCategoriesRes, err); rpcErr != nil {
			return nil, fmt.Errorf("txnCatClient.GetTxnCategoryDetails rpc call failed for toActor for credit card txn. err: %s, priority: %w", rpcErr.Error(), datacollectionerrors.P1RewardDataCollectorError)
		}

		var ontologyIds []string
		lo.ForEach[*categorizerPb.OntologyDetails](txnCategoriesRes.GetTxnCategories().GetOntologies(), func(ontologyDetails *categorizerPb.OntologyDetails, _ int) {
			ontologyIds = append(ontologyIds, ontologyDetails.GetOntologyId())
		})
		actorIdToTxnCategoryOntologyIds[toActor] = &dataCollectorPb.CategoryOntologyIds{OntologyIds: ontologyIds}
	}

	return actorIdToTxnCategoryOntologyIds, nil
}
