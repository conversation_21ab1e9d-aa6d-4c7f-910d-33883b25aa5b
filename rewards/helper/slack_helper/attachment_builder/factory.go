package attachment_builder

import (
	"fmt"

	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"

	"github.com/google/wire"
)

var AttachmentBuilderFactoryWireSet = wire.NewSet(NewAttachmentBuilderFactory, wire.Bind(new(IAttachmentBuilderFactory), new(*AttachmentBuilderFactory)))

type IAttachmentBuilderFactory interface {
	// GetBuilder fetches the slack message attachment builder based on the message passed
	GetBuilder(message interface{}) (IAttachmentBuilder, error)
}

type AttachmentBuilderFactory struct {
	rewardOfferAttachmentBuilder *RewardOfferAttachmentBuilder
}

func NewAttachmentBuilderFactory(
	rewardOfferAttachmentBuilder *RewardOfferAttachmentBuilder,
) *AttachmentBuilderFactory {
	return &AttachmentBuilderFactory{
		rewardOfferAttachmentBuilder: rewardOfferAttachmentBuilder,
	}
}

func (a *AttachmentBuilderFactory) GetBuilder(message interface{}) (IAttachmentBuilder, error) {
	switch message.(type) {
	case *rewardOffersPb.RewardOffer:
		return a.rewardOfferAttachmentBuilder, nil
	default:
		return nil, fmt.Errorf("invalid message type received: %T", message)
	}
}
