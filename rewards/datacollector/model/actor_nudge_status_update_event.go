package model

import (
	"fmt"

	nudgeDataColletorPb "github.com/epifi/gamma/api/nudge/consumer"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	dataCollectorPb "github.com/epifi/gamma/api/rewards/datacollector"

	"google.golang.org/protobuf/types/known/timestamppb"
)

type ActorNudgeStatusUpdateCollectedData struct {
	*nudgeDataColletorPb.ActorNudgeStatusUpdateEvent
}

var _ CollectedData = &ActorNudgeStatusUpdateCollectedData{}

func (a *ActorNudgeStatusUpdateCollectedData) GetActionTime() (*timestamppb.Timestamp, error) {
	return a.GetEventTimestamp(), nil
}

func (a *ActorNudgeStatusUpdateCollectedData) GetDataType() rewardsPb.CollectedDataType {
	return rewardsPb.CollectedDataType_ACTOR_NUDGE_STATUS_UPDATE_EVENT
}

func (a *ActorNudgeStatusUpdateCollectedData) GetProtoCollectedData() (*dataCollectorPb.CollectedData, error) {
	actionTime, err := a.GetActionTime()
	if err != nil {
		return nil, fmt.Errorf("error creating proto collected data from collected data, err : %w", err)
	}
	return &dataCollectorPb.CollectedData{
		Id:       a.GetId(), // actor_nudge_id from nudge service
		DataType: a.GetDataType(),
		ActorId:  a.GetActorId(),
		Data: &dataCollectorPb.CollectedData_ActorNudgeStatusUpdateEvent{
			ActorNudgeStatusUpdateEvent: a.ActorNudgeStatusUpdateEvent,
		},
		ActionTime:   actionTime,
		CreationTime: timestamppb.Now(),
	}, nil
}
