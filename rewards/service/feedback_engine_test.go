package service

import (
	"context"
	"fmt"
	"testing"

	moneypb "google.golang.org/genproto/googleapis/type/money"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	feedbackEnginePb "github.com/epifi/gamma/api/inapphelp/feedback_engine"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/rewards/config/genconf"
	"github.com/epifi/gamma/rewards/generator/model"
	daoMock "github.com/epifi/gamma/rewards/test/mocks/generator/dao"
)

func TestService_IsActorEligibleForSurvey(t *testing.T) {
	type args struct {
		ctx context.Context
		req *feedbackEnginePb.IsActorEligibleForSurveyRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(rewardsDaoMock *daoMock.MockRewardsDao)
		want       *feedbackEnginePb.IsActorEligibleForSurveyResponse
		wantErr    bool
	}{
		{
			name: "should return record not found error when custom evaluator rule not found",
			args: args{
				ctx: context.Background(),
				req: &feedbackEnginePb.IsActorEligibleForSurveyRequest{
					FlowId: &feedbackEnginePb.FlowIdentifier{
						FlowIdentifier: &feedbackEnginePb.FlowIdentifier_FeedbackAppFlowIdentifier{
							FeedbackAppFlowIdentifier: typesv2.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_NUDGE_DISMISSAL,
						},
					},
					ActorId: "actor-id",
				},
			},
			setupMocks: func(rewardsDaoMock *daoMock.MockRewardsDao) {
			},
			want: &feedbackEnginePb.IsActorEligibleForSurveyResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg("no custom evaluator rule found for given feedback app flow identifier: FEEDBACK_APP_FLOW_IDENTIFIER_NUDGE_DISMISSAL"),
			},
			wantErr: false,
		},
		{
			name: "should return internal error when rewards client call fails",
			args: args{
				ctx: context.Background(),
				req: &feedbackEnginePb.IsActorEligibleForSurveyRequest{
					FlowId: &feedbackEnginePb.FlowIdentifier{
						FlowIdentifier: &feedbackEnginePb.FlowIdentifier_FeedbackAppFlowIdentifier{
							FeedbackAppFlowIdentifier: typesv2.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS,
						},
					},
					ActorId: "actor-id",
				},
			},
			setupMocks: func(rewardsDaoMock *daoMock.MockRewardsDao) {
				rewardsDaoMock.EXPECT().FetchPaginatedRewardsByFiltersV2(gomock.Any(), "actor-id", gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, fmt.Errorf("error while fetching rewards"))
			},
			want: &feedbackEnginePb.IsActorEligibleForSurveyResponse{
				Status: rpc.StatusInternalWithDebugMsg(rpc.StatusAsError(rpc.StatusInternalWithDebugMsg("error while fetching rewards")).Error()),
			},
			wantErr: false,
		},
		{
			name: "should return actor not eligible for survey when actor has not claimed enough rewards",
			args: args{
				ctx: context.Background(),
				req: &feedbackEnginePb.IsActorEligibleForSurveyRequest{
					FlowId: &feedbackEnginePb.FlowIdentifier{
						FlowIdentifier: &feedbackEnginePb.FlowIdentifier_FeedbackAppFlowIdentifier{
							FeedbackAppFlowIdentifier: typesv2.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS,
						},
					},
					ActorId: "actor-id",
				},
			},
			setupMocks: func(rewardsDaoMock *daoMock.MockRewardsDao) {
				rewardsDaoMock.EXPECT().FetchPaginatedRewardsByFiltersV2(gomock.Any(), "actor-id", gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Reward{
					{
						Status: rewardsPb.RewardStatus_PROCESSED,
						ChosenReward: &rewardsPb.RewardOption{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Option: &rewardsPb.RewardOption_FiCoins{
								FiCoins: &rewardsPb.FiCoins{
									Units: 100,
								},
							},
						},
					},
					{
						Status: rewardsPb.RewardStatus_CREATED,
						ChosenReward: &rewardsPb.RewardOption{
							RewardType: rewardsPb.RewardType_CASH,
							Option: &rewardsPb.RewardOption_Cash{
								Cash: &rewardsPb.Cash{
									Amount: &moneypb.Money{
										CurrencyCode: "INR",
										Units:        100,
									},
								},
							},
						},
					},
				}, nil, nil)
			},
			want: &feedbackEnginePb.IsActorEligibleForSurveyResponse{
				Status:                   rpc.StatusOk(),
				IsActorEligibleForSurvey: false,
			},
			wantErr: false,
		},
		{
			name: "should return actor not eligible for survey when actor has not claimed enough cashback or fi-coins",
			args: args{
				ctx: context.Background(),
				req: &feedbackEnginePb.IsActorEligibleForSurveyRequest{
					FlowId: &feedbackEnginePb.FlowIdentifier{
						FlowIdentifier: &feedbackEnginePb.FlowIdentifier_FeedbackAppFlowIdentifier{
							FeedbackAppFlowIdentifier: typesv2.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS,
						},
					},
					ActorId: "actor-id",
				},
			},
			setupMocks: func(rewardsDaoMock *daoMock.MockRewardsDao) {
				rewardsDaoMock.EXPECT().FetchPaginatedRewardsByFiltersV2(gomock.Any(), "actor-id", gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Reward{
					{
						Status: rewardsPb.RewardStatus_PROCESSED,
						ChosenReward: &rewardsPb.RewardOption{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Option: &rewardsPb.RewardOption_FiCoins{
								FiCoins: &rewardsPb.FiCoins{
									Units: 10,
								},
							},
						},
					},
					{
						Status: rewardsPb.RewardStatus_PROCESSED,
						ChosenReward: &rewardsPb.RewardOption{
							RewardType: rewardsPb.RewardType_CASH,
							Option: &rewardsPb.RewardOption_Cash{
								Cash: &rewardsPb.Cash{
									Amount: &moneypb.Money{
										CurrencyCode: "INR",
										Units:        1,
									},
								},
							},
						},
					},
				}, nil, nil)
			},
			want: &feedbackEnginePb.IsActorEligibleForSurveyResponse{
				Status:                   rpc.StatusOk(),
				IsActorEligibleForSurvey: false,
			},
			wantErr: false,
		},
		{
			name: "should return eligible for survey when actor has claimed enough rewards and cashback or fi-coins",
			args: args{
				ctx: context.Background(),
				req: &feedbackEnginePb.IsActorEligibleForSurveyRequest{
					FlowId: &feedbackEnginePb.FlowIdentifier{
						FlowIdentifier: &feedbackEnginePb.FlowIdentifier_FeedbackAppFlowIdentifier{
							FeedbackAppFlowIdentifier: typesv2.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS,
						},
					},
					ActorId: "actor-id",
				},
			},
			setupMocks: func(rewardsDaoMock *daoMock.MockRewardsDao) {
				rewardsDaoMock.EXPECT().FetchPaginatedRewardsByFiltersV2(gomock.Any(), "actor-id", gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.Reward{
					{
						Status: rewardsPb.RewardStatus_PROCESSED,
						ChosenReward: &rewardsPb.RewardOption{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Option: &rewardsPb.RewardOption_FiCoins{
								FiCoins: &rewardsPb.FiCoins{
									Units: 100,
								},
							},
						},
					},
					{
						Status: rewardsPb.RewardStatus_PROCESSED,
						ChosenReward: &rewardsPb.RewardOption{
							RewardType: rewardsPb.RewardType_CASH,
							Option: &rewardsPb.RewardOption_Cash{
								Cash: &rewardsPb.Cash{
									Amount: &moneypb.Money{
										CurrencyCode: "INR",
										Units:        100,
									},
								},
							},
						},
					},
				}, nil, nil)
			},
			want: &feedbackEnginePb.IsActorEligibleForSurveyResponse{
				Status:                   rpc.StatusOk(),
				IsActorEligibleForSurvey: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockRewardsDao := daoMock.NewMockRewardsDao(ctrl)
			tt.setupMocks(mockRewardsDao)
			dynConf, _ := genconf.Load()
			rgs := &RewardsService{
				dyconf:    dynConf,
				rewardDao: mockRewardsDao,
			}
			got, err := rgs.IsActorEligibleForSurvey(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsActorEligibleForSurvey() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("IsActorEligibleForSurvey() diff = %s", diff)
			}
		})
	}
}
