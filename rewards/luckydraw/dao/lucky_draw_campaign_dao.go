package dao

import (
	"context"
	"time"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	luckydrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	"github.com/epifi/gamma/rewards/luckydraw/dao/model"
)

type LuckyDrawCampaignDaoImpl struct {
	db *gormv2.DB
}

func NewLuckyDrawCampaignDaoImpl(db pkgTypes.RewardsPGDB) *LuckyDrawCampaignDaoImpl {
	return &LuckyDrawCampaignDaoImpl{db: db}
}

func (l *LuckyDrawCampaignDaoImpl) Create(ctx context.Context, request *luckydrawPb.CreateLuckyDrawCampaignRequest) (*luckydrawPb.LuckyDrawCampaign, error) {
	defer metric_util.TrackDuration("rewards/luckydraw/dao", "LuckyDrawCampaignDaoImpl", "Create", time.Now())
	txn := gormctxv2.FromContextOrDefault(ctx, l.db)
	dbModel := convertCreateCampaignRequestToDBModel(ctx, request)
	if res := txn.Create(dbModel); res.Error != nil {
		return nil, errors.Wrap(res.Error, "error creating lucky draw campaign")
	}
	return convertCampaignModelToProto(ctx, dbModel), nil
}

func (l *LuckyDrawCampaignDaoImpl) GetById(ctx context.Context, luckyDrawCampaignId string) (*luckydrawPb.LuckyDrawCampaign, error) {
	defer metric_util.TrackDuration("rewards/luckydraw/dao", "LuckyDrawCampaignDaoImpl", "GetById", time.Now())
	txn := gormctxv2.FromContextOrDefault(ctx, l.db)
	dbModel := &model.LuckyDrawCampaign{}
	res := txn.Where("id = ?", luckyDrawCampaignId).First(dbModel)
	if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, errors.Wrap(res.Error, "error fetching lucky draw campaign by id")
	}
	return convertCampaignModelToProto(ctx, dbModel), nil
}

func convertCreateCampaignRequestToDBModel(ctx context.Context, req *luckydrawPb.CreateLuckyDrawCampaignRequest) *model.LuckyDrawCampaign {
	return &model.LuckyDrawCampaign{
		Description: req.GetDescription(),
	}
}

func convertCampaignModelToProto(ctx context.Context, dbModel *model.LuckyDrawCampaign) *luckydrawPb.LuckyDrawCampaign {
	return &luckydrawPb.LuckyDrawCampaign{
		Id:          dbModel.ID,
		Description: dbModel.Description,
	}
}
