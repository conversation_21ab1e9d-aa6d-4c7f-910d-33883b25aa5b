package consumer

import (
	"context"
	"encoding/base64"
	"reflect"
	"time"

	"github.com/golang/protobuf/ptypes"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	queueb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/gamma/api/customdelayqueue/consumer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
)

type OrchestratorConsumerService struct {
	maxDelaySupportedByDestPublisher time.Duration
	// maps required delay to the corresponding delay queue publisher
	// where event needs to be pushed in order to achieve that delay.
	delayToDelayQueuePublisherMap map[time.Duration]queue.Publisher
	// maps destination queue name to a
	// publisher that can publish to that queue
	destQueueNameToPublisherMap map[string]queue.DelayPublisher

	// maps destination queue name to the event proto
	// that needs to be published to the destination queue
	destQueueNameToEventProtoMap map[string]proto.Message
}

func NewOrchestratorConsumerService(maxDelaySupportedByDestPublisher time.Duration, delayToDelayQueuePublisherMap map[time.Duration]queue.Publisher, destQueueNameToPublisherMap map[string]queue.DelayPublisher, destQueueNameToEventProtoMap map[string]proto.Message) *OrchestratorConsumerService {
	return &OrchestratorConsumerService{maxDelaySupportedByDestPublisher: maxDelaySupportedByDestPublisher, delayToDelayQueuePublisherMap: delayToDelayQueuePublisherMap, destQueueNameToPublisherMap: destQueueNameToPublisherMap, destQueueNameToEventProtoMap: destQueueNameToEventProtoMap}
}

// ProcessEvent consumes the event and calculates the delay that needs to be achieved and then pushes the event into
// appropriate delay queue or destination queue (if delay is already achieved or can be achieved by destination queue itself)
func (o OrchestratorConsumerService) ProcessEvent(ctx context.Context, req *consumer.OrchestratorConsumerRequest) (*consumer.OrchestratorConsumerResponse, error) {
	delayDuration := time.Until(req.GetHeader().GetDeliveryToDestAt().AsTime())

	if delayDuration <= o.maxDelaySupportedByDestPublisher {
		// get destination publisher
		destinationPublisher := o.destQueueNameToPublisherMap[req.GetHeader().GetDestQueueName()]
		destinationEventProto := o.destQueueNameToEventProtoMap[req.GetHeader().GetDestQueueName()]
		actualEvent, err := unmarshalEventPayloadToEventProto(req.GetHeader().GetActualEventPayload(), destinationEventProto, req.GetHeader().GetDestQueueName())
		if err != nil {
			logger.Error(ctx, "error unmarshalling payload to proto message", zap.String("payload", req.GetHeader().GetActualEventPayload()))
			return &consumer.OrchestratorConsumerResponse{ResponseHeader: &queueb.ConsumerResponseHeader{Status: queueb.MessageConsumptionStatus_PERMANENT_FAILURE}}, nil
		}
		if delayDuration < 1*time.Second {
			// underlying delay publisher supports only non negative delays.
			delayDuration = time.Second
		}
		// push to destination publisher with delay
		_, err = destinationPublisher.PublishWithDelay(ctx, actualEvent, delayDuration)
		if err != nil {
			logger.Error(ctx, "error publishing event to destination queue", zap.Error(err))
			return &consumer.OrchestratorConsumerResponse{ResponseHeader: &queueb.ConsumerResponseHeader{Status: queueb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}
		return &consumer.OrchestratorConsumerResponse{ResponseHeader: &queueb.ConsumerResponseHeader{Status: queueb.MessageConsumptionStatus_SUCCESS}}, nil
	}

	chosenDelayFromMap := time.Duration(0)
	delayToPublisherMap := o.delayToDelayQueuePublisherMap
	for delay := range delayToPublisherMap {
		if delay <= delayDuration && delay >= chosenDelayFromMap {
			chosenDelayFromMap = delay
		}
	}

	chosenPublisher := delayToPublisherMap[chosenDelayFromMap]
	if chosenPublisher == nil {
		logger.Error(ctx, "invalid delayToDelayQueuePublisherMap configuration, no queue found for delay", zap.Duration("delay", delayDuration))
		return &consumer.OrchestratorConsumerResponse{ResponseHeader: &queueb.ConsumerResponseHeader{Status: queueb.MessageConsumptionStatus_PERMANENT_FAILURE}}, nil
	}

	processAtTimestamp, err := ptypes.TimestampProto(time.Now().Add(chosenDelayFromMap))
	if err != nil {
		logger.Error(ctx, "error parsing time", zap.Error(err))
		return &consumer.OrchestratorConsumerResponse{ResponseHeader: &queueb.ConsumerResponseHeader{Status: queueb.MessageConsumptionStatus_PERMANENT_FAILURE}}, nil
	}
	event := &consumer.OrchestratorEventRequest{
		Header:    req.GetHeader(),
		ProcessAt: processAtTimestamp,
	}

	logger.Debug(ctx, "chosen delay for msg", zap.Duration("duration", chosenDelayFromMap), zap.Duration("total_delay_duration", delayDuration))
	// push event to publisher
	msgId, err := chosenPublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error publishing event to  queue", zap.Error(err))
		return &consumer.OrchestratorConsumerResponse{ResponseHeader: &queueb.ConsumerResponseHeader{Status: queueb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	logger.Debug(ctx, "message published successfully to intermediate queue", zap.String(logger.EVENT_ID, msgId))
	return &consumer.OrchestratorConsumerResponse{ResponseHeader: &queueb.ConsumerResponseHeader{Status: queueb.MessageConsumptionStatus_SUCCESS}}, nil
}

func unmarshalEventPayloadToEventProto(payload string, eventProto proto.Message, destQueue string) (proto.Message, error) {
	defer func() {
		if r := recover(); r != nil {
			logger.Panic("panic while typecasting eventPayload to eventProto", zap.String("destQueue", destQueue), zap.Any("recovery message", r))
		}
	}()
	// creating a new event proto to be used for unmarshalling the event payload
	eventProto = reflect.New(reflect.TypeOf(eventProto).Elem()).Interface().(proto.Message)
	jsonPayload, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		return nil, err
	}
	err = protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(jsonPayload, eventProto)
	if err != nil {
		return nil, err
	}
	return eventProto, nil
}
