// nolint
package jarvisAws

import (
	"context"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/elasticloadbalancingv2/types"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/v2/elbv2"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/jarvis/metrics"
)

func stringInSlice(a string, list []string) bool {
	for _, b := range list {
		if b == a {
			return true
		}
	}
	return false
}

func GetTargetGroups(ctx context.Context, client *elbv2.Client, tg_arn []string) ([]types.TargetGroup, error) {
	defer metrics.RecordJarvisAws("elbv2", "GetTargetGroups", "ok", time.Now())
	targetGroups, err := elbv2.GetTargetGroups(ctx, client, tg_arn)
	if err != nil {
		return nil, err
	}
	return targetGroups, nil
}

func GetLoadBalancers(ctx context.Context, client *elbv2.Client, lb_arns []string) ([]types.LoadBalancer, error) {
	defer metrics.RecordJarvisAws("elbv2", "GetLoadBalancers", "ok", time.Now())
	loadBalancers, err := elbv2.GetLoadBalancers(ctx, client, lb_arns)
	if err != nil {
		return nil, err
	}
	return loadBalancers, nil
}

func GetTargetHealth(ctx context.Context, client *elbv2.Client, tg_arn *string) ([]types.TargetHealthDescription, error) {
	defer metrics.RecordJarvisAws("elbv2", "GetTargetHealth", "ok", time.Now())
	targetHealth, err := elbv2.GetTargetHealth(ctx, client, tg_arn)
	if err != nil {
		return nil, err
	}
	return targetHealth, nil
}

func (jarvisAws *AwsService) GetTrafficStatus(ctx context.Context, app string, env string, tg_arn *string, client *elbv2.Client) (string, bool, string, string, error) {
	if strings.Contains(*tg_arn, "blue") == false && strings.Contains(*tg_arn, "green") == false {
		return "Active", false, "application", "", nil
	}

	tg_arn_list := []string{
		*tg_arn,
	}
	tgs, err := GetTargetGroups(ctx, client, tg_arn_list)
	if err != nil {
		logger.ErrorNoCtx("Error getting target groups ", zap.Error(err))
		return "", false, "", "", err
	}
	var lb_arns []string
	for _, tg := range tgs {
		if tg.TargetGroupArn == tg_arn {
			lb_arns = tg.LoadBalancerArns
			break
		}
	}

	lbs, err := GetLoadBalancers(ctx, client, lb_arns)
	if err != nil {
		logger.ErrorNoCtx("Error getting load balancers ", zap.Error(err))
		return "", false, "", "", err
	}
	lb_type := lbs[0].Type
	targets, err := GetTargetHealth(ctx, client, tg_arn)
	if err != nil {
		logger.ErrorNoCtx("Error getting target health ", zap.Error(err))
		return "", false, "", "", err
	}
	passive_listener_exists := false
	healthy := ""
	traffic_status := ""
	if len(targets) == 0 {
		passive_listener_exists = true
		traffic_status = "Passive"
		healthy = "TG without servers"
	} else {
		traffic_status = "Active"
		for _, target := range targets {
			healthy = string(target.TargetHealth.State)
			if healthy != "healthy" {
				break
			}
		}
	}
	return traffic_status, passive_listener_exists, string(lb_type), healthy, nil
}
