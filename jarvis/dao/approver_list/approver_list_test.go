package approver_list_test

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	pbtypes "github.com/epifi/gamma/api/jarvis/types"
	"github.com/epifi/be-common/pkg/pagination"
)

var (
	approverList1 = &pbtypes.ApproverList{
		ApproverRole: "Admin",
		Approver:     "<EMAIL>",
	}
	approverList2 = &pbtypes.ApproverList{
		ApproverRole: "Admin",
		Approver:     "<EMAIL>",
	}
	approverList3 = &pbtypes.ApproverList{
		ApproverRole: "BU_Admin",
		Approver:     "<EMAIL>",
	}
)

func TestApproverListImpl_CreateApproverList(t *testing.T) {
	// pkgTest.TruncateTestDatabase(t, qts.Db, qts.Conf.JarvisDb.Title)
	type args struct {
		ctx  context.Context
		form *pbtypes.ApproverList
	}

	tests := []struct {
		name    string
		args    args
		want    *pbtypes.ApproverList
		wantErr bool
	}{
		{
			name: "create approver list",
			args: args{
				ctx:  context.Background(),
				form: approverList3,
			},
			want:    approverList3,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := qts.ApproverListDao.Create(tt.args.ctx, tt.args.form)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateApproverList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				// setting id of want to generated uuid
				tt.want.Id = got.GetId()
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&pbtypes.ApproverList{}, "created_at", "updated_at", "deleted_at"),
				}
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("CreateApproverList() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

// nolint:govet
func TestApproverListDao_UpdateApproverList(t *testing.T) {
	// pkgTest.TruncateTestDatabase(t, qts.Db, qts.Conf.JarvisDb.Title)
	a := require.New(t)
	fmAllFields, err := fieldmaskpb.New(&pbtypes.ApproverList{}, "approver_role", "approver", "created_at", "updated_at")
	a.NoError(err, "couldn't create field mask")
	fmFewFields, err := fieldmaskpb.New(&pbtypes.ApproverList{}, "approver_role", "approver", "updated_at")
	a.NoError(err, "couldn't create field mask")
	fmNoFields, err := fieldmaskpb.New(&pbtypes.ApproverList{})
	a.NoError(err, "couldn't create field mask")
	fmFields, err := fieldmaskpb.New(&pbtypes.ApproverList{}, "id", "approver_role", "approver", "created_at", "updated_at")
	a.NoError(err, "couldn't create field mask")
	approverList1, err := qts.ApproverListDao.Create(context.Background(), approverList1)
	a.NoError(err, "couldn't create approver list")
	approverList2, err := qts.ApproverListDao.Create(context.Background(), approverList2)
	a.NoError(err, "couldn't create approver list")
	approverList3, err := qts.ApproverListDao.Create(context.Background(), approverList3)
	a.NoError(err, "couldn't create approver list")
	updateClone1 := getUpdatedClone(approverList1, fmAllFields)
	updateClone2 := getUpdatedClone(approverList2, fmFewFields)
	updateClone3 := getUpdatedClone(approverList3, fmNoFields)
	updateClone4 := getUpdatedClone(approverList3, fmNoFields)
	updateClone4.Id = uuid.New().String()

	type args struct {
		ctx                context.Context
		updateApproverList *pbtypes.ApproverList
		updatedValues      *fieldmaskpb.FieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *pbtypes.ApproverList
		wantErr bool
	}{
		{
			name: "updating all values",
			args: args{
				ctx:                context.Background(),
				updateApproverList: updateClone1,
				updatedValues:      fmAllFields,
			},
			want:    updateClone1,
			wantErr: false,
		},
		{
			name: "updating just description, status, updated_at",
			args: args{
				ctx:                context.Background(),
				updateApproverList: updateClone2,
				updatedValues:      fmFewFields,
			},
			want:    updateClone2,
			wantErr: false,
		},
		{
			name: "updating no field",
			args: args{
				ctx:                context.Background(),
				updateApproverList: updateClone3,
				updatedValues:      fmNoFields,
			},
			want:    updateClone3,
			wantErr: false,
		},
		{
			name: "Id not found",
			args: args{
				ctx:                context.Background(),
				updateApproverList: updateClone4,
				updatedValues:      fmNoFields,
			},
			want:    updateClone4,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := qts.ApproverListDao.Update(tt.args.ctx, tt.args.updateApproverList, tt.args.updatedValues)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateApproverList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			got, _ := qts.ApproverListDao.GetById(tt.args.ctx, tt.args.updateApproverList.GetId(), fmFields)
			if !tt.wantErr {
				// setting id, createdAt and updatedAt
				tt.want.Id = got.GetId()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&pbtypes.ApproverList{}, "created_at", "updated_at", "deleted_at"),
				}
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("UpdateApproverList() got = %v, want %v", protojson.Format(got), protojson.Format(tt.want))
				}
			}
		})
	}
}

func TestApproverListDao_GetById(t *testing.T) {
	// pkgTest.TruncateTestDatabase(t, qts.Db, qts.Conf.JarvisDb.Name)
	a := require.New(t)
	form, err := qts.ApproverListDao.Create(context.Background(), approverList1)
	a.NoError(err, "couldn't create approver list")
	fmFields, err := fieldmaskpb.New(&pbtypes.ApproverList{}, "id", "approver_role", "approver", "created_at", "updated_at")
	a.NoError(err, "couldn't create field mask")

	type args struct {
		ctx      context.Context
		formId   string
		fmFields *fieldmaskpb.FieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *pbtypes.ApproverList
		wantErr bool
	}{
		{
			name: "Getting existing entity",
			args: args{
				ctx:      context.Background(),
				formId:   form.GetId(),
				fmFields: fmFields,
			},
			want:    form,
			wantErr: false,
		},
		{
			name: "Unknown UUID",
			args: args{
				ctx:      context.Background(),
				formId:   uuid.New().String(),
				fmFields: fmFields,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := qts.ApproverListDao.GetById(tt.args.ctx, tt.args.formId, tt.args.fmFields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				// setting Id of want to generated uuid
				tt.want.Id = got.GetId()
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&pbtypes.ApproverList{}, "created_at", "updated_at", "deleted_at"),
				}
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("GetById() got = %v, want %v", protojson.Format(got), protojson.Format(tt.want))
				}
			}
		})
	}
}

func TestApproverListDao_GetByFilters(t *testing.T) {
	// pkgTest.TruncateTestDatabase(t, qts.Db, qts.Conf.JarvisDb.Title)
	a := require.New(t)
	fmFields, err := fieldmaskpb.New(&pbtypes.ApproverList{}, "id", "approver_role", "approver", "created_at", "updated_at")
	a.NoError(err, "couldn't create field mask")
	approverList1, err = qts.ApproverListDao.Create(context.Background(), approverList1)
	a.NoError(err, "couldn't create approver list")
	approverList2, err = qts.ApproverListDao.Create(context.Background(), approverList2)
	a.NoError(err, "couldn't create approver list")
	approverList3, err = qts.ApproverListDao.Create(context.Background(), approverList3)
	a.NoError(err, "couldn't create approver list")

	type args struct {
		ctx                   context.Context
		approverListIds       []string
		approverRole          string
		approver              string
		startTimeForCreatedAt *timestamp.Timestamp
		endTimeForCreatedAt   *timestamp.Timestamp
		startTimeForUpdatedAt *timestamp.Timestamp
		endTimeForUpdatedAt   *timestamp.Timestamp
		fmFields              *fieldmaskpb.FieldMask
		pageToken             *pagination.PageToken
		pageSize              uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []*pbtypes.ApproverList
		wantErr bool
	}{
		{
			name: "Get all forms for given filters of form Ids",
			args: args{
				ctx:                   context.Background(),
				approverListIds:       []string{approverList1.GetId(), approverList2.GetId()},
				startTimeForCreatedAt: nil,
				endTimeForCreatedAt:   nil,
				startTimeForUpdatedAt: nil,
				endTimeForUpdatedAt:   nil,
				fmFields:              fmFields,
			},
			want:    []*pbtypes.ApproverList{approverList1, approverList2},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _, err := qts.ApproverListDao.GetByFilters(tt.args.ctx, tt.args.approverListIds, tt.args.approverRole, tt.args.approver,
				tt.args.fmFields, tt.args.pageToken, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByFilters() error = %v, wantErr %v got %v", err, tt.wantErr, got)
				return
			}
			if !tt.wantErr {
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&pbtypes.ApproverList{}, "created_at", "updated_at", "deleted_at"),
				}
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("GetByFilters() \ngot: %v, \nwant: %v \ndiff: %v", got, tt.want, diff)
				}
			}
		})
	}
}

func getUpdatedClone(form *pbtypes.ApproverList, mask *fieldmaskpb.FieldMask) *pbtypes.ApproverList {
	clone := &pbtypes.ApproverList{}
	_ = copier.Copy(clone, form)

	for _, field := range mask.GetPaths() {
		// nolint: exhaustive
		switch field {
		case "approver_role":
			clone.ApproverRole = form.GetApproverRole() + "-updated"
		case "approver":
			clone.Approver = form.GetApprover() + " [Updated Approver]."
		}
	}
	return clone
}
