package slack_bot

import (
	"golang.org/x/net/context"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/servergen/meta"

	"github.com/epifi/gamma/slack_bot/manager"
	"github.com/epifi/gamma/slack_bot/wire"
)

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer: wire.InitializeSlackBotManagerService,
	},
}

func AfterServiceGroupInit(slackBotService *manager.Service) error {
	if !slackBotService.Conf.Disable() {
		goroutine.RunWithCtx(context.Background(), func(ctx context.Context) {
			err := slackBotService.RunService(ctx)
			if err != nil {
				logger.PanicWithCtx(ctx, "Error in slackbot initialisation")
			}
		})
	}
	return nil
}
