//nolint:dupl
package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/analyser/dao/model"
	"github.com/epifi/gamma/api/analyser/enums"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pinotClient "github.com/epifi/gamma/pkg/pinot"
	qb "github.com/epifi/gamma/pkg/pinot/querybuilder"
)

const AnalyserAATransactionUpdateTable = "analyser_aa_transactions_update"

type AnalyserAATransactionUpdateDaoPinot struct {
	tableName string
	client    pinotClient.Client
}

func NewAnalyserAATransactionUpdateDao(client pinotClient.Client) *AnalyserAATransactionUpdateDaoPinot {
	return &AnalyserAATransactionUpdateDaoPinot{
		tableName: qb.TableWithEnvSuffix(AnalyserAATransactionUpdateTable, client.GetEnv()),
		client:    client,
	}
}

// GetCategoryAggregates returns the total amount spent and total number of aa transactions per ontology id
// It requires a list of transaction ids over which aggregate is to be performed
// Additional L0_ontologies and accounting entry filters are applied if specified
// Note: if l0_ontologies are specified then all records which contain any of the given l0s are returned
// e.g. if [SPEND, LOAN] are l0 filters from request then a record which has [SPEND, INVESTMENTS] is also returned
func (t *AnalyserAATransactionUpdateDaoPinot) GetCategoryAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, fromTime int64, toTime int64, txnIds []string, l0Ontologies []categorizerPb.L0, accountingEntry paymentPb.AccountingEntryType) ([]*model.AnalyserAATransaction, error) {
	defer metric_util.TrackDuration("analyser/dao", "AnalyserAATransactionUpdateDaoPinot", "GetCategoryAggregates", time.Now())
	filterBuilder := qb.NewFilterSqlBuilder()
	filterBuilder.Filter(SuccessfulAATransaction(fromActorId, fromAccountIds, fromTime, toTime)...)
	filterBuilder.Filter(qb.WhereIn("transaction_id", txnIds))

	// category ontology of txn should be part of given list of l0 ontologies
	filterBuilder.Filter(qb.WhereOptionalIn("l0_ontologies", l0Ontologies))
	// apply credit or debit filter (if specified) otherwise both are considered
	if accountingEntry != paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED {
		filterBuilder.Filter(qb.WhereEqual("transaction_type", accountingEntry))
	}

	filter, err := filterBuilder.Build()
	if err != nil {
		return nil, fmt.Errorf("error in building sql where clause: %w", err)
	}
	sql := fmt.Sprintf(aaCategoryAggregateSql, model.AAOntologyId, model.AASumAmount, model.AACount,
		t.tableName, filter, model.AASumAmount)

	return model.QueryAndParseAATransactions(ctx, t.client, t.tableName, sql)
}

// GetTransaction fetches generic representation of analyser aa transaction update record
func (t *AnalyserAATransactionUpdateDaoPinot) GetTransaction(ctx context.Context, txnId string) ([]map[string]interface{}, error) {
	defer metric_util.TrackDuration("analyser/dao", "AnalyserAATransactionUpdateDaoPinot", "GetTransaction", time.Now())
	return getRecord(ctx, t.client, selectAATransactionSql, t.tableName, WithTxnId(txnId))
}

// GetTimeAggregates fetches aggregate on given time granularity with optional filters on ontology ids
// nolint: dupl
func (t *AnalyserAATransactionUpdateDaoPinot) GetTimeAggregates(ctx context.Context, actorId string, fromAccountIds []string, fromTime int64, toTime int64, column txnAggregatesPb.GroupByDimension, excludedTxnIds []string, ontologyIds []string) ([]*model.AnalyserAATransaction, error) {
	defer metric_util.TrackDuration("analyser/dao", "AnalyserAATransactionUpdateDaoPinot", "GetTimeAggregates", time.Now())
	if actorId == "" {
		return nil, fmt.Errorf("actorId cannot be enpty: %w", epifierrors.ErrInvalidArgument)
	}
	filterBuilder := qb.NewFilterSqlBuilder()
	filterBuilder.Filter(SuccessfulDebitAATransaction(actorId, fromAccountIds, fromTime, toTime)...)
	filterBuilder.Filter(qb.WhereIn("transaction_id", excludedTxnIds),
		qb.WhereOptionalIn("ontology_ids", ontologyIds))
	filter, err := filterBuilder.Build()
	if err != nil {
		return nil, fmt.Errorf("error in building sql where clause: %w", err)
	}

	conversionFunc, err := timestampConversionFunc("executed_at_date_unix_ist", column)
	if err != nil {
		return nil, fmt.Errorf("failed to get timestamp conversion function: %w", err)
	}

	sql := fmt.Sprintf(aaTimeAggregateSql, conversionFunc, model.AATimeDurationV2, model.AASumAmount, model.AACount, model.AAAbsoluteMaxAmount,
		t.tableName, filter, model.AATimeDurationV2, model.AASumAmount)

	return model.QueryAndParseAATransactions(ctx, t.client, t.tableName, sql)
}

// GetUserAggregates returns the amount_sum and count aggregates group by with_actor_id
// Additional L0_ontologies and accounting entry filters are applied if specified
// Note: if l0_ontologies are specified then all records which contain any of the given l0s are returned
// e.g. if [SPEND, LOAN] are l0 filters from request then a record which has [SPEND, INVESTMENTS] is also returned
func (t *AnalyserAATransactionUpdateDaoPinot) GetUserAggregates(ctx context.Context, fromActorId string, fromAccountIds []string, fromTime int64, toTime int64, includedTxnIds []string,
	l0Ontologies []categorizerPb.L0, ontologyIds []string, accountingEntry paymentPb.AccountingEntryType) ([]*model.AnalyserAATransaction, error) {
	defer metric_util.TrackDuration("analyser/dao", "AnalyserAATransactionUpdateDaoPinot", "GetUserAggregates", time.Now())
	if len(fromActorId) == 0 {
		return nil, fromActorIdEmptyErr
	}
	if len(fromAccountIds) == 0 {
		return nil, fromAccountIdsListEmptyErr
	}
	filterBuilder := qb.NewFilterSqlBuilder()
	filterBuilder.Filter(SuccessfulAATransaction(fromActorId, fromAccountIds, fromTime, toTime)...)
	filterBuilder.Filter(qb.WhereIn("transaction_id", includedTxnIds))
	// category ontology of txn should be part of given list of l0 ontologies
	filterBuilder.Filter(qb.WhereOptionalIn("l0_ontologies", l0Ontologies))
	// category ontology of txn should be part of ontology ids
	filterBuilder.Filter(qb.WhereOptionalIn("ontology_ids", ontologyIds))
	// apply credit or debit filter (if specified) otherwise both are considered
	if accountingEntry != paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED {
		filterBuilder.Filter(qb.WhereEqual("transaction_type", accountingEntry))
	}
	filter, err := filterBuilder.Build()
	if err != nil {
		return nil, fmt.Errorf("error in building sql where clause: %w", err)
	}

	sql := fmt.Sprintf(aaUserAggregateSql, model.AAWithActorId, model.AASumAmount, model.AACount, model.AAAbsoluteMaxAmount,
		t.tableName, filter, model.AASumAmount)
	return model.QueryAndParseAATransactions(ctx, t.client, t.tableName, sql)
}

// GetTransactionTimeStampAggregate returns aggregated execution timestamp across updated fi transactions
func (t *AnalyserAATransactionUpdateDaoPinot) GetTransactionTimeStampAggregate(
	ctx context.Context,
	actorId string, fromAccountIds []string,
	accountingEntryType paymentPb.AccountingEntryType,
	excludedOntologyIds []string,
	aggregateType enums.AggregateType,
	includedTxnIds []string) ([]*model.AnalyserAATransaction, error) {
	defer metric_util.TrackDuration("analyser/dao", "AnalyserAATransactionUpdateDaoPinot", "GetTransactionTimeStampAggregate", time.Now())

	sql, err := prepareAATransactionTimeStampAggregateSql(t.tableName, actorId, fromAccountIds, accountingEntryType, excludedOntologyIds, aggregateType, qb.WhereIn("transaction_id", includedTxnIds))
	if err != nil {
		return nil, fmt.Errorf("failed to prepare sql for aa update txn timestamp aggregate: %w", err)
	}
	return model.QueryAndParseAATransactions(ctx, t.client, t.tableName, sql)
}
