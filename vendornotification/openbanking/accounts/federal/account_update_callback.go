package federal

import (
	"context"
	"strings"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	vnFedPb "github.com/epifi/gamma/api/vendornotification/openbanking/accounts/federal"
	"github.com/epifi/gamma/api/vendors/federal"

	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"
)

// AccountStatusDateLayout is YYYY-MM-DD HH:MM:SS
const AccountStatusDateLayout = "2006-01-02 15:04:05"

var (
	freezeStringToEnum = map[string]vnFedPb.AccountStatusCallBackData_AccountFreezeStatus{
		"C": vnFedPb.AccountStatusCallBackData_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE,
		"D": vnFedPb.AccountStatusCallBackData_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE,
		"T": vnFedPb.AccountStatusCallBackData_ACCOUNT_FREEZE_STATUS_FULL_FREEZE,
	}

	accountHoldingStringToEnum = map[string]vnFedPb.AccountStatusCallBackData_HolderType{
		"SG": vnFedPb.AccountStatusCallBackData_HOLDER_TYPE_SINGLE,
		"ES": vnFedPb.AccountStatusCallBackData_HOLDER_TYPE_EITHER_OR_SURVIVOR,
		"JT": vnFedPb.AccountStatusCallBackData_HOLDER_TYPE_JOINT,
	}

	accountTypeStringToEnum = map[string]vnFedPb.AccountStatusCallBackData_Type{
		"SBA": vnFedPb.AccountStatusCallBackData_TYPE_SAVINGS,
		"TDA": vnFedPb.AccountStatusCallBackData_TYPE_RECURRING_DEPOSIT,
		"TUA": vnFedPb.AccountStatusCallBackData_TYPE_SMART_DEPOSIT,
		"NRE": vnFedPb.AccountStatusCallBackData_TYPE_SAVINGS,
		"NRO": vnFedPb.AccountStatusCallBackData_TYPE_SAVINGS,
	}

	Timezone = datetime.IST
)

func (s *Service) processAccountUpdateCallBack(ctx context.Context, req *federal.AccountUpdateCallBackResponse) (*empty.Empty, error) {
	data := &vnFedPb.AccountStatusCallBackData{
		AccountNumber:           strings.TrimSpace(req.GetAccountNumber()),
		SolId:                   strings.TrimSpace(req.GetSolId()),
		ReferenceNumber:         req.GetReferenceNumber(),
		SchemeCode:              req.GetSchemeCode(),
		LastCreditTransactionId: strings.TrimSpace(req.GetLastTranIdCr()),
		LastDebitTransactionId:  strings.TrimSpace(req.GetLastTranIdDr()),
		CustomerId:              req.GetCifId(),
		BankId:                  req.GetBankId(),
	}

	var err error

	// status fields
	data.FreezeReasonCodes = []string{
		strings.TrimSpace(req.GetFreezeReasonCode()),
		strings.TrimSpace(req.GetFreezeReasonCode2()),
		strings.TrimSpace(req.GetFreezeReasonCode3()),
		strings.TrimSpace(req.GetFreezeReasonCode4()),
		strings.TrimSpace(req.GetFreezeReasonCode5()),
	}
	if strings.ToUpper(req.GetAccountClosed()) == "Y" {
		data.AccountStatus = vnFedPb.AccountStatusCallBackData_ACCOUNT_STATUS_CLOSED
	} else {
		data.AccountStatus = vnFedPb.AccountStatusCallBackData_ACCOUNT_STATUS_UNSPECIFIED
	}
	data.FreezeStatus = freezeStringToEnum[strings.ToUpper(req.GetAccountFreezed())]

	// dates
	if strings.TrimSpace(req.GetAccountOpenDate()) != "" {
		data.AccountOpeningDate, err = datetime.ParseStringTimestampProtoInLocation(AccountStatusDateLayout, req.GetAccountOpenDate(), Timezone)
		if err != nil {
			logger.Error(ctx, "failed to parse account opening date", zap.Error(err))
			return &empty.Empty{}, nil
		}
	}
	if strings.TrimSpace(req.GetAccountCloseDate()) != "" {
		data.AccountClosingDate, err = datetime.ParseStringTimestampProtoInLocation(AccountStatusDateLayout, req.GetAccountCloseDate(), Timezone)
		if err != nil {
			logger.Error(ctx, "failed to parse account closing date", zap.Error(err))
			return &empty.Empty{}, nil
		}
	}
	if req.GetLastTranDateCr() != "" {
		data.LastCreditTransactionDate, err = datetime.ParseStringTimestampProtoInLocation(AccountStatusDateLayout, req.GetLastTranDateCr(), Timezone)
		if err != nil {
			logger.Error(ctx, "failed to parse last transaction date", zap.Error(err))
		}
	}
	if req.GetLastTranDateDr() != "" {
		data.LastDebitTransactionDate, err = datetime.ParseStringTimestampProtoInLocation(AccountStatusDateLayout, req.GetLastTranDateDr(), Timezone)
		if err != nil {
			logger.Error(ctx, "failed to parse account closing date", zap.Error(err))
		}
	}

	// balances fields parsing
	if req.GetLienMarking() != "" {
		data.LienMarking, err = pkgMoney.ParseString(req.GetLienMarking(), pkgMoney.RupeeCurrencyCode)
		if err != nil {
			logger.Error(ctx, "failed to parse lien money", zap.Error(err))
			return &empty.Empty{}, nil
		}
	}
	if req.GetCumulativeDrAmount() != "" {
		data.CumulativeDebitAmount, err = pkgMoney.ParseString(req.GetCumulativeDrAmount(), pkgMoney.RupeeCurrencyCode)
		if err != nil {
			logger.Error(ctx, "failed to parse cumulative debit money", zap.Error(err))
		}
	}
	if req.GetCumulativeCrAmount() != "" {
		data.CumulativeCreditAmount, err = pkgMoney.ParseString(req.GetCumulativeCrAmount(), pkgMoney.RupeeCurrencyCode)
		if err != nil {
			logger.Error(ctx, "failed to parse cumulative credit money", zap.Error(err))
		}
	}
	if req.GetClrBalance() != "" {
		data.ClearBalance, err = pkgMoney.ParseString(req.GetClrBalance(), pkgMoney.RupeeCurrencyCode)
		if err != nil {
			logger.Error(ctx, "failed to parse clear balance money", zap.Error(err))
		}
	}
	if req.GetUnClearedBalAmount() != "" {
		data.UnclearBalance, err = pkgMoney.ParseString(req.GetUnClearedBalAmount(), pkgMoney.RupeeCurrencyCode)
		if err != nil {
			logger.Error(ctx, "failed to parse uncleared balance money", zap.Error(err))
		}
	}

	// account identifier related
	holderType, ok := accountHoldingStringToEnum[strings.ToUpper(req.GetModeOfOperationCode())]
	if !ok {
		logger.Error(ctx, "failed to parse account holder type", zap.Error(err))
	}
	data.HolderType = holderType
	accountType, ok := accountTypeStringToEnum[strings.ToUpper(req.GetSchemeType())]
	if !ok {
		logger.Error(ctx, "failed to parse account type", zap.Error(err))
		return &empty.Empty{}, nil
	}
	data.AccountType = accountType

	msgId, pubErr := s.accountStatusCallBackPublisher.Publish(ctx, &vnFedPb.AccountStatusCallBackEvent{
		CallBackData: data,
	})
	if pubErr != nil {
		logger.Error(ctx, "error while publishing message in account update callback queue", zap.Error(pubErr), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	}
	return &empty.Empty{}, nil
}
