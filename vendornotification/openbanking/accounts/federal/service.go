package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/savings"
	accountsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/vendors/federal"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/redactor"
)

const CustomerResidentialStatusChangeCallbackRequest = "CustomerResidentialStatusChangeCallbackRequest"

// this is to help dependency injector
type BankCustCallbackPublisher queue.Publisher
type AccountCreationCallbackPublisher queue.Publisher
type AccountStatusCallBackPublisher queue.Publisher
type FederalResidentialStatusUpdatePublisher queue.Publisher

var CheckBankCustomerCreationStatusMap = map[string]bankcust.CustomerCreationState{
	"000":     bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED,
	"CCE_003": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_004": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_005": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_006": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_007": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_008": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_009": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_010": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_011": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_012": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_013": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_014": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_015": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_016": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_017": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_018": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_019": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_020": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"CCE_021": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
	"OBE0022": bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED,
}

var AccountCreationStatusMap = map[string]accountsPb.State{
	"000":     accountsPb.State_CREATED,
	"SB_000":  accountsPb.State_CREATED,
	"SB_001":  accountsPb.State_FAILED,
	"SB_002":  accountsPb.State_FAILED,
	"SB_003":  accountsPb.State_FAILED,
	"SB_004":  accountsPb.State_FAILED,
	"SB_005":  accountsPb.State_FAILED,
	"SB_006":  accountsPb.State_FAILED,
	"SB_013":  accountsPb.State_FAILED,
	"SB_011":  accountsPb.State_FAILED,
	"SB_015":  accountsPb.State_FAILED,
	"OBE0022": accountsPb.State_FAILED,
}

type Service struct {
	BankCustomerCallbackPublisher    BankCustCallbackPublisher
	AccountCreationCallbackPublisher AccountCreationCallbackPublisher
	config                           *genconf.Config
	accountStatusCallBackPublisher   AccountStatusCallBackPublisher
	residentStatusUpdateCallbackPub  FederalResidentialStatusUpdatePublisher
}

func NewService(bankCustomerCallbackPublisher BankCustCallbackPublisher, accountCreationCallbackPublisher AccountCreationCallbackPublisher, config *genconf.Config, accountStatusCallBackPublisher AccountStatusCallBackPublisher, residentStatusUpdateCallbackPub FederalResidentialStatusUpdatePublisher) *Service {
	return &Service{
		BankCustomerCallbackPublisher:    bankCustomerCallbackPublisher,
		AccountCreationCallbackPublisher: accountCreationCallbackPublisher,
		config:                           config,
		accountStatusCallBackPublisher:   accountStatusCallBackPublisher,
		residentStatusUpdateCallbackPub:  residentStatusUpdateCallbackPub,
	}
}

func (s *Service) ProcessCustomerCreationCallBack(ctx context.Context, req *federal.CustomerCreationCallBackResponse) (*empty.Empty, error) {

	logger.Info(ctx, fmt.Sprintf("Received customer creation callback response for request-id: , responseCode %s %s",
		req.RequestId, req.ResponseCode))
	creationStatus, ok := CheckBankCustomerCreationStatusMap[req.ResponseCode]
	if !ok {
		logger.Error(ctx, fmt.Sprintf("unknown response code from vendor, %v",
			req.GetResponseCode()), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		return &empty.Empty{}, nil
	}

	// ensure created customers don't have nil customer id
	if creationStatus == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED && req.GetCustomerId() == "" {
		logger.Error(ctx, "nil customer id in callback", zap.String(logger.REQUEST_ID, req.GetRequestId()))
		return &empty.Empty{}, nil
	}

	var createdAt *timestamppb.Timestamp
	if req.GetCreatedOn() != "" {
		var err error
		createdAt, err = datetime.ParseFederalTS(req.GetCreatedOn())
		if err != nil {
			logger.Error(ctx, "error parsing federal timestamp", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		}
	}
	bankCustCCCallbackReq := &bankcust.ProcessCustomerCreationCallbackRequest{
		RequestHeader: &queuePb.ConsumerRequestHeader{},
		RequestId:     req.RequestId,
		CustomerId:    req.CustomerId,
		Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
		Status:        creationStatus,
		CreatedAt:     createdAt,
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        req.GetResponseCode(),
			Description: req.GetResponseReason(),
		},
	}
	if s.config.FeatureFlags().AllowCustomerCallbackProcessing() {
		_, err := s.BankCustomerCallbackPublisher.Publish(ctx, bankCustCCCallbackReq)
		if err != nil {
			logger.Error(ctx, "failed to process callback", zap.Error(err))
			return nil, err
		}
	}
	return &empty.Empty{}, nil
}

func (s *Service) ProcessAccountCreationCallBack(ctx context.Context, req *federal.AccountCreationCallBackResponse) (*empty.Empty, error) {

	logger.Info(ctx, fmt.Sprintf("received account creation callback response for request-id: , responseCode %s %s",
		req.RequestId, req.ResponseCode))
	var creationStatus accountsPb.State
	creationStatus, ok := AccountCreationStatusMap[req.GetResponseCode()]
	if !ok {
		logger.Error(ctx, fmt.Sprintf("unknown response code from vendor, %v",
			req.GetResponseCode()), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		return &empty.Empty{}, nil
	}

	var createdAt *timestamppb.Timestamp
	if req.GetCreatedOn() != "" {
		var err error
		createdAt, err = datetime.ParseFederalTS(req.GetCreatedOn())
		if err != nil {
			logger.Error(ctx, "error parsing federal timestamp", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		}
	}

	accountCreationCallbackReq := &savings.ProcessAccountCreationCallbackRequest{
		RequestHeader:       &queuePb.ConsumerRequestHeader{},
		RequestId:           req.RequestId,
		SavingAccountNumber: req.SavingAccountNumber,
		Vendor:              commonvgpb.Vendor_FEDERAL_BANK,
		CreationStatus:      creationStatus,
		CreatedAt:           createdAt,
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        req.GetResponseCode(),
			Description: req.GetResponseReason(),
		},
	}
	// Allow callback only if enabled in config
	if s.config.FeatureFlags().AllowAccountCallbackProcessing() {
		_, err := s.AccountCreationCallbackPublisher.Publish(ctx, accountCreationCallbackReq)
		if err != nil {
			logger.Error(ctx, "failed to process callback", zap.Error(err))
			return nil, err
		}
	}
	return &empty.Empty{}, nil
}

func (s *Service) ProcessAccountUpdateCallBack(ctx context.Context, req *federal.AccountUpdateCallBackResponse) (*empty.Empty, error) {
	return s.processAccountUpdateCallBack(ctx, req)
}

func (s *Service) ProcessCustomerResidentialStatusChange(ctx context.Context, req *federal.CustomerResidentialStatusChangeCallback) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, CustomerResidentialStatusChangeCallbackRequest, req.GetCustomerId(), vendorsRedactor.Config)
	return s.processResidentialUpdateCallback(ctx, req)
}
