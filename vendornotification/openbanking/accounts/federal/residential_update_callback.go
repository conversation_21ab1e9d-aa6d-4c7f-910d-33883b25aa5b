package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	ResidentialUpdateReasonToAccountTypeMap = map[string]bankcust.AccountType{
		"Device Deactivated on NRE Updation": bankcust.AccountType_ACCOUNT_TYPE_NRE,
	}
)

const ResidentialStatusDateLayout = "2006-01-02 15:04:05.000000"

func (s *Service) processResidentialUpdateCallback(ctx context.Context, req *federal.CustomerResidentialStatusChangeCallback) (*emptypb.Empty, error) {
	accountType := ResidentialUpdateReasonToAccountTypeMap[req.GetReason()]
	if accountType == bankcust.AccountType_ACCOUNT_TYPE_UNSPECIFIED {
		logger.Error(ctx, fmt.Sprintf("residential update reason does not exit in map, reason: %v", req.GetReason()))
	}
	var updatedAt *timestamppb.Timestamp
	var err error
	if strings.TrimSpace(req.GetTimestamp()) != "" {
		updatedAt, err = datetime.ParseStringTimestampProtoInLocation(ResidentialStatusDateLayout, req.GetTimestamp(), Timezone)
		if err != nil {
			logger.Error(ctx, "failed to parse account opening date", zap.Error(err))
		}
	}
	msgId, pubErr := s.residentStatusUpdateCallbackPub.Publish(ctx, &bankcust.ProcessResidentialStatusUpdateCallbackRequest{
		CustomerId:  req.GetCustomerId(),
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
		AccountType: accountType,
		Reason:      req.GetReason(),
		UpdatedAt:   updatedAt,
	})
	if pubErr != nil {
		logger.Error(ctx, "error while publishing message in residential update callback queue", zap.Error(pubErr), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	}
	return &emptypb.Empty{}, nil
}
