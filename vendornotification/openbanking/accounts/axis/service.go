package axis

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/epifi/gamma/api/vendors/axis"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"
)

type Service struct {
	config *config.Config
}

func NewService(conf *config.Config) *Service {
	return &Service{config: conf}
}

func (s *Service) ProcessProductAllocationCallBack(ctx context.Context, req *axis.ProductAllocationCallBackResponse) (*emptypb.Empty, error) {
	logger.Info(ctx, fmt.Sprintf("Received product allocation callback response : %s",
		req.String()))

	return &emptypb.Empty{}, nil
}

func (s *Service) ProcessAccountActivationCallBack(ctx context.Context, req *axis.AccountActivationCallBackResponse) (*emptypb.Empty, error) {
	logger.Info(ctx, fmt.Sprintf("Received account activation callback response : %s",
		req.String()))
	return &emptypb.Empty{}, nil
}
