package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpConsumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	rpFederalPb "github.com/epifi/gamma/api/vendornotification/openbanking/recurringpayment/federal"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
)

const (
	processEnachMandateRegistrationCallbackRequest = "ProcessEnachMandateRegistrationCallbackRequest"
	transactionSuccessFlagString                   = "1"
)

type EnachRegistrationAuthorisationCallbackPublisher queue.Publisher

type Service struct {
	enachRegistrationAuthorisationCallbackPublisher queue.Publisher
}

// compile time check to ensure Service implements rpFederalPb.RecurringPaymentServer
var _ rpFederalPb.RecurringPaymentServer = &Service{}

// NewService method for creating an instance of recurring payemnt notification service. This method will be used by the
// injector when providing the dependencies at initialization.
func NewService(
	enachRegistrationAuthorisationCallbackPublisher EnachRegistrationAuthorisationCallbackPublisher,
) *Service {
	return &Service{
		enachRegistrationAuthorisationCallbackPublisher: enachRegistrationAuthorisationCallbackPublisher,
	}
}

func (s *Service) ProcessENachMandateRegistrationCallback(ctx context.Context, req *rpFederalPb.ENachMandateRegistrationCallbackRequest) (*emptypb.Empty, error) {
	// If validation fails before request reaches NPCI
	if req.GetError() {
		logger.Info(ctx, "received validation error in enach mandate registration authorization callback", zap.String(logger.REQUEST_ID, req.GetTransactionNumber()), zap.String(logger.ERROR_REASON, req.GetMessage()))
		metrics.RecordEnachMandateRegistrationAuthCallbackStatus(commonvgpb.Vendor_FEDERAL_BANK, metrics.GenericFailureStatus)
		return &emptypb.Empty{}, nil
	}

	callbackPayload := req.GetCallbackPayload()
	redactor.LogCallbackRequestData(ctx, callbackPayload, processEnachMandateRegistrationCallbackRequest, req.GetTransactionNumber(), vendorsRedactor.Config)

	// check the registration authorization status in the callback payload, if the status is not success then return from here only.
	if callbackPayload.GetResponse().GetTxnFlag() != transactionSuccessFlagString {
		logger.Info(ctx, "received failed status in enach mandate registration authorization callback", zap.String(logger.REQUEST_ID, req.GetTransactionNumber()), zap.String(logger.STATUS, callbackPayload.GetResponse().GetTxnFlag()), zap.String(logger.ERROR_CODE, callbackPayload.GetResponse().GetErrCode()), zap.String(logger.ERROR_REASON, callbackPayload.GetResponse().GetErrDesc()))
		metrics.RecordEnachMandateRegistrationAuthCallbackStatus(commonvgpb.Vendor_FEDERAL_BANK, metrics.GenericFailureStatus)
		return &emptypb.Empty{}, nil
	}

	// if authorization was successful, publish authorization payload to recurring payment service
	_, err := s.enachRegistrationAuthorisationCallbackPublisher.Publish(ctx, &rpConsumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest{
		VendorRequestId:      req.GetTransactionNumber(),
		RecurringPaymentType: rpPb.RecurringPaymentType_ENACH_MANDATES,
		RecurringPaymentTypeSpecificPayload: &rpConsumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload_{
			EnachPayload: &rpConsumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload{
				Umrn:                       callbackPayload.GetResponse().GetUmrn(),
				NpciRefId:                  callbackPayload.GetResponse().GetNpciRefId(),
				DestBankReferenceNumber:    callbackPayload.GetResponse().GetDestinationBankRefNum(),
				MerchantReferenceMessageId: callbackPayload.GetResponse().GetMerchantMsgRefId(),
			},
		},
	})
	if err != nil {
		logger.Error(ctx, "error publishing enach mandate registration authorization event", zap.String(logger.UMRN, callbackPayload.GetResponse().GetUmrn()), zap.String(logger.REQUEST_ID, req.GetTransactionNumber()), zap.Error(err))
		metrics.RecordEnachMandateRegistrationAuthCallbackProcessingFailure(commonvgpb.Vendor_FEDERAL_BANK)
		// returning minimal error message as this api is called by external party and we do not want to expose too much info to them for security reasons.
		return nil, errors.New("failure")
	}

	metrics.RecordEnachMandateRegistrationAuthCallbackStatus(commonvgpb.Vendor_FEDERAL_BANK, metrics.GenericSuccessStatus)
	logger.Info(ctx, "successfully published enach mandate registration authorization callback", zap.String(logger.UMRN, callbackPayload.GetResponse().GetUmrn()), zap.String(logger.REQUEST_ID, req.GetTransactionNumber()))

	return &emptypb.Empty{}, nil
}
