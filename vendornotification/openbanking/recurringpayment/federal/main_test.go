package federal_test

import (
	"os"
	"testing"

	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/test"
)

var (
	conf *config.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	var teardown func()
	conf, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
