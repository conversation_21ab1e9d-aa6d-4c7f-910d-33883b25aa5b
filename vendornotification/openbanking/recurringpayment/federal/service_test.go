package federal

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rpConsumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	rpFederalVnPb "github.com/epifi/gamma/api/vendornotification/openbanking/recurringpayment/federal"
	"github.com/epifi/be-common/pkg/epifierrors"
	queueMock "github.com/epifi/be-common/pkg/queue/mocks"
)

func TestService_ProcessENachMandateRegistrationCallback(t *testing.T) {
	const (
		defaultTransactionNumber        = "default_test_transaction_number"
		defaultMerchantReferenceId      = "default_test_merchant_reference_id"
		defaultErrorCode                = "default_test_error_code"
		defaultErrorDescription         = "default_test_error_description"
		defaultNPCIRefId                = "default_test_npci_ref_id"
		defaultDestinationBankCode      = "default_test_destination_bank_code"
		defaultDestinationBankRefNumber = "default_test_destination_bank_ref_number"
		defaultUmrn                     = "default_test_umrn"
	)
	var (
		defaultSuccessResponsePayload = &rpFederalVnPb.ENachMandateRegistrationCallbackPayload{
			Response: &rpFederalVnPb.PayloadDetails{
				MerchantMsgRefId:      defaultMerchantReferenceId,
				ErrCode:               defaultErrorCode,
				ErrDesc:               defaultErrorDescription,
				TxnFlag:               transactionSuccessFlagString,
				NpciRefId:             defaultNPCIRefId,
				DestinationBankCode:   defaultDestinationBankCode,
				DestinationBankRefNum: defaultDestinationBankRefNumber,
				Umrn:                  defaultUmrn,
			},
		}
		defaultFailedResponsePayload = &rpFederalVnPb.ENachMandateRegistrationCallbackPayload{
			Response: &rpFederalVnPb.PayloadDetails{
				MerchantMsgRefId:      defaultMerchantReferenceId,
				ErrCode:               defaultErrorCode,
				ErrDesc:               defaultErrorDescription,
				TxnFlag:               "0",
				NpciRefId:             defaultNPCIRefId,
				DestinationBankCode:   defaultDestinationBankCode,
				DestinationBankRefNum: defaultDestinationBankRefNumber,
				Umrn:                  defaultUmrn,
			},
		}
	)

	tests := []struct {
		name       string
		req        *rpFederalVnPb.ENachMandateRegistrationCallbackRequest
		setupMocks func(mockPublisher *queueMock.MockPublisher)
		wantErr    bool
	}{
		{
			name: "Should log error if failed to validate request with federal.",
			req: &rpFederalVnPb.ENachMandateRegistrationCallbackRequest{
				Error:   true,
				Message: "Transaction Number Already Used'",
			},
			setupMocks: func(mockPublisher *queueMock.MockPublisher) {},
		},
		{
			name: "Should return if txnFlag is set to false",
			req: &rpFederalVnPb.ENachMandateRegistrationCallbackRequest{
				CallbackPayload:   defaultFailedResponsePayload,
				TransactionNumber: defaultTransactionNumber,
			},
			setupMocks: func(mockPublisher *queueMock.MockPublisher) {},
		},
		{
			name: "Should log error if failed to publish to callback queue",
			req: &rpFederalVnPb.ENachMandateRegistrationCallbackRequest{
				CallbackPayload:   defaultSuccessResponsePayload,
				TransactionNumber: defaultTransactionNumber,
			},
			setupMocks: func(mockPublisher *queueMock.MockPublisher) {
				mockPublisher.EXPECT().Publish(gomock.Any(), &rpConsumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest{
					VendorRequestId:      defaultTransactionNumber,
					RecurringPaymentType: rpPb.RecurringPaymentType_ENACH_MANDATES,
					RecurringPaymentTypeSpecificPayload: &rpConsumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload_{
						EnachPayload: &rpConsumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload{
							Umrn:                       defaultUmrn,
							NpciRefId:                  defaultNPCIRefId,
							DestBankReferenceNumber:    defaultDestinationBankRefNumber,
							MerchantReferenceMessageId: defaultMerchantReferenceId,
						},
					}}).Return("", epifierrors.ErrPermanent)
			},
			wantErr: true,
		},
		{
			name: "Should publish to consumer if txnFlag is set to true",
			req: &rpFederalVnPb.ENachMandateRegistrationCallbackRequest{
				CallbackPayload:   defaultSuccessResponsePayload,
				TransactionNumber: defaultTransactionNumber,
			},
			setupMocks: func(mockPublisher *queueMock.MockPublisher) {
				mockPublisher.EXPECT().Publish(gomock.Any(), &rpConsumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest{
					VendorRequestId:      defaultTransactionNumber,
					RecurringPaymentType: rpPb.RecurringPaymentType_ENACH_MANDATES,
					RecurringPaymentTypeSpecificPayload: &rpConsumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload_{
						EnachPayload: &rpConsumerPb.RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload{
							Umrn:                       defaultUmrn,
							NpciRefId:                  defaultNPCIRefId,
							DestBankReferenceNumber:    defaultDestinationBankRefNumber,
							MerchantReferenceMessageId: defaultMerchantReferenceId,
						},
					}}).Return("", nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockFederalEnachRegistrationAuthorisationCallbackPublisher := queueMock.NewMockPublisher(ctr)

			s := &Service{
				enachRegistrationAuthorisationCallbackPublisher: mockFederalEnachRegistrationAuthorisationCallbackPublisher,
			}

			tt.setupMocks(mockFederalEnachRegistrationAuthorisationCallbackPublisher)

			_, err := s.ProcessENachMandateRegistrationCallback(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessENachMandateRegistrationCallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
