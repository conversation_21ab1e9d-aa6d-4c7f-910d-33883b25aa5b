package federal

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/rpc/code"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	authConsumerPb "github.com/epifi/gamma/api/auth/consumer"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/vendors/federal"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	federalPkg "github.com/epifi/gamma/pkg/vendors/federal"
	"github.com/epifi/gamma/vendornotification/redactor"
)

type DeviceRegSMSAckPublisher queue.Publisher
type DeviceReRegCallbackPublisher queue.Publisher
type FederalMobileNumberUpdatePublisher queue.Publisher

var (
	errInternalServer = errors.New("internal servDeviceReRegistrationCallbacker error")
	Timezone          = datetime.IST
)

const (
	DeviceReRegistrationCallback = "DeviceReRegistrationCallback"
	MobileUpdateCallback         = "MobileUpdateCallback"
	MobileUpdateDateLayout       = "2006-01-02 15:04:05.000000"
	DeviceDeactivationReason     = "Device Deactivated on MOBILE_NUMBER Updation"
)

type Service struct {
	deviceReregCallbackPublisher  DeviceReRegCallbackPublisher
	deviceRegSMSAckPublisher      DeviceRegSMSAckPublisher
	mobileNumberUpdateCallbackPub FederalMobileNumberUpdatePublisher
}

func NewService(deviceReregCallbackPublisher DeviceReRegCallbackPublisher, deviceRegSMSAckPublisher DeviceRegSMSAckPublisher, mobileNumberUpdateCallbackPub FederalMobileNumberUpdatePublisher) *Service {
	return &Service{
		deviceReregCallbackPublisher:  deviceReregCallbackPublisher,
		deviceRegSMSAckPublisher:      deviceRegSMSAckPublisher,
		mobileNumberUpdateCallbackPub: mobileNumberUpdateCallbackPub,
	}
}

func (s *Service) ProcessDeviceReRegistrationEvent(ctx context.Context, req *federal.DeviceReRegistrationCallback) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, DeviceReRegistrationCallback, req.GetRequestId(), vendorsRedactor.Config)
	zapReqId := zap.String(logger.REQUEST_ID, req.GetRequestId())
	logger.Info(ctx, fmt.Sprintf("AFU DevReReg callback received with action: %v | code: %v | desc: %v",
		req.GetResponseAction(), req.GetResponseCode(), req.GetResponseReason()), zapReqId)

	reqId := req.GetRequestId()
	debugMsg := fmt.Sprintf("received response(%v): %v", req.GetResponseCode(), req.GetResponseReason())
	afuStatus := federalPkg.MapDeviceReRegEnquiryCodes(req.GetResponseCode(), req.GetResponseReason())

	// process response action
	switch req.GetResponseAction() {
	case federalPkg.SuccessResponseAction:
		// ensure afu status is also success
		if !afuStatus.IsSuccess() {
			errMsg := fmt.Sprintf("invalid res code in success action: %s", req.ResponseCode)
			logger.Error(ctx, fmt.Sprintf("AFU DevReReg callback: %v", errMsg), zapReqId)
			afuStatus = rpc.NewStatusWithoutDebug(uint32(code.Code_INTERNAL), errMsg)
		}

	case federalPkg.FailureResponseAction:
		// ensure afu status is failure response code
		if afuStatus.IsSuccess() {
			errMsg := fmt.Sprintf("success code in failure action: %s", req.ResponseCode)
			logger.Error(ctx, fmt.Sprintf("AFU DevReReg callback: %v", errMsg), zapReqId)
			afuStatus = rpc.NewStatusWithoutDebug(uint32(code.Code_INTERNAL), errMsg)
		}

	default:
		errMsg := fmt.Sprintf("invalid response action: %s", req.GetResponseAction())
		logger.Error(ctx, fmt.Sprintf("AFU DevReReg callback: %v", errMsg), zapReqId)
		afuStatus = rpc.NewStatusWithoutDebug(uint32(code.Code_INTERNAL), errMsg)
	}

	// TODO(aditya): keeping for bwd compatibility; remove this code once deployed to prod
	var reqSt authConsumerPb.DeviceReRegCallbackConsumerRequest_RequestStatus
	if afuStatus.IsSuccess() {
		logger.Info(ctx, "received successful device re-registration callback", zap.String("requestId", reqId))
		reqSt = authConsumerPb.DeviceReRegCallbackConsumerRequest_SUCCESS
	} else {
		logger.Error(ctx, "received failed device re-registration callback", zap.String("requestId", reqId),
			zap.String("debugMsg", debugMsg))
		reqSt = authConsumerPb.DeviceReRegCallbackConsumerRequest_FAILURE
	}

	if messageId, err := s.deviceReregCallbackPublisher.Publish(ctx, &authConsumerPb.DeviceReRegCallbackConsumerRequest{
		RequestId:              reqId,
		DeviceToken:            req.GetDeviceToken(),
		AuthFactorUpdateStatus: afuStatus,
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        req.GetResponseCode(),
			Description: req.GetResponseReason(),
		},
		Status: reqSt,
	}); err != nil {
		logger.Error(ctx, "unable to publish device re-registration event", zap.String("requestId", reqId), zap.Error(err))
		return nil, errInternalServer
	} else {
		logger.Info(ctx, "device re-registration message published successfuly", zap.String("requestId", reqId),
			zap.String("messageId", messageId))
	}

	return &empty.Empty{}, nil
}

func (s *Service) ProcessDeviceRegistrationSMSUpdate(ctx context.Context, req *federal.DeviceRegistrationSMSUpdateCallback) (*empty.Empty, error) {
	logger.Info(ctx, "DevReg SMS update callback received", zap.String(logger.PAYLOAD, req.String()))
	const successSMSAck = "SUCCESS"
	var (
		smsFrom   *commontypes.PhoneNumber
		err       error
		timeNow   = timestamppb.Now()
		messageId string
	)

	if smsFrom, err = commontypes.ParsePhoneNumber(req.MobileNumber); err != nil {
		logger.Error(ctx, fmt.Sprintf("unable to parse mobile number: %s", req.GetMobileNumber()), zap.Error(err))
		return &empty.Empty{}, nil
	}
	if req.GetStatus() == successSMSAck {
		if messageId, err = s.deviceRegSMSAckPublisher.Publish(ctx, &authConsumerPb.ProcessDevRegSMSAcknowledgementRequest{
			SmsFrom:          smsFrom,
			SmsAckAt:         timeNow,
			SmsAckReceivedAt: timeNow,
		}); err != nil {
			logger.Error(ctx, "unable to publish dev reg sms ack update", zap.Error(err))
		} else {
			logger.Info(ctx, "dev reg sms ack update published successfully", zap.String(logger.QUEUE_MESSAGE_ID, messageId))
		}
	}
	return &empty.Empty{}, nil
}

func (s *Service) ProcessPhoneUpdate(ctx context.Context, req *federal.PhoneUpdateCallback) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, MobileUpdateCallback, req.GetRequestId(), vendorsRedactor.Config)
	logger.Info(ctx, fmt.Sprintf("Mobile number update callback received with reason: %v",
		req.GetReason()), zap.String(logger.PAYLOAD, req.String()))
	var updatedAt *timestamppb.Timestamp
	var err error
	if strings.TrimSpace(req.GetTimestamp()) != "" {
		updatedAt, err = datetime.ParseStringTimestampProtoInLocation(MobileUpdateDateLayout, req.GetTimestamp(), Timezone)
		if err != nil {
			logger.Error(ctx, "failed to parse mobile update date", zap.Error(err))
		}
	}
	if req.GetReason() != DeviceDeactivationReason {
		logger.Error(ctx, fmt.Sprintf("mobile number update reason does not match with the contract, reason: %v", req.GetReason()))
		return &emptypb.Empty{}, nil
	}
	msgId, pubErr := s.mobileNumberUpdateCallbackPub.Publish(ctx, &bankcust.ProcessMobileNumberUpdateCallbackRequest{
		CustomerId:      req.GetCustomerId(),
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		Reason:          req.GetReason(),
		UpdatedAt:       updatedAt,
		RequestId:       req.GetRequestId(),
		OldMobileNumber: req.GetOldMobileNumber(),
		CbsMobileNumber: req.GetCbsMobileNumber(),
	})
	if pubErr != nil {
		logger.Error(ctx, "error while publishing message in mobile number update callback queue", zap.Error(pubErr), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	}
	return &emptypb.Empty{}, nil
}
