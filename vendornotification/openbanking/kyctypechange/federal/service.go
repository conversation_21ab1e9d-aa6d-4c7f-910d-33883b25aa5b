package federal

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/bankcust"
	kycTypeChangePb "github.com/epifi/gamma/api/vendornotification/openbanking/kyctypechange/federal"
	vendorFederal "github.com/epifi/gamma/api/vendors/federal"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/redactor"
	//"github.com/golang/protobuf/ptypes"
)

//var internalServerErr = errors.New("internal server error")

const (
	KycTypeChangeCallbackRequest = "KycTypeChangeCallbackRequest"
)

type Service struct {
	fedVkycUpdPublisher                queue.Publisher
	fedBankCustKycStateChangePublisher queue.Publisher
}

type FederalVkycUpdatePublisher queue.Publisher
type FederalBankCustKycStateChangePublisher queue.Publisher

// Constructor to create an instance of the service to process KycTypeChange notifications from Federal bank
func NewService(fedVkycUpdPublisher FederalVkycUpdatePublisher, fedBankCustKycStateChangePublisher FederalBankCustKycStateChangePublisher) *Service {
	return &Service{
		fedVkycUpdPublisher:                fedVkycUpdPublisher,
		fedBankCustKycStateChangePublisher: fedBankCustKycStateChangePublisher,
	}
}
func (s *Service) KycTypeChangeCallback(ctx context.Context, req *vendorFederal.KycTypeChangeCallbackRequest) (*vendorFederal.KycTypeChangeCallbackResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, KycTypeChangeCallbackRequest, req.GetUniqueVkycTxnId(), vendorsRedactor.Config)

	if req.GetUniqueVkycTxnId() == "" {
		// whenever transcation id is missing we will trigger alert to keep track on those
		logger.Info(ctx, fmt.Sprintf("Transcation id missing in fed response for ekyc %v", req.GetUniqueUidaiRrn()))
	}

	beEvent := &kycTypeChangePb.KYCStateChangeEvent{
		UniqueUidaiRrn: req.GetUniqueUidaiRrn(),
		KycStatus:      req.GetKycStatus(),
		KycDate:        req.GetKycDate(),
		Remarks:        req.GetRemarks(),
		EventTimestamp: timestamppb.Now(),
		TransactionId:  req.GetUniqueVkycTxnId(),
		EventId:        uuid.New().String(),
	}

	sqsMsgId, err := s.fedVkycUpdPublisher.Publish(ctx, beEvent)
	if err != nil {
		logger.Error(ctx, "error publishing federal vkyc update event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "federal vkyc update event published in queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))

	beKycUpdEvent := &bankcust.ProcessKYCStateChangeEventRequest{
		UniqueUidaiRrn: req.GetUniqueUidaiRrn(),
		KycStatus:      req.GetKycStatus(),
		KycDate:        req.GetKycDate(),
		Remarks:        req.GetRemarks(),
		EventTimestamp: timestamppb.Now(),
		TransactionId:  req.GetUniqueVkycTxnId(),
		EventId:        uuid.New().String(),
	}

	sqsMsgId, err = s.fedBankCustKycStateChangePublisher.Publish(ctx, beKycUpdEvent)
	if err != nil {
		logger.Error(ctx, "error publishing federal kyc state change event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}

	logger.Info(ctx, "federal kyc state change event published in queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))

	return &vendorFederal.KycTypeChangeCallbackResponse{
		UniqueVkycTxnId:     req.GetUniqueVkycTxnId(),
		UniqueUidaiRrn:      req.GetUniqueUidaiRrn(),
		KycStatusUpdtStatus: req.GetKycStatus(),
	}, nil
}
