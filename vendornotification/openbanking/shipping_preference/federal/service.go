package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/jinzhu/copier"

	"go.uber.org/zap"

	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/vendornotification/redactor"

	"github.com/golang/protobuf/ptypes/empty"

	shippingPreferencePb "github.com/epifi/gamma/api/user/shipping_preference"
	federalPb "github.com/epifi/gamma/api/vendornotification/openbanking/shipping_preference/federal"
	vdPb "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/be-common/pkg/queue"
)

// CodeToShippingAddressUpdateStatusMap maps the federal ResponseCode parameter to the internal shipping preference update state.
// Flag error if the ResponseCode value received is not part of this map. Denotes that the error codes
// provided as part of api spec has changed. Should be addressed asap or we may miss to process callbacks.
var CodeToShippingAddressUpdateStatusMap = map[string]shippingPreferencePb.RequestState{
	"000":     shippingPreferencePb.RequestState_SUCCESS,
	"OBE0071": shippingPreferencePb.RequestState_FAILED, // System Failure
	"OBE0007": shippingPreferencePb.RequestState_FAILED, // Internal Failure
}

type UpdateShippingAddressCallbackPublisher queue.Publisher

// Service implements RPC methods that would consume shipping preference related vendor notifications from Federal Bank
type Service struct {
	federalPb.UnimplementedShippingPreferenceServer
	addressUpdateCallbackPublisher queue.Publisher
}

func NewService(addressUpdateCallbackPublisher UpdateShippingAddressCallbackPublisher) *Service {
	return &Service{addressUpdateCallbackPublisher: addressUpdateCallbackPublisher}
}

const (
	UpdateShippingAddressCallBackRequest = "UpdateShippingAddressCallBackRequest"
)

func (s *Service) ProcessUpdateShippingAddressCallBack(ctx context.Context, req *vdPb.UpdateShippingAddressCallBackRequest) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, UpdateShippingAddressCallBackRequest, req.GetRequestId(), vendorsRedactor.Config)

	event := &shippingPreferencePb.ProcessShippingAddressUpdateCallbackRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        req.GetResponseCode(),
			Description: req.GetResponseReason(),
		},
	}

	logger.Info(ctx, "received callback for update shipping address", zap.String(logger.REQUEST_ID, req.GetRequestId()))

	if err := constructEventRequest(ctx, req, event); err != nil {
		return nil, fmt.Errorf("failed to construct event")
	}
	// publish the packet to the queue to be consumed by the BE service. The retries are also handled using the
	// same queue.
	if _, err := s.addressUpdateCallbackPublisher.Publish(ctx, event); err != nil {
		logger.Error(ctx, fmt.Sprintf("Unable to publish shipping address update packet to the queue %v", event), zap.Error(err))
		return nil, fmt.Errorf("error while publishing shipping address update packet")
	}
	return &empty.Empty{}, nil
}

func constructEventRequest(ctx context.Context, req *vdPb.UpdateShippingAddressCallBackRequest,
	event *shippingPreferencePb.ProcessShippingAddressUpdateCallbackRequest) error {
	// copy all the other fields with same name
	if copierErr := copier.Copy(event, req); copierErr != nil {
		logger.Error(ctx, fmt.Sprintf("Error in Copy from %T to %T. Err: %v", req, event, copierErr), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		return fmt.Errorf("error in Copy from %T to %T. Err: %v", req, event, copierErr)
	}

	if state, ok := CodeToShippingAddressUpdateStatusMap[req.GetResponseCode()]; ok {
		event.State = state
		if state == shippingPreferencePb.RequestState_FAILED {
			if len(req.GetErrors()) != 0 {
				var errors string
				for _, error := range req.GetErrors() {
					errors += " " + error.GetReason()
				}
				logger.Error(ctx, fmt.Sprintf("error in shipping address update callback %v", errors))
			}
			logger.Error(ctx, "received failure state for shipping address update callback",
				zap.String(logger.STATUS_CODE, req.GetResponseCode()))
			return nil
		}
	} else {
		// This should not happen. Indicates: changes in specs without prior communication or some error on Federal
		// end. Must be followed up with federal immediately to resolve this.
		logger.Error(ctx, "received unknown response code from federal in shipping address update callback",
			zap.String(logger.REQUEST_ID, req.GetRequestId()), zap.String("rsp code: ", req.GetResponseCode()))
		return fmt.Errorf("unknown response code returned from federal for request id %s", req.GetRequestId())
	}
	return nil
}
