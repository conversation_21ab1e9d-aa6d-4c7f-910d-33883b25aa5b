package axis

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/epifi/gamma/api/vendors/axis"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"
)

type Service struct {
	conf *config.Config
}

func NewService(conf *config.Config) *Service {
	return &Service{
		conf: conf,
	}
}

func (s *Service) ProcessPaymentCallBack(ctx context.Context, req *axis.PaymentCallBackResponse) (*emptypb.Empty, error) {
	logger.Info(ctx, fmt.Sprintf("Received payment callback response : %s",
		req.String()))

	return &emptypb.Empty{}, nil
}
