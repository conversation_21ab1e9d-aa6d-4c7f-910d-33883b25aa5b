package federal_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/xml"
	"errors"
	"net"
	"reflect"
	"testing"
	"time"

	encodingtest "github.com/epifi/be-common/pkg/syncwrapper/encoding/testing"
	mock_syncresp "github.com/epifi/be-common/pkg/syncwrapper/response/mocks"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/stretchr/testify/require"

	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	upiPb "github.com/epifi/gamma/api/upi"
	federalPaymentPb "github.com/epifi/gamma/api/vendornotification/openbanking/payment/federal"
	federalUPI "github.com/epifi/gamma/api/vendors/federal/upi"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"
	"github.com/epifi/gamma/vendornotification/openbanking/payment/federal"
)

type upiTestSuite struct {
	paymentService federalPaymentPb.PaymentServer
}

var (
	upiTs           upiTestSuite
	encodedIPString = encodingtest.EncodeIPAddress(net.IPv4(127, 0, 0, 1).To4())

	reqAuthDetails = &federalUPI.ReqAuthDetails{
		Head: federalUPI.Head{
			Msg:   "FDR121",
			OrgId: "40002",
		},
	}

	resPay = &federalUPI.RespPay{
		Head: federalUPI.Head{
			Msg:   "FDR121",
			OrgId: "40002",
		},
	}

	reqTxnConfirmation = &federalUPI.ReqTxnConfirmation{
		Head: federalUPI.Head{
			Msg:   "FDR121",
			OrgId: "40002",
		},
	}

	reqValAddress = &federalUPI.ReqValAdd{
		Head: federalUPI.Head{
			Msg:   "FDR121",
			OrgId: "40002",
		},
	}

	respValidateAddr = &federalUPI.RespValAddress{
		Resp: federalUPI.ValidateAddressResp{
			ReqMsgId: encodedIPString,
		},
	}

	listPSPKeysResponse = &federalUPI.ListKeyResponse{
		Head: federalUPI.Head{
			Msg:   "FDR121",
			OrgId: "40002",
		},
		Txn: federalUPI.Transaction{
			Type: "ListPSPKeys",
		},
	}

	listKeysResponse = &federalUPI.ListKeyResponse{
		Head: federalUPI.Head{
			Msg:   "FDR121",
			OrgId: "40002",
		},
		Txn: federalUPI.Transaction{
			Type: "ListKeys",
		},
		Resp: federalUPI.Resp{
			ReqMsgId: encodedIPString,
		},
	}
)

func TestService_ProcessUpiEvent(t *testing.T) {
	var (
		ctrl = gomock.NewController(t)
	)
	defer ctrl.Finish()

	m := mock_queue.NewMockPublisher(ctrl)
	p := mock_syncresp.NewMockHandler(ctrl)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctrl)
	upiTs.paymentService = federal.NewService(
		m,
		m,
		p,
		m,
		m,
		m,
		m,
		m,
		m,
		nil,
		mockOrderClient,
		conf,
		m,
		m,
		m,
		m,
		m,
		m,
		m,
		m,
		nil,
	)

	reqAuthRawData, err := xml.Marshal(reqAuthDetails)
	require.Nil(t, err)

	resPayRawData, err := xml.Marshal(resPay)
	require.Nil(t, err)

	reqTxnConfirmationRawData, err := xml.Marshal(reqTxnConfirmation)
	require.Nil(t, err)

	reqValAddressRawData, err := xml.Marshal(reqValAddress)
	require.Nil(t, err)

	respValidateAddr, err := xml.Marshal(respValidateAddr)
	require.Nil(t, err)

	listPspKeysResRawData, err := xml.Marshal(listPSPKeysResponse)
	require.Nil(t, err)

	listKeysResRawData, err := xml.Marshal(listKeysResponse)
	require.Nil(t, err)

	type mockSyncHandler struct {
		enable bool
		req    interface{}
		err    error
	}

	type mockPublishing struct {
		enable bool
		req    interface{}
		msgId  string
		err    error
	}

	_ = encodedIPString
	type mockPopulateActor struct {
		enable bool
		req    *orderPb.GetActorsFromReqIdRequest
		res    *orderPb.GetActorsFromReqIdResponse
		err    error
	}

	_ = encodedIPString
	tests := []struct {
		name              string
		req               *federalPaymentPb.ProcessUpiEventRequest
		mockPublishing    mockPublishing
		mockPopulateActor mockPopulateActor
		mockSyncHandler   mockSyncHandler
		want              *empty.Empty
		wantErr           bool
	}{
		{
			name: "ReqAuthDetails successful publishing",
			req: &federalPaymentPb.ProcessUpiEventRequest{
				RawData: reqAuthRawData,
			},
			mockPublishing: mockPublishing{
				enable: true,
				req:    &upiPb.ProcessReqAuthRequest{},
				msgId:  "",
				err:    nil,
			},
			mockPopulateActor: mockPopulateActor{
				enable: true,
				req:    &orderPb.GetActorsFromReqIdRequest{},
				res:    &orderPb.GetActorsFromReqIdResponse{},
				err:    nil,
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "RespPay successful publishing",
			req: &federalPaymentPb.ProcessUpiEventRequest{
				RawData: resPayRawData,
			},
			mockPublishing: mockPublishing{
				enable: true,
				req:    &upiPb.ProcessResPayRequest{},
				msgId:  "",
				err:    nil,
			},
			mockPopulateActor: mockPopulateActor{
				enable: true,
				req:    &orderPb.GetActorsFromReqIdRequest{},
				res:    &orderPb.GetActorsFromReqIdResponse{},
				err:    nil,
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "sync wrapper successful publishing",
			req: &federalPaymentPb.ProcessUpiEventRequest{
				RawData: respValidateAddr,
			},
			mockSyncHandler: mockSyncHandler{
				enable: true,
				req:    &commonvgpb.ProcessEventRequest{},
				err:    nil,
			},
			mockPublishing: mockPublishing{
				enable: false,
			},
			mockPopulateActor: mockPopulateActor{
				enable: false,
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "publish failure",
			req: &federalPaymentPb.ProcessUpiEventRequest{
				RawData: resPayRawData,
			},
			mockPublishing: mockPublishing{
				enable: true,
				req:    &upiPb.ProcessResPayRequest{},
				msgId:  "",
				err:    errors.New("failed"),
			},
			mockPopulateActor: mockPopulateActor{
				enable: true,
				req:    &orderPb.GetActorsFromReqIdRequest{},
				res:    &orderPb.GetActorsFromReqIdResponse{},
				err:    nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ReqTxnConfirmation successful publishing",
			req: &federalPaymentPb.ProcessUpiEventRequest{
				RawData: reqTxnConfirmationRawData,
			},
			mockPublishing: mockPublishing{
				enable: true,
				req:    &upiPb.ProcessReqTxnConfirmationRequest{},
				msgId:  "",
				err:    nil,
			},
			mockPopulateActor: mockPopulateActor{
				enable: true,
				req:    &orderPb.GetActorsFromReqIdRequest{},
				res:    &orderPb.GetActorsFromReqIdResponse{},
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "ReqValAddress successful publishing",
			req: &federalPaymentPb.ProcessUpiEventRequest{
				RawData: reqValAddressRawData,
			},
			mockPublishing: mockPublishing{
				enable: true,
				req:    &upiPb.ProcessReqValAddressRequest{},
				msgId:  "",
				err:    nil,
			},
			mockPopulateActor: mockPopulateActor{
				enable: false,
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "ListPSPKeys successful publishing",
			req: &federalPaymentPb.ProcessUpiEventRequest{
				RawData: listPspKeysResRawData,
			},
			mockPublishing: mockPublishing{
				enable: true,
				req:    &upiPb.ProcessListPspKeysRequest{},
				msgId:  "",
				err:    nil,
			},
			mockPopulateActor: mockPopulateActor{
				enable: false,
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "ListKeys successful publishing",
			req: &federalPaymentPb.ProcessUpiEventRequest{
				RawData: listKeysResRawData,
			},
			mockSyncHandler: mockSyncHandler{
				enable: true,
				req:    &commonvgpb.ProcessEventRequest{},
				err:    nil,
			},
			mockPublishing: mockPublishing{
				enable: false,
			},
			mockPopulateActor: mockPopulateActor{
				enable: false,
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockPublishing.enable {
				m.EXPECT().
					Publish(context.Background(), gomock.AssignableToTypeOf(tt.mockPublishing.req)).
					Return(tt.mockPublishing.msgId, tt.mockPublishing.err)
			}
			if tt.mockPopulateActor.enable {
				mockOrderClient.EXPECT().
					GetActorsFromReqId(context.Background(), tt.mockPopulateActor.req).
					Return(tt.mockPopulateActor.res, nil)
			}
			if tt.mockSyncHandler.enable {
				p.EXPECT().
					Serve(gomock.AssignableToTypeOf(tt.mockSyncHandler.req)).
					Return(tt.mockSyncHandler.err)
			}
			got, err := upiTs.paymentService.ProcessUpiEvent(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessUpiEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessUpiEvent() got = %v, want %v", got, tt.want)
			}
			time.Sleep(time.Second)
		})
	}
}
