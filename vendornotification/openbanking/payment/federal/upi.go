package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"encoding/xml"
	"fmt"

	upiMandatePb "github.com/epifi/gamma/api/upi/mandate/consumer"
	upiOnbConsumerPb "github.com/epifi/gamma/api/upi/onboarding/consumer"

	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"

	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	upiPb "github.com/epifi/gamma/api/upi"
	federalPaymentPb "github.com/epifi/gamma/api/vendornotification/openbanking/payment/federal"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	ReqPendingMsg  = "ReqPendingMsg"
	ReqListKeys    = "ReqListKeys"
	ReqListPsp     = "ReqListPsp"
	ReqListVae     = "ReqListVae"
	ReqListAccount = "ReqListAccount"
	ReqManageVae   = "ReqManageVae"
	ReqValAdd      = "ReqValAdd"
	ReqSetCre      = "ReqSetCre"
	ReqRegMob      = "ReqRegMob"
	ReqChkTxn      = "ReqChkTxn"
	ReqOtp         = "ReqOtp"
	ReqBalEnq      = "ReqBalEnq"
	ReqListAccPvd  = "ReqListAccPvd"
	ReqComplaint   = "ReqComplaint"

	RespPendingMsg  = "RespPendingMsg"
	RespListKeys    = "RespListKeys"
	RespListPsp     = "RespListPsp"
	RespListVae     = "RespListVae"
	RespListAccount = "RespListAccount"
	RespManageVae   = "RespManageVae"
	RespValAdd      = "RespValAdd"
	RespSetCre      = "RespSetCre"
	RespRegMob      = "RespRegMob"
	RespChkTxn      = "RespChkTxn"
	RespOtp         = "RespOtp"
	RespBalEnq      = "RespBalEnq"
	RespListAccPvd  = "RespListAccPvd"
	RespComplaint   = "RespComplaint"

	TxnTypeListPSPKeys = "ListPSPKeys"

	ReqPay             = "ReqPay"
	ReqAuthDetails     = "ReqAuthDetails"
	ReqTxnConfirmation = "ReqTxnConfirmation"
	// UPI transaction confirmation update for complaint status
	ReqTxnConfirmationComplaint = "ReqTxnConfirmationComplaint"
	ReqMandate                  = "ReqMandate"
	ReqAuthMandate              = "ReqAuthMandate"
	ReqMandateConfirmation      = "ReqMandateConfirmation"
	ReqAuthValCust              = "ReqAuthValCust"

	RespPay                = "RespPay"
	ResAuthDetails         = "ResAuthDetails"
	ResTxnConfirmation     = "ResTxnConfirmation"
	RespMandate            = "RespMandate"
	ResAuthMandate         = "ResAuthMandate"
	ResMandateConfirmation = "ResMandateConfirmation"
	ProcessUpiEventRequest = "ProcessUpiEventRequest"
	RespGetAdd             = "RespGetAdd"
	RespRegMapper          = "RespRegMapper"
	RespActivation         = "RespActivation"
	RespValQr              = "RespValQr"
	TxnConfirmationNote    = "AUTOUPDATE"
	ReqMapperConfirmation  = "ReqMapperConfirmation"
)

// Federal posts validation errors with Request API name and not Response API name
// apiReqResMap maps request to response
var apiReqResMap = map[string]string{
	ReqValAdd:      RespValAdd,
	ReqPendingMsg:  RespPendingMsg,
	ReqListKeys:    RespListKeys,
	ReqOtp:         RespOtp,
	ReqRegMob:      RespRegMob,
	ReqSetCre:      RespSetCre,
	ReqListPsp:     RespListPsp,
	ReqListAccount: RespListAccount,
	ReqListVae:     RespListVae,
	ReqChkTxn:      RespChkTxn,
	ReqBalEnq:      RespBalEnq,
	ReqListAccPvd:  RespListAccPvd,
	ReqManageVae:   RespManageVae,
	ReqComplaint:   RespComplaint,
}

var apiMap = map[string]commonvgpb.SyncWrappedApi{
	RespValAdd:      commonvgpb.SyncWrappedApi_OB_UPI_VALIDATE_ADDRESS,
	RespPendingMsg:  commonvgpb.SyncWrappedApi_OB_UPI_PENDING_MSG,
	RespListKeys:    commonvgpb.SyncWrappedApi_OB_UPI_LIST_KEYS,
	RespOtp:         commonvgpb.SyncWrappedApi_OB_UPI_OTP,
	RespRegMob:      commonvgpb.SyncWrappedApi_OB_UPI_REGISTER_MOBILE_BANKING,
	RespSetCre:      commonvgpb.SyncWrappedApi_OB_UPI_SET_CREDENTIALS,
	RespListPsp:     commonvgpb.SyncWrappedApi_OB_UPI_LIST_PSP,
	RespListAccount: commonvgpb.SyncWrappedApi_OB_UPI_LIST_ACCOUNT,
	RespListVae:     commonvgpb.SyncWrappedApi_OB_UPI_LIST_VAE,
	RespChkTxn:      commonvgpb.SyncWrappedApi_OB_UPI_CHECK_TXN,
	RespBalEnq:      commonvgpb.SyncWrappedApi_OB_UPI_BAL_ENQUIRY,
	RespListAccPvd:  commonvgpb.SyncWrappedApi_OB_UPI_LIST_ACCOUNT_PROVIDERS,
	RespManageVae:   commonvgpb.SyncWrappedApi_OB_UPI_MANAGE_VAE,
	RespGetAdd:      commonvgpb.SyncWrappedApi_OB_UPI_GET_MAPPER_INFO,
	RespRegMapper:   commonvgpb.SyncWrappedApi_OB_UPI_REG_MAPPER,
	RespActivation:  commonvgpb.SyncWrappedApi_OB_UPI_ACTIVATE_INTERNATIONAL_PAYMENT,
	RespValQr:       commonvgpb.SyncWrappedApi_OB_UPI_VALIDATE_INTERNATIONAL_QR,
}

type Node struct {
	XMLName xml.Name
	Attrs   []xml.Attr `xml:"-"`
	//Content []byte     `xml:",innerxml"`
	Nodes []Node `xml:",any"`
}

func (n *Node) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	n.Attrs = start.Attr
	type node Node
	// TODO(pruthvi): Why do we need to type cast here? Discuss with kunal and understand why it doesn't work otherwise
	return d.DecodeElement((*node)(n), &start)
}

// nolint:funlen
func (s *Service) ProcessUpiEvent(ctx context.Context, req *federalPaymentPb.ProcessUpiEventRequest) (*empty.Empty, error) {
	if err := security.CheckWhiteList(
		ctx, s.conf.UPIWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix,
	); err != nil {
		return nil, err
	}

	// TODO(pruthvi: Will message Id in ResPay be the same as ReqPay?
	// Do we need to pass reqMsgId for resPay as well?
	apiName, reqMsgId, txnId, err := getEventDetails(ctx, req.RawData)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in fetching event details. err: %v", err))
		return nil, errInternalServer
	}
	redactor.LogCallbackXMLRawRequestData(ctx, req.RawData, ProcessUpiEventRequest, reqMsgId, vendorsRedactor.Config)
	logger.Info(ctx, "Received callback event for UPI", zap.String("reqMsgId", reqMsgId), zap.String("apiName", apiName))

	switch apiName {
	case
		RespValAdd,
		RespPendingMsg,
		RespListKeys,
		RespOtp,
		RespRegMob,
		RespSetCre,
		RespListPsp,
		RespListAccount,
		RespChkTxn,
		RespBalEnq,
		RespListAccPvd,
		RespManageVae,
		RespGetAdd,
		RespRegMapper,
		RespActivation,
		RespValQr:
		api, ok := apiMap[apiName]
		if !ok {
			api = commonvgpb.SyncWrappedApi_SYNC_WRAPPED_API_UNSPECIFIED
		}

		// Events that are posted as a response for event initiated by Epifi & that don't require direct response
		// e.g., Response to the Events initiated from Epifi e.g., ListKeys, ReqPay, ListAcctProviders
		// Such events will be consumed by Vendor gateway. This queue would help orchestrate a sync wrapper over the
		// UPI's Async calls that were made from vendor gateway event.
		event := &commonvgpb.ProcessEventRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
			Api:         api,
			RequestId:   reqMsgId,
		}
		// process the response in separate routine
		// and return immediately as this is not dependant on the prcessing.
		go s.syncRespHandler.Serve(event)
	// Incoming ReqAuth event requires Epifi to initiate a RespAuth request to Federal
	case ReqAuthDetails:
		event := &upiPb.ProcessReqAuthRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiReqAuthEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish request auth details with partner id: %v", event.PartnerBank))
			return nil, errInternalServer
		}

	// EpiFi receives a `RespPay` callback in response to `ReqPay`. It contains final result of the transaction
	case RespPay:
		event := &upiPb.ProcessResPayRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiResPayEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish resp pay event for partner bank %v to the queue",
				event.PartnerBank))
			return nil, errInternalServer
		}
	case RespMandate:
		event := &upiMandatePb.ProcessRespMandateRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiRespMandateEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish resp mandate event for partner bank %v to the queue",
				event.PartnerBank))
			return nil, errInternalServer
		}

	// EpiFi receives `ReqTxnConfirmation` from NPCI in following cases
	// 1) when payee is an epiFi user for a PAY txn
	// 2) when payer is an epIfi user for a COLLECT txn
	// we need to process and send `RespTxnConfirmation` back to NPCI via partner banks for such events
	case ReqTxnConfirmation:
		event := &upiPb.ProcessReqTxnConfirmationRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiReqTxnConfirmationEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish req txn confirmation event for partner bank %v to the queue",
				event.PartnerBank))
			return nil, errInternalServer
		}
	// Epifi receives `ReqValAddress` from NPCI to validate the VPA if it is an epifi user
	// Need to send RespValAddress callback with the user info associated with the VPA
	case ReqValAdd:
		event := &upiPb.ProcessReqValAddressRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}
		if _, err := s.upiReqValAddressEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish req val address event for partner bank %v to the queue",
				event.PartnerBank))
			return nil, errInternalServer
		}
	// ListKeys API response have 3 type of TransactionType (i.e. GetToken,ListKeys, ListPSPKeys) action.
	//	On TxnType ListPSPKeys action, response need to publish in queue which will be consumed by UPI service.
	case TxnTypeListPSPKeys:
		event := &upiPb.ProcessListPspKeysRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}
		if _, err := s.upiListPspKeysEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish list psp key event for partner bank %v to the queue",
				event.PartnerBank))
			return nil, errInternalServer
		}
	case RespListVae:
		event := &upiPb.ProcessListVaeRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}
		if _, err := s.upiListVaePublisher.PublishExtendedMessage(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("unable to publish list vae request event for partner bank %v to the queue",
				event.PartnerBank))
			return nil, errInternalServer
		}
	case ReqAuthMandate:
		event := &upiMandatePb.ProcessReqAuthMandateRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiReqAuthMandateEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish request auth mandate with partner id: %v", event.PartnerBank), zap.Error(err))
			return nil, errInternalServer
		}
	case ReqMandateConfirmation:
		event := &upiMandatePb.ProcessReqMandateConfirmationRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiReqMandateConfirmationEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish request mandate confirmation with partner id: %v", event.PartnerBank), zap.Error(err))
			return nil, errInternalServer
		}
	case ReqTxnConfirmationComplaint:
		event := &upiPb.ProcessReqTxnConfirmationComplaintRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.GetRawData(),
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiReqTxnConfirmationComplaintEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish req txn confirmation event for partner bank %v to the queue",
				event.PartnerBank))
			return nil, errInternalServer
		}
	case ReqAuthValCust:
		event := &upiMandatePb.ProcessReqAuthValCustRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.GetRawData(),
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiReqAuthValCustEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish req auth val cust event for partner bank %v to the queue",
				event.GetPartnerBank()))
			return nil, errInternalServer
		}
	case ReqMapperConfirmation:
		event := &upiOnbConsumerPb.ProcessReqMapperConfirmationRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.GetRawData(),
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiReqMapperConfirmationEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish req mapper confirmation event for partner bank %v to the queue",
				event.GetPartnerBank()))
			return nil, errInternalServer
		}
	case RespComplaint:
		event := &upiPb.ProcessRespComplaintRequest{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			RawData:     req.RawData,
		}

		ctx = s.populateContextWithActors(ctx, txnId)

		if _, err := s.upiRespComplaintEventPublisher.Publish(ctx, event); err != nil {
			logger.Error(ctx, fmt.Sprintf("Unable to publish resp cpmplaint event with partner id: %v", event.PartnerBank), zap.Error(err))
			return nil, errInternalServer
		}
	default:
		logger.Error(ctx, fmt.Sprintf("Unhandled request for API: %v: \n", apiName))
	}
	return &empty.Empty{}, nil
}

// Process request data and return API name and Msg ID
func getEventDetails(ctx context.Context, rawData []byte) (string, string, string, error) {
	var apiName, reqMsgId, txnId, errorType string
	var n Node
	var err error

	buf := bytes.NewBuffer(rawData)
	dec := xml.NewDecoder(buf)

	if err = dec.Decode(&n); err != nil {
		logger.Error(ctx, fmt.Sprintf("unable to decode XML data :%v", string(rawData)))
		// TODO(pruthvi): Confirm with federal the expected response here
		return "", "", "", errInternalServer
	}

	if n.XMLName.Local == "Ack" {
		// Federal posts Ack when there are validation errors
		apiName, reqMsgId, errorType = processAck(ctx, &n)
		logger.Error(ctx, fmt.Sprintf("%v error for API %v with msgID:%v\n", errorType, reqMsgId,
			apiName))
	} else {
		apiName = n.XMLName.Local
		reqMsgId = getReqMsgIdFromPayload(&n)
		txnId = getTxnIdFromPayload(&n)

		switch apiName {
		case RespListKeys:
			// TxnType is differentiator between ListKeys and ListPSPKeys api response.
			// Overriding apiName based on TxnType for ListPSPKeys
			if txnType := getTxnTypeFromPayload(&n); txnType == TxnTypeListPSPKeys {
				apiName = txnType
			}
		case ReqTxnConfirmation:
			// ReqTxnConfirmation response will come for two different API.
			// 1. To provide update on transaction confirmation
			// 2. Auto update on complaint response. If ReqTxnConfirmation response is with Txn.note == AUTOUPDATE
			//    then it will be of complaint status response and we need to handle it differently.
			if txnConfirmationNote := getTxnConfirmationNote(&n); txnConfirmationNote == TxnConfirmationNote {
				apiName = ReqTxnConfirmationComplaint
			}
		}
	}

	return apiName, reqMsgId, txnId, nil
}

// Federal posts validation errors as Ack
// processAck would parse the XML and return with apiName, reqMsgId and errorType
func processAck(ctx context.Context, n *Node) (apiName, reqMsgId, errorType string) {

	for idx := range n.Attrs {
		attr := &n.Attrs[idx]

		switch attr.Name.Local {
		case "api":
			// Federal sends request API name and not response API name
			// Map would help translate request API name to response API name
			apiName = apiReqResMap[attr.Value]
			if apiName == "" {
				logger.Error(ctx, fmt.Sprintf("unknown API %v", attr.Value))
				apiName = attr.Value
			}
		case "err":
			errorType = attr.Value
		case "reqMsgId":
			reqMsgId = attr.Value
		}

		if apiName != "" && errorType != "" && reqMsgId != "" {
			return
		}
	}
	return
}

func getReqMsgIdFromPayload(n *Node) string {
	for idx := range n.Nodes {
		node := &n.Nodes[idx]

		if node.XMLName.Local == "Resp" {
			for idx1 := range node.Attrs {
				attr := &node.Attrs[idx1]
				if attr.Name.Local == "reqMsgId" {
					return attr.Value
				}
			}
			break
		}
	}
	// Payloads of events such as ReqAuth will not have reqMsgId
	return ""
}

func getTxnTypeFromPayload(n *Node) string {
	for idx := range n.Nodes {
		node := &n.Nodes[idx]

		if node.XMLName.Local == "Txn" {
			for idx1 := range node.Attrs {
				attr := &node.Attrs[idx1]
				if attr.Name.Local == "type" {
					return attr.Value
				}
			}
			break
		}
	}

	return ""
}

func getTxnIdFromPayload(n *Node) string {
	for idx := range n.Nodes {
		node := &n.Nodes[idx]
		if node.XMLName.Local == "Txn" {
			for idx1 := range node.Attrs {
				attr := &node.Attrs[idx1]
				if attr.Name.Local == "id" {
					return attr.Value
				}
			}
			break
		}
	}
	return ""
}

// getTxnConfirmationNote will search for Txn.note from passed node from xml
func getTxnConfirmationNote(n *Node) string {
	for idx := range n.Nodes {
		node := &n.Nodes[idx]
		if node.XMLName.Local == "Txn" {
			for idx1 := range node.Attrs {
				attr := &node.Attrs[idx1]
				if attr.Name.Local == "note" {
					return attr.Value
				}
			}
		}
	}
	return ""
}
