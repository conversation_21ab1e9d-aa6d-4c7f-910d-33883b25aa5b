package federal

import (
	"context"

	"google.golang.org/protobuf/types/known/emptypb"

	vendorsFederalAddFunds "github.com/epifi/gamma/api/vendors/federal/payment/addFunds"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/redactor"
)

const (
	ProcessAddFundsCallbackRequest = "ProcessAddFundsCallbackRequest"
)

// nolint:dupl
// ProcessAddFundsCallbackEvent method processes updates for fund addition transactions from the Federal bank.
// We will be logging that callback response for now .
func (s *Service) ProcessAddFundsCallbackEvent(ctx context.Context, req *vendorsFederalAddFunds.AddFundsCallbackRequest) (*emptypb.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, ProcessAddFundsCallbackRequest, req.GetUpiTransactionId(), vendorsRedactor.Config)
	return &emptypb.Empty{}, nil
}
