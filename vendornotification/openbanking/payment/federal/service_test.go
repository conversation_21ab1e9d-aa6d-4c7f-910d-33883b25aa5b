package federal_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/stretchr/testify/assert"

	mock_syncresp "github.com/epifi/be-common/pkg/syncwrapper/response/mocks"

	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	federalPaymentPb "github.com/epifi/gamma/api/vendornotification/openbanking/payment/federal"
	b2cPaymentPb "github.com/epifi/gamma/api/vendors/federal/payment/b2c"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"
	"github.com/epifi/gamma/vendornotification/openbanking/payment/federal"
)

func TestService_UpdateTransaction(t *testing.T) {

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := mock_queue.NewMockPublisher(ctrl)
	p := mock_syncresp.NewMockHandler(ctrl)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctrl)
	federalPaymentService := federal.NewService(
		m,
		m,
		p,
		m,
		m,
		m,
		m,
		m,
		m,
		nil,
		mockOrderClient,
		conf,
		m,
		m,
		m,
		m,
		m,
		m,
		m,
		m,
		nil,
	)

	event := &paymentPb.ProcessCallBackRequest{PartnerBank: commonvgpb.Vendor_FEDERAL_BANK}
	getActorResp := &orderPb.GetActorsFromReqIdResponse{}
	ctx := context.Background()

	t.Run("Update Transaction success on successful Publish event to queue", func(t *testing.T) {
		m.
			EXPECT().
			Publish(ctx, gomock.AssignableToTypeOf(event)).
			Return("mesgId", nil)

		mockOrderClient.
			EXPECT().
			GetActorsFromReqId(ctx, gomock.Any()).
			Return(getActorResp, nil)

		req := &federalPaymentPb.UpdateTransactionRequest{Amount: "0.00", TransTimestamp: "2020-11-18 03:16:24.721155"}
		res, err := federalPaymentService.UpdateTransaction(ctx, req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
	})

	t.Run("Update Transaction failure on failed Publish event to queue", func(t *testing.T) {
		m.
			EXPECT().
			Publish(ctx, gomock.AssignableToTypeOf(event)).
			Return("", errors.New("asd"))

		mockOrderClient.
			EXPECT().
			GetActorsFromReqId(ctx, gomock.Any()).
			Return(getActorResp, nil)

		req := &federalPaymentPb.UpdateTransactionRequest{Amount: "0.00", TransTimestamp: "2020-11-18 03:16:24.721155"}
		res, err := federalPaymentService.UpdateTransaction(ctx, req)
		assert.NotNil(t, err)
		assert.Nil(t, res)
	})
}

func TestService_UpdateB2CTransaction(t *testing.T) {
	var (
		ctrl = gomock.NewController(t)
	)
	defer ctrl.Finish()
	m := mock_queue.NewMockPublisher(ctrl)
	p := mock_syncresp.NewMockHandler(ctrl)
	federalPaymentService := federal.NewService(m, m, p, m, m, m, m, m, m, nil, nil, nil, m, m, m, m, m, m, m, m, nil)

	event := &paymentPb.ProcessCallBackRequest{PartnerBank: commonvgpb.Vendor_FEDERAL_BANK}

	type mockPublishing struct {
		enable bool
		req    interface{}
		msgId  string
		err    error
	}
	tests := []struct {
		name           string
		req            *b2cPaymentPb.UpdateB2CTransactionRequest
		mockPublishing mockPublishing
		want           *empty.Empty
		wantErr        bool
	}{
		{
			name: "Update B2C Transaction success on successful Publish event to queue",
			req: &b2cPaymentPb.UpdateB2CTransactionRequest{
				Amount:         20,
				TransTimestamp: "2020-11-18 03:16:24.721155",
			},
			mockPublishing: mockPublishing{
				enable: true,
				req:    event,
				msgId:  "",
				err:    nil,
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "Update B2C Transaction failure on failed Publish event to queue",
			req: &b2cPaymentPb.UpdateB2CTransactionRequest{
				Amount:         20,
				TransTimestamp: "2020-11-18 03:16:24.721155",
			},
			mockPublishing: mockPublishing{
				enable: true,
				req:    event,
				msgId:  "",
				err:    errors.New("asd"),
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			if tt.mockPublishing.enable {
				m.EXPECT().
					Publish(context.Background(), gomock.AssignableToTypeOf(tt.mockPublishing.req)).
					Return(tt.mockPublishing.msgId, tt.mockPublishing.err)
			}
			got, err := federalPaymentService.UpdateB2CTransaction(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateB2CTransaction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateB2CTransaction() got = %v, want %v", got, tt.want)
			}
		})
	}
}
