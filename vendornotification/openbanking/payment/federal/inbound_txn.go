package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"

	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/accounts"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentNotificationPb "github.com/epifi/gamma/api/order/payment/notification"
	vendorsFederal "github.com/epifi/gamma/api/vendors/federal"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	payPkg "github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
)

// TODO(kunal): Read from secrets after is moved by Anand.
//
//	Ref: https://github.com/epiFi/gamma/blob/master/vendorgateway/federal/requests.go#L13
const (
	// layout format Ref: https://golang.org/src/time/format.go
	ProcessInboundTxnRequest   = "ProcessInboundTxnRequest"
	ProcessInboundTxnRequestV1 = "ProcessInboundTxnRequestV1"
)

var (
	cfg, _ = config.Load()
)

// nolint:dupl
func (s *Service) ProcessInboundTxnV1(ctx context.Context, req *vendorsFederal.ProcessInboundTxnRequest) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, ProcessInboundTxnRequestV1, req.GetRequestId(), vendorsRedactor.Config)

	if req.SenderCode != cfg.Secrets.Ids[config.SenderCode] {
		logger.Error(ctx, fmt.Sprintf("Received invalid sendor code: %s", req.SenderCode))
		return nil, errInternalServer
	}

	processInboundTxnOrderReq := buildInboundTxnOrderReq(req)
	processInboundTxnOrderReq.NotificationEndPointVersion = orderPb.ProcessInboundTxnOrderRequest_NOTIFICATION_END_POINT_VERSION_V1
	return s.processInboundTxn(ctx, req, processInboundTxnOrderReq)
}

// nolint:dupl
func (s *Service) ProcessInboundTxn(ctx context.Context, req *vendorsFederal.ProcessInboundTxnRequest) (*empty.Empty, error) {

	redactor.LogCallbackRequestData(ctx, req, ProcessInboundTxnRequest, req.GetRequestId(), vendorsRedactor.Config)

	if req.SenderCode != cfg.Secrets.Ids[config.SenderCode] {
		logger.Error(ctx, fmt.Sprintf("Received invalid sendor code: %s", req.SenderCode))
		return nil, errInternalServer
	}

	processInboundTxnOrderReq := buildInboundTxnOrderReq(req)
	processInboundTxnOrderReq.NotificationEndPointVersion = orderPb.ProcessInboundTxnOrderRequest_NOTIFICATION_END_POINT_VERSION_V0

	return s.processInboundTxn(ctx, req, processInboundTxnOrderReq)

}

// processInboundTxn is wrapped from endpoint handler with end point version in processInboundTxnOrderReq.
// It will build the OrderInboundTransaction Req and publish to order inbound consumer (UPI and Non-UPI transaction are published to different queue to meet SLA's of processing)
// nolint:funlen
func (s *Service) processInboundTxn(ctx context.Context, req *vendorsFederal.ProcessInboundTxnRequest, processInboundTxnOrderReq *orderPb.ProcessInboundTxnOrderRequest) (*empty.Empty, error) {
	var (
		err error
	)

	// if new end point is enabled then suppressing notification from v0 endpoint and vice versa in case new end point is disabled.
	switch processInboundTxnOrderReq.GetNotificationEndPointVersion() {
	case orderPb.ProcessInboundTxnOrderRequest_NOTIFICATION_END_POINT_VERSION_V0:
		if s.dynamicConf.Flags().EnableNewEndpointInboundNotification() {
			return &empty.Empty{}, nil
		}
	case orderPb.ProcessInboundTxnOrderRequest_NOTIFICATION_END_POINT_VERSION_V1:
		if !s.dynamicConf.Flags().EnableNewEndpointInboundNotification() {
			return &empty.Empty{}, nil
		}
	}

	td := processInboundTxnOrderReq.GetTransactionDetails()

	processInboundTxnOrderReq.NotificationAccountType, processInboundTxnOrderReq.Apo, err = payPkg.GetAccountTypeAndApoByNumberForVendor(td.GetAccountNumber(), commonvgpb.Vendor_FEDERAL_BANK)
	if err != nil {
		logger.Error(ctx, "failed to fetch notification account type", zap.Error(err))
		return nil, errInternalServer
	}

	if processInboundTxnOrderReq.TransactionDetails.Amount, err = money.ParseString(req.GetTransactionDetails().GetAmount(), money.RupeeCurrencyCode); err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to parse money: %s", req.GetTransactionDetails().GetAmount()),
			zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()), zap.Error(err))
		return nil, errInternalServer
	}

	if processInboundTxnOrderReq.TransactionDetails.Timestamp, err = datetime.ParseFederalTS(req.GetTransactionDetails().GetTimestamp()); err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to parse transaction timestamp: %s", req.GetTransactionDetails().GetTimestamp()),
			zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()), zap.Error(err))
		return nil, errInternalServer
	}

	if processInboundTxnOrderReq.TransactionDetails.Date, err = datetime.ParseFederalDate(req.GetTransactionDetails().GetDate()); err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to parse transaction date: %s", req.GetTransactionDetails().GetDate()),
			zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()), zap.Error(err))
		return nil, errInternalServer
	}

	if processInboundTxnOrderReq.TransactionDetails.ValueDate, err = datetime.ParseFederalDate(req.GetTransactionDetails().GetValueDate()); err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to parse transaction value date: %s", req.GetTransactionDetails().GetValueDate()),
			zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()), zap.Error(err))
		return nil, errInternalServer
	}

	if req.GetTransactionDetails().GetInstrumentDetails() != nil &&
		req.GetTransactionDetails().GetInstrumentDetails().GetInstrumentDate() != "" {
		if processInboundTxnOrderReq.TransactionDetails.InstrumentDetails.InstrumentDate, err = datetime.ParseFederalTS(req.GetTransactionDetails().GetInstrumentDetails().GetInstrumentDate()); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse transaction's instrument date: %s",
				req.GetTransactionDetails().GetInstrumentDetails().GetInstrumentDate()),
				zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()), zap.Error(err))
		}
	}

	// Manually set amount and event type
	switch {
	case req.GetTransactionDetails().GetType() == "D":
		processInboundTxnOrderReq.TransactionDetails.NotificationEventType = paymentNotificationPb.TransactionDetails_DEBIT
	case req.GetTransactionDetails().GetType() == "C":
		processInboundTxnOrderReq.TransactionDetails.NotificationEventType = paymentNotificationPb.TransactionDetails_CREDIT
	default:
		logger.Error(ctx, fmt.Sprintf("Invalid amount: %s",
			req.GetTransactionDetails().GetAmount()))
		return nil, errInternalServer
	}

	parser := vendorsFederal.Parser
	if processInboundTxnOrderReq.NotificationAccountType == accounts.Type_LOAN_ACCOUNT {
		parser = vendorsFederal.LoanParser
	}
	processInboundTxnOrderReq.ParsedTxnParticulars, err = parser.Evaluate(td)
	if err != nil {
		logger.Error(ctx, "failed to parse inbound txn request",
			zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()), zap.Error(err))
		return nil, errInternalServer
	}

	// for notifications where reference number is missing, cbsId is used as reference number
	// if reference number is same as CBS ID then append the date
	if req.GetTransactionDetails().GetReferenceNumber() == "" {
		td.ReferenceNumber = td.Id
	} else if td.GetReferenceNumber() == strings.TrimSpace(req.GetTransactionDetails().GetId()) {
		td.ReferenceNumber = td.Id
	}

	if processInboundTxnOrderReq.GetParsedTxnParticulars().GetAtmMachineName() != "" {
		processInboundTxnOrderReq.GetTransactionDetails().Remarks = processInboundTxnOrderReq.GetParsedTxnParticulars().GetAtmMachineName()
	}

	if processInboundTxnOrderReq.GetParsedTxnParticulars().GetIsParsedByGenericRule() {
		logger.Info(ctx, fmt.Sprintf("Notification doesn't match known parsing rule : %v", td.GetParticular()),
			zap.String(logger.PARTNER_TXN_ID, processInboundTxnOrderReq.GetParsedTxnParticulars().GetUniqPartnerRefId()),
			zap.String(logger.ACCOUNT_TYPE, processInboundTxnOrderReq.GetNotificationAccountType().String()))
		metrics.RecordInboundNotification(metrics.GenericTransactions)
	} else {
		if processInboundTxnOrderReq.GetParsedTxnParticulars().GetTxnCategory() == paymentNotificationPb.ParsedTxnParticulars_MISCELLANEOUS_CHARGES {
			metrics.RecordInboundMiscChargeNotification(commonvgpb.Vendor_FEDERAL_BANK)
		}

		metrics.RecordInboundNotification(metrics.ParsedTransactions)
	}

	err = validateTransactionDetails(processInboundTxnOrderReq.GetTransactionDetails())
	if err != nil {
		return &empty.Empty{}, errInternalServer
	}
	if processInboundTxnOrderReq.NotificationAccountType == accounts.Type_LOAN_ACCOUNT {
		// publish loan transaction request
		if _, err = s.inboundLoanTxnPublisher.Publish(ctx, processInboundTxnOrderReq); err != nil {
			logger.Error(ctx, "unable to publish loan inbound txn request to the queue",
				zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()), zap.Error(err))
			return nil, errInternalServer
		}
		return &empty.Empty{}, nil
	}
	// Since 80% off-app transactions are UPI and due to latency of validate vpa address api creating backlogs for non-upi transaction.
	// All non-upi transaction will be redirected to different queue.
	switch processInboundTxnOrderReq.GetParsedTxnParticulars().GetProtocol() {
	case paymentPb.PaymentProtocol_UPI:
		if _, err = s.inboundUpiTxnPublisher.Publish(ctx, processInboundTxnOrderReq); err != nil {
			logger.Error(ctx, "unable to publish inbound txn request to the queue",
				zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()), zap.Error(err))
			return nil, errInternalServer
		}
	default:
		if _, err = s.inboundTxnPublisher.Publish(ctx, processInboundTxnOrderReq); err != nil {
			logger.Error(ctx, "unable to publish inbound txn request to the queue",
				zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()), zap.Error(err))
			return nil, errInternalServer
		}
	}

	return &empty.Empty{}, nil
}

func buildInboundTxnOrderReq(req *vendorsFederal.ProcessInboundTxnRequest) *orderPb.ProcessInboundTxnOrderRequest {
	txnDetails := req.GetTransactionDetails()
	return &orderPb.ProcessInboundTxnOrderRequest{
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		TransactionDetails: &paymentNotificationPb.TransactionDetails{
			AccountNumber:        strings.TrimSpace(txnDetails.GetAccountNumber()),
			Id:                   strings.TrimSpace(txnDetails.GetId()),
			Type:                 strings.TrimSpace(txnDetails.GetType()),
			Particular:           strings.TrimSpace(txnDetails.GetParticular()),
			ReferenceNumber:      strings.TrimSpace(txnDetails.GetReferenceNumber()),
			Remarks:              strings.TrimSpace(txnDetails.GetRemarks()),
			ReportCode:           strings.TrimSpace(txnDetails.GetReportCode()),
			AdditionalParticular: strings.TrimSpace(txnDetails.GetAdditionalParticulars()),
			BatchSerialId:        strings.TrimSpace(txnDetails.GetBatchSerialId()),
			InstrumentDetails: &paymentPb.InstrumentDetails{
				InstrumentNumber: strings.TrimSpace(txnDetails.GetInstrumentDetails().GetInstrumentNumber()),
				InstrumentType:   strings.TrimSpace(txnDetails.GetInstrumentDetails().GetInstrumentType()),
			},
		},
		RequestId:             req.GetRequestId(),
		NotificationTimestamp: timestamp.Now(),
	}
}

func validateTransactionDetails(txnDetail *paymentNotificationPb.TransactionDetails) error {
	if txnDetail.GetId() == "" || txnDetail.GetTimestamp() == nil || txnDetail.GetDate() == nil {
		return epifierrors.ErrInvalidArgument
	}
	return nil
}
