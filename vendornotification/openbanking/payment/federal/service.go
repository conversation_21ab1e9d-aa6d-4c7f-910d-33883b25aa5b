package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"strings"

	types "github.com/epifi/gamma/api/typesv2"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"

	syncresp "github.com/epifi/be-common/pkg/syncwrapper/response"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	federalPaymentPb "github.com/epifi/gamma/api/vendornotification/openbanking/payment/federal"
	"github.com/epifi/gamma/api/vendors/federal/payment/b2c"
	"github.com/epifi/gamma/api/vendors/paystatusmapping"
	genConf "github.com/epifi/gamma/vendornotification/config/genconf"
)

const (
	// layout format Ref: https://golang.org/src/time/format.go
	federalTimeLayoutCallback   = "2006-01-02 15:04:05"
	UpdateTransactionRequest    = "UpdateTransactionRequest"
	UpdateB2CTransactionRequest = "UpdateB2CTransactionRequest"
)

// TODO(pruthvi): Standardisation around this? Ideally, we don't need any other errors other than this for callbacks
var errInternalServer = errors.New("internal server error")

var (
	responseActionStrToEnumMap = map[string]paymentPb.ProcessCallBackRequest_ResponseAction{
		"PROCESSED":       paymentPb.ProcessCallBackRequest_PROCESSED,
		"UNKNOWN":         paymentPb.ProcessCallBackRequest_UNKNOWN,
		"SUCCESS":         paymentPb.ProcessCallBackRequest_SUCCESS,
		"FAILURE":         paymentPb.ProcessCallBackRequest_FAILURE,
		"FAILED":          paymentPb.ProcessCallBackRequest_FAILURE,
		"SUSPECT":         paymentPb.ProcessCallBackRequest_UNKNOWN,
		"DEEMED APPROVED": paymentPb.ProcessCallBackRequest_DEEMED_APPROVED,
	}
)

// Service implements RPC methods that would consume paymentPb related vendor notifications from Federal Bank
type Service struct {
	// UnimplementedPaymentServer is embedded to have forward compatible implementations.
	federalPaymentPb.UnimplementedPaymentServer

	conf                             *config.Config
	dynamicConf                      *genConf.Config
	updateTransactionEventsPublisher queue.Publisher
	inboundTxnPublisher              queue.Publisher
	inboundUpiTxnPublisher           queue.Publisher
	inboundLoanTxnPublisher          queue.Publisher

	// TODO(nitesh): remove the UPI event publisher when we have pub-sub based filtering enabled
	upiReqAuthEventPublisher queue.Publisher

	upiResPayEventPublisher queue.Publisher

	upiRespMandateEventPublisher queue.Publisher

	upiReqTxnConfirmationEventPublisher queue.Publisher

	upiReqValAddressEventPublisher queue.Publisher
	// Events that are posted as a response for event initiated by Epifi & that don't require direct response
	// e.g., Response to the Events initiated from Epifi e.g., UserReRegistration, ListKeys, ReqPay, ListAcctProviders
	// Such events will be pushed to Vendor gateway. This would help orchestrate a sync wrapper over the
	// UPI's Async calls that were made from vendor gateway
	syncRespHandler syncresp.Handler

	upiListPspKeysEventPublisher queue.Publisher

	upiListVaePublisher queue.ExtendedPublisher

	orderClient orderPb.OrderServiceClient

	upiReqAuthMandateEventPublisher queue.Publisher

	upiReqMandateConfirmationEventPublisher queue.Publisher

	upiReqTxnConfirmationComplaintEventPublisher queue.Publisher

	upiReqAuthValCustEventPublisher queue.Publisher

	upiRespComplaintEventPublisher queue.Publisher

	upiReqMapperConfirmationEventPublisher queue.Publisher
}

// Typedefs to help dependency injection for wire
type UpdateTransactionEventsPublisher queue.Publisher
type InboundTxnPublisher queue.Publisher
type UPIReqAuthEventPublisher queue.Publisher
type UPIRespPayEventPublisher queue.Publisher
type UPIRespMandateEventPublisher queue.Publisher
type UPIReqTxnConfirmationEventPublisher queue.Publisher
type UPIReqValAddressEventPublisher queue.Publisher
type UPIListPspKeysEventPublisher queue.Publisher
type UPIListVaePublisher queue.ExtendedPublisher
type UPIReqAuthMandateEventPublisher queue.Publisher
type UPIReqMandateConfirmationEventPublisher queue.Publisher
type UPIReqTxnConfirmationComplaintEventPublisher queue.Publisher
type UPIReqAuthValCustEventPublisher queue.Publisher
type InboundUpiTxnPublisher queue.Publisher
type InboundLoanTxnPublisher queue.Publisher
type UPIRespComplaintEventPublisher queue.Publisher
type UPIReqMapperConfirmationEventPublisher queue.Publisher

// Constructor to create an instance of the service to process payment notifications from Federal bank
func NewService(
	updateTransactionEventsPublisher UpdateTransactionEventsPublisher,
	inboundTxnPublisher InboundTxnPublisher,
	syncRespHandler syncresp.Handler,
	upiReqAuthEventPublisher UPIReqAuthEventPublisher,
	upiResPayEventPublisher UPIRespPayEventPublisher,
	upiRespMandateEventPublisher UPIRespMandateEventPublisher,
	upiReqTxnConfirmationEventPublisher UPIReqTxnConfirmationEventPublisher,
	upiReqValAddressEventPublisher UPIReqValAddressEventPublisher,
	upiListPspKeysEventPublisher UPIListPspKeysEventPublisher,
	upiListVaePublisher UPIListVaePublisher,
	orderClient orderPb.OrderServiceClient,
	conf *config.Config,
	upiReqAuthMandateEventPublisher UPIReqAuthMandateEventPublisher,
	upiReqMandateConfirmationEventPublisher UPIReqMandateConfirmationEventPublisher,
	upiReqTxnConfirmationComplaintEventPublisher UPIReqTxnConfirmationComplaintEventPublisher,
	upiReqAuthValCustEventPublisher UPIReqAuthValCustEventPublisher,
	inboundUpiTxnPublisher InboundUpiTxnPublisher,
	inboundLoanTxnPublisher InboundLoanTxnPublisher,
	upiRespComplaintEventPublisher UPIRespComplaintEventPublisher,
	upiReqMapperConfirmationEventPublisher UPIReqMapperConfirmationEventPublisher,
	dynamicConf *genConf.Config,
) *Service {
	return &Service{
		updateTransactionEventsPublisher:             updateTransactionEventsPublisher,
		inboundTxnPublisher:                          inboundTxnPublisher,
		upiReqAuthEventPublisher:                     upiReqAuthEventPublisher,
		upiResPayEventPublisher:                      upiResPayEventPublisher,
		upiRespMandateEventPublisher:                 upiRespMandateEventPublisher,
		upiReqTxnConfirmationEventPublisher:          upiReqTxnConfirmationEventPublisher,
		upiReqValAddressEventPublisher:               upiReqValAddressEventPublisher,
		syncRespHandler:                              syncRespHandler,
		upiListPspKeysEventPublisher:                 upiListPspKeysEventPublisher,
		upiListVaePublisher:                          upiListVaePublisher,
		orderClient:                                  orderClient,
		conf:                                         conf,
		upiReqAuthMandateEventPublisher:              upiReqAuthMandateEventPublisher,
		upiReqMandateConfirmationEventPublisher:      upiReqMandateConfirmationEventPublisher,
		upiReqTxnConfirmationComplaintEventPublisher: upiReqTxnConfirmationComplaintEventPublisher,
		upiReqAuthValCustEventPublisher:              upiReqAuthValCustEventPublisher,
		inboundUpiTxnPublisher:                       inboundUpiTxnPublisher,
		inboundLoanTxnPublisher:                      inboundLoanTxnPublisher,
		upiRespComplaintEventPublisher:               upiRespComplaintEventPublisher,
		upiReqMapperConfirmationEventPublisher:       upiReqMapperConfirmationEventPublisher,
		dynamicConf:                                  dynamicConf,
	}
}

func (s *Service) UpdateTransaction(ctx context.Context, req *federalPaymentPb.UpdateTransactionRequest) (*empty.Empty, error) {
	var (
		err error
	)

	redactor.LogCallbackRequestData(ctx, req, UpdateTransactionRequest, req.GetRequestId(), vendorsRedactor.Config)

	event := &paymentPb.ProcessCallBackRequest{PartnerBank: commonvgpb.Vendor_FEDERAL_BANK}

	logger.Debug(ctx, fmt.Sprintf("Received a callback from FEDERAL with id: %v", req.RequestId))

	convertResponseAttributesToEpifiResponseAttributes(event, req.GetResponseCode(), req.GetResponseDesc())

	if copierErr := copier.Copy(event, req); copierErr != nil {
		logger.Error(ctx, fmt.Sprintf("Error in Copy from %T to %T, err: %v", req, event, copierErr))
		return nil, errInternalServer
	}
	// Over-write the UTR using the new UTR22 field if this is being passed.
	txnUtr22 := strings.TrimSpace(req.GetUtrTwentyTwo())
	if txnUtr22 != "" {
		event.Utr = txnUtr22
	}

	if event.Amount, err = money.ParseString(req.GetAmount(), money.RupeeCurrencyCode); err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to parse money: %s, err: %v", req.GetAmount(), err))
		return nil, errInternalServer
	}

	if event.TransTimestamp, err = datetime.ParseStringTimestampProtoInLocation(federalTimeLayoutCallback,
		req.GetTransTimestamp(), datetime.IST); err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to parse proto timestamp: %s, err: %v", req.GetAmount(), err))
		return nil, errInternalServer
	}

	event.ResponseAction = responseActionStrToEnumMap[req.GetResponseAction()]
	event.Utr = strings.TrimSpace(event.Utr)
	event.RequestId = strings.TrimSpace(event.RequestId)
	event.CreditedAccountName = strings.TrimSpace(event.CreditedAccountName)
	event.RemitterAccountNumber = strings.TrimSpace(event.RemitterAccountNumber)
	event.BeneficiaryAccountNumber = strings.TrimSpace(event.BeneficiaryAccountNumber)

	ctx = s.populateContextWithActors(ctx, req.GetRequestId())

	if _, err := s.updateTransactionEventsPublisher.Publish(ctx, event); err != nil {
		logger.Error(ctx, fmt.Sprintf("Unable to publish Call Back Request to the queue %v", event), zap.Error(err))
		return nil, errInternalServer
	}
	return &empty.Empty{}, nil
}

func (s *Service) UpdateB2CTransaction(ctx context.Context, req *b2c.UpdateB2CTransactionRequest) (*empty.Empty, error) {
	logger.Debug(ctx, fmt.Sprintf("Received a callback from FEDERAL with id: %v", req.RequestId))

	redactor.LogCallbackRequestData(ctx, req, UpdateB2CTransactionRequest, req.GetRequestId(), vendorsRedactor.Config)

	event, err := CopyB2CRequestToCallBackRequest(ctx, req)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Error in Copy from %T to %T, err: %v", req, event, err))
		return nil, errInternalServer
	}

	if _, err := s.updateTransactionEventsPublisher.Publish(ctx, event); err != nil {
		logger.Error(ctx, fmt.Sprintf("Unable to publish Call Back Request to the queue %v", event), zap.Error(err))
		return nil, errInternalServer
	}

	return &empty.Empty{}, nil
}

func CopyB2CRequestToCallBackRequest(ctx context.Context, req *b2c.UpdateB2CTransactionRequest) (*paymentPb.ProcessCallBackRequest, error) {
	amount := money.ParseFloat(req.GetAmount(), money.RupeeCurrencyCode)

	protoTs, err := datetime.ParseStringTimestampProtoInLocation(federalTimeLayoutCallback, req.GetTransTimestamp(), datetime.IST)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to parse timestamp %v err: %v ", req.GetTransTimestamp(), err))
		return nil, errInternalServer
	}

	fundTransferMapping := paystatusmapping.GetFundTransferStatusMapping(commonvgpb.Vendor_FEDERAL_BANK, req.GetResponseCode())

	callBackRequest := &paymentPb.ProcessCallBackRequest{
		PartnerBank:              commonvgpb.Vendor_FEDERAL_BANK,
		RequestId:                req.GetRequestId(),
		Utr:                      req.GetUtr(),
		RemitterAccountNumber:    req.GetRemitterAccountNumber(),
		BeneficiaryAccountNumber: req.GetBeneficiaryAccountNumber(),
		CreditedAccountName:      req.GetCreditedAccountName(),
		Amount:                   amount,
		TransTimestamp:           protoTs,
		RawResponseCode:          req.GetResponseCode(),
		RawResponseDescription:   req.GetResponseReason(),
		ResponseAction:           responseActionStrToEnumMap[req.GetResponseAction()],
		StatusCode:               fundTransferMapping.StatusCode,
		StatusDescriptionPayer:   fundTransferMapping.StatusCodeDescriptionPayer,
		StatusDescriptionPayee:   fundTransferMapping.StatusCodeDescriptionPayee,
		DeclineType:              fundTransferMapping.DeclineType,
	}
	return callBackRequest, nil
}

func convertResponseAttributesToEpifiResponseAttributes(event *paymentPb.ProcessCallBackRequest, rawResponseCode, rawResponseDescription string) {
	fundTransferMapping := paystatusmapping.GetFundTransferStatusMapping(commonvgpb.Vendor_FEDERAL_BANK, rawResponseCode)
	event.RawResponseCode = rawResponseCode
	event.RawResponseDescription = rawResponseDescription
	event.StatusCode = fundTransferMapping.StatusCode
	event.StatusDescriptionPayer = fundTransferMapping.StatusCodeDescriptionPayer
	event.StatusDescriptionPayee = fundTransferMapping.StatusCodeDescriptionPayee
	event.DeclineType = fundTransferMapping.DeclineType
}

func (s *Service) populateContextWithActors(ctx context.Context, reqId string) context.Context {
	resp, err := s.orderClient.GetActorsFromReqId(ctx, &orderPb.GetActorsFromReqIdRequest{
		ReqId: reqId,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "Error in populating actor for req id ", zap.Error(err), zap.String(logger.REQUEST_ID, reqId))
		return ctx
	case !resp.GetStatus().IsSuccess():
		logger.Error(ctx, "non-ok in populating actor for req id ", zap.String(logger.REQUEST_ID, reqId))
		return ctx
	}
	fromActor, toActor := resp.GetFromActor(), resp.GetToActor()
	ctx = epificontext.CtxWithActorId(ctx, fromActor.GetId())
	// assuming that the from_actor is always internal, checking only whether the to_actor is internal or not before enriching in context
	if toActor.GetType() == types.Actor_USER {
		ctx = epificontext.CtxWithSecondaryActorId(ctx, toActor.GetId())
	}
	return ctx
}
