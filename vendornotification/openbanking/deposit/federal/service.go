package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	depositPkg "github.com/epifi/gamma/pkg/deposit"
	"github.com/epifi/gamma/vendornotification/redactor"

	"github.com/golang/protobuf/ptypes"
	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	depositPb "github.com/epifi/gamma/api/deposit"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	federalDepositPb "github.com/epifi/gamma/api/vendornotification/openbanking/deposit/federal"
	"github.com/epifi/gamma/api/vendors/depositresponsestatusmapping"
	vendorFederal "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/config"
)

const (
	layout                           = "2006-01-02 15:04:05"
	CreateDepositCallbackResponse    = "CreateDepositCallbackResponse"
	CloseDepositCallbackResponse     = "CloseDepositCallbackResponse"
	DepositAutoRenewCallbackResponse = "DepositAutoRenewCallbackResponse"
)

var (
	cfg, _ = config.Load()
)

var internalServerError = errors.New("internal server error")

type Service struct {
	// UnimplementedDepositServer is embedded to have forward compatible implementations.
	federalDepositPb.UnimplementedDepositServer
	createDepositCallbackPublisher   queue.Publisher
	preCloseDepositCallbackPublisher queue.Publisher
	fdAutoRenewCallbackPublisher     queue.Publisher
}

// Typedefs to help dependency injection for wire
type CreateDepositCallbackPublisher queue.Publisher
type PreCloseDepositCallbackPublisher queue.Publisher
type FdAutoRenewCallbackPublisher queue.Publisher

func NewService(createDepositCallbackPublisher CreateDepositCallbackPublisher, preCloseDepositCallbackPublisher PreCloseDepositCallbackPublisher,
	fdAutoRenewCallbackPublisher FdAutoRenewCallbackPublisher) *Service {
	return &Service{
		createDepositCallbackPublisher:   createDepositCallbackPublisher,
		preCloseDepositCallbackPublisher: preCloseDepositCallbackPublisher,
		fdAutoRenewCallbackPublisher:     fdAutoRenewCallbackPublisher,
	}
}

func (s *Service) ProcessCreateDepositCallback(ctx context.Context, req *vendorFederal.CreateDepositCallbackResponse) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, CreateDepositCallbackResponse, req.GetRequestId(), vendorsRedactor.Config)

	var err error

	if req.GetSenderCode() != cfg.Secrets.Ids[config.SenderCode] {
		logger.Error(ctx, "Received invalid sender code",
			zap.String("senderCode", req.GetSenderCode()),
			zap.String(logger.REQUEST_ID, req.GetRequestId()))
		return nil, internalServerError
	}

	// convert string based timestamp to proto timestamp
	// if we are unable to parse tranTimestamp, set it to current timestamp
	protoTransactionTimestamp, err := convertToProtoTimestamp(req.GetTranTimeStamp())
	if err != nil {
		logger.Error(ctx, "failed to parse tranTimestamp from create deposit callback",
			zap.String(logger.REQUEST_ID, req.GetRequestId()),
			zap.Error(err))
		protoTransactionTimestamp = ptypes.TimestampNow()
	}

	createDepositCallbackRequest := &depositPb.ProcessCreateDepositCallbackRequest{
		PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
		RequestId:              req.GetRequestId(),
		TransactionTimestamp:   protoTransactionTimestamp,
		DepositAccountNumber:   req.GetDepositAccountNumber(),
		RawResponseCode:        req.GetResponseCode(),
		RawResponseDescription: req.GetResponseReason(),
		ResponseAction: depositPb.ProcessCreateDepositCallbackRequest_ResponseAction(
			depositPb.ProcessCreateDepositCallbackRequest_ResponseAction_value[req.GetResponseAction()]),
	}

	// Maturity Amount will be empty for SD accounts
	if req.GetMaturityAmount() != "" {
		if createDepositCallbackRequest.MaturityAmount, err = money.ParseString(req.GetMaturityAmount(), money.RupeeCurrencyCode); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse amount from string, maturity amount: %s", req.GetMaturityAmount()),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err))
			return nil, internalServerError
		}
	}

	if req.GetMaturityDate() != "" {
		createDepositCallbackRequest.MaturityDate, err = convertToProtoTimestamp(req.GetMaturityDate())
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse maturityDate"),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err),
			)
			return nil, internalServerError
		}
	}

	// Concatenate error list into an error string
	concatenatedErrorString := vendorFederal.GetConcatenatedErrorString(req.GetErrorList())

	// convert raw response code to epifi response code
	depositResponseStatus := depositresponsestatusmapping.GetDepositResponseEpifiStatus(
		commonvgpb.Vendor_FEDERAL_BANK,
		vgDepositPb.ApiType_CREATE_DEPOSIT,
		createDepositCallbackRequest.RawResponseCode,
	)
	createDepositCallbackRequest.StatusCode = depositResponseStatus.StatusCode
	createDepositCallbackRequest.StatusDescription = fmt.Sprintf("%s: %s",
		createDepositCallbackRequest.RawResponseDescription,
		concatenatedErrorString,
	)

	failureReasonString := depositPkg.GetFailureStringFromRawErrorMessages(
		req.GetResponseCode(),
		req.GetResponseReason(),
		concatenatedErrorString,
		depositResponseStatus.StatusCode,
		depositResponseStatus.StatusDescription,
	)
	createDepositCallbackRequest.FailureReason = depositPkg.GetFailureReason(failureReasonString)

	if _, err := s.createDepositCallbackPublisher.Publish(ctx, createDepositCallbackRequest); err != nil {
		logger.Error(ctx, "Unable to publish create deposit callback request to the queue",
			zap.String(logger.REQUEST_ID, req.GetRequestId()),
			zap.Error(err))
		return nil, internalServerError
	}

	logger.Info(ctx, "Successfully published to deposit create callback queue",
		zap.String(logger.REQUEST_ID, req.GetRequestId()))

	return &empty.Empty{}, nil
}

func (s *Service) ProcessPreCloseDepositCallback(ctx context.Context, req *vendorFederal.CloseDepositCallbackResponse) (*empty.Empty, error) {
	var err error
	redactor.LogCallbackRequestData(ctx, req, CloseDepositCallbackResponse, req.GetRequestId(), vendorsRedactor.Config)

	if req.GetSenderCode() != cfg.Secrets.Ids[config.SenderCode] {
		logger.Error(ctx, "Received invalid sender code",
			zap.String("senderCode", req.GetSenderCode()),
			zap.String(logger.REQUEST_ID, req.GetRequestId()))
		return nil, internalServerError
	}

	// convert string based timestamp to proto timestamp
	// if we are unable to parse tranTimestamp, set it to current timestamp
	protoTransactionTimestamp, err := convertToProtoTimestamp(req.GetTranTimeStamp())
	if err != nil {
		logger.Error(ctx, "failed to parse tranTimestamp from deposit preclose callback",
			zap.String(logger.REQUEST_ID, req.GetRequestId()),
			zap.Error(err))
		protoTransactionTimestamp = ptypes.TimestampNow()
	}

	closeValueDateTimestamp, err := convertToProtoTimestamp(req.GetCloseValueDate())
	if err != nil {
		logger.Error(ctx, "failed to parse close value date from deposit pre-close callback",
			zap.String(logger.REQUEST_ID, req.GetRequestId()),
			zap.Error(err))
	}

	preCloseDepositCallbackRequest := &depositPb.ProcessPreCloseDepositCallbackRequest{
		PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
		RequestId:              req.GetRequestId(),
		DepositAccountNumber:   req.GetDepositAccountNumber(),
		RepayAccountNum:        req.GetOperativeAccountNum(),
		RawResponseCode:        req.GetResponseCode(),
		RawResponseDescription: req.GetResponseReason(),
		TransactionTimestamp:   protoTransactionTimestamp,
		CloseValueDate:         closeValueDateTimestamp,
		ResponseAction: depositPb.ProcessPreCloseDepositCallbackRequest_ResponseAction(
			depositPb.ProcessCreateDepositCallbackRequest_ResponseAction_value[req.GetResponseAction()],
		),
	}

	// for fail close deposit response gross amount could be blank
	if req.GetGrossAmount() != "" {
		if preCloseDepositCallbackRequest.GrossAmount, err = money.ParseString(req.GetGrossAmount(), money.RupeeCurrencyCode); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse gross amount from string: %s", req.GetGrossAmount()),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err))
			return nil, internalServerError
		}
	}

	// for fail close deposit response interest amount could be blank
	if req.GetInterestAmount() != "" {
		if preCloseDepositCallbackRequest.InterestAmount, err = money.ParseString(req.GetInterestAmount(), money.RupeeCurrencyCode); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse interest amount from string: %s", req.GetInterestAmount()),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err))
			return nil, internalServerError
		}
	}

	// for fail close deposit response tds amount could be blank
	if req.GetTdsAmount() != "" {
		if preCloseDepositCallbackRequest.TdsAmount, err = money.ParseString(req.GetTdsAmount(), money.RupeeCurrencyCode); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse tds amount from string: %s", req.GetTdsAmount()),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err))
			return nil, internalServerError
		}
	}

	// for fail close deposit response net amount could be blank
	if req.GetNetAmount() != "" {
		if preCloseDepositCallbackRequest.NetAmount, err = money.ParseString(req.GetNetAmount(), money.RupeeCurrencyCode); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse net amount from string: %s", req.GetNetAmount()),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err))
			return nil, internalServerError
		}
	}

	if req.GetOriginalDepositBalance() != "" {
		if preCloseDepositCallbackRequest.OriginalDepositBalance, err = money.ParseString(req.GetOriginalDepositBalance(),
			money.RupeeCurrencyCode); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse original deposit balance from string: %s", req.GetOriginalDepositBalance()),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err))
			return nil, internalServerError
		}
	}

	if req.GetPreClosureAmount() != "" {
		if preCloseDepositCallbackRequest.PreClosureAmount, err = money.ParseString(req.GetPreClosureAmount(),
			money.RupeeCurrencyCode); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse pre closure amount from string: %s", req.GetPreClosureAmount()),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err))
			return nil, internalServerError
		}
	}

	// Concatenate error list into an error string
	concatenatedErrorString := vendorFederal.GetConcatenatedErrorString(req.GetErrorList())

	// convert raw response code to epifi response code
	depositResponseStatus := depositresponsestatusmapping.GetDepositResponseEpifiStatus(
		commonvgpb.Vendor_FEDERAL_BANK,
		vgDepositPb.ApiType_PRECLOSE_DEPOSIT,
		preCloseDepositCallbackRequest.RawResponseCode,
	)
	preCloseDepositCallbackRequest.StatusCode = depositResponseStatus.StatusCode
	preCloseDepositCallbackRequest.StatusDescription = fmt.Sprintf("%s: %s",
		preCloseDepositCallbackRequest.RawResponseDescription,
		concatenatedErrorString,
	)

	failureReasonString := depositPkg.GetFailureStringFromRawErrorMessages(
		req.GetResponseCode(),
		req.GetResponseReason(),
		concatenatedErrorString,
		depositResponseStatus.StatusCode,
		depositResponseStatus.StatusDescription,
	)
	preCloseDepositCallbackRequest.FailureReason = depositPkg.GetFailureReason(failureReasonString)

	if _, err := s.preCloseDepositCallbackPublisher.Publish(ctx, preCloseDepositCallbackRequest); err != nil {
		logger.Error(ctx, "Unable to publish preclose deposit callback request to the queue",
			zap.String(logger.REQUEST_ID, req.GetRequestId()),
			zap.Error(err))
		return nil, internalServerError
	}

	logger.Info(ctx, "Successfully published to deposit preclose callback queue",
		zap.String(logger.REQUEST_ID, req.GetRequestId()))

	return &empty.Empty{}, nil
}

// convertToProtoTimestamp converts string based timestamp in Federal format to protobuf timestamp
func convertToProtoTimestamp(ts string) (*timestamppb.Timestamp, error) {
	var protoTimestamp *timestamppb.Timestamp

	if ts != "" {
		parsedTimestamp, err := time.ParseInLocation(layout, ts, datetime.IST)
		if err != nil {
			return nil, fmt.Errorf("failed to parse ts from string, ts: %s: %w", ts, err)
		}
		if protoTimestamp, err = ptypes.TimestampProto(parsedTimestamp); err != nil {
			return nil, fmt.Errorf("failed to parse proto time from time.Time, parsedTimestamp: %s: %w", parsedTimestamp, err)
		}

		return protoTimestamp, nil
	}

	return nil, fmt.Errorf("failed to convert to proto timestamp because timestamp string is empty")
}

// nolint: funlen
func (s *Service) ProcessDepositMaturityActionCallback(ctx context.Context, req *vendorFederal.DepositAutoRenewCallbackResponse) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, DepositAutoRenewCallbackResponse, req.GetRequestId(), vendorsRedactor.Config)

	var err error

	if req.GetSenderCode() != cfg.Secrets.Ids[config.SenderCode] {
		logger.Error(ctx, "Received invalid sender code",
			zap.String("senderCode", req.GetSenderCode()),
			zap.String(logger.REQUEST_ID, req.GetRequestId()))
		return nil, internalServerError
	}

	// convert string based timestamp to proto timestamp
	// if we are unable to parse tranTimestamp, set it to current timestamp
	protoTransactionTimestamp, err := convertToProtoTimestamp(req.GetTranTimeStamp())
	if err != nil {
		logger.Error(ctx, "failed to parse tranTimestamp from create deposit callback",
			zap.String(logger.REQUEST_ID, req.GetRequestId()),
			zap.Error(err))
		protoTransactionTimestamp = ptypes.TimestampNow()
	}

	depositAutoRenewCallbackRequest := &depositPb.ProcessDepositMaturityActionCallbackRequest{
		PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
		RequestId:              req.GetRequestId(),
		TransactionTimestamp:   protoTransactionTimestamp,
		RenewalAccountNumber:   req.GetRenewalAccountNumber(),
		RawResponseCode:        req.GetResponseCode(),
		RawResponseDescription: req.GetResponseReason(),
		ResponseAction: depositPb.ProcessDepositMaturityActionCallbackRequest_ResponseAction(
			depositPb.ProcessDepositMaturityActionCallbackRequest_ResponseAction_value[req.GetResponseAction()]),
	}

	if req.GetMaturityAmount() != "" {
		if depositAutoRenewCallbackRequest.MaturityAmount, err = money.ParseString(req.GetMaturityAmount(), money.RupeeCurrencyCode); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse amount from string, maturity amount: %s", req.GetMaturityAmount()),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err))
			return nil, internalServerError
		}
	}

	if req.GetMaturityDate() != "" {
		depositAutoRenewCallbackRequest.MaturityDate, err = convertToProtoTimestamp(req.GetMaturityDate())
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to parse maturityDate"),
				zap.String(logger.REQUEST_ID, req.GetRequestId()),
				zap.Error(err),
			)
			return nil, internalServerError
		}
	}

	// Concatenate error list into an error string
	concatenatedErrorString := vendorFederal.GetConcatenatedErrorString(req.GetErrorList())

	// convert raw response code to epifi response code
	depositResponseStatus := depositresponsestatusmapping.GetDepositResponseEpifiStatus(
		commonvgpb.Vendor_FEDERAL_BANK,
		vgDepositPb.ApiType_AUTO_RENEW_FD,
		depositAutoRenewCallbackRequest.RawResponseCode,
	)
	depositAutoRenewCallbackRequest.StatusCode = depositResponseStatus.StatusCode
	depositAutoRenewCallbackRequest.StatusDescription = fmt.Sprintf("%s: %s",
		depositAutoRenewCallbackRequest.RawResponseDescription,
		concatenatedErrorString,
	)

	failureReasonString := depositPkg.GetFailureStringFromRawErrorMessages(
		req.GetResponseCode(),
		req.GetResponseReason(),
		concatenatedErrorString,
		depositResponseStatus.StatusCode,
		depositResponseStatus.StatusDescription,
	)
	depositAutoRenewCallbackRequest.FailureReason = depositPkg.GetFailureReason(failureReasonString)

	if _, err := s.fdAutoRenewCallbackPublisher.Publish(ctx, depositAutoRenewCallbackRequest); err != nil {
		logger.Error(ctx, "Unable to publish deposit Auto renew callback request to the queue",
			zap.String(logger.REQUEST_ID, req.GetRequestId()),
			zap.Error(err))
		return nil, internalServerError
	}

	return &empty.Empty{}, nil
}
