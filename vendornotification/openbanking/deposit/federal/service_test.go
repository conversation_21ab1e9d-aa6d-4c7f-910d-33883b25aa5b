package federal_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	vendorFederal "github.com/epifi/gamma/api/vendors/federal"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"
	vnFederal "github.com/epifi/gamma/vendornotification/openbanking/deposit/federal"
)

var (
	testSenderCode = "EpiFi_Test_Cd"
)

func TestService_ProcessCreateDepositCallback(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockQueue := mock_queue.NewMockPublisher(ctrl)
	mockQueue.EXPECT().
		Publish(context.Background(), gomock.Any()).Return("messageId", nil).AnyTimes()

	federalDepositCallbackService := vnFederal.NewService(mockQueue, mockQueue, mockQueue)

	defer func() {
		ctrl.Finish()
		federalDepositCallbackService = nil
	}()

	type args struct {
		ctx context.Context
		req *vendorFederal.CreateDepositCallbackResponse
	}
	tests := []struct {
		name    string
		args    args
		want    *empty.Empty
		wantErr bool
	}{
		{
			name: "Should successfully process create deposit callback for FD accounts",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.CreateDepositCallbackResponse{
					SenderCode:           testSenderCode,
					RequestId:            "NEOLIFDOThu20201022044959rHpKU",
					TranTimeStamp:        "2020-10-22 17:23:16.593004",
					CustomerId:           "*********",
					DepositAccountNumber: "**************",
					MaturityAmount:       "1348",
					MaturityDate:         "2021-01-22 00:00:00",
					ResponseCode:         "000",
					ResponseReason:       "SUCCESS",
					ResponseAction:       "SUCCESS",
					ErrorList:            nil,
				},
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "Should successfully process create deposit callback for SD accounts (no maturity date)",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.CreateDepositCallbackResponse{
					SenderCode:           testSenderCode,
					RequestId:            "NEOLIFRDThu20201022044959rHpKU",
					TranTimeStamp:        "2020-10-22 17:23:16.593004",
					CustomerId:           "*********",
					DepositAccountNumber: "**************",
					ResponseCode:         "000",
					ResponseReason:       "SUCCESS",
					ResponseAction:       "SUCCESS",
					ErrorList:            nil,
				},
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "Should successfully process create deposit callback for a failure response from federal",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.CreateDepositCallbackResponse{
					SenderCode:     testSenderCode,
					RequestId:      "NEOLIFDOThu20201022044959rHpKU",
					TranTimeStamp:  "2020-10-22 17:23:16.593004",
					CustomerId:     "*********",
					ResponseCode:   "LAE1111",
					ResponseReason: "System Failure",
					ResponseAction: "FAILURE",
					ErrorList: []*vendorFederal.DepositError{{
						ErrorCode: "E4639",
						Reason:    "The deposit period must be more than [6] months and [0] days for the scheme",
					}},
				},
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "Should fail to process a create deposit callback with invalid sender code",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.CreateDepositCallbackResponse{
					SenderCode: "random-sender-code",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := federalDepositCallbackService.ProcessCreateDepositCallback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessCreateDepositCallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ProcessCreateDepositCallback() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ProcessPreCloseDepositCallback(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockQueue := mock_queue.NewMockPublisher(ctrl)
	mockQueue.EXPECT().
		Publish(context.Background(), gomock.Any()).Return("messageId", nil).AnyTimes()

	federalDepositCallbackService := vnFederal.NewService(mockQueue, mockQueue, mockQueue)

	defer func() {
		ctrl.Finish()
		federalDepositCallbackService = nil
	}()

	type args struct {
		ctx context.Context
		req *vendorFederal.CloseDepositCallbackResponse
	}
	tests := []struct {
		name    string
		args    args
		want    *empty.Empty
		wantErr bool
	}{
		{
			name: "Should successfully process preclose deposit callback response",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.CloseDepositCallbackResponse{
					SenderCode:             testSenderCode,
					RequestId:              "NEOLICDA215254800010030001021",
					TranTimeStamp:          "2020-10-22 17:23:16.593004",
					CustomerId:             "*********",
					CustomerName:           "random-name",
					DepositAccountNumber:   "*********",
					OperativeAccountNum:    "*********",
					OriginalDepositBalance: "1200",
					InterestAmount:         "40",
					GrossAmount:            "1240",
					TdsAmount:              "20",
					NetAmount:              "1220",
					PreClosureAmount:       "1220",
					CloseValueDate:         "2020-10-22",
					ResponseCode:           "000",
					ResponseReason:         "SUCCESS",
					ResponseAction:         "SUCCESS",
					ErrorList:              nil,
				},
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "Should successfully process callback with failure response from federal",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.CloseDepositCallbackResponse{
					SenderCode:     testSenderCode,
					ResponseCode:   "162",
					ResponseReason: "The account does not exist",
					ResponseAction: "FAILURE",
					ErrorList:      nil,
				},
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "Should fail to process a close deposit callback with invalid sender code",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.CloseDepositCallbackResponse{
					SenderCode: "random-sender-code",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := federalDepositCallbackService.ProcessPreCloseDepositCallback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessPreCloseDepositCallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ProcessPreCloseDepositCallback() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ProcessDepositMaturityActionCallback(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockQueue := mock_queue.NewMockPublisher(ctrl)
	mockQueue.EXPECT().
		Publish(context.Background(), gomock.Any()).Return("messageId", nil).AnyTimes()

	federalDepositCallbackService := vnFederal.NewService(mockQueue, mockQueue, mockQueue)

	defer func() {
		ctrl.Finish()
		federalDepositCallbackService = nil
	}()

	type args struct {
		ctx context.Context
		req *vendorFederal.DepositAutoRenewCallbackResponse
	}
	tests := []struct {
		name    string
		args    args
		want    *empty.Empty
		wantErr bool
	}{
		{
			name: "Should successfully process Auto-Renew deposit callback response",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.DepositAutoRenewCallbackResponse{
					SenderCode:     testSenderCode,
					RequestId:      "NEOLICDA215254800012310001021",
					TranTimeStamp:  "2020-10-22 17:23:16.593004",
					CustomerId:     "*********",
					ResponseCode:   "000",
					ResponseReason: "SUCCESS",
					ResponseAction: "SUCCESS",
					ErrorList:      nil,
				},
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "Should successfully process callback with failure response from federal",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.DepositAutoRenewCallbackResponse{
					SenderCode:     testSenderCode,
					ResponseCode:   "162",
					ResponseReason: "The account does not exist",
					ResponseAction: "FAILURE",
					ErrorList:      nil,
				},
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
		{
			name: "Should fail to process a close deposit callback with invalid sender code",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.DepositAutoRenewCallbackResponse{
					SenderCode: "random-sender-code",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Should successfully process callback with failure response from federal",
			args: args{
				ctx: context.Background(),
				req: &vendorFederal.DepositAutoRenewCallbackResponse{
					SenderCode:     testSenderCode,
					ResponseCode:   "LAE1111",
					ResponseReason: "The account does not exist",
					ResponseAction: "FAILURE",
					ErrorList:      nil,
				},
			},
			want:    &empty.Empty{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := federalDepositCallbackService.ProcessDepositMaturityActionCallback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessDepositMaturityActionCallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ProcessDepositMaturityActionCallback() got = %v, want %v", got, tt.want)
			}
		})
	}
}
