package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/golang/protobuf/ptypes/empty" // nolint:depguard
	"github.com/jinzhu/copier"
	"go.uber.org/zap"

	cardProvisioningPb "github.com/epifi/gamma/api/card/provisioning"
	vgPbApi "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	vdPb "github.com/epifi/gamma/api/vendors/federal"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/api/vendors/responsemapping/card"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/redactor"
)

// maps the federal ResponseCode parameter to the internal card creation state.
// Flag error if the ResponseCode value received is not part of this map. Denotes that the error codes
// provided as part of api spec has changed. Should be addressed asap or we may miss to process callbacks.
var (
	codeToCardPhysicalDispatchStatusMap = map[string]cardProvisioningPb.RequestState{
		"000":     cardProvisioningPb.RequestState_SUCCESS,
		"OBE0071": cardProvisioningPb.RequestState_FAILED,  // System Failure
		"OBE0007": cardProvisioningPb.RequestState_FAILED,  // Internal Failure
		"OBE0999": cardProvisioningPb.RequestState_SUCCESS, // Physical card is already issued to the customer.
	}
)

func (s *Service) UpdateDispatchPhysicalCardStatus(ctx context.Context, req *vdPb.UpdateDispatchPhysicalCardStatusRequest) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, UpdateDispatchPhysicalCardStatus, req.GetRequestId(), vendorsRedactor.Config)

	event := &cardProvisioningPb.ProcessDispatchPhysicalCardCallbackRequest{PartnerBank: commonvgpb.Vendor_FEDERAL_BANK}

	logger.Debug(ctx, fmt.Sprintf("Received card physical dispatch callback from FEDERAL: id: %v, response code: %v, response desc: %v",
		req.RequestId, req.ResponseCode, req.ResponseReason))

	err := constructDispatchPhysicalCardEventRequest(ctx, req, event)
	if err != nil {
		return nil, err
	}
	// publish the packet to the queue to be consumed by the card BE service. The retries are also handled using the
	// same queue.
	if _, err = s.dispatchPhysicalCardCallbackPublisher.Publish(ctx, event); err != nil {
		logger.Error(ctx, "Unable to publish physical dispatch call back to the queue", zap.Error(err),
			zap.String(logger.REQUEST_ID, req.GetRequestId()))
		return nil, internalServerErr
	}
	return &empty.Empty{}, nil
}

func constructDispatchPhysicalCardEventRequest(ctx context.Context, req *vdPb.UpdateDispatchPhysicalCardStatusRequest,
	event *cardProvisioningPb.ProcessDispatchPhysicalCardCallbackRequest) error {
	// copy all the other fields with same name
	if copierErr := copier.Copy(event, req); copierErr != nil {
		logger.Error(ctx, fmt.Sprintf("Error in Copy from %T to %T", req, event), zap.Error(copierErr))
		return internalServerErr
	}
	// convert raw response code to internal status code
	responseInternalStatus := card.GetCardResponseInternalStatus(commonvgpb.Vendor_FEDERAL_BANK,
		vgPbApi.ApiType_DISPATCH_PHYSICAL_CARD, req.GetResponseCode())

	// set the card dispatch state
	if state, ok := codeToCardPhysicalDispatchStatusMap[req.GetResponseCode()]; ok {
		event.State = state
		if state == cardProvisioningPb.RequestState_FAILED {
			logger.Error(ctx, "received failure state for card physical dispatch response",
				zap.String(logger.STATUS_CODE, req.GetResponseCode()))
			event.InternalResponseCode = responseInternalStatus.StatusCode
			event.ResponseReason = req.GetResponseReason()
			return nil
		}
	} else {
		// This should not happen. Indicates: changes in card specs without prior communication or some error on Federal
		// end. Must be followed up with federal immediately to resolve this.
		logger.Error(ctx, "received unknown response code from federal in card dispatch request callback",
			zap.String(logger.REQUEST_ID, req.RequestId), zap.String(logger.RESPONSE_CODE, req.GetResponseCode()))
		return internalServerErr
	}
	return nil
}
