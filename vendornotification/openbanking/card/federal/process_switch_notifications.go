package federal

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	protoJson "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	cardNotificationPb "github.com/epifi/gamma/api/card/notification"
	vdPb "github.com/epifi/gamma/api/vendors/federal"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	vendorsPkg "github.com/epifi/gamma/pkg/vendors"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
)

var (
	transactionCodeToNotificationTypeMap = map[string]cardEnumsPb.NotificationType{
		"MR": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_MOBILE_BANKING_REGISTRATION,
		"81": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_PIN_CHANGE,
		"88": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_PIN_SET,
		"89": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_PIN_SET,
		"LU": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_LIMIT_UPDATE,
		"FU": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_STATE_UPDATE,
		"TC": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_TOKEN_COMPLETION,
		"TV": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_TOKEN_EVENT,
		"TA": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_TOKEN_AUTHORIZATION_TRANSACTION,
		// TODO(priyansh) : Check with Federal on this
		// "10": cardEnumsPb.NotificationType_WITHDRAWAL, s ¯
		"14": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_VISA_MONEY_TRANSFER,
		"RF": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_REFUND_TRANSACTION,
		"10": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_POS_ECOMM_PURCHASE,
		"RP": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_RECURRING_PAYMENT_TRANSACTION,
		"RC": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_RECURRING_REGISTRATION_TRANSACTION,
		"GP": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_PIN_SET,
		"49": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL,
		"47": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_DEBIT,
		"48": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_CREDIT,
		"45": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CARD_INITIATED,
		"46": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_OTHER_BANK_CARD_FEDERAL_BANK_ATM,
		"70": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_MINI_STATEMENT,
		"CV": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_VALIDATION,
		"PV": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_PIN_VALIDATION,
		"30": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_BALANCE_ENQUIRY,
		"16": cardEnumsPb.NotificationType_NOTIFICATION_TYPE_ACCOUNT_STATUS_ENQUIRY,
	}

	messageTypeToTransactionTypeMap = map[string]cardEnumsPb.MessageType{
		"200":  cardEnumsPb.MessageType_MESSAGE_TYPE_REQUEST,
		"210":  cardEnumsPb.MessageType_MESSAGE_TYPE_RESPONSE,
		"220":  cardEnumsPb.MessageType_MESSAGE_TYPE_ADVICE,
		"402":  cardEnumsPb.MessageType_MESSAGE_TYPE_REVERSAL_REQUEST,
		"412":  cardEnumsPb.MessageType_MESSAGE_TYPE_REVERSAL_RESPONSE,
		"420":  cardEnumsPb.MessageType_MESSAGE_TYPE_REVERSAL,
		"9991": cardEnumsPb.MessageType_MESSAGE_TYPE_LOG_ONLY,
	}

	// TODO(Sanskriti): add authorization switch mappings for two valued switches.
	posServiceConditionCodeToAuthorizationSwitchMap = map[string]cardEnumsPb.AuthorizationSwitch{
		"":   cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_UNSPECIFIED,
		"07": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_TELEPHONIC_DEVICE_REQUEST,
		"08": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_MAIL_OR_TELEPHONE_ORDER,
		"09": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_SECURITY_ALERT,
		"10": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_CUSTOMER_IDENTITY_VERIFIED,
		"11": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_SUSPECTED_FRAUD,
		"12": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_SECURITY_REASONS,
		"13": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_REPRESENTMENT_OF_ITEM,
		"14": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_PUBLIC_UTILITY_TERMINAL,
		"15": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_CUSTOMER_TERMINAL,
		"16": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_ADMINISTRATION_TERMINAL,
		"17": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_RETURNED_ITEM,
		"21": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_MANUAL_REVERSAL,
		"22": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_COUNTED,
		"23": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_NOT_COUNTED,
		"24": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_DEPOSIT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS,
		"25": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_PAYMENT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS,
		"26": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_WITHDRAWAL_HAD_ERROR_REVERSED,
		"27": cardEnumsPb.AuthorizationSwitch_AUTHORIZATION_SWITCH_UNATTENDED_TERMINAL_UNABLE_TO_RETAIN_CARD,
	}
	// vendor time formats
	timeFormats = []string{"2006-01-02 15:04:05"}
	// pos types where transaction mode will be NFC (contactless)
	nfcPosTypes = []string{"07", "91"}
	// successful response codes for financial notifications
	successResponseCodes = []string{"3001", "5000", "5001"}

	financialNotificationTypes = []cardEnumsPb.NotificationType{
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_TOKEN_AUTHORIZATION_TRANSACTION,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_VISA_MONEY_TRANSFER,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_REFUND_TRANSACTION,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_POS_ECOMM_PURCHASE,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_RECURRING_PAYMENT_TRANSACTION,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_RECURRING_REGISTRATION_TRANSACTION,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_DEBIT,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_CREDIT,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CARD_INITIATED,
		cardEnumsPb.NotificationType_NOTIFICATION_TYPE_CARD_TO_CARD_OTHER_BANK_CARD_FEDERAL_BANK_ATM,
	}

	posEcomTransactionResponseCodeToSwitchNotificationResponse = map[string]cardEnumsPb.SwitchNotificationResponse{
		"3101": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_PROCESS_TRANSACTION,
		"3201": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INCORRECT_PIN,
		"3051": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CARD_EXPIRED,
		"3221": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ECOM_TRANSACTIONS_NOT_ENABLED,
		"3107": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_DAILY_MAX,
		"3105": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_POS_NOT_SUPPORTED,
		"3052": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS,
		"3059": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DUPLICATE_TRANSACTION,
		"3050": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_DECLINED,
		"3056": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_TYPE_NOT_SUPPORTED,
		"3055": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_TRANSACTION,
		"3220": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED,
		"3224": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CONTACTLESS_CARD_USAGE_NOT_ENABLED,
		"3095": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DAILY_CONTACTLESS_PAYMENTS_LIMIT_EXCEEDED,
		"3219": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CARD_OFF_FOR_TRANSACTIONS,
		"3223": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_HOST_DOWN,
		"5223": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_HOST_NOT_AVAILABLE,
		"3225": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DOMESTIC_TRANSACTIONS_NOT_ENABLED,
		"3204": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ELA_FAILURE,
		"5076": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_LOW_FUNDS_FOR_TRANSACTION,
		"3208": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_EXPIRY_DATE,
		"3218": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_PRM_DECLINED,
		"3064": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CVV_ERROR,
		"5059": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INSUFFICIENT_FUNDS_FOR_TRANSACTION, // cbs response code
		"3206": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CAF_NOT_FOUND,
		"3057": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_LOST_OR_STOLEN_CARD,
		"5056": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INELIGIBLE_ACCOUNT,
		"3232": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_POS_USAGE_NOT_ENABLED,
		"5050": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_AUTHORIZE_TRANSACTION,
		"5231": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_SI_HUB_DECLINE,
		"3074": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_AUTHORIZE_TRANSACTION, // switch response code
		"5074": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_AUTHORIZE_TRANSACTION, // cbs response code
		"3089": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CAF_STATUS_DECLINE,
		"6074": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_MESSAGE_EDIT_ERROR,
		// TODO(cb): get clarity if this is actually an error, ref: https://github.com/epiFi/gamma/blob/112ab7845837119681e449532e91cda24359596e/vendornotification/openbanking/card/federal/process_switch_notifications.go#L94
		"3001": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_APPROVED_NO_BALANCES,
		"3408": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_FALLBACK_DECLINE,
		"5072": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DEST_NOT_AVAILABLE,
		"3228": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TOKEN_IN_APP_FLAG_OFF,
		"5070": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_NO_IDF,
		"3229": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TOKEN_COF_FLAG_OFF,
		"3400": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ARQC_FAILURE,
		"3067": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_TXN_DATE,
		"3903": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CARD_TO_BE_CAPTURED_IN_CAF,
		"5058": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_BAD_CARD_STATUS,
		"3081": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_MAX_CREDIT_PER_REFUND,
		"3226": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TOKEN_NFC_FLAG_OFF,
		"3082": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_USAGE_LIMIT_EXCEEDED,
	}
	atmTransactionResponseCodeToSwitchNotificationResponse = map[string]cardEnumsPb.SwitchNotificationResponse{
		"3053": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INCORRECT_PIN,
		"3051": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CARD_EXPIRED,
		"3052": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_CARD,
		"3107": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_DAILY_MAX,
		"3050": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNAUTHORIZED_USAGE,
		"3056": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INSUFFICIENT_FUNDS_FOR_TRANSACTION, // switch response code
		"5059": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INSUFFICIENT_FUNDS_FOR_TRANSACTION, // cbs response code
		"3055": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_TRANSACTION,
		"3094": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED,
		"3061": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DAILY_WITHDRAWAL_LIMIT_REACHED,
		"5076": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_LOW_FUNDS_FOR_TRANSACTION,
		"3218": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_PRM_DECLINED,
		"3098": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ATM_USAGE_NOT_ENABLED,
		"3057": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_TYPE_NOT_SUPPORTED,
		"5056": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INELIGIBLE_ACCOUNT,
		"5050": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNAUTHORIZED_USAGE, // cbs response code
		"3150": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNAUTHORIZED_USAGE, // switch response code
		"3074": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_MESSAGE_EDIT_ERROR, // switch response code
		"5074": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_MESSAGE_EDIT_ERROR, // cbs response code
		"3062": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS,
		"3063": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_WITHDRAWAL_LIMIT,
		"3093": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CARD_OFF_FOR_TRANSACTIONS,
		"3088": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_FALLBACK_DECLINE,
		"5072": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DEST_NOT_AVAILABLE, // switch response code
		"3072": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DEST_NOT_AVAILABLE, // cbs response code
		"3084": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ATC_CHECK_FAILURE,
		"5070": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_SYSTEM_ERROR, // / cbs response code
		"3070": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_SYSTEM_ERROR, // switch response code
		"3087": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_ARQC_FAILURE,
		"3096": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_DOMESTIC_TRANSACTIONS_NOT_ENABLED,
		"7074": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_MESSAGE_EDIT_ERROR,
		"3075": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_RESERVED_B24_CODE,
		"3081": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_HSM_PARAM_ERROR,
		"7055": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_TRANSACTION,
		"6074": cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_MESSAGE_EDIT_ERROR,
	}
)

func logRequestId(requestId string) zap.Field {
	return zap.String(logger.REQUEST_ID, requestId)
}

func (s *Service) ProcessCardSwitchNotification(ctx context.Context, req *vdPb.ProcessCardSwitchNotificationRequest) (*emptypb.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, processCardSwitchNotification, req.GetRequestId(), vendorsRedactor.Config)

	req = sanitiseNotificationRequest(req)
	logger.Info(ctx, "processing card switch notification", zap.String(logger.NOTIFICATION_TYPE,
		req.GetTransactionCode()), logRequestId(req.GetRequestId()))

	// increase the counter for switch notification callback with transaction mode
	metrics.RecordDebitCardSwitchNotificationTxnModeCount(req.GetTransactionMode())

	// Only the first two values of the txn code are used to map to the corresponding notification type. For more details refer:
	// https://docs.google.com/document/d/1bTlo7d1fAVTqvMf4FxaP5u0ihoNSz0OR/edit
	txnCode := req.GetTransactionCode()[0:2]
	notificationType, ok := transactionCodeToNotificationTypeMap[txnCode]
	if !ok {
		logger.Error(ctx, "notification type does not exist for the transaction code",
			zap.String(logger.TRANSACTION_CODE, req.GetTransactionCode()), logRequestId(req.GetRequestId()))
		notificationType = cardEnumsPb.NotificationType_NOTIFICATION_TYPE_UNSPECIFIED
	}

	financialNotificationType := lo.Contains(financialNotificationTypes, notificationType)
	switch {
	case !financialNotificationType:
		event, err := constructNonFinancialNotificationEventRequest(req, notificationType)
		if err != nil {
			logger.Error(ctx, "error in constructing non financial notification event request", zap.Error(err), logRequestId(req.GetRequestId()))
			return nil, internalServerErr
		}
		_, err = s.cardSwitchNonFinancialNotificationPublisher.Publish(ctx, event)
		if err != nil {
			logger.Error(ctx, "error in publishing card non financial notification event", zap.Error(err), logRequestId(req.GetRequestId()))
			return nil, internalServerErr
		}

		logger.Info(ctx, "non-financial notification published successfully")
	default:
		event, err := constructFinancialNotificationEventRequest(ctx, req, notificationType)
		if err != nil {
			logger.Error(ctx, "error in constructing financial notification event request", zap.Error(err), logRequestId(req.GetRequestId()))
			return nil, internalServerErr
		}
		_, err = s.cardSwitchFinancialNotificationPublisher.PublishWithDelay(ctx, event, 3*time.Second)
		if err != nil {
			logger.Error(ctx, "error in publishing card financial notification event", zap.Error(err), logRequestId(req.GetRequestId()))
			return nil, internalServerErr
		}
		logger.Info(ctx, "financial notification published successfully")
	}
	return &emptypb.Empty{}, nil
}

// constructNonFinancialNotificationEventRequest constructs non financial notification request from vendor's notification
func constructNonFinancialNotificationEventRequest(req *vdPb.ProcessCardSwitchNotificationRequest,
	notificationType cardEnumsPb.NotificationType) (*cardNotificationPb.ProcessCardSwitchNonFinancialNotificationsRequest, error) {
	completedAt, err := parseStringToTimeStamp(req.GetTransactionExecutedTimestamp())
	if err != nil {
		return nil, fmt.Errorf("error in parsing timestamp %w", err)
	}
	event := &cardNotificationPb.ProcessCardSwitchNonFinancialNotificationsRequest{
		CompletedAt:      completedAt,
		NotificationType: notificationType,
		VendorCardId:     req.GetCardNumber(),
		RequestId:        req.GetRequestId(),
	}
	return event, nil
}

// constructFinancialNotificationEventRequest constructs financial notification request from vendor's notification
func constructFinancialNotificationEventRequest(ctx context.Context, req *vdPb.ProcessCardSwitchNotificationRequest,
	notificationType cardEnumsPb.NotificationType) (*cardNotificationPb.ProcessCardSwitchFinancialNotificationsRequest, error) {
	executedAt, err := parseStringToTimeStampV2(ctx, req.GetTransactionExecutedTimestamp(), req.GetTransactionTime())
	if err != nil {
		return nil, fmt.Errorf("error in parsing timestamp %w", err)
	}
	// check if arn is empty
	if len(req.GetArn()) == 0 {
		logger.Error(ctx, "arn is empty in financial switch notification request", logRequestId(req.GetRequestId()))
		return nil, fmt.Errorf("arn is empty in financial switch notification request")
	}

	transactionType, ok := messageTypeToTransactionTypeMap[req.GetTransactionType()]
	if !ok {
		logger.Error(ctx, "error in fetching transaction type for transaction type", zap.String(logger.TXN_TYPE, req.GetTransactionType()))
	}
	amount, err := money.ParseString(req.GetTransactionAmount(), money.RupeeCurrencyCode)
	if err != nil {
		return nil, fmt.Errorf("error in parsing amount from string %w", err)
	}
	authorisationSwitch, ok := posServiceConditionCodeToAuthorizationSwitchMap[req.GetPosServiceConditionCode()]
	if !ok {
		logger.Error(ctx, "error in fetching authorisation switch for pos service condition code", zap.String(logger.AUTH_REQUEST_ID, req.GetPosServiceConditionCode()))
	}
	cardTxnCategory := parseTransactionCategoryV2(ctx, req)

	isMarkup := parseMarkupTransaction(cardTxnCategory, req)

	isDccTxn := parseDccTransaction(cardTxnCategory, req)

	switchNotificationResponse, err := parseSwitchNotificationResponse(cardTxnCategory, req.GetTransactionResponseCode())
	if err != nil {
		logger.Error(ctx, "error in fetching switch notification txn response enum for txn response code", zap.Error(err))
		switchNotificationResponse = cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNSPECIFIED
	}

	isCardTapTxn, err := convertYNToBool(req.GetCardTap())
	if err != nil {
		logger.Error(ctx, "error in converting card_tap string value to bool", zap.Error(err))
	}

	isDeviceTapTxn, err := convertYNToBool(req.GetDeviceTap())
	if err != nil {
		logger.Error(ctx, "error in converting device_tap string value to bool", zap.Error(err))
	}

	txnAmountInOrgCurrency, err := parseOrgTxnAmount(req.GetOrgCurrencyValue(), req.GetOrgTxnCurrencyCode())
	if err != nil {
		logger.Error(ctx, "error in parsing original currency txn amount", zap.Error(err))
	}

	rawData, marshallErr := protoJson.Marshal(req)
	if marshallErr != nil {
		logger.Error(ctx, "error while marshalling the financial switch notification request", zap.Error(marshallErr))
	}

	event := &cardNotificationPb.ProcessCardSwitchFinancialNotificationsRequest{
		CardTransactionCategory: cardTxnCategory,
		RequestId:               req.GetRequestId(),
		ExecutedAt:              executedAt,
		AccountNumber:           req.GetAccountNumber(),
		Amount:                  amount,
		NotificationType:        notificationType,
		MessageType:             transactionType,
		Arn:                     req.GetArn(),
		OriginalTransactionId:   req.GetOriginalTxnId(),
		VendorCardId:            req.GetCardNumber(),
		CountryCode:             req.GetCountryCode(),
		TransactionRemarks:      req.GetTransactionRemarks(),
		MerchantName:            req.GetMerchantName(),
		MerchantId:              req.GetMerchantId(),
		PaymentGateway:          req.GetPaymentGateway(),
		SubMerchantId:           req.GetSubMerchantId(),
		TerminalId:              req.GetTerminalId(),
		AcquiringBank:           req.GetAcquiringBank(),
		Mcc:                     req.GetMcc(),
		MerchantLocation:        req.GetMerchantLocation(),
		AuthorisationSwitch:     authorisationSwitch,
		TransactionResponseCode: req.GetTransactionResponseCode(),
		// TODO(priyansh) : Add this once clarity is there from product
		TransactionEntryMode:       cardEnumsPb.TransactionEntryMode_TRANSACTION_ENTRY_MODE_UNSPECIFIED,
		AuthId:                     req.GetAuthId(),
		TransactionState:           getTransactionStatus(req.GetTransactionResponseCode()),
		SwitchNotificationResponse: switchNotificationResponse,
		RemitterCode:               req.GetRemitterDetails(),
		RemitterInstrumentType:     req.GetRemitterInstrumentType(),
		RawNotificationData:        string(rawData),
		IsForexMarkupTransaction:   isMarkup,
		TransactionTime:            executedAt,
		IsDccTransaction:           isDccTxn,
		TxnAmountInOrgCurrency:     txnAmountInOrgCurrency,
		IsCardTap:                  isCardTapTxn,
		IsDeviceTap:                isDeviceTapTxn,
		AtmPincode:                 req.GetGeocode(),
	}
	return event, nil
}

func convertYNToBool(ynString string) (bool, error) {
	switch strings.ToUpper(ynString) {
	case "Y":
		return true, nil
	case "N":
		return false, nil
	}
	return false, fmt.Errorf("invalid YN value: %s", ynString)
}

func parseSwitchNotificationResponse(txnCategory cardEnumsPb.CardTransactionCategory, txnResponseCode string) (cardEnumsPb.SwitchNotificationResponse, error) {
	var (
		switchRes cardEnumsPb.SwitchNotificationResponse
		ok        bool
	)
	switch txnCategory {
	case cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ECOMM, cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_POS,
		cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_NFC:
		switchRes, ok = posEcomTransactionResponseCodeToSwitchNotificationResponse[txnResponseCode]
	case cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL:
		switchRes, ok = atmTransactionResponseCodeToSwitchNotificationResponse[txnResponseCode]
	}
	if !ok {
		return cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNSPECIFIED, fmt.Errorf("error typecasting txn response code to switch notification response: %s", txnResponseCode)
	}
	return switchRes, nil
}

// parseStringToTimeStamp tries to parse the timestamp string in one of the timeFormats timestamp formats
func parseStringToTimeStamp(timeString string) (*timestamppb.Timestamp, error) {
	var (
		timestamp *timestamppb.Timestamp
		err       error
	)
	for i := range timeFormats {
		timestamp, err = datetime.ParseStringTimeStampProto(timeFormats[i], timeString)
		if err != nil {
			continue
		}
		return timestamp, nil
	}
	return nil, fmt.Errorf("error in parsing time string to any format %w", err)
}

func parseStringToTimeStampV2(_ context.Context, dateString, timeString string) (*timestamppb.Timestamp, error) {
	var (
		timestamp           *timestamppb.Timestamp
		fullTimestampString string
		err                 error
	)
	if len(timeString) >= 6 && len(dateString) >= 10 {
		fullTimestampString = dateString[0:10] + " " + timeString[0:2] + ":" + timeString[2:4] + ":" + timeString[4:6]
	} else {
		return nil, fmt.Errorf("received invalid timeStamp in ProcessCardSwitchNotification callback: %s", timeString)
	}

	// parse date
	for i := range timeFormats {
		timestamp, err = datetime.ParseStringTimestampProtoInLocation(timeFormats[i], fullTimestampString, datetime.IST)
		if err != nil {
			continue
		}
		return timestamp, nil
	}
	return nil, fmt.Errorf("error in parsing time string to any format %w", err)
}

// parseTransactionCategory returns the card transaction category based on multiple fields in notification request
// 1. We will check the transaction mode
// 2. If transaction mode is ECOM/POS then we will check the length of pos type because as per vendor POS type length will be 2 for POS/NFC transactions
// 3. If POS type length is 2 then we will check for the POS type mapping to NFC transactions to get the transaction category
func parseTransactionCategory(ctx context.Context, req *vdPb.ProcessCardSwitchNotificationRequest) cardEnumsPb.CardTransactionCategory {
	switch req.GetTransactionMode() {
	case "ATM":
		return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL
	case "ECOM/POS":
		if len(req.GetPosType()) == 2 {
			if lo.Contains(nfcPosTypes, req.GetPosType()) {
				return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_NFC
			}
			return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_POS
		}
		return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ECOMM
	default:
		logger.Info(ctx, "could not parse transaction category in notification", logRequestId(req.GetRequestId()))
		return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_UNSPECIFIED
	}
}

func parseMarkupTransaction(txnCategory cardEnumsPb.CardTransactionCategory, req *vdPb.ProcessCardSwitchNotificationRequest) bool {
	if txnCategory == cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_POS || txnCategory == cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ECOMM {
		return len(req.GetEcomPosMarkupTransaction()) > 0 && req.GetEcomPosMarkupTransaction()[0] == 'M'
	}

	if txnCategory == cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL {
		return len(req.GetAtmMarkupTransaction()) > 0 && req.GetAtmMarkupTransaction()[0] == 'M'
	}

	return false
}

func parseDccTransaction(txnCategory cardEnumsPb.CardTransactionCategory, req *vdPb.ProcessCardSwitchNotificationRequest) bool {
	if txnCategory == cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_POS || txnCategory == cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ECOMM {
		return len(req.GetEcomPosMarkupTransaction()) > 0 && req.GetEcomPosMarkupTransaction()[0] == 'D'
	}

	if txnCategory == cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL {
		return len(req.GetAtmMarkupTransaction()) > 0 && req.GetAtmMarkupTransaction()[0] == 'D'
	}

	return false
}

func parseTransactionCategoryV2(ctx context.Context, req *vdPb.ProcessCardSwitchNotificationRequest) cardEnumsPb.CardTransactionCategory {
	switch req.GetTransactionMode() {
	case "ATM":
		return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL
	case "ECOM/POS":
		// check if the pos type is empty
		if len(req.GetPosType()) < 1 {
			logger.Error(ctx, "posType is empty in switch notification payload", logRequestId(req.GetRequestId()))
			return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_UNSPECIFIED
		}

		switch req.GetPosType()[0] {
		// for ecom txn first digit of field is 1
		case '1':
			return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ECOMM
			// first digit is '5' is for chip based pos txn
		case '5', '2':
			return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_POS
			// first digit is '7' is for contactless pos txn
		case '7':
			return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_NFC
		default:
			// continue
		}

	default:
		logger.Info(ctx, "could not parse transaction category in notification", logRequestId(req.GetRequestId()))
		return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_UNSPECIFIED
	}

	logger.Info(ctx, "could not parse transaction category in notification", logRequestId(req.GetRequestId()))
	return cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_UNSPECIFIED
}

// sanitiseNotificationRequest removes leading and trailing spaces for all requests fields
func sanitiseNotificationRequest(req *vdPb.ProcessCardSwitchNotificationRequest) *vdPb.ProcessCardSwitchNotificationRequest {
	txnTimeString := strings.TrimSpace(req.GetTransactionTime())
	if len(txnTimeString) == 0 && len(req.GetTransationTime()) > 0 {
		txnTimeString = strings.TrimSpace(req.GetTransationTime())
	}
	return &vdPb.ProcessCardSwitchNotificationRequest{
		SenderCode:                   strings.TrimSpace(req.GetSenderCode()),
		TransactionMode:              strings.TrimSpace(req.GetTransactionMode()),
		RequestId:                    strings.TrimSpace(req.GetRequestId()),
		TransactionExecutedTimestamp: strings.TrimSpace(req.GetTransactionExecutedTimestamp()),
		AccountNumber:                strings.TrimSpace(req.GetAccountNumber()),
		TransactionAmount:            strings.TrimSpace(req.GetTransactionAmount()),
		TransactionType:              strings.TrimSpace(req.GetTransactionType()),
		TransactionCode:              strings.TrimSpace(req.GetTransactionCode()),
		Arn:                          strings.TrimSpace(req.GetArn()),
		OriginalTxnId:                strings.TrimSpace(req.GetOriginalTxnId()),
		RemitterInstrumentType:       strings.TrimSpace(req.GetRemitterInstrumentType()),
		RemitterDetails:              strings.TrimSpace(req.GetRemitterDetails()),
		CountryCode:                  strings.TrimSpace(req.GetCountryCode()),
		TransactionRemarks:           strings.TrimSpace(req.GetTransactionRemarks()),
		MerchantName:                 strings.TrimSpace(req.GetMerchantName()),
		MerchantId:                   strings.TrimSpace(req.GetMerchantId()),
		PaymentGateway:               strings.TrimSpace(req.GetPaymentGateway()),
		SubMerchantId:                strings.TrimSpace(req.GetSubMerchantId()),
		TerminalId:                   strings.TrimSpace(req.GetTerminalId()),
		AcquiringBank:                strings.TrimSpace(req.GetAcquiringBank()),
		Mcc:                          strings.TrimSpace(req.GetMcc()),
		MerchantLocation:             strings.TrimSpace(req.GetMerchantLocation()),
		PosServiceConditionCode:      strings.TrimSpace(req.GetPosServiceConditionCode()),
		TransactionResponseCode:      strings.TrimSpace(req.GetTransactionResponseCode()),
		PosType:                      strings.TrimSpace(req.GetPosType()),
		AuthId:                       strings.TrimSpace(req.GetAuthId()),
		CardNumber:                   strings.TrimSpace(req.GetCardNumber()),
		TransactionTime:              txnTimeString,
		ServiceEntryMode:             strings.TrimSpace(req.GetServiceEntryMode()),
		AtmMarkupTransaction:         strings.TrimSpace(req.GetAtmMarkupTransaction()),
		EcomPosMarkupTransaction:     strings.TrimSpace(req.GetEcomPosMarkupTransaction()),
		TransationTime:               txnTimeString,
		OrgTxnCurrencyCode:           strings.TrimSpace(req.GetOrgTxnCurrencyCode()),
		CardTap:                      strings.TrimSpace(req.GetCardTap()),
		DeviceTap:                    strings.TrimSpace(req.GetDeviceTap()),
		OrgCurrencyValue:             strings.TrimSpace(req.GetOrgCurrencyValue()),
		Geocode:                      strings.TrimSpace(req.GetGeocode()),
	}
}

// getTransactionStatus returns if the transaction is successful or failure based on the raw response codes
func getTransactionStatus(rawResponseCode string) cardEnumsPb.TransactionState {
	if lo.Contains(successResponseCodes, rawResponseCode) {
		return cardEnumsPb.TransactionState_TRANSACTION_STATE_SUCCESS
	}
	return cardEnumsPb.TransactionState_TRANSACTION_STATE_FAILURE
}

// parseOrgTxnAmount parses the original transaction amount from vendor notification into a standardized Money protobuf.
// It takes the transaction amount and numbers currency code in string format,
// converts the numbers currency code to ISO standard currency code,
// applies appropriate decimal place formatting based on the currency's decimal configuration, and returns a Money protobuf object.
//
// Parameters:
//   - orgCurrencyTxnAmountStr: The original transaction amount as a string from the vendor
//   - orgTxnCurrencyCodeInNumbers: The currency code in numeric format (e.g., "840" for USD)
//
// Returns:
//   - *moneyPb.Money: A Money protobuf object with the parsed amount and ISO currency code
//   - error: An error if currency code conversion or amount parsing fails
func parseOrgTxnAmount(orgCurrencyTxnAmountStr string, orgTxnCurrencyCodeInNumbers string) (*moneyPb.Money, error) {
	isoCurrencyCode, decimalPlace, err := vendorsPkg.GetIsoStandardCurrencyCodeConfFromNumberCode(orgTxnCurrencyCodeInNumbers)
	if err != nil {
		return nil, fmt.Errorf("error in fetching iso currency code from number: %w", err)
	}

	orgCurrencyValueFloat, convertErr := convertCurrencyValueToFloatAndApplyDecimal(orgCurrencyTxnAmountStr, decimalPlace)
	if convertErr != nil {
		return nil, fmt.Errorf("error converting currency value to float and applying decimal: %w", convertErr)
	}
	return money.ParseFloat(orgCurrencyValueFloat, isoCurrencyCode), nil
}

// convertCurrencyValueToFloatAndApplyDecimal converts a currency value string to float64 and applies
// decimal place adjustments based on the currency's decimal configuration.
//
// The decimal adjustment logic:
//   - If decimalValue == 0: Returns the value as-is (no decimal places)
//   - If decimalValue < 0: Multiplies by abs(decimalValue) * 10 (for currencies with negative decimal places)
//   - If decimalValue > 0: Divides by (10 * decimalValue) to properly position decimal places
//
// For example, if the vendor sends "12345" for $123.45 with decimalValue=2, this function
// will return 12.345 (12345 / (10 * 2) = 12345 / 20 = 617.25), which would then be
// correctly parsed as $123.45 by the money parsing logic.
//
// Parameters:
//   - currencyValue: The currency amount as a string from the vendor
//   - decimalValue: The number of decimal places for the currency (can be negative, zero, or positive)
//
// Returns:
//   - float64: The converted currency value with decimal adjustment applied
//   - error: An error if the string cannot be parsed as a float
func convertCurrencyValueToFloatAndApplyDecimal(currencyValue string, decimalValue int) (float64, error) {
	floatCurrencyValue, err := strconv.ParseFloat(currencyValue, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to convert original currency value to float: %w", err)
	}
	switch {
	case decimalValue == 0:
		return floatCurrencyValue, nil
	case decimalValue < 0:
		return floatCurrencyValue * math.Abs(float64(decimalValue)) * 10, nil
	default:
		return floatCurrencyValue / (10 * float64(decimalValue)), nil
	}
}
