package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"

	"github.com/epifi/gamma/api/vendors/responsemapping/card"

	"github.com/epifi/be-common/pkg/datetime"

	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/redactor"

	"github.com/golang/protobuf/ptypes/empty" // nolint:depguard
	"github.com/jinzhu/copier"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	cardPb "github.com/epifi/gamma/api/card"
	cardProvisioningPb "github.com/epifi/gamma/api/card/provisioning"
	vgPbApi "github.com/epifi/gamma/api/vendorgateway/openbanking/card"

	"github.com/epifi/be-common/pkg/queue"

	federalCardPb "github.com/epifi/gamma/api/vendornotification/openbanking/card/federal"
	vdPb "github.com/epifi/gamma/api/vendors/federal"
)

// maps the federal ResponseCode parameter to the internal card creation state.
// Flag error if the ResponseCode value received is not part of this map. Denotes that the error codes
// provided as part of api spec has changed. Should be addressed asap or we may miss to process callbacks.
var codeToCardCreationStatusMap = map[string]cardProvisioningPb.RequestState{
	"000":     cardProvisioningPb.RequestState_SUCCESS,
	"OBE0071": cardProvisioningPb.RequestState_FAILED, // System Failure
	"OBE0007": cardProvisioningPb.RequestState_FAILED, // Internal Failure
}

var cardTypeToCardForm = map[string]cardPb.CardForm{
	"P": cardPb.CardForm_PHYSICAL,
	"V": cardPb.CardForm_DIGITAL,
}

const (
	UpdateCardCreationRequest        = "UpdateCardCreationRequest"
	UpdateDispatchPhysicalCardStatus = "UpdateDispatchPhysicalCardStatus"
	processCardSwitchNotification    = "ProcessCardSwitchNotification"
)

// TODO(anand): check with Pruthvi why is this required to be standardised??
var internalServerErr = errors.New("internal server error")

// Service implements RPC methods that would consume card related vendor notifications from Federal Bank
type Service struct {
	federalCardPb.UnimplementedCardServer
	creationCallbackPublisher                   CreateCardCallbackPublisher
	dispatchPhysicalCardCallbackPublisher       DispatchPhysicalCardCallbackPublisher
	cardSwitchFinancialNotificationPublisher    CardSwitchFinancialNotificationPublisher
	cardSwitchNonFinancialNotificationPublisher CardSwitchNonFinancialNotificationPublisher
}

type CreateCardCallbackPublisher queue.Publisher
type DispatchPhysicalCardCallbackPublisher queue.Publisher
type CardSwitchFinancialNotificationPublisher queue.DelayPublisher
type CardSwitchNonFinancialNotificationPublisher queue.Publisher

// Factory method for creating an instance of card vendor notification service. This method will be used by the
// injector when providing the dependencies at initialization.
func NewService(creationCallbackPublisher CreateCardCallbackPublisher, dispatchPhysicalCardCallbackPublisher DispatchPhysicalCardCallbackPublisher,
	cardSwitchFinancialNotificationPublisher CardSwitchFinancialNotificationPublisher, cardSwitchNonFinancialNotificationPublisher CardSwitchNonFinancialNotificationPublisher) *Service {
	return &Service{
		creationCallbackPublisher:                   creationCallbackPublisher,
		dispatchPhysicalCardCallbackPublisher:       dispatchPhysicalCardCallbackPublisher,
		cardSwitchFinancialNotificationPublisher:    cardSwitchFinancialNotificationPublisher,
		cardSwitchNonFinancialNotificationPublisher: cardSwitchNonFinancialNotificationPublisher,
	}
}

// RPC processes the card creation callback posted by Federal on the given endpoint.
// The received message must be processed as minimal as possible due to avoid message loss due to internal processing
// errors. An event is published to a queue which can be consumed by Card backend service. The event can be retried
// using the same queue fixed number of times.
func (s *Service) UpdateCardCreation(ctx context.Context, req *vdPb.UpdateCardCreationRequest) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, UpdateCardCreationRequest, req.GetRequestId(), vendorsRedactor.Config)

	event := &cardProvisioningPb.ProcessCardCreationCallBackRequest{PartnerBank: commonvgpb.Vendor_FEDERAL_BANK}

	logger.Debug(ctx, fmt.Sprintf("Received card creation callback from FEDERAL: id: %v, response code: %v, response desc: %v",
		req.RequestId, req.ResponseCode, req.ResponseDesc))
	if err := constructEventRequest(ctx, req, event); err != nil {
		return nil, internalServerErr
	}

	// publish the packet to the queue to be consumed by the card BE service. The retries are also handled using the
	// same queue.
	if _, err := s.creationCallbackPublisher.Publish(ctx, event); err != nil {
		logger.Error(ctx, fmt.Sprintf("Unable to publish creation call back to the queue %v", event), zap.Error(err))
		return nil, internalServerErr
	}
	return &empty.Empty{}, nil
}

// Constructs the queue event using the callback request received from the vendor. Any vendor specific attributes to
// what Epifi Card server can understand.
// For example: convert the Response code to Card creation request state using codeToCardCreationStatusMap
//
//	convert the card type to Card Form using cardTypeToCardForm mapping
//
// Copy the remaining fields.
//
// nolint:funlen
func constructEventRequest(ctx context.Context, req *vdPb.UpdateCardCreationRequest, event *cardProvisioningPb.ProcessCardCreationCallBackRequest) error {
	// copy all the other fields with same name
	if copierErr := copier.Copy(event, req); copierErr != nil {
		logger.Error(ctx, fmt.Sprintf("Error in Copy from %T to %T. Err: %v", req, event, copierErr))
		return internalServerErr
	}

	// convert raw response code to internal status code
	responseInternalStatus := card.GetCardResponseInternalStatus(commonvgpb.Vendor_FEDERAL_BANK,
		vgPbApi.ApiType_CARD_CREATION_ENQUIRY, req.GetResponseCode())

	// set the card creation state
	if state, ok := codeToCardCreationStatusMap[req.GetResponseCode()]; ok {
		event.State = state
		if state == cardProvisioningPb.RequestState_FAILED {
			var errors string
			if len(req.GetErrors()) != 0 {
				for _, error := range req.GetErrors() {
					errors += " " + error.GetReason()
				}
				logger.Error(ctx, fmt.Sprintf("error in card creation callback %v", errors))
			}
			logger.Error(ctx, "received failure state for card creation response",
				zap.String(logger.STATUS_CODE, req.GetResponseCode()))
			event.FailureResponseCode = responseInternalStatus.StatusCode
			event.FailureResponseReason = errors
			return nil
		}
	} else {
		// This should not happen. Indicates: changes in card specs without prior communication or some error on Federal
		// end. Must be followed up with federal immediately to resolve this.
		logger.Error(ctx, "received unknown response code from federal in card creation callback",
			zap.String(" request-id ", req.RequestId), zap.String("rsp code: ", req.GetResponseCode()))
		return internalServerErr
	}

	tokenExpiryTime, err := datetime.ParseStringTimestampProtoInLocation(vdPb.ExpiryTimeStampFormat,
		req.GetTokenValidTill(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "could not parse set otp expiry time string to timestamp",
			zap.String(logger.REQUEST_ID, req.RequestId), zap.Error(err))
		return internalServerErr
	}
	event.TokenExpireAt = tokenExpiryTime
	event.PinSetToken = req.GetPinSetToken()

	event.CardInfo = &cardPb.BasicCardInfo{
		MaskedCardNumber: req.GetBasicCardInfo().GetMaskedCardNumber(),
		CardNumber:       req.GetBasicCardInfo().GetCardNumber(),
		Expiry:           req.GetBasicCardInfo().GetExpiry(),
		Cvv:              "***",
		CustomerName:     req.GetEmbossName(),
	}

	// set the card form
	if form, ok := cardTypeToCardForm[req.GetCardType()]; ok {
		event.Form = form
	} else {
		logger.Error(ctx, "received unknown card form from federal in card creation callback",
			zap.String(" request-id ", req.RequestId), zap.String("card form: ", req.GetCardType()))
		return internalServerErr
	}
	return nil
}
