package federal_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	pb "github.com/epifi/gamma/api/card/provisioning"
	vdPb "github.com/epifi/gamma/api/vendors/federal"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"
	"github.com/epifi/gamma/vendornotification/openbanking/card/federal"
	"github.com/epifi/gamma/vendornotification/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	_, teardown := test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_UpdateCardCreation(t *testing.T) {
	var (
		req       *vdPb.UpdateCardCreationRequest
		requestId = "dummy-id"
		ctrl      = gomock.NewController(t)
		m         = mock_queue.NewMockPublisher(ctrl)
		s         = federal.NewService(m, nil, nil, nil)
		ctx       = context.Background()
		event     = &pb.ProcessCardCreationCallBackRequest{PartnerBank: commonvgpb.Vendor_FEDERAL_BANK}
	)
	defer ctrl.Finish()

	t.Run("unknown-response-code", func(t *testing.T) {
		req = &vdPb.UpdateCardCreationRequest{
			RequestId:      requestId,
			ResponseCode:   "unknown",
			TokenValidTill: "2006-01-02 15:04:05.999999",
		}
		got, err := s.UpdateCardCreation(ctx, req)
		assert.NotNil(t, err)
		assert.Nil(t, got)
	})

	t.Run("unknown-card-form", func(t *testing.T) {
		req = &vdPb.UpdateCardCreationRequest{
			RequestId:      requestId,
			ResponseCode:   "000",
			CardType:       "X",
			TokenValidTill: "2006-01-02 15:04:05.999999",
		}
		got, err := s.UpdateCardCreation(ctx, req)
		assert.NotNil(t, err)
		assert.Nil(t, got)
	})

	t.Run("failure due to invalid token expiry time", func(t *testing.T) {
		req = &vdPb.UpdateCardCreationRequest{
			RequestId:      requestId,
			ResponseCode:   "000",
			CardType:       "P",
			TokenValidTill: "14-10-2020 15:04:05.999999",
		}
		got, err := s.UpdateCardCreation(ctx, req)
		assert.Nil(t, got)
		assert.NotNil(t, err)
	})

	t.Run("publish-failure", func(t *testing.T) {
		m.EXPECT().Publish(ctx, gomock.AssignableToTypeOf(event)).
			Return("msg-id", errors.New("publish-error"))
		req = &vdPb.UpdateCardCreationRequest{
			RequestId:      requestId,
			ResponseCode:   "000",
			CardType:       "P",
			TokenValidTill: "2006-01-02 15:04:05.999999",
		}
		got, err := s.UpdateCardCreation(ctx, req)
		assert.NotNil(t, err)
		assert.Nil(t, got)
	})

	t.Run("success", func(t *testing.T) {
		m.EXPECT().Publish(ctx, gomock.AssignableToTypeOf(event)).Return("msg-id", nil)
		req = &vdPb.UpdateCardCreationRequest{
			RequestId:      requestId,
			ResponseCode:   "000",
			CardType:       "P",
			TokenValidTill: "2006-01-02 15:04:05.999999",
		}
		got, err := s.UpdateCardCreation(ctx, req)
		assert.NotNil(t, got)
		assert.Nil(t, err)
	})
}
