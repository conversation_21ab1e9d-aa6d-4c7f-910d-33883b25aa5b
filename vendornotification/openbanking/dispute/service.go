package dispute

import (
	"context"

	dPb "github.com/epifi/gamma/api/cx/dispute"
	vgPb "github.com/epifi/gamma/api/vendornotification/openbanking/dispute"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
)

type Service struct {
	disputeClient dPb.DisputeClient
}

func NewService(disputeClient dPb.DisputeClient) *Service {
	return &Service{
		disputeClient: disputeClient,
	}
}

// status code mapping is according to
// REFRENCE: https://docs.google.com/document/d/1x0qoKQo16u02-5hoZAXTeGfJMGT7eZw6hcHv6YVivKY

var statusCodeToStringMapping = map[vgPb.StatusCode]string{
	vgPb.StatusCode_STATUS_CODE_SUCCESS:                       "100",
	vgPb.StatusCode_STATUS_CODE_INTERNAL_ERROR:                "101",
	vgPb.StatusCode_STATUS_CODE_DISPUTE_CASE_NUMBER_NOT_FOUND: "102",
	vgPb.StatusCode_STATUS_CODE_VALIDATION_FAIL:               "103",
}

func (s *Service) AttachCorrespondence(ctx context.Context, request *vgPb.AttachCorrespondenceRequest) (*vgPb.AttachCorrespondenceResponse, error) {
	// mandatory parameter check
	if request.GetDisputeCaseNumber() == "" || request.GetCorrespondenceText() == "" {
		logger.Info(ctx, "dispute case number and correspondence text are mandatory",
			zap.String("disputeCaseNumber", request.GetDisputeCaseNumber()),
			zap.String("correspondenceText", request.GetCorrespondenceText()),
		)
		return &vgPb.AttachCorrespondenceResponse{
			StatusCode: statusCodeToStringMapping[vgPb.StatusCode_STATUS_CODE_VALIDATION_FAIL],
		}, nil
	}
	// attach correspondence
	attachCorrespondenceResp, err := s.disputeClient.AttachCorrespondence(ctx, &dPb.AttachCorrespondenceRequest{
		Organisation:       dPb.Organisation_ORGANISATION_FEDERAL_BANK,
		CorrespondenceText: request.GetCorrespondenceText(),
		DisputeCaseNumber:  request.GetDisputeCaseNumber(),
		AgentEmail:         request.GetAgentEmail(),
	})
	if te := epifigrpc.RPCError(attachCorrespondenceResp, err); te != nil {
		logger.Error(ctx, "failed to attach correspondence", zap.Error(te))
		return &vgPb.AttachCorrespondenceResponse{
			StatusCode: statusCodeToStringMapping[vgPb.StatusCode_STATUS_CODE_INTERNAL_ERROR],
		}, nil
	}
	return &vgPb.AttachCorrespondenceResponse{
		StatusCode: statusCodeToStringMapping[vgPb.StatusCode_STATUS_CODE_SUCCESS],
	}, nil
}
