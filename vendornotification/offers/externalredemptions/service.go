package externalredemptions

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"

	"google.golang.org/grpc/codes"

	"github.com/gogo/status"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/crypto/aes"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	"github.com/epifi/gamma/api/user"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	offersVnPb "github.com/epifi/gamma/api/vendornotification/offers/externalredemptions"
	"github.com/epifi/gamma/api/vendors/fistore"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/security"
)

type Service struct {
	conf                           *genconf.Config
	vendorMappingClient            vendormappingPb.VendorMappingServiceClient
	externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient
	userClient                     user.UsersClient
}

func NewService(conf *genconf.Config, vendorMappingClient vendormappingPb.VendorMappingServiceClient, externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient, userClient user.UsersClient) *Service {
	return &Service{
		conf:                           conf,
		vendorMappingClient:            vendorMappingClient,
		externalVendorRedemptionClient: externalVendorRedemptionClient,
		userClient:                     userClient,
	}
}

var _ offersVnPb.ExternalRedemptionsServer = &Service{}

// CreateFiStoreRedemption creates fi store order redemption details record.
// Any update in this service and its dependencies, needs a corresponding update in scripts/dpanda_redemptions_sftp_recon
func (s *Service) CreateFiStoreRedemption(ctx context.Context, req *fistore.FiStoreRedemptionRequest) (*fistore.FiStoreRedemptionResponse, error) {
	err1 := security.CheckWhiteList(ctx, s.conf.DPandaWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	err2 := security.CheckWhiteList(ctx, s.conf.PoshVineWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	if err1 != nil && err2 != nil {
		return nil, status.Error(codes.PermissionDenied, "Access forbidden")
	}

	vendor, err := s.validateVendor(ctx, req.GetVendor())
	if err != nil {
		logger.Error(ctx, "vendor missing in request", zap.String("vendor", req.GetVendor()))
		return nil, status.Errorf(codes.InvalidArgument, "vendor missing in request")
	}

	payload, err := s.getDecryptedCreateReqPayload(ctx, vendor, req.GetData())
	if err != nil {
		logger.Error(ctx, "error in decrypting create payload", zap.String("vendor", req.GetVendor()), zap.String("encyptedPayload", req.GetData()), zap.Error(err))
		return nil, status.Errorf(codes.Internal, "error in decrypting create payload")
	}

	err = s.validateCreateFiStoreRedemptionPayload(ctx, payload)
	if err != nil {
		logger.Error(ctx, "error while validating payload", zap.String("payload", payload.String()), zap.Error(err))
		return nil, err
	}
	createFiStoreRedemptionsRequest, err := s.buildCreateFiStoreRedemptionsRequest(ctx, payload)
	if err != nil {
		logger.Error(ctx, "error while building request", zap.String("payload", payload.String()), zap.Error(err))
		return nil, err
	}
	actorId, err := s.getActorIdFromVendorId(ctx, vendor, payload.GetUserId())
	if err != nil {
		logger.Error(ctx, "error while fetching actor id from vendor's id", zap.String(logger.VENDOR, vendor.String()), zap.String(logger.USER_ID, payload.GetUserId()), zap.String(logger.VENDOR_ORDER_ID, createFiStoreRedemptionsRequest.GetVendorRefId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, status.Errorf(codes.NotFound, "user not found")
		}
		return nil, status.Errorf(codes.Internal, "error while fetching user details")
	}
	createFiStoreRedemptionsRequest.ActorId = actorId
	createFiStoreRedemptionsRequest.Vendor = vendor

	resp, err := s.externalVendorRedemptionClient.CreateFiStoreRedemptions(ctx, createFiStoreRedemptionsRequest)
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error while create fi store redemption details", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.VENDOR_ORDER_ID, createFiStoreRedemptionsRequest.GetVendorRefId()), zap.Error(rpcErr))
		return nil, status.Errorf(codes.Internal, "error while create fi store redemption details")
	}

	return &fistore.FiStoreRedemptionResponse{
		Status: rpc.StatusOk().GetShortMessage(),
	}, nil
}

// UpdateFiStoreRedemption updates fi store order redemption details record.
// Any update in this service and its dependencies, needs a corresponding update in scripts/dpanda_redemptions_sftp_recon
func (s *Service) UpdateFiStoreRedemption(ctx context.Context, req *fistore.FiStoreRedemptionRequest) (*fistore.FiStoreRedemptionResponse, error) {
	err1 := security.CheckWhiteList(ctx, s.conf.DPandaWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	err2 := security.CheckWhiteList(ctx, s.conf.PoshVineWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	if err1 != nil && err2 != nil {
		return nil, status.Error(codes.PermissionDenied, "Access forbidden")
	}

	vendor, err := s.validateVendor(ctx, req.GetVendor())
	if err != nil {
		logger.Error(ctx, "vendor missing in request", zap.String("vendor", req.GetVendor()))
		return nil, status.Errorf(codes.InvalidArgument, "vendor missing in request")
	}

	payload, err := s.getDecryptedUpdateReqPayload(ctx, vendor, req.GetData())
	if err != nil {
		logger.Error(ctx, "error in decrypting update payload", zap.String("vendor", req.GetVendor()), zap.String("encyptedPayload", req.GetData()), zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "error in decrypting update payload")
	}

	err = s.validateUpdateFiStoreRedemptionPayload(ctx, payload)
	if err != nil {
		logger.Error(ctx, "error while validating update payload request", zap.String("payload", payload.String()), zap.Error(err))
		return nil, err
	}
	updateFiStoreRedemptionsRequest, err := s.buildUpdateFiStoreRedemptionsRequest(ctx, payload)
	if err != nil {
		logger.Error(ctx, "error while building update payload request", zap.String("payload", payload.String()), zap.Error(err))
		return nil, err
	}
	updateFiStoreRedemptionsRequest.Vendor = vendor
	updateFiStoreRedemptionsRequest.LastUpdatedBy = evrPb.Source_SOURCE_VENDOR
	resp, err := s.externalVendorRedemptionClient.UpdateFiStoreRedemptions(ctx, updateFiStoreRedemptionsRequest)
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error while update fi store redemption details", zap.String("vendorRefId", payload.GetVendorRefId()), zap.String("vendor", vendor.String()), zap.Error(rpcErr))
		if resp.GetStatus().IsRecordNotFound() {
			return &fistore.FiStoreRedemptionResponse{
				Status: rpc.StatusRecordNotFound().GetShortMessage(),
			}, nil
		}
		return nil, status.Errorf(codes.Internal, "error while update fi store redemption details")
	}
	return &fistore.FiStoreRedemptionResponse{
		Status: rpc.StatusOk().GetShortMessage(),
	}, nil

}

// getActorIdFromVendorId get actor id from given vendor's id
func (s *Service) getActorIdFromVendorId(ctx context.Context, vendor evrPb.Vendor, userId string) (string, error) {
	// if user id is empty return empty id, validation should be taken care by caller.
	if userId == "" {
		return "", nil
	}
	vgVendor := s.getVendorGatewayMappingEnum(ctx, vendor)
	actorIdResp, err := s.vendorMappingClient.GetInputIdByVendor(ctx, &vendormappingPb.GetInputIdByVendorRequest{
		Id:     userId,
		Vendor: vgVendor,
	})
	if rpcErr := epifigrpc.RPCError(actorIdResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching ActorId mapped to vendor user ID", zap.String(logger.USER_ID, userId), zap.Error(rpcErr))
		return "", fmt.Errorf("error while fetching ActorId mapped to vendor user ID, err: %w", rpcErr)
	}
	return actorIdResp.GetInputId(), nil
}

// validateFiStoreRedemptionPayload validates redemption payload
func (s *Service) validateCreateFiStoreRedemptionPayload(ctx context.Context, payload *fistore.CreateFiStoreRedemptionPayload) error {
	if payload.GetUserId() == "" {
		return status.Errorf(codes.InvalidArgument, "user id shouldn't be empty")
	}
	if payload.GetVendorRefId() == "" {
		return status.Errorf(codes.InvalidArgument, "vendor ref id shouldn't be empty")
	}
	if len(payload.GetRedemptionDetails()) == 0 {
		return status.Errorf(codes.InvalidArgument, "redemption details shouldn't be empty")
	}
	_, err := s.getFiStoreRedemptionCategory(ctx, payload.GetCategory())
	if err != nil {
		return status.Errorf(codes.InvalidArgument, "invalid category in request")
	}
	_, err = time.Parse(time.RFC3339, payload.GetOrderTimestamp())
	if err != nil {
		return status.Errorf(codes.InvalidArgument, "error in parsing order timestamp from string to timestamp, please check input : %v", err)
	}
	for _, product := range payload.GetRedemptionDetails() {
		if product.GetProductId() == "" {
			return status.Errorf(codes.InvalidArgument, "product id shouldn't be empty")
		}
		_, parseErr := s.getOrderStatus(ctx, product.GetOrderStatus())
		if parseErr != nil {
			return status.Errorf(codes.InvalidArgument, "unsupported order status provided, err: %v", parseErr)
		}
	}
	return nil
}

// buildCreateFiStoreRedemptionsRequest builds create request
func (s *Service) buildCreateFiStoreRedemptionsRequest(ctx context.Context, payload *fistore.CreateFiStoreRedemptionPayload) (*evrPb.CreateFiStoreRedemptionsRequest, error) {
	category, err := s.getFiStoreRedemptionCategory(ctx, payload.GetCategory())
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid category in request")
	}
	orderTimestamp, err := time.Parse(time.RFC3339, payload.GetOrderTimestamp())
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "error in parsing order timestamp from string to timestamp, please check input : %v", err)
	}
	var redemptionDetails []*evrPb.CreateFiStoreRedemptionsRequest_RedemptionDetails
	for _, product := range payload.GetRedemptionDetails() {
		if product.GetProductId() == "" {
			return nil, status.Errorf(codes.InvalidArgument, "product id shouldn't be empty")
		}
		orderStatus, parseErr := s.getOrderStatus(ctx, product.GetOrderStatus())
		if parseErr != nil {
			return nil, status.Errorf(codes.InvalidArgument, "unsupported order status provided, err: %v", parseErr)
		}
		redemptionDetails = append(redemptionDetails, &evrPb.CreateFiStoreRedemptionsRequest_RedemptionDetails{
			ProductId:                        product.GetProductId(),
			ProductName:                      product.GetProductName(),
			ProductPrice:                     money.ParseFloat(product.GetProductPrice(), "INR"),
			SpentCashUnits:                   money.ParseFloat(product.GetSpentCashUnits(), "INR"),
			SpentFiCoinUnits:                 product.GetSpentFiCoinUnits(),
			BrandName:                        product.GetBrandName(),
			SubCategory:                      product.GetSubCategory(),
			OrderStatus:                      orderStatus,
			OrderTrackingLink:                product.GetOrderTrackingLink(),
			Quantity:                         product.GetQuantity(),
			SpentFiCoinUnitsEquivalentToCash: money.ParseFloat(product.GetSpentFiCoinUnitsEquivalentToCash(), "INR"),
			DiscountPrice:                    money.ParseFloat(product.GetDiscountPrice(), "INR"),
			LastUpdatedBy:                    evrPb.Source_SOURCE_VENDOR,
		})
	}
	return &evrPb.CreateFiStoreRedemptionsRequest{
		VendorRefId:                 payload.GetVendorRefId(),
		PaymentInstrumentIdentifier: payload.GetPaymentInstrumentIdentifier(),
		Category:                    category,
		OrderTimestamp:              timestamp.New(orderTimestamp),
		RedemptionDetails:           redemptionDetails,
	}, nil
}

// validateUpdateFiStoreRedemptionPayload validates update redemption details payload.
func (s *Service) validateUpdateFiStoreRedemptionPayload(ctx context.Context, payload *fistore.UpdateFiStoreRedemptionPayload) error {
	if payload.GetVendorRefId() == "" {
		return status.Errorf(codes.InvalidArgument, "vendor ref id shouldn't be empty")
	}
	if payload.GetProductId() == "" {
		return status.Errorf(codes.InvalidArgument, "product id shouldn't be empty")
	}
	_, parseErr := s.getOrderStatus(ctx, payload.GetOrderStatus())
	if parseErr != nil {
		return status.Errorf(codes.InvalidArgument, "unsupported order status provided, err: %v", parseErr)
	}
	return nil
}

// buildUpdateFiStoreRedemptionsRequest builds update request
func (s *Service) buildUpdateFiStoreRedemptionsRequest(ctx context.Context, payload *fistore.UpdateFiStoreRedemptionPayload) (*evrPb.UpdateFiStoreRedemptionsRequest, error) {
	orderStatus, parseErr := s.getOrderStatus(ctx, payload.GetOrderStatus())
	if parseErr != nil {
		return nil, status.Errorf(codes.InvalidArgument, "unsupported order status provided, err: %v", parseErr)
	}
	return &evrPb.UpdateFiStoreRedemptionsRequest{
		VendorRefId:       payload.GetVendorRefId(),
		ProductId:         payload.GetProductId(),
		OrderStatus:       orderStatus,
		OrderTrackingLink: payload.GetOrderTrackingLink(),
	}, nil
}

// getVendorGatewayMappingEnum returns vendor gateway vendor
func (s *Service) getVendorGatewayMappingEnum(ctx context.Context, vendor evrPb.Vendor) commonvgpb.Vendor {
	switch vendor {
	case evrPb.Vendor_DPANDA:
		return commonvgpb.Vendor_DPANDA
	case evrPb.Vendor_POSHVINE:
		return commonvgpb.Vendor_POSHVINE
	case evrPb.Vendor_RAZORPAY:
		return commonvgpb.Vendor_RAZORPAY
	default:
		logger.Error(ctx, "unsupported vendor", zap.String("vendor", vendor.String()))
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED
	}
}

// validateVendor validate vendor provider
func (s *Service) validateVendor(ctx context.Context, vendor string) (evrPb.Vendor, error) {
	switch strings.ToUpper(vendor) {
	case evrPb.Vendor_DPANDA.String():
		return evrPb.Vendor_DPANDA, nil
	case evrPb.Vendor_POSHVINE.String():
		return evrPb.Vendor_POSHVINE, nil
	case evrPb.Vendor_RAZORPAY.String():
		return evrPb.Vendor_RAZORPAY, nil
	default:
		logger.Error(ctx, "unsupported vendor provided", zap.String("vendor", vendor))
		return evrPb.Vendor_VENDOR_UNSPECIFIED, fmt.Errorf("unsupported vendor provided, vendor: %s", vendor)
	}
}

// getDecryptedCreateReqPayload returns decrypted redemption payload
// nolint: dupl
func (s *Service) getDecryptedCreateReqPayload(ctx context.Context, vendor evrPb.Vendor, payload string) (*fistore.CreateFiStoreRedemptionPayload, error) {
	cipherText, err := s.getDecryptedCipherText(ctx, vendor, payload)
	if err != nil {
		logger.Error(ctx, "error while decrypting payload", zap.Error(err))
		return nil, err
	}
	redemptionPayload := &fistore.CreateFiStoreRedemptionPayload{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(cipherText, redemptionPayload)
	if err != nil {
		return nil, fmt.Errorf("Could not unmarshal create fi store redemption payload: %v err: %v", cipherText, err)
	}
	return redemptionPayload, nil
}

// getDecryptedUpdateReqPayload returns decrypted redemption payload
// nolint: dupl
func (s *Service) getDecryptedUpdateReqPayload(ctx context.Context, vendor evrPb.Vendor, payload string) (*fistore.UpdateFiStoreRedemptionPayload, error) {
	cipherText, err := s.getDecryptedCipherText(ctx, vendor, payload)
	if err != nil {
		logger.Error(ctx, "error while decrypting payload", zap.Error(err))
		return nil, err
	}
	redemptionPayload := &fistore.UpdateFiStoreRedemptionPayload{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(cipherText, redemptionPayload)
	if err != nil {
		return nil, fmt.Errorf("Could not unmarshal update fi store redemption payload: %v err: %v", cipherText, err)
	}
	return redemptionPayload, nil
}

// getDecryptedCipherText decrypts vendor payload and returns decrypted bytes.
func (s *Service) getDecryptedCipherText(ctx context.Context, vendor evrPb.Vendor, payload string) ([]byte, error) {
	decryptedPayload, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to base64.Decode encrypted string: %w", err)
	}
	vendorSecrets := s.getVendorPayloadDecryptSecrets(vendor)
	if vendorSecrets == nil {
		return nil, fmt.Errorf("vendor secrets should not be nil")
	}
	hexEncodedKey := hex.EncodeToString([]byte(vendorSecrets.SecretKey))
	aesCbcCryptor := aes.NewAesCbcCryptor(hexEncodedKey)
	cipherText, err := aesCbcCryptor.Decrypt(ctx, decryptedPayload, vendorSecrets.Iv)
	if err != nil {
		return nil, err
	}
	return cipherText, nil
}

// getVendorPayloadDecryptSecrets returns vendor's payload decrypt secrets
func (s *Service) getVendorPayloadDecryptSecrets(vendor evrPb.Vendor) *config.FiStoreVendorSecrets {
	switch vendor {
	case evrPb.Vendor_DPANDA:
		return s.conf.DpandaVnSecrets()
	case evrPb.Vendor_POSHVINE:
		return s.conf.PoshvineVnSecrets()
	case evrPb.Vendor_RAZORPAY:
		return s.conf.RazorpayVnSecrets()
	default:
		return nil
	}
}

// getFiStoreRedemptionCategory returns category enum from string.
// nolint: dupl
func (s *Service) getFiStoreRedemptionCategory(ctx context.Context, category string) (evrPb.Category, error) {
	switch "CATEGORY_" + strings.ToUpper(category) {
	case evrPb.Category_CATEGORY_ECOM.String():
		return evrPb.Category_CATEGORY_ECOM, nil
	case evrPb.Category_CATEGORY_GIFT_CARDS.String():
		return evrPb.Category_CATEGORY_GIFT_CARDS, nil
	case evrPb.Category_CATEGORY_FLIGHTS.String():
		return evrPb.Category_CATEGORY_FLIGHTS, nil
	case evrPb.Category_CATEGORY_HOTELS.String():
		return evrPb.Category_CATEGORY_HOTELS, nil
	case evrPb.Category_CATEGORY_MILES_EXCHANGE.String():
		return evrPb.Category_CATEGORY_MILES_EXCHANGE, nil
	default:
		logger.Error(ctx, "unsupported category provided", zap.String("category", category))
		return evrPb.Category_CATEGORY_UNSPECIFIED, fmt.Errorf("unsupported category provided, category: %s", category)
	}
}

// getOrderStatus returns order status enum from string
// NOTE: for any new order status addition, add in `scripts/scheduled/dpanda_redemptions_sftp_recon/helper.go` file also.
// nolint: dupl
func (s *Service) getOrderStatus(ctx context.Context, orderStatus string) (evrPb.OrderStatus, error) {
	switch "ORDER_STATUS_" + strings.ToUpper(orderStatus) {
	case evrPb.OrderStatus_ORDER_STATUS_CREATED.String():
		return evrPb.OrderStatus_ORDER_STATUS_CREATED, nil
	case evrPb.OrderStatus_ORDER_STATUS_PARTIALLY_CONFIRMED.String():
		return evrPb.OrderStatus_ORDER_STATUS_PARTIALLY_CONFIRMED, nil
	case evrPb.OrderStatus_ORDER_STATUS_CONFIRMED.String():
		return evrPb.OrderStatus_ORDER_STATUS_CONFIRMED, nil
	case evrPb.OrderStatus_ORDER_STATUS_SHIPPED.String():
		return evrPb.OrderStatus_ORDER_STATUS_SHIPPED, nil
	case evrPb.OrderStatus_ORDER_STATUS_DELIVERED.String():
		return evrPb.OrderStatus_ORDER_STATUS_DELIVERED, nil
	case evrPb.OrderStatus_ORDER_STATUS_CANCELLED.String():
		return evrPb.OrderStatus_ORDER_STATUS_CANCELLED, nil
	case evrPb.OrderStatus_ORDER_STATUS_ABANDONED.String():
		return evrPb.OrderStatus_ORDER_STATUS_ABANDONED, nil
	case evrPb.OrderStatus_ORDER_STATUS_PROCESSING.String():
		return evrPb.OrderStatus_ORDER_STATUS_PROCESSING, nil
	case evrPb.OrderStatus_ORDER_STATUS_FAILED.String():
		return evrPb.OrderStatus_ORDER_STATUS_FAILED, nil
	case evrPb.OrderStatus_ORDER_STATUS_OUT_FOR_DELIVERY.String():
		return evrPb.OrderStatus_ORDER_STATUS_OUT_FOR_DELIVERY, nil
	case evrPb.OrderStatus_ORDER_STATUS_RTO_INITIATED.String():
		return evrPb.OrderStatus_ORDER_STATUS_RTO_INITIATED, nil
	case evrPb.OrderStatus_ORDER_STATUS_RTO_COMPLETED.String():
		return evrPb.OrderStatus_ORDER_STATUS_RTO_COMPLETED, nil
	case evrPb.OrderStatus_ORDER_STATUS_RETURN_INITIATED.String():
		return evrPb.OrderStatus_ORDER_STATUS_RETURN_INITIATED, nil
	case evrPb.OrderStatus_ORDER_STATUS_RETURNED.String():
		return evrPb.OrderStatus_ORDER_STATUS_RETURNED, nil
	default:
		logger.Error(ctx, "unsupported order status provided", zap.String("orderStatus", orderStatus))
		return evrPb.OrderStatus_ORDER_STATUS_UNSPECIFIED, fmt.Errorf("unsupported order status provided, order status: %s", orderStatus)
	}
}
