package paisabazaar

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/firefly"
	enums2 "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/vendornotification/creditcard/paisabazaar"
	"github.com/epifi/gamma/api/vendors/paisabazaar/creditcard"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/security"
)

const (
	statusCodePermissionDenied   = "FI07"
	statusCodeInternal           = "FI13"
	statusCodeFailedPrecondition = "FI09"
	statusCodeInvalidArgument    = "FI03"
)

type Service struct {
	paisabazaar.UnimplementedPaisabazaarCallbackServer
	ffClient firefly.FireflyClient
	conf     config.Config
}

func NewService(ffClient firefly.FireflyClient) *Service {
	return &Service{
		ffClient: ffClient,
	}
}

var (
	cardRequestStageStatusToStatusMap = map[enums2.CardRequestStatus]string{
		enums2.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:            "ONBOARDING_FAILED",
		enums2.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS:       "ONBOARDING_IN_PROGRESS",
		enums2.CardRequestStatus_CARD_REQUEST_STATUS_CREATED:           "ONBOARDING_IN_PROGRESS",
		enums2.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:           "ONBOARDING_COMPLETE",
		enums2.CardRequestStatus_CARD_REQUEST_STATUS_PERMANENT_FAILURE: "ONBOARDING_FAILED",
	}

	cardRequestStageNameMap = map[enums2.CardRequestStageName]string{
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PAN_DOB_COLLECTION:            "PAN_DOB_COLLECTION",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_IN_HOUSE_BRE_CHECK:            "INHOUSE_BRE_CHECK",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_EMPLOYMENT_DETAILS_COLLECTION: "EMPLOYEMENT_DETAILS_COLLECTION",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_EKYC:                          "EKYC",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_BANK_DEDUPE_CHECK:             "BANK_DEDUPE_CHECK",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UPDATE_CUSTOMER_DETAILS:       "UPDATE_CUSTOMER_DETAILS",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PAN_NAME_CHECK:                "PAN_NAME_CHECK",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_KYC_NAME_DOB_VALIDATION:       "KYC_NAME_DOB_VALIDATION",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CHECK_VENDOR_REALTIME_OFFER:   "BANK_BRE_CHECK",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PARENTS_NAME_COLLECTION:       "PARENTS_NAME_COLLECTION",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_AUTH:                          "LIVENESS",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_KYC_DEDUPE_CHECK:              "KYC_DEDUPE_CHECK",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_RISK_CHECK:                    "RISK_CHECK",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_ACTIVE_PRODUCTS_CHECK:         "ACTIVE_PRODUCTS_CHECK",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_DEVICE_REGISTRATION:           "DEVICE_REGISTRATION",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PRE_CUSTOMER_CREATION_CHECK:   "CUSTOMER_CREATION",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CARD_CREATION:                 "CARD_CREATION",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_SUBMIT_VKYC:                   "VKYC",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_VKYC:                          "VKYC",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_REQUEST_PHYSICAL_CARD:         "PHYSICAL_CARD_REQUEST",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_SET_BILLING_DAYS_AT_VENDOR:    "SET_BILLING_DAYS",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PRE_CIF_CREATION_CHECK:        "PRE_CUSTOMER_CERATION",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UN_NAME_CHECK:                 "UN_NAME_CHECK",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_ADDRESS_CAPTURE:               "ADDRESS_CAPTURE",
		enums2.CardRequestStageName_CARD_REQUEST_STAGE_NAME_FETCH_CREDIT_REPORT:           "FETCH_CREDIT_REPORT",
	}

	cardRequestStageStatusMap = map[enums2.CardRequestStageStatus]string{
		enums2.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS:             "STAGE_STATUS_SUCCESSFUL",
		enums2.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_IN_PROGRESS:         "STAGE_STATUS_IN_PROGRESS",
		enums2.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED:              "STAGE_STATUS_FAILED",
		enums2.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_MANUAL_INTERVENTION: "STAGE_STATUS_FAILED",
		enums2.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED:             "STAGE_STATUS_IN_PROGRESS",
	}
)

func (s *Service) GetCreditCardOnboardingStatus(ctx context.Context, req *creditcard.GetCreditCardOnboardingStatusRequest) (*creditcard.GetCreditCardOnboardingStatusResponse, error) {
	var (
		actorId            string
		overallOnbStatus   string
		currOnbStage       string
		currOnbStageStatus string
		currCrStageResp    *firefly.GetCardRequestAndCardRequestStageResponse
		onbStageHistory    []*creditcard.OnboardingStageDetails
	)
	if err := security.CheckWhiteList(ctx, s.conf.PaisabazaarWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return getResponseWithException(statusCodePermissionDenied, "Access forbidden"), nil
	}
	extVendorId := req.GetUniqueUserId()
	crResp, err := s.ffClient.GetCardRequestByExternalVendorId(ctx, &firefly.GetCardRequestByExternalVendorIdRequest{
		ExternalVendorId: extVendorId,
	})
	if grpcErr := epifigrpc.RPCError(crResp, err); grpcErr != nil {
		logger.Error(ctx, "error in GetCardRequestByExternalVendorId", zap.Error(grpcErr))
		return getResponseWithException(statusCodeInternal, "unexpected system error"), nil
	}

	for idx, cr := range crResp.GetCardRequests() {
		if idx == 0 {
			actorId = cr.GetActorId()
			continue
		}

		if cr.GetActorId() != actorId {
			return getResponseWithException(statusCodeFailedPrecondition, "there are two users with same external vendor id"), nil
		}
	}

	onbStagesResp, err := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &firefly.GetCardRequestAndCardRequestStageRequest{
		ActorId:             actorId,
		CardRequestWorkflow: enums2.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	if grpcErr := epifigrpc.RPCError(onbStagesResp, err); grpcErr != nil && !onbStagesResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in GetCardRequestAndCardRequestStage", zap.Error(grpcErr))
		return getResponseWithException(statusCodeInternal, "unexpected system error"), nil
	}
	currCrStageResp = onbStagesResp

	if onbStagesResp.GetStatus().IsRecordNotFound() || req.GetFetchHistory() {
		elgStagesResp, err := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &firefly.GetCardRequestAndCardRequestStageRequest{
			ActorId:             actorId,
			CardRequestWorkflow: enums2.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
		})
		if grpcErr := epifigrpc.RPCError(elgStagesResp, err); grpcErr != nil && !elgStagesResp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in GetCardRequestAndCardRequestStage", zap.Error(grpcErr))
			return getResponseWithException(statusCodeInternal, "unexpected system error"), nil
		}

		if elgStagesResp.GetStatus().IsRecordNotFound() {
			return getResponseWithException(statusCodeInvalidArgument, "no user found with given user id"), nil
		}

		if onbStagesResp.GetStatus().IsRecordNotFound() {
			currCrStageResp = elgStagesResp
		}
		if req.GetFetchHistory() {
			onbStageHistory = getOnbStageHistory(onbStagesResp, elgStagesResp)
		}
	}

	overallOnbStatus, currOnbStage, currOnbStageStatus = getOnboardingStatuses(currCrStageResp)

	return &creditcard.GetCreditCardOnboardingStatusResponse{
		OverallOnboardingStatus:      overallOnbStatus,
		CurrentOnboardingStage:       currOnbStage,
		CurrentOnboardingStageStatus: currOnbStageStatus,
		OnboardingStageHistory:       onbStageHistory,
	}, nil
}

func getOnbStageHistory(onbStagesResp, elgStagesResp *firefly.GetCardRequestAndCardRequestStageResponse) []*creditcard.OnboardingStageDetails {
	var stageHistory []*creditcard.OnboardingStageDetails
	for _, stage := range elgStagesResp.GetCardRequestStages() {
		stageHistory = append(stageHistory, &creditcard.OnboardingStageDetails{
			StageName:   cardRequestStageNameMap[stage.GetStage()],
			StageStatus: cardRequestStageStatusMap[stage.GetStatus()],
			CreatedAt:   stage.GetCreatedAt().AsTime().String(),
			UpdatedAt:   stage.GetUpdatedAt().AsTime().String(),
		})
	}

	for _, stage := range onbStagesResp.GetCardRequestStages() {
		stageHistory = append(stageHistory, &creditcard.OnboardingStageDetails{
			StageName:   cardRequestStageNameMap[stage.GetStage()],
			StageStatus: cardRequestStageStatusMap[stage.GetStatus()],
			CreatedAt:   stage.GetCreatedAt().AsTime().String(),
			UpdatedAt:   stage.GetUpdatedAt().AsTime().String(),
		})
	}

	return stageHistory
}

func getOnboardingStatuses(ffResp *firefly.GetCardRequestAndCardRequestStageResponse) (string, string, string) {
	overallStatus := cardRequestStageStatusToStatusMap[ffResp.GetCardRequest().GetStatus()]
	currStage, currStageStatus := getCurrentStageDetails(ffResp.GetCardRequestStages())

	return overallStatus, currStage, currStageStatus
}

func getCurrentStageDetails(stages []*firefly.CardRequestStage) (string, string) {
	for _, stage := range stages {
		if isStageInProgress(stage.GetStatus()) {
			return cardRequestStageNameMap[stage.GetStage()], cardRequestStageStatusMap[stage.GetStatus()]
		}
	}
	return "", ""
}

func isStageInProgress(state enums2.CardRequestStageStatus) bool {
	return state == enums2.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_IN_PROGRESS ||
		state == enums2.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED
}
func getResponseWithException(statusCode string, errorMsg string) *creditcard.GetCreditCardOnboardingStatusResponse {
	return &creditcard.GetCreditCardOnboardingStatusResponse{
		Exception: &creditcard.Exception{
			StatusCode:   statusCode,
			ErrorMessage: errorMsg,
		},
	}
}
