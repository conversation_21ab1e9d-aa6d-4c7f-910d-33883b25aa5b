package m2p

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"crypto/sha256"
	"encoding/csv"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"

	//nolint:depguard
	"github.com/samber/lo"
	"google.golang.org/grpc/metadata"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/golang/protobuf/proto"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	anyPb "google.golang.org/protobuf/types/known/anypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace"
	"github.com/epifi/be-common/pkg/faas"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"

	ffAccPb "github.com/epifi/gamma/api/firefly/accounting/consumer"
	ffAccEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	notifPb "github.com/epifi/gamma/api/firefly/accounting/notification"
	wfPb "github.com/epifi/gamma/api/firefly/accounting/workflow"
	ffBillingConsumerPb "github.com/epifi/gamma/api/firefly/billing/consumer"
	"github.com/epifi/gamma/api/firefly/billing/enums"
	ffConsumerPb "github.com/epifi/gamma/api/firefly/consumer"
	ffEnums "github.com/epifi/gamma/api/firefly/enums"
	ccPb "github.com/epifi/gamma/api/vendornotification/creditcard"
	ccVendorsPb "github.com/epifi/gamma/api/vendors/m2p/lending/creditcard"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	ffAccHelper "github.com/epifi/gamma/firefly/accounting/helper"
	config "github.com/epifi/gamma/vendornotification/config"
	genConf "github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type CCTransactionNotificationPublisher queue.Publisher
type CCStatementNotificationPublisher queue.Publisher
type CCAcsNotificationPublisher queue.Publisher
type CcSwitchNotificationsS3Client s3.S3Client
type CcRawSwitchNotificationsS3Client s3.S3Client
type CCNonFinancialNotificationPublisher queue.Publisher

type Service struct {
	ccPb.UnimplementedCreditCardServer
	ccTransactionNotificationPublisher  queue.Publisher
	ccStatementNotificationPublisher    queue.Publisher
	ccAcsNotificationPublisher          queue.Publisher
	faasExecutor                        faas.FaaSExecutor
	dynamicConf                         *genConf.Config
	conf                                *config.Config
	switchNotificationsS3Client         CcSwitchNotificationsS3Client
	rawSwitchNotificationsS3Client      CcRawSwitchNotificationsS3Client
	ccNonFinancialNotificationPublisher queue.Publisher
}

func NewService(ccTransactionNotificationPublisher CCTransactionNotificationPublisher,
	ccStatementNotificationPublisher CCStatementNotificationPublisher,
	ccAcsNotificationPublisher CCAcsNotificationPublisher,
	faasExecutor faas.FaaSExecutor, dynamicConf *genConf.Config, conf *config.Config,
	switchNotificationsS3Client CcSwitchNotificationsS3Client, rawSwitchNotificationsS3Client CcRawSwitchNotificationsS3Client,
	ccNonFinancialNotificationPublisher CCNonFinancialNotificationPublisher) *Service {
	return &Service{
		ccTransactionNotificationPublisher:  ccTransactionNotificationPublisher,
		ccStatementNotificationPublisher:    ccStatementNotificationPublisher,
		ccAcsNotificationPublisher:          ccAcsNotificationPublisher,
		faasExecutor:                        faasExecutor,
		dynamicConf:                         dynamicConf,
		conf:                                conf,
		switchNotificationsS3Client:         switchNotificationsS3Client,
		rawSwitchNotificationsS3Client:      rawSwitchNotificationsS3Client,
		ccNonFinancialNotificationPublisher: ccNonFinancialNotificationPublisher,
	}
}

const (
	CardNotificationRequest = "CardNotificationRequest"
	// TIME STAMP IS EQUIVALENT TO time.RFC1123
	transactionTimestampFormat              = "20060102150405"
	AcsNotificationRequest                  = "AcsNotificationRequest"
	FrmNotificationRequest                  = "FrmNotificationRequest"
	acsNotificationSuccessRespCode          = "00"
	acsNotificationFailureRespCode          = "01"
	acsNotificationSuccessErrMsg            = "Successfully received"
	acsNotificationFailureErrMsg            = "Request failed"
	acsNotificationDateLayoutDDMMYYYYHHMMSS = "02012006150405" // DDMMYYYYHH24MinSecs
	acsNotificationDateLayoutDDMMYYYYHHMM   = "020120061504"   // DDMMYYYYHH24Min
	fileNotFound                            = "NoSuchKey"
	rawS3BucketClientName                   = "rawS3Client"
	fireflyS3BucketClientName               = "fireflyS3Client"
	cardNotificationRequestCsvHeader        = "entity_id,txn_reference_number,mobile_number,merchant_id,merchant_name,txn_date,transaction_type,merchant_location,txn_status,mcc,amount,channel,proxy_card_number,retrieval_ref_number,terminal_id,txn_currency,txn_origin,acquirer_id,network,bin,card_ending,wallet_list_general_balance,wallet_list_general_currency_code,description,accqInstitutionCode,corporate,transaction_date_time,billing_currency,billing_amount,sub_template,awb_number,order_number,cif_number,card_number,bank,date,kit_number,statement_amount,due_date,min_due,statement_date,currency_code,crdr,auth_code,external_txn_id," +
		// Frm Notification fields
		"frm_transactionAmount,frm_additional_info_cardLastSix,frm_additional_info_entityId,frm_additional_info_cardNo,frm_response,frm_transactionDate,frm_transactionId,frm_rrn,dpd,minimum_amount_due,total_amount_due\n"
	accountClosureDueToInactivitySubTemplate = "CLOSURE_NO_TRANSACTIONS"
	// first reminder notification from M2P for account closure
	accountClosureReminderDueToInactivitySubTemplate = "Account.Closure.for.parent.1"
	accountClosureTransactionType                    = "Account.Closure.for.parent"
)

var (
	// these are the notification types for which consumption support is added, and we are using them for our different use cases
	cardTransactionNotificationTypes = []string{"ATM", "POS", "CASH_AT_POS", "ECOM", "ATM_REVERSAL",
		"POS_REVERSAL", "ECOM_REVERSAL", "REFUND", "VIRTUAL_ACCOUNT_CREDIT", "IMPS_DEBIT", "IMPS_DEBIT_REVERSAL",
		"FEES", "FEES_REVERSAL", "FUNDPOST_CREDIT", "FUNDPOST_DEBIT", "C2M", "C2C", "M2C", "SERVICETAX",
		"SERVICETAX_REVERSAL", "INTEREST", "DIRECT_CREDIT", "PG", "UPI_COLLECT_CREDIT", "netc_registered_failure",
		"netc_registered_success", "NETC_CORPORATE_DEBIT", "NETC_RETAIL_DEBIT", "CASHBACK_CREDIT",
		"CHARGEBACK_CREDIT", "DIRECT_DEBIT", "LOAN_PRINCIPAL", "LOAN_INTEREST", "LOAN_INTEREST_TAX",
		"LOAN_PROCESSING_FEES", "LOAN_PROCESSING_FEES_TAX", "LOAN_PRE_CLOSURE_PRINCIPAL",
		"LOAN_PRE_CLOSURE_INTEREST", "LOAN_PRE_CLOSURE_INTEREST_TAX", "LOAN_PRE_CLOSURE_FEES", "LOAN_PRE_CLOSURE_FEES_TAX",
		"LOAN_PRE_CLOSURE_PROCESSING_FEES", "LOAN_PRE_CLOSURE_PROCESSING_FEES_TAX", "LOAN_PRE_CLOSURE_CREDIT",
		"LOAN_CANCEL_CREDIT", "LOAN_CANCEL", "LOAN_CANCEL_INTEREST", "LOAN_PROCESSING_FEES_REVERSAL",
		"LOAN_PROCESSING_FEES_TAX_REVERSAL", "LOAN_REVERSAL", "EMI_CONVERSION"}

	// these are the notification types for which consumption support isn't added, and we might not need them for now so anytime we'll be encountering one these
	// types of notification we'll log and ignore them for now
	blacklistedCardTransactionNotificationTypes = []string{"", "ACCOUNT_CREATION", "KYC_UPGRADE", "CUSTOMER_REGISTERED",
		"ATM_MINI_STATEMENT", "ATM_BALANCE_ENQUIRY", "PIN_CHANGE", "TRANSACTION_OTP", "CUSTOMER_CARDSTATUS_UPDATE", "PIN_CHANGE_SUCCESS",
		"PDF_STATEMENT_GENERATED", "SET_LIMIT", "INTEREST", "BLACKLIST_ADD_CORPORATE", "BLACKLIST_ADD_TRUCK", "BLACKLIST_REMOVE_CORPORATE",
		"BLACKLIST_REMOVE_TRUCK", "LOWBALANCE_ADD_CORPORATE", "LOWBALANCE_ADD_TRUCK", "PREFERENCE_UPDATE", "REQUEST_PHYSICAL_CARD", "CREDIT_CUSTOMER_REGISTRATION",
		"CARD_UNLOCK_SUCCESS", "CARD_LOCK_SUCCESS", "CARD_REPLACEMENT", "CUSTOMER_PROFILE_UPDATE", "ACCOUNT.CLOSURE.FOR.PARENT", "CARD_BLOCK_SUCCESS",
		"DISPUTE_CREDIT", "CARD.BLOCK.SUCCESS", "CARD.LOCK.SUCCESS", "CARD.UNLOCK.SUCCESS", "SET_PREFERENCE",
	}

	nonFinancialNotificationTypeMap = map[string]ffEnums.NonFinancialNotificationType{
		"NOTIFY_DPD": ffEnums.NonFinancialNotificationType_NON_FINANCIAL_NOTIFICATION_TYPE_DPD_NOTIFY,
	}

	notificationTypeToTransactionCategoryMap = map[string]ffAccEnumsPb.TransactionCategory{
		"ATM":                                  ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_ATM_WITHDRAWAL,
		"POS":                                  ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_POS,
		"CASH_AT_POS":                          ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_CASH_AT_POS,
		"ECOM":                                 ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_ECOM,
		"ATM_REVERSAL":                         ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_ATM_REVERSAL,
		"POS_REVERSAL":                         ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_POS_REVERSAL,
		"ECOM_REVERSAL":                        ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_ECOM_REVERSAL,
		"REFUND":                               ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_REFUND,
		"VIRTUAL_ACCOUNT_CREDIT":               ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_VIRTUAL_ACCOUNT_CREDIT,
		"IMPS_DEBIT":                           ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_IMPS_DEBIT,
		"IMPS_DEBIT_REVERSAL":                  ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_IMPS_DEBIT_REVERSAL,
		"FEES":                                 ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_FEES,
		"FEES_REVERSAL":                        ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_FEES_REVERSAL,
		"FUNDPOST_CREDIT":                      ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_FUND_POST_CREDIT,
		"FUNDPOST_DEBIT":                       ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_FUND_POST_DEBIT,
		"C2M":                                  ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_C2M,
		"C2C":                                  ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_C2C,
		"M2C":                                  ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_M2C,
		"SERVICETAX":                           ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_SERVICE_TAX,
		"SERVICETAX_REVERSAL":                  ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_SERVICE_TAX_REVERSAL,
		"INTEREST":                             ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_INTEREST,
		"DIRECT_CREDIT":                        ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_DIRECT_CREDIT,
		"PG":                                   ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_PG,
		"UPI_COLLECT_CREDIT":                   ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_UPI_COLLECT_CREDIT,
		"netc_registered_failure":              ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_NETC_REGISTERED_FAILURE,
		"netc_registered_success":              ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_NETC_REGISTERED_SUCCESS,
		"NETC_CORPORATE_DEBIT":                 ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_NETC_CORPORATE_DEBIT,
		"NETC_RETAIL_DEBIT":                    ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_NETC_RETAIL_DEBIT,
		"CASHBACK_CREDIT":                      ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_CASHBACK_CREDIT,
		"CHARGEBACK_CREDIT":                    ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_CHARGEBACK_CREDIT,
		"DIRECT_DEBIT":                         ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_DIRECT_DEBIT,
		"LOAN_PRINCIPAL":                       ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRINCIPAL,
		"LOAN_INTEREST":                        ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_INTEREST,
		"LOAN_INTEREST_TAX":                    ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_INTEREST_TAX,
		"LOAN_PROCESSING_FEES":                 ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES,
		"LOAN_PROCESSING_FEES_TAX":             ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES_TAX,
		"LOAN_PRE_CLOSURE_PRINCIPAL":           ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_PRINCIPAL,
		"LOAN_PRE_CLOSURE_INTEREST":            ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_INTEREST,
		"LOAN_PRE_CLOSURE_INTEREST_TAX":        ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_INTEREST_TAX,
		"LOAN_PRE_CLOSURE_FEES":                ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_FEES,
		"LOAN_PRE_CLOSURE_FEES_TAX":            ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_FEES_TAX,
		"LOAN_PRE_CLOSURE_PROCESSING_FEES":     ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_PROCESSING_FEES,
		"LOAN_PRE_CLOSURE_PROCESSING_FEES_TAX": ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_PROCESSING_FEES_TAX,
		"LOAN_PRE_CLOSURE_CREDIT":              ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_CREDIT,
		"LOAN_CANCEL_CREDIT":                   ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_CANCEL_CREDIT,
		"LOAN_CANCEL":                          ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_CANCEL,
		"LOAN_CANCEL_INTEREST":                 ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_CANCEL_INTEREST,
		"LOAN_PROCESSING_FEES_REVERSAL":        ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES_REVERSAL,
		"LOAN_PROCESSING_FEES_TAX_REVERSAL":    ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES_TAX_REVERSAL,
		"LOAN_REVERSAL":                        ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_REVERSAL,
		"EMI_CONVERSION":                       ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_EMI_CONVERSION,
	}

	vendorTxnStatusToBeTxnStatusMap = map[string]ffAccEnumsPb.TransactionStatus{
		"PAYMENT_SUCCESS": ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS,
		"COMPLETED":       ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS,
		"PAYMENT_FAILURE": ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE,
	}
	currencyCodeToCurrencyMap = map[string]string{
		"0356": moneyPkg.RupeeCurrencyCode,
		"356":  moneyPkg.RupeeCurrencyCode,
		"INR":  moneyPkg.RupeeCurrencyCode,
	}
	vendorTxnOriginToBeTxnOriginMap = map[string]ffAccEnumsPb.TransactionOrigin{
		"ATM":         ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_ATM,
		"POS":         ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_POS,
		"ECOM":        ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_ECOM,
		"MVISA":       ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_MVISA,
		"ECOLLECT":    ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_ECOLLECT,
		"MOBILE":      ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_MOBILE,
		"CHARGEBACK":  ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_CHARGEBACK,
		"FEES":        ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_FEES,
		"FUND_POST":   ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_FUND_POST,
		"CASH_AT_POS": ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_CASH_AT_POS,
		"UPICOLLECT":  ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_UPI_COLLECT,
		"NFC":         ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_NFC,
		"LOAN":        ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_LOAN,
		"WEB":         ffAccEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_WEB,
	}

	vendorSubTemplatesToOverrideByVendorDescription = []string{"EXCEEDS_LIMIT"}

	vendorSubTemplateToTransactionFailureTypeMap = map[string]ffAccEnumsPb.TransactionFailureType{
		"UNALLOCATED":                          ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_UNALLOCATED,
		"ALLOCATED":                            ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_ALLOCATED,
		"LOCKED":                               ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_LOCKED,
		"BLOCKED":                              ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_BLOCKED,
		"SURRENDERD":                           ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_SURRENDERD,
		"DELINKED":                             ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_DELINKED,
		"REPLACED":                             ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_REPLACED,
		"DORMANT":                              ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_DORMANT,
		"PICKUP":                               ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_PICKUP,
		"EXPIRED_CARD":                         ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_EXPIRED,
		"STOLEN":                               ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_STOLEN,
		"LOST":                                 ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_LOST,
		"RESTRICTED":                           ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_RESTRICTED,
		"FRAUD":                                ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_FRAUD,
		"ISSUER_ATTEMPT":                       ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_ISSUER_ATTEMPT,
		"COF_NOT_ALLOWED":                      ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_COF_NOT_ALLOWED,
		"NO_CAVV":                              ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_NO_CAVV,
		"NO_CVV2":                              ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_NO_CVV2,
		"INTL_NA":                              ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INTL_NA,
		"MOTO_NOT_ALLOWED":                     ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_MOTO_NOT_ALLOWED,
		"RP_NOT_ALLOWED":                       ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_RP_NOT_ALLOWED,
		"IP_NOT_ALLOWED":                       ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_IP_NOT_ALLOWED,
		"PRESTORED_NOT_ALLOWED":                ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PRESTORED_NOT_ALLOWED,
		"DCC_NA":                               ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_DCC_NA,
		"EMV_FAIL":                             ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_EMV_FAIL,
		"CAVV_FAIL":                            ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CAVV_FAIL,
		"CVV2_FAIL":                            ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CVV2_FAIL,
		"CVV_FAIL":                             ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CVV_FAIL,
		"INVALID_EXPIRY_DATE":                  ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INVALID_EXPIRY_DATE,
		"INVALID_CAVV":                         ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INVALID_CAVV,
		"INVALID_PIN":                          ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INVALID_PIN,
		"ecom_preference_failed_international": ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE,
		"INSUFFICIENT_FUND":                    ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INSUFFICIENT_FUND,
		"DEBIT_TXN_LIMIT_EXCEEDED":             ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_DEBIT_TXN_LIMIT_EXCEEDED,
		"CARD_REPLACED":                        ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_NOT_ACTIVE,
		"EXCEEDS_LIMIT":                        ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT,
		"CARD_LOCKED":                          ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_LOCKED,
		"CARD_BLOCKED":                         ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_BLOCKED,
		"PIN_RETRIES_EXCEEDED":                 ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PIN_RETRIES_EXCEEDED,
		"CARD_SURRENDERD":                      ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_SURRENDERD,
	}

	descriptionToTransactionFailureTypeMap = map[string]ffAccEnumsPb.TransactionFailureType{
		"Maximum amount for a transaction reached":                     ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT,
		"Daily Transaction Limit for Credit exceeded":                  ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CREDIT_TXN_LIMIT_EXCEEDED,
		"Monthly Transaction Limit for Credit exceeded":                ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CREDIT_TXN_LIMIT_EXCEEDED,
		"Yearly Transaction Limit for Credit exceeded":                 ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CREDIT_TXN_LIMIT_EXCEEDED,
		"Daily Transaction Limit for this transaction type exceeded":   ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT,
		"Daily Transaction Count for this transaction type exceeded":   ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_TXN_COUNT_LIMIT_EXCEEDED,
		"Monthly Transaction Limit for this transaction type exceeded": ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT,
		"Monthly Transaction Count for this transaction type exceeded": ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_TXN_COUNT_LIMIT_EXCEEDED,
		"Yearly Transaction Limit for this transaction type exceeded":  ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT,
		"Yearly Transaction Count for this transaction type exceeded":  ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_TXN_COUNT_LIMIT_EXCEEDED,
		"Customer Preference Failed":                                   ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE,
		"Customer Preference Failed : CONTACTLESS":                     ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE_CONTACTLESS,
		"Customer Preference Failed : txnOrigin":                       ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE_TXN_ORIGIN,
		"Customer Preference Failed : INTERNATIONAL":                   ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE_INTERNATIONAL,
		"Maximum amount for contactless transaction is exceeded":       ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT_CONTACTLESS,
		"Invalid Pin":                           ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INVALID_PIN,
		"Invalid MAC":                           ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INVALID_MAC,
		"Pin retry attempts exceeded":           ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PIN_RETRIES_EXCEEDED,
		"MCC is not allowed":                    ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_MCC_NOT_ALLOWED,
		"Balance is insufficient":               ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INSUFFICIENT_FUND,
		"Revolver Category Transaction decline": ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CREDIT_CARD_BILL_UNPAID,
		"Security Credentials Failed : ERROR":   ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CVV2_FAIL,
		"Card not active":                       ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_NOT_ACTIVE,
		"Suspected fraud":                       ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_FRAUD,
		"Block Code Transaction decline":        ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CREDIT_CARD_BILL_UNPAID,
	}
	vendorCrDrToTransactionTypeMap = map[string]ffAccEnumsPb.TransactionType{
		"DEBIT":  ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		"CREDIT": ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT,
	}
	descriptionToTransactionCategoryMap = map[string]ffAccEnumsPb.TransactionCategory{
		"CASH":            ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_CASH_WITHDRAWAL_FEE,
		"CASH_SERVICETAX": ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_CASH_WITHDRAWAL_FEE,
	}

	statementNotificationTypes    = []string{"STATEMENT_GENERATED"}
	nonFinancialNotificationTypes = []string{"NOTIFY_DPD", "Account.Closure.for.parent"}

	vendorTxnStatusToBeAcsTxnStatusMap = map[string]ffAccEnumsPb.TransactionStatus{
		"Y": ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS,
		"N": ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE,
	}

	vendorTxnTypeToBeAcsTxnTypeMap = map[string]ffAccEnumsPb.AcsTransactionType{
		"purchase": ffAccEnumsPb.AcsTransactionType_ACS_TRANSACTION_TYPE_PURCHASE,
	}
)

func isNonFinancialNotificationType(req *ccVendorsPb.CardNotificationRequest) bool {
	if req.GetEntityId() == "" {
		return false
	}
	if lo.Contains(nonFinancialNotificationTypes, req.GetTransactionType()) {
		return true
	}

	if req.GetSubTemplate() == accountClosureDueToInactivitySubTemplate {
		return true
	}

	return false
}

func (s *Service) CardNotification(ctx context.Context, req *ccVendorsPb.CardNotificationRequest) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, CardNotificationRequest, req.GetTxnReferenceNumber(), vendorsRedactor.Config)
	metrics.IncreaseCreditCardNotificationTypeCount(req.GetTransactionType())

	err := s.whitelistNotifications(ctx)
	if err != nil {
		logger.Error(ctx, "error in whitelisting card notification request", zap.Error(err))
		return nil, err
	}
	writeToS3Ctx := epificontext.WithTraceId(ctx, metadata.New(map[string]string{}))
	goroutine.RunWithCtx(writeToS3Ctx, func(ctx context.Context) {
		s.writeNotificationToS3(ctx, req, s.conf)
	})

	if isNonFinancialNotificationType(req) {
		if pubErr := s.processNonFinancialNotification(ctx, req); pubErr != nil {
			logger.Error(ctx, "error in processNonFinancialNotification", zap.Error(pubErr))
			return nil, err
		}
		return &empty.Empty{}, nil
	}

	if isStatementNotification(req.GetTransactionType()) {
		if err := s.processStatementNotification(ctx, req); err != nil {
			logger.Error(ctx, "Error in processing notification from vendor", zap.Error(err))
			return nil, err
		}
		return &empty.Empty{}, nil
	}

	if !lo.Contains(cardTransactionNotificationTypes, req.GetTransactionType()) && req.GetFrmResponse() == "" {
		err = validateTxnNotificationType(ctx, req.GetTransactionType())
		if err != nil {
			logger.Info(ctx, "non supported card transaction type", zap.String(logger.TXN_TYPE, req.GetTransactionType()), zap.Error(err))
			return nil, err
		}
		return nil, nil
	}
	event := &ffAccPb.ProcessCardTransactionNotificationRequest{}
	if err := constructEventRequest(ctx, req, event); err != nil {
		logger.Error(ctx, "error in constructing event request", zap.Error(err))
		return nil, err
	}

	// this flag is turned off in prod as we do not want to go via faas executor
	// this is done to avoid the overhead of invoking Procrastinator workflow for every card transaction notification
	// in prod we will be directly publishing the notification to the queue
	// and this queue's subscriber will invoke transaction workflow
	if s.dynamicConf.Flags().EnableCCTransactionProcessingViaTemporal() {
		actReq, err := getWfActivityReq(ctx, event)
		if err != nil {
			logger.Error(ctx, "error in creating initiate workflow activity request", zap.Error(err))
			return nil, err
		}
		// TODO(priyansh) : Update this to Execute once changes for adding Execute method in faas executor gets merged
		msgId, err := s.faasExecutor.ScheduleExecution(ctx, &faas.ScheduleExecutionRequest{
			Msg:           actReq,
			FunctionName:  string(epifitemporal.InitiateWorkflow),
			DelayDuration: 1 * time.Second,
		})
		if err != nil {
			logger.Error(ctx, "failed to execute card transaction notification activity", zap.Error(err))
			return nil, err
		}
		logger.Debug(ctx, "card notification executed successfully", zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	} else {
		_, err := s.ccTransactionNotificationPublisher.Publish(ctx, event)
		if err != nil {
			logger.Error(ctx, "error in publishing cc transaction notification event", zap.Error(err))
			return nil, err
		}
	}

	return &empty.Empty{}, nil
}

func (s *Service) whitelistNotifications(ctx context.Context) error {
	if err := security.CheckWhiteList(
		ctx, s.conf.M2PWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix,
	); err != nil {
		return err
	}
	return nil
}

// nolint:funlen
func constructEventRequest(ctx context.Context, req *ccVendorsPb.CardNotificationRequest, event *ffAccPb.ProcessCardTransactionNotificationRequest) error {
	if req.GetFrmResponse() != "" {
		return constructEventRequestForFrmDecline(ctx, req, event)
	}

	var (
		amount *moneyPb.Money
	)
	transactionCategory, ok := notificationTypeToTransactionCategoryMap[req.GetTransactionType()]
	if !ok {
		return fmt.Errorf("error in fetching transaction category for notification type %s", req.GetTransactionType())
	}
	transactionCategory = ffAccHelper.GetUpdatedTransactionCategory(transactionCategory, req.GetDescription())
	txnStatus, ok := vendorTxnStatusToBeTxnStatusMap[req.GetTxnStatus()]
	if !ok {
		return fmt.Errorf("error in fetching transaction status for txn status %s", req.GetTxnStatus())
	}
	event.TransactionCategory = transactionCategory
	event.EntityId = req.GetEntityId()
	event.TransactionReferenceNumber = req.GetTxnReferenceNumber()
	event.MerchantDetails = &notifPb.MerchantDetails{
		MerchantId:       req.GetMerchantId(),
		MerchantName:     req.GetMerchantName(),
		MerchantLocation: req.GetMerchantLocation(),
		Mcc:              req.GetMcc(),
	}
	transactionTimestamp, err := datetime.ParseStringTimestampProtoInLocation(transactionTimestampFormat,
		req.GetTxnDate(), datetime.IST)
	if err != nil {
		return fmt.Errorf("failed to parse time %s %w", req.GetTxnDate(), err)
	}
	if req.GetCrdr() != "" {
		transactionType, ok := vendorCrDrToTransactionTypeMap[req.GetCrdr()]
		if !ok {
			return fmt.Errorf("error in fetching transaction type %s", req.GetCrdr())
		}
		event.TransactionType = transactionType
	} else {
		// crdr will not be present in case of failure txns, we will assume that failure txns will be of type debit
		// as per discussion with product
		event.TransactionType = ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT
	}
	event.TransactionTimestamp = transactionTimestamp
	event.TxnStatus = txnStatus
	// TODO(Atirek) : Add support for multiple currencies
	currency, ok := currencyCodeToCurrencyMap[req.GetTxnCurrency()]
	if !ok {
		logger.Error(ctx, "failed to parse currency code")
		// adding fallback in case we are not able to parse the currency code
		currency = moneyPkg.RupeeCurrencyCode
	}
	if txnStatus == ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE {
		event.FailureType = getTransactionFailureType(req)
		// For failure transactions we will try to parse the amount via the billing amount if not empty otherwise
		// we will use the amount field
		if req.GetBillingAmount() != "" {
			billingAmount, err := strconv.Atoi(req.GetBillingAmount())
			if err != nil {
				logger.Error(ctx, "error in conversion for billing amount", zap.Error(err),
					zap.String(logger.AMOUNT, req.GetBillingAmount()))
			} else {
				// we get amount in paisa from vendor converting it into rupees
				amountInRupees := float64(billingAmount) / 100.0
				amount = moneyPkg.ParseFloat(amountInRupees, moneyPkg.RupeeCurrencyCode)
			}
		}
	}
	if amount == nil || moneyPkg.IsZero(amount) {
		amount, err = moneyPkg.ParseString(req.GetAmount(), currency)
		if err != nil {
			return fmt.Errorf("failed to parse amount %s %w", req.GetAmount(), err)
		}
	}
	event.TxnAmount = amount
	txnOrigin, ok := vendorTxnOriginToBeTxnOriginMap[req.GetTxnOrigin()]
	if !ok {
		return fmt.Errorf("error in fetching transaction origin for txn origin %s", req.GetTxnOrigin())
	}
	event.TxnOrigin = txnOrigin
	event.AcquirerId = req.GetAcquirerId()
	event.TransactionDescription = req.GetDescription()
	// if proxy card number is not present in the notification we will move to kit number as fallback
	if req.GetProxyCardNumber() != "" {
		event.VendorCardIdentifier = req.GetProxyCardNumber()
	} else {
		event.VendorCardIdentifier = req.GetKitNumber()
	}
	event.RetrievalReferenceNo = req.GetRetrievalRefNumber()
	event.ExternalTxnId = req.GetExternalTxnId()
	event.AuthCode = req.GetAuthCode()
	event.OriginalTxnCurrency = req.GetTxnCurrency()
	return nil
}

func constructEventRequestForFrmDecline(ctx context.Context, req *ccVendorsPb.CardNotificationRequest, event *ffAccPb.ProcessCardTransactionNotificationRequest) error {
	var (
		amount   *moneyPb.Money
		currency string
	)
	transactionCategory, ok := notificationTypeToTransactionCategoryMap[req.GetTransactionType()]
	if !ok {
		logger.Error(ctx, "failed to parse txn category", zap.String(logger.TXN_CATEGORY, req.GetTransactionType()))
	}
	event.TransactionCategory = transactionCategory
	event.EntityId = req.GetFrmAdditionalInfo().GetEntityId()
	event.TransactionReferenceNumber = req.GetFrmRrn()
	event.MerchantDetails = &notifPb.MerchantDetails{
		MerchantId:       req.GetMerchantId(),
		MerchantName:     req.GetMerchantName(),
		MerchantLocation: req.GetMerchantLocation(),
		Mcc:              req.GetMcc(),
	}

	transactionTimestamp, err := datetime.ParseStringTimestampProtoInLocation(datetime.DATE_LAYOUT_YYYYMMDD, req.GetFrmTransactionDate(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "failed to parse txn time", zap.String("transactionTime", req.GetFrmTransactionDate()), zap.Error(err))
		return fmt.Errorf("failed to parse txn time %s %w", req.GetTxnDate(), err)
	}

	if req.GetCrdr() != "" {
		transactionType, ok := vendorCrDrToTransactionTypeMap[req.GetCrdr()]
		if !ok {
			return fmt.Errorf("error in fetching transaction type %s", req.GetCrdr())
		}
		event.TransactionType = transactionType
	} else {
		// crdr will not be present in case of failure txns, we will assume that failure txns will be of type debit
		// as per discussion with product
		event.TransactionType = ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT
	}
	event.TransactionTimestamp = transactionTimestamp

	// for FRM decline txns, transaction status will always be failed
	event.TxnStatus = ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE

	if req.GetTxnCurrency() == "" {
		logger.Error(ctx, "empty currency code in CardNotification callback payload")
		// adding fallback in case we are not able to parse the currency code
		currency = moneyPkg.RupeeCurrencyCode
	} else {
		var ok bool
		currency, ok = currencyCodeToCurrencyMap[req.GetTxnCurrency()]
		if !ok {
			logger.Error(ctx, "failed to parse currency code", zap.String("currencyCode", req.GetTxnCurrency()))
			return fmt.Errorf("failed to parse currency code: %s", req.GetTxnCurrency())
		}
	}

	if event.GetTxnStatus() == ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE {
		event.FailureType = ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_FRAUD
	}
	amount, err = moneyPkg.ParseString(req.GetFrmTransactionAmount(), currency)
	if err != nil {
		logger.Error(ctx, "failed to parse amount", zap.String(logger.AMOUNT, req.GetAmount()), zap.Error(err))
		return fmt.Errorf("failed to parse amount %s %w", req.GetAmount(), err)
	}
	event.TxnAmount = amount
	event.AcquirerId = req.GetAcquirerId()
	event.TransactionDescription = req.GetFrmResponse()
	// if proxy card number is not present in the notification we will move to kit number as fallback
	if req.GetProxyCardNumber() != "" {
		event.VendorCardIdentifier = req.GetProxyCardNumber()
	} else {
		event.VendorCardIdentifier = req.GetKitNumber()
	}
	event.RetrievalReferenceNo = req.GetFrmRrn()
	event.ExternalTxnId = req.GetFrmTransactionId()
	event.AuthCode = req.GetAuthCode()
	event.OriginalTxnCurrency = req.GetTxnCurrency()
	return nil
}

// getTransactionFailureType returns the transaction failure type from vendor's subtemplate and txn description
func getTransactionFailureType(req *ccVendorsPb.CardNotificationRequest) ffAccEnumsPb.TransactionFailureType {
	txnFailureType, ok := vendorSubTemplateToTransactionFailureTypeMap[req.GetSubTemplate()]
	if !ok {
		txnFailureType, ok = descriptionToTransactionFailureTypeMap[req.GetDescription()]
		if !ok {
			return ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_ERROR
		}
		return txnFailureType
	}
	// In cases where we have to have both sub-template & description, but override the txn failure type based on the description
	// ex. the sub-template EXCEEDS_LIMIT can be mapped to txn limit exceeded or contactless txn limit exceed based on the description
	if lo.Contains(vendorSubTemplatesToOverrideByVendorDescription, req.GetSubTemplate()) {
		txnFailureType, _ = descriptionToTransactionFailureTypeMap[req.GetDescription()]
	}
	return txnFailureType
}

func (s *Service) processStatementNotification(ctx context.Context, req *ccVendorsPb.CardNotificationRequest) error {
	event := getStatementNotificationPayload(req, commonvgpb.Vendor_M2P)
	_, err := s.ccStatementNotificationPublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error in publishing cc statement notification event", zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) processNonFinancialNotification(ctx context.Context, req *ccVendorsPb.CardNotificationRequest) error {
	event := getNonFinancialNotificationPayLoad(ctx, req, commonvgpb.Vendor_M2P)
	_, err := s.ccNonFinancialNotificationPublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error in publishing cc non financial notification event", zap.Error(err))
		return err
	}
	return nil
}

func getStatementNotificationPayload(req *ccVendorsPb.CardNotificationRequest, vendor commonvgpb.Vendor) *ffBillingConsumerPb.ProcessStatementGeneratedNotificationRequest {
	return &ffBillingConsumerPb.ProcessStatementGeneratedNotificationRequest{
		RequestHeader:             nil,
		EntityId:                  req.GetEntityId(),
		StatementNotificationType: enums.CreditCardStatementNotificationType_CREDIT_CARD_STATEMENT_NOTIFICATION_TYPE_STATEMENT_GENERATED,
		StatementDate:             datetime.DateFromString(req.GetStatementDate()),
		Vendor:                    vendor,
	}
}

func getNonFinancialNotificationPayLoad(ctx context.Context, req *ccVendorsPb.CardNotificationRequest, vendor commonvgpb.Vendor) *ffConsumerPb.ProcessNonFinancialNotificationEventRequest {
	notifType := nonFinancialNotificationTypeMap[req.GetTransactionType()]
	var closureReminderSequenceNumber int32

	switch {
	case req.GetSubTemplate() == accountClosureDueToInactivitySubTemplate:
		notifType = ffEnums.NonFinancialNotificationType_NON_FINANCIAL_NOTIFICATION_TYPE_CLOSURE_DUE_TO_TXN_INACTIVITY
	case req.GetTransactionType() == accountClosureTransactionType && strings.Contains(req.GetSubTemplate(), accountClosureTransactionType):
		notifType = ffEnums.NonFinancialNotificationType_NON_FINANCIAL_NOTIFICATION_TYPE_CLOSURE_DUE_TO_TXN_INACTIVITY_REMINDER
		var err error
		closureReminderSequenceNumber, err = getClosureReminderSeqNumFromRequestSubTemplate(req.GetSubTemplate())
		if err != nil {
			logger.Error(ctx, "error getting closure notification reminder sequence number from request sub-template", zap.Error(err))
		}
	default:
		// continue
	}

	return &ffConsumerPb.ProcessNonFinancialNotificationEventRequest{
		EntityId:                      req.GetEntityId(),
		Vendor:                        vendor,
		NotificationType:              notifType,
		ClosureReminderSequenceNumber: closureReminderSequenceNumber,
	}
}

// getClosureReminderSeqNumFromRequestSubTemplate extracts the closure reminder sequence number
// from a sub-template string used for closure reminder notifications.
//
// The function expects a sub-template string in the format: "Account.Closure.for.parent.sequenceNumber"
// where the 5th part (index 4) contains the sequence number as a string that can be parsed to int32.
//
// Example:
//
//	seqNum, err := getClosureReminderSeqNumFromRequestSubTemplate("Account.Closure.for.parent.3")
//	// Returns: 3, nil
//
// Errors:
//   - Returns error if sub-template doesn't have exactly 5 dot-separated parts
//   - Returns wrapped error if the 5th part cannot be parsed as a valid int32
func getClosureReminderSeqNumFromRequestSubTemplate(subTemplate string) (int32, error) {
	subTemplateParts := strings.Split(subTemplate, ".")
	if len(subTemplateParts) == 5 {
		closureReminderSequenceNumber, err := strconv.ParseInt(subTemplateParts[4], 10, 32)
		if err != nil {
			return 0, errors.Wrap(err, "error parsing closure reminder sequence number")
		}
		return int32(closureReminderSequenceNumber), nil
	}
	return 0, errors.New("passed sub template is not for closure reminder notification")
}

func isStatementNotification(notificationType string) bool {
	for _, statementNotificationType := range statementNotificationTypes {
		if statementNotificationType == notificationType {
			return true
		}
	}
	return false
}

// getWfActivityReq returns the initiate wf activity request which will be triggered via the procrastinator workflow
func getWfActivityReq(ctx context.Context, event *ffAccPb.ProcessCardTransactionNotificationRequest) (*activityPb.InitiateWorkflowActivityRequest, error) {
	processCardTxnWfRequest := &wfPb.ProcessCardNotificationWorkflowPayload{
		CardTransactionNotificationPayload: &notifPb.CardTransactionNotificationPayload{
			EntityId:                   event.GetEntityId(),
			TransactionReferenceNumber: event.GetTransactionReferenceNumber(),
			MerchantDetails:            event.GetMerchantDetails(),
			TransactionTimestamp:       event.GetTransactionTimestamp(),
			TxnStatus:                  event.GetTxnStatus(),
			TxnAmount:                  event.GetTxnAmount(),
			TxnOrigin:                  event.GetTxnOrigin(),
			AcquirerId:                 event.GetAcquirerId(),
			TransactionDescription:     event.GetTransactionDescription(),
			VendorCardIdentifier:       event.GetVendorCardIdentifier(),
			TransactionCategory:        event.GetTransactionCategory(),
			FailureType:                event.GetFailureType(),
			TransactionType:            event.GetTransactionType(),
			ExternalTxnId:              event.GetExternalTxnId(),
			AuthCode:                   event.GetAuthCode(),
			RetrievalReferenceNo:       event.GetRetrievalReferenceNo(),
			OriginalTxnCurrency:        event.GetOriginalTxnCurrency(),
		}}
	wfPayload, err := anyPb.New(processCardTxnWfRequest)
	if err != nil {
		return nil, fmt.Errorf("error marshalling process card txn wf request into type *Any")
	}
	// we will use hash of the workflow request proto message as wf req id to uniquely identify the
	// notification and initiate workflow
	marshalledString, err := proto.Marshal(processCardTxnWfRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal txn wf request %w", err)
	}
	hash := sha256.Sum256(marshalledString)
	wfReqId := hex.EncodeToString(hash[:])
	logger.Info(ctx, "workflow request Id for initiating txn processing",
		zap.String(logger.TXN_ID, event.GetExternalTxnId()), zap.String(logger.ID, event.GetRetrievalReferenceNo()),
		zap.String(logger.WORKFLOW_REQ_ID, wfReqId))
	return &activityPb.InitiateWorkflowActivityRequest{
		Namespace: string(namespace.Firefly),
		WfReqId:   wfReqId,
		Version:   workflowPb.Version_V0,
		Type:      workflowPb.Type_PROCESS_CARD_TRANSACTION,
		WfPayload: wfPayload,
	}, nil
}

// writeNotificationToS3 serializes the CardNotificationRequest payload to CSV format
// and writes it to two different S3 buckets - raw-data-dev and cc-switch-transaction-notifications.
// It takes a context, CardNotificationRequest, and configuration as input parameters.
func (s *Service) writeNotificationToS3(ctx context.Context, req *ccVendorsPb.CardNotificationRequest, conf *config.Config) {
	// Serialize the CardNotificationRequest payload to CSV format
	payload, csvErr := serializeToCsv(req)
	if csvErr != nil {
		// Log an error if there's an issue with payload serialization
		logger.Error(ctx, "error in parsing payload from cardNotificationRequest, can't write in S3 buckets")
		return
	}
	// Log a success message after successfully extracting payload
	logger.Info(ctx, "successfully extracted payload from cardNotificationRequest")

	// Write payload to raw-data-dev S3 bucket
	err := s.writeToCsvInS3Bucket(ctx, payload, conf.RawBucketM2pFederalSwitchNotificationFilePath, true)
	if err != nil {
		// Log an error if there's an issue writing to raw-data-dev bucket
		logger.Error(ctx, "error writing in raw-data-dev bucket", zap.Error(err))
		return
	}
	// Log a success message after successfully writing to raw-data-dev bucket
	logger.Info(ctx, "successfully written switchNotifications into raw-data-dev s3 bucket ")

	// Write payload to cc-switch-transaction-notifications S3 bucket
	err = s.writeToCsvInS3Bucket(ctx, payload, conf.M2pFederalSwitchNotificationFilePath, false)
	if err != nil {
		// Log an error if there's an issue writing to cc-switch-transaction-notifications bucket
		logger.Error(ctx, "error writing in cc-switch-transaction-notifications bucket", zap.Error(err))
		return
	}
	// Log a success message after successfully writing to cc-switch-transaction-notifications bucket
	logger.Info(ctx, "successfully written switchNotifications into cc-switch-transaction-notifications s3 bucket")
}

// writeToCsvInS3Bucket appends the given payload to a CSV file in an S3 bucket.
// It takes the payload string, file path, and a flag indicating whether to write in the raw bucket.
// Returns an error if any operation encounters issues.
func (s *Service) writeToCsvInS3Bucket(ctx context.Context, payload string, filePath string, writeInRawBucket bool) error {
	var (
		existingData         []byte
		fileNameWithLocation string
		err                  error
	)

	// Get the current date for the CSV file name
	today := time.Now().Format("2006-01-02")
	todayWithHour := time.Now().Format("2006-01-02 15")

	// Use the s3 clients to read the existing content from the S3 bucket
	switch {
	case writeInRawBucket:
		// Create or open the CSV file for the current day in the raw S3 bucket
		fileNameWithLocation = fmt.Sprintf(filePath, today, todayWithHour)
		existingData, err = s.rawSwitchNotificationsS3Client.ReadObject(ctx, fileNameWithLocation)
	case !writeInRawBucket:
		// Create or open the CSV file for the current day in the main S3 bucket
		fileNameWithLocation = fmt.Sprintf(filePath, today, todayWithHour)
		existingData, err = s.switchNotificationsS3Client.ReadObject(ctx, fileNameWithLocation)
	}

	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return fmt.Errorf("failed to read CSV file from S3: %w", err)
	}
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		existingData = []byte(cardNotificationRequestCsvHeader)
	}

	// Append the new data to the existing content
	existingData = append(existingData, []byte(payload)...)

	switch {
	case writeInRawBucket:
		// Use the rawSwitchNotificationsS3Client to write the data back to the S3 bucket
		err = s.rawSwitchNotificationsS3Client.Write(ctx, fileNameWithLocation, existingData, string(s3types.ObjectCannedACLBucketOwnerFullControl))
		if err != nil {
			return fmt.Errorf("error while appending data to CSV: %w", err)
		}
	case !writeInRawBucket:
		// Use the switchNotificationsS3Client to write the data back to the S3 bucket
		err = s.switchNotificationsS3Client.Write(ctx, fileNameWithLocation, existingData, string(s3types.ObjectCannedACLBucketOwnerFullControl))
		if err != nil {
			return fmt.Errorf("error while appending data to CSV: %w", err)
		}
	}

	return nil
}

// serializeToCsv is a function that takes a ccVendorsPb.CardNotificationRequest pointer as input and serializes its data
// into a CSV (Comma Separated Values) format represented as a string. The function returns the serialized CSV string
// along with an error (if any occurred during the serialization process). It starts by creating a strings.Builder
// variable called 'tuple' to efficiently construct the CSV string. A csv.Writer 'writer' is initialized using the
// 'tuple' string builder. The csv.Writer is used to write the CSV data row by row.
//
//nolint:funlen
func serializeToCsv(req *ccVendorsPb.CardNotificationRequest) (string, error) {
	var tuple strings.Builder
	writer := csv.NewWriter(&tuple)

	// Remove the trailing newline character
	csvHeader := cardNotificationRequestCsvHeader[:len(cardNotificationRequestCsvHeader)-1]

	// Split the header string into individual field names
	headerFields := strings.Split(csvHeader, ",")

	// Create a map to hold values for each field
	fieldValues := make(map[string]string)

	// Populate values using the provided 'req' object
	fieldValues["entity_id"] = req.GetEntityId()
	fieldValues["txn_reference_number"] = req.GetTxnReferenceNumber()
	fieldValues["mobile_number"] = req.GetMobileNumber()
	fieldValues["merchant_id"] = req.GetMerchantId()
	fieldValues["merchant_name"] = req.GetMerchantName()
	fieldValues["txn_date"] = req.GetTxnDate()
	fieldValues["transaction_type"] = req.GetTransactionType()
	fieldValues["merchant_location"] = req.GetMerchantLocation()
	fieldValues["txn_status"] = req.GetTxnStatus()
	fieldValues["mcc"] = req.GetMcc()
	fieldValues["amount"] = req.GetAmount()
	fieldValues["channel"] = req.GetChannel()
	fieldValues["proxy_card_number"] = req.GetProxyCardNumber()
	fieldValues["retrieval_ref_number"] = req.GetRetrievalRefNumber()
	fieldValues["terminal_id"] = req.GetTerminalId()
	fieldValues["txn_currency"] = req.GetTxnCurrency()
	fieldValues["txn_origin"] = req.GetTxnOrigin()
	fieldValues["acquirer_id"] = req.GetAcquirerId()
	fieldValues["network"] = req.GetNetwork()
	fieldValues["bin"] = req.GetBin()
	fieldValues["card_ending"] = req.GetCardEnding()
	fieldValues["wallet_list_general_balance"] = req.GetWalletList().GetGeneral().GetBalance()
	fieldValues["wallet_list_general_currency_code"] = req.GetWalletList().GetGeneral().GetCurrencyCode()
	fieldValues["description"] = req.GetDescription()
	fieldValues["accqInstitutionCode"] = req.GetAccqInstitutionCode()
	fieldValues["corporate"] = req.GetCorporate()
	fieldValues["transaction_date_time"] = req.GetTransactionDateTime()
	fieldValues["billing_currency"] = req.GetBillingCurrency()
	fieldValues["billing_amount"] = req.GetBillingAmount()
	fieldValues["sub_template"] = req.GetSubTemplate()
	fieldValues["awb_number"] = req.GetAwbNumber()
	fieldValues["order_number"] = req.GetOrderNumber()
	fieldValues["cif_number"] = req.GetCifNumber()
	fieldValues["card_number"] = req.GetCardNumber()
	fieldValues["bank"] = req.GetBank()
	fieldValues["date"] = req.GetDate()
	fieldValues["kit_number"] = req.GetKitNumber()
	fieldValues["statement_amount"] = req.GetStatementAmount()
	fieldValues["due_date"] = req.GetDueDate()
	fieldValues["min_due"] = req.GetMinDue()
	fieldValues["statement_date"] = req.GetStatementDate()
	fieldValues["currency_code"] = req.GetCurrencyCode()
	fieldValues["crdr"] = req.GetCrdr()
	fieldValues["auth_code"] = req.GetAuthCode()
	fieldValues["external_txn_id"] = req.GetExternalTxnId()
	fieldValues["dpd"] = req.GetDpd()
	fieldValues["minimum_amount_due"] = req.GetMinimumAmountDue()
	fieldValues["total_amount_due"] = req.GetTotalAmountDue()

	// Updated fields for Frm Notification
	// If the Frm response is not empty, populate the corresponding non-frm fields with values from the frm fields.
	if req.GetFrmResponse() != "" {
		fieldValues["frm_transactionAmount"] = req.GetFrmTransactionAmount()
		fieldValues["amount"] = req.GetFrmTransactionAmount()
		fieldValues["frm_additional_info_cardLastSix"] = req.GetFrmAdditionalInfo().GetCardLastSix()
		fieldValues["card_ending"] = req.GetFrmAdditionalInfo().GetCardLastSix()
		fieldValues["frm_additional_info_entityId"] = req.GetFrmAdditionalInfo().GetEntityId()
		fieldValues["entity_id"] = req.GetFrmAdditionalInfo().GetEntityId()
		fieldValues["frm_additional_info_cardNo"] = req.GetFrmAdditionalInfo().GetCardNo()
		fieldValues["card_number"] = req.GetFrmAdditionalInfo().GetCardNo()
		fieldValues["frm_response"] = req.GetFrmResponse()
		fieldValues["frm_transactionDate"] = req.GetFrmTransactionDate()
		fieldValues["txn_date"] = req.GetFrmTransactionDate()
		fieldValues["frm_transactionId"] = req.GetFrmTransactionId()
		fieldValues["external_txn_id"] = req.GetFrmTransactionId()
		fieldValues["frm_rrn"] = req.GetFrmRrn()
		fieldValues["retrieval_ref_number"] = req.GetFrmRrn()
	}

	// Append values in the order of header fields
	row := make([]string, 0)
	for _, field := range headerFields {
		row = append(row, fieldValues[field])
	}

	err := writer.Write(row)
	if err != nil {
		return "", err
	}
	// Flush and close the writer
	writer.Flush()
	return tuple.String(), nil
}

// function responsible for validating the txn type for the switch notifications if the notification is form blacklisted list then we'll
// log otherwise we'll throw the error and fail the rpc as it is unrecognised txn type notification
func validateTxnNotificationType(ctx context.Context, txnType string) error {
	// validating whether the type is of blacklisted notifications
	if lo.Contains(blacklistedCardTransactionNotificationTypes, strings.ToUpper(txnType)) {
		logger.Info(ctx, "blacklisted card switch transaction notification", zap.String(logger.TXN_TYPE, txnType))
		return nil
	}
	return errors.New("unrecognised card switch transaction notification")
}

func (s *Service) AcsNotification(ctx context.Context, req *ccVendorsPb.AcsNotificationRequest) (*ccVendorsPb.AcsNotificationResponse, error) {
	redactor.LogCallbackRequestData(ctx, req, AcsNotificationRequest, req.GetTxnId(), vendorsRedactor.Config)
	err := s.whitelistNotifications(ctx)
	if err != nil {
		logger.Error(ctx, "error in whitelisting acs notification request", zap.Error(err))
		return nil, err
	}
	notificationPayload, err := getAcsNotificationPayload(req)
	if err != nil {
		logger.Error(ctx, "error in parsing cc acs notification req", zap.Error(err))
		return &ccVendorsPb.AcsNotificationResponse{
			TxnId:        req.GetTxnId(),
			ResponseCode: acsNotificationFailureRespCode,
			ErrMsg:       acsNotificationFailureErrMsg,
			DateAndTime:  req.GetDateAndTime(),
		}, nil
	}
	_, publishErr := s.ccAcsNotificationPublisher.Publish(ctx, &ffAccPb.ProcessAcsNotificationRequest{
		AcsNotificationPayload: notificationPayload,
	})

	if publishErr != nil {
		logger.Error(ctx, "error in publishing cc acs notification to queue", zap.Error(err))
		return &ccVendorsPb.AcsNotificationResponse{
			TxnId:        req.GetTxnId(),
			ResponseCode: acsNotificationFailureRespCode,
			ErrMsg:       acsNotificationFailureErrMsg,
			DateAndTime:  req.GetDateAndTime(),
		}, nil
	}

	return &ccVendorsPb.AcsNotificationResponse{
		TxnId:        req.GetTxnId(),
		ResponseCode: acsNotificationSuccessRespCode,
		ErrMsg:       acsNotificationSuccessErrMsg,
		DateAndTime:  req.GetDateAndTime(),
	}, nil
}

func getAcsNotificationPayload(req *ccVendorsPb.AcsNotificationRequest) (*notifPb.AcsNotificationPayload, error) {
	txnTime, err := getDateToTimestamp(req.GetDateAndTime())
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("error in parsing dateAndTime string : %v", req.GetDateAndTime()))
	}
	txnAmount := &moneyPb.Money{}
	if req.GetTxnAmount() != "" {
		amt, err := strconv.ParseFloat(req.GetTxnAmount(), 64)
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("error in parsing amount string to float: %v", req.GetTxnAmount()))
		}

		// we are dividing txnAmount by 100 here because from vendor we are getting two extra zeroes in amount string as compared to original amount (getting amount in paisa)
		txnAmount = moneyPkg.ParseFloat(amt/100, currencyCodeToCurrencyMap[req.GetCurrencyCode()])
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("error in parsing amount string : %v", req.GetTxnAmount()))
		}
	}
	txnStatus := vendorTxnStatusToBeAcsTxnStatusMap[req.GetTxnStatus()]
	if txnStatus == ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, fmt.Sprintf("acs notification txn status is unspecified : %v", txnStatus))
	}
	notificationPayload := &notifPb.AcsNotificationPayload{
		VendorIdentifier: req.GetKitNo(),
		TxnTime:          txnTime,
		TxnId:            req.GetTxnId(),
		TxnStatus:        txnStatus,
		FailureMsg:       req.GetErrMsg(),
		MerchantName:     req.GetMerchantName(),
		MerchantId:       req.GetMerchantId(),
		TxnAmount:        txnAmount,
		TxnType:          vendorTxnTypeToBeAcsTxnTypeMap[req.GetTransType()],
		AdditionalDetails: &notifPb.AdditionalDetails{
			PgUrl:      req.GetPgUrl(),
			MsgVersion: req.GetMsgVersion(),
			IpAddress:  req.GetIpAddress(),
		},
	}

	return notificationPayload, nil
}

func getDateToTimestamp(dateStr string) (*timestampPb.Timestamp, error) {
	var parsedTime time.Time
	var err error

	switch len(dateStr) {
	case len(acsNotificationDateLayoutDDMMYYYYHHMMSS):
		parsedTime, err = time.Parse(acsNotificationDateLayoutDDMMYYYYHHMMSS, dateStr)
	case len(acsNotificationDateLayoutDDMMYYYYHHMM):
		parsedTime, err = time.Parse(acsNotificationDateLayoutDDMMYYYYHHMM, dateStr)
	default:
		return nil, errors.New("invalid date format")
	}

	if err != nil {
		return nil, err
	}

	return timestampPb.New(parsedTime), nil
}
