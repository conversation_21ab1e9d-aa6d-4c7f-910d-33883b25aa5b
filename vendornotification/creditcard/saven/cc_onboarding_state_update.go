package saven

import (
	"context"
	"strings"

	"github.com/epifi/be-common/pkg/logger"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"

	ffConsumerV2Pb "github.com/epifi/gamma/api/firefly/v2/consumer"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/api/vendors/saven/creditcard"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

var (
	ccOnbStageToOnbStageEnumMap = map[string]ccEnumsV2Pb.CardRequestStage{
		"PAN_CHECK":                   ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_PAN_CHECK,
		"PRE_ELIGIBILITY_CHECK":       ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_PRE_ELIGIBILITY_CHECK,
		"EKYC":                        ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_EKYC,
		"EKYC_VERIFICATION":           ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_EKYC_VERIFICATION,
		"BRE":                         ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_BRE,
		"SELFIE_CHECK":                ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_SELFIE_CHECK,
		"VKYC":                        ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_VKYC,
		"VKYC_POLLING":                ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_VKYC_POLLING,
		"EMBOSS_NAME":                 ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_EMBOSS_NAME,
		"CIF_POLLING":                 ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_CIF_POLLING,
		"CUSTOMER_REGISTRATION":       ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_CUSTOMER_REGISTRATION,
		"SIM_BINDING":                 ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_SIM_BINDING,
		"SETUP_MPIN":                  ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_SETUP_MPIN,
		"PENNY_DROP":                  ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_PENNY_DROP,
		"ADDITIONAL_REVIEW_REQUIRED":  ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_ADDITIONAL_REVIEW_REQUIRED,
		"REJECTED":                    ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_STAGE_REJECTED,
		"PAN_SUCCESS_CHECK":           ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_PAN_SUCCESS_CHECK,
		"LIMIT_CHECK":                 ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_LIMIT_CHECK,
		"LIMIT_RE_CHECK":              ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_LIMIT_RE_CHECK,
		"CARD_ISSUED":                 ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_CARD_ISSUED,
		"PENNY_DROP_POLLING":          ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_PENNY_DROP_POLLING,
		"ONBOARDING_COMPLETED":        ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_ONBOARDING_COMPLETED,
		"CMS_READY":                   ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_CMS_READY,
		"SELECT_DELIVERY_ADDRESS":     ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_SELECT_DELIVERY_ADDRESS,
		"ADDITIONAL_DETAILS":          ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_ADDITIONAL_DETAILS,
		"TERMS_CONSENTS":              ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_TERMS_CONSENTS,
		"PERSONAL_DETAILS":            ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_PERSONAL_DETAILS,
		"CARD_CONSENTS":               ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_CARD_CONSENTS,
		"PRE_APPROVED":                ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_PRE_APPROVED,
		"VERIFY_CUSTOMER":             ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_VERIFY_CUSTOMER,
		"CUSTOMER_DETAILS":            ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_CUSTOMER_DETAILS,
		"OFFICE_ADDRESS":              ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_OFFICE_ADDRESS,
		"RE_KYC":                      ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_RE_KYC,
		"CUSTOMER_REGISTRATION_RETRY": ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_CUSTOMER_REGISTRATION_RETRY,
		"PENNY_DROP_HOLD":             ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_PENNY_DROP_HOLD,
	}

	ccOnbWorkflowStatusToOnbStatusMap = map[string]ccEnumsV2Pb.CardRequestStatus{
		"COMPLETED":   ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS,
		"IN_PROGRESS": ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
		"REJECTED":    ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
		"DISCARDED":   ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
	}
)

func (s *Service) ProcessCreditCardOnboardingStateUpdateEvent(ctx context.Context, req *creditcard.ProcessCreditCardOnboardingStateUpdateEventRequest) (*emptypb.Empty, error) {
	// Check if the request is coming from whitelisted IP
	if err := security.CheckWhiteList(ctx, s.conf.SavenWhiteList, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, status.Errorf(codes.Unauthenticated, "")
	}
	redactor.LogCallbackRequestData(ctx, req, ProcessCreditCardOnboardingStateUpdateEvent, req.GetEventPayload().GetVendorWorkflowId(), vendorsRedactor.Config)

	workflowStatus, ok := ccOnbWorkflowStatusToOnbStatusMap[strings.ToUpper(strings.TrimSpace(req.GetEventPayload().GetWorkflowPayload().GetOnboardingStatus()))]
	if !ok {
		logger.Error(ctx, "unknown credit card onboarding status", zap.String(logger.STATUS, req.GetEventPayload().GetWorkflowPayload().GetOnboardingStatus()))
		return nil, status.Errorf(codes.FailedPrecondition, "unknown credit card onboarding status")
	}

	currentStage, ok := ccOnbStageToOnbStageEnumMap[strings.ToUpper(strings.TrimSpace(req.GetEventPayload().GetWorkflowPayload().GetCurrentState()))]
	if !ok {
		logger.Error(ctx, "unknown credit card onboarding current stage", zap.String(logger.STAGE, req.GetEventPayload().GetWorkflowPayload().GetCurrentState()))
		return nil, status.Errorf(codes.FailedPrecondition, "unknown credit card onboarding current stage")
	}

	nextStage, ok := ccOnbStageToOnbStageEnumMap[strings.ToUpper(strings.TrimSpace(req.GetEventPayload().GetWorkflowPayload().GetNextState()))]
	if !ok {
		logger.Error(ctx, "unknown credit card onboarding next stage", zap.String(logger.STAGE, req.GetEventPayload().GetWorkflowPayload().GetNextState()))
		return nil, status.Errorf(codes.FailedPrecondition, "unknown credit card onboarding next stage")
	}

	msgId, err := s.ccOnboardingStateEventPublisher.Publish(ctx, &ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventRequest{
		ExternalUserId:   req.GetEventPayload().GetVendorInternalUserId(),
		VendorWorkflowId: req.GetEventPayload().GetVendorWorkflowId(),
		WorkflowStatus:   workflowStatus,
		StageTransitionInfo: &ffConsumerV2Pb.CreditCardOnboardingStageTransitionInfo{
			CurrentStage: currentStage,
			NextStage:    nextStage,
			Description:  req.GetEventPayload().GetWorkflowPayload().GetWorkflowMessage(),
		},
	})
	if err != nil {
		logger.Error(ctx, "error publishing credit card onboarding stage update event", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "system error")
	}
	logger.Info(ctx, "credit card onboarding stage update event published successfully", zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	return &emptypb.Empty{}, nil
}
