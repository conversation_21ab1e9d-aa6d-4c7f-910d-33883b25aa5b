package saven

import (
	"context"
	"time"

	"github.com/epifi/be-common/api/rpc"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	"go.uber.org/zap"

	fireflyV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	"github.com/epifi/gamma/api/vendornotification/creditcard/saven"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	creditCard "github.com/epifi/gamma/api/vendors/saven/creditcard"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type Service struct {
	saven.UnimplementedSavenCallbackServer
	ffV2Client                      fireflyV2Pb.FireflyV2Client
	conf                            *config.Config
	ccOnboardingStateEventPublisher CcOnboardingStateUpdateEventPublisher
}

type CcOnboardingStateUpdateEventPublisher queue.Publisher

func NewService(ffV2Client fireflyV2Pb.FireflyV2Client, conf *config.Config, ccOnboardingStateEventPublisher CcOnboardingStateUpdateEventPublisher) *Service {
	return &Service{
		ffV2Client:                      ffV2Client,
		conf:                            conf,
		ccOnboardingStateEventPublisher: ccOnboardingStateEventPublisher,
	}
}

const (
	// API names
	ProcessCreditCardOnboardingStateUpdateEvent = "ProcessCreditCardOnboardingStateUpdateEvent"
	GetCreditCardTrackingDetailsRequest         = "GetCreditCardTrackingDetailsRequest"

	statusCodeInternal         = "FI13"
	statusCodeInvalidArgument  = "FI03"
	statusCodeNoDataFound      = "FI05"
	statusCodePermissionDenied = "FI07"

	// DISPATCHING - awb is assigned but shipment is either not registered at shipway or yet to be picked by carrier
	dispatching = "DISPATCHING"

	// if tracking details are older than 20 days, we consider them stale
	// TODO(cb): figure out how to handle staleness when user requests a replacement in first 20 days.
	trackingDetailsStaleTTL = 20 * 24 * time.Hour
)

func (s *Service) GetCreditCardTrackingDetails(ctx context.Context, req *creditCard.GetCreditCardTrackingDetailsRequest) (*creditCard.GetCreditCardTrackingDetailsResponse, error) {
	// Check if the request is coming from whitelisted IP
	if err := security.CheckWhiteList(ctx, s.conf.SavenWhiteList, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return getResponseWithException(statusCodePermissionDenied, "Access forbidden"), nil
	}
	redactor.LogCallbackRequestData(ctx, req, GetCreditCardTrackingDetailsRequest, "", vendorsRedactor.Config)

	if req.GetInternalUserId() == "" {
		logger.Error(ctx, "mandatory params missing", zap.String("InternalUserId", req.GetInternalUserId()))
		return getResponseWithException(statusCodeInvalidArgument, "mandatory params missing"), nil
	}

	ccTrackingDetailsResp, err := s.ffV2Client.GetCreditCardTrackingDetails(ctx, &fireflyV2Pb.GetCreditCardTrackingDetailsRequest{
		Identifier: &fireflyV2Pb.GetCreditCardTrackingDetailsRequest_ExternalUserId{
			// we store vendor's internal user id as external user id in credit cards system
			ExternalUserId: req.GetInternalUserId(),
		},
	})
	if te := epifigrpc.RPCError(ccTrackingDetailsResp, err); te != nil && !rpc.StatusFromError(te).IsRecordNotFound() {
		logger.Error(ctx, "failed to get credit card tracking details", zap.Error(te))
		return getResponseWithException(statusCodeInternal, "unexpected system error"), nil
	}
	if ccTrackingDetailsResp.GetStatus().IsRecordNotFound() ||
		// Check for stale tracking details
		// TODO(cb): check with product if we need to return some intermediate state here instead of exception, for eg - PRINTING
		time.Since(ccTrackingDetailsResp.GetTrackingDetails().GetUploadedAt().AsTime()) > trackingDetailsStaleTTL {
		return getResponseWithException(statusCodeNoDataFound, "no tracking data found"), nil
	}

	if ccTrackingDetailsResp.GetTrackingDetails().GetAwb() == "" {
		return getResponseWithException(statusCodeNoDataFound, "awb not assigned"), nil
	}

	trackingData := &creditCard.TrackingData{
		PickupDate:         datetimePkg.TimestampToString(ccTrackingDetailsResp.GetTrackingDetails().GetPickupDate(), datetimePkg.DATE_LAYOUT_YYYYMMDD, datetimePkg.IST),
		CurrentStatus:      ccTrackingDetailsResp.GetTrackingDetails().GetDeliveryState().String(),
		Scans:              make([]*creditCard.Scan, 0),
		Carrier:            ccTrackingDetailsResp.GetTrackingDetails().GetCarrier(),
		CardPrintingVendor: ccTrackingDetailsResp.GetTrackingDetails().GetPrintingVendor().String(),
		AwbNumber:          ccTrackingDetailsResp.GetTrackingDetails().GetAwb(),
		ExtraFields: &creditCard.ExtraFields{
			ExpectedDeliveryDate: datetimePkg.TimestampToString(ccTrackingDetailsResp.GetTrackingDetails().GetExpectedDeliveryDate(), datetimePkg.DATE_LAYOUT_YYYYMMDD, datetimePkg.IST),
		},
		TrackingUrl: ccTrackingDetailsResp.GetTrackingDetails().GetTrackingUrl(),
	}
	if ccTrackingDetailsResp.GetTrackingDetails().GetDeliveryState() == ccEnumsV2Pb.CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED {
		trackingData.CurrentStatus = dispatching
	}

	for _, scan := range ccTrackingDetailsResp.GetTrackingDetails().GetScans() {
		trackingData.Scans = append(trackingData.Scans, &creditCard.Scan{
			ScanTime:     datetimePkg.TimestampToString(scan.GetUpdatedAt(), time.DateTime, datetimePkg.IST),
			Location:     scan.GetLocation(),
			StatusDetail: scan.GetStatusDescription(),
		})
	}

	return &creditCard.GetCreditCardTrackingDetailsResponse{
		TrackingData: trackingData,
	}, nil
}

func getResponseWithException(statusCode string, errorMsg string) *creditCard.GetCreditCardTrackingDetailsResponse {
	return &creditCard.GetCreditCardTrackingDetailsResponse{
		Exception: &creditCard.Exception{
			StatusCode:   statusCode,
			ErrorMessage: errorMsg,
		},
	}
}
