package security

import (
	"context"
	"fmt"
	"strings"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
)

const xForwardedFor = "X-Forwarded-For"

// nolint: funlen
func CheckWhiteList(
	ctx context.Context, whitelist *config.IPWhiteListing, numberOfHopsThatAddXForwardedFor int,
	vpcCidrIPPrefix string,
) error {
	var rawClientIp string
	// By pass IP check if whitelist is not enabled
	if whitelist == nil || !whitelist.EnableWhitelist {
		return nil
	}

	md, _ := metadata.FromIncomingContext(ctx)

	// GRPC metadata Header keys will automatically be converted to lower case. Conflicting values will be merged
	headerVals := md.Get(xForwardedFor)
	if len(headerVals) == 0 || len(headerVals[0]) == 0 {
		logger.Info(ctx, "Missing x-forwarded-for header")
		return isNonWhitelistedIpAllowed(whitelist)
	}

	logger.Info(ctx, "x-forwarded-for header found during IP check", zap.Strings("x-forwarded-for", headerVals))

	ipList := strings.Split(headerVals[0], ",")
	// Given that load balancers or proxies keep appending their IPs to x-forwarded-for, first IP in the list
	// gives the real IP of the client in ideal cases
	// However, an attacker can generate a header with wrong IP and pass it which can break this assumption
	// Hence, IP that epiFi infra sees at its perimeter will be the right choice
	lenIplist := len(ipList)
	if lenIplist < numberOfHopsThatAddXForwardedFor {
		// Fallback in case number of ipList is less than the number of IPs in the header
		// We are defaulting to the last entry as we are expecting at-least one IP to be added to the header by our infra
		logger.Debug(
			ctx, "Fallback to last IP entry in the header",
			zap.String(xForwardedFor, fmt.Sprintf("%v", ipList)),
		)
		rawClientIp = ipList[lenIplist-1]
	} else {
		rawClientIp = ipList[lenIplist-numberOfHopsThatAddXForwardedFor]
		// We use consul to client load balance b/w different instances
		// e.g., Each server initiates an IP watcher which fetches the IPs from Consul
		// Server will then client load balance based on the IPs fetched from Consul
		// However, in case this flow breaks, server falls back to the load balancer. This flow adds another IP
		// to x-forwarded-for
		//
		// To address this fallback scenario where an additional IP gets added, looking at IP in previous index
		// if current IP falls into the VPC CIDR range
		// Possible Optimisation: Trim the prefix in cidr prefix while initialising the service
		if strings.HasPrefix(
			strings.TrimSpace(rawClientIp), strings.TrimSpace(vpcCidrIPPrefix),
		) && lenIplist > numberOfHopsThatAddXForwardedFor {
			rawClientIp = ipList[lenIplist-numberOfHopsThatAddXForwardedFor-1]
		}
	}
	clientIp := strings.TrimSpace(rawClientIp)
	// Can be optimised by caching the list of whitelisted IPs for each method instead of parsing them in each RPC
	ips := strings.Split(whitelist.WhitelistedIPs, ",")

	for _, wlIP := range ips {
		if clientIp == strings.TrimSpace(wlIP) {
			logger.Debug(
				ctx, "Client IP whitelisted", zap.String("IP", clientIp),
				zap.String(xForwardedFor, fmt.Sprintf("%v", headerVals)),
			)
			return nil
		}
	}

	logger.WarnWithCtx(ctx, "Client IP not whitelisted", zap.String("IP", clientIp),
		zap.String(xForwardedFor, fmt.Sprintf("%v", headerVals)),
	)
	return isNonWhitelistedIpAllowed(whitelist)
}

func isNonWhitelistedIpAllowed(whitelist *config.IPWhiteListing) error {
	if !whitelist.SoftBlock {
		return status.Error(codes.PermissionDenied, "Access forbidden")
	}
	return nil
}
