package redactor

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/logger"
	redactLogger "github.com/epifi/be-common/pkg/logger/redact_logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
)

const maxPayloadToLog = 100 * 1024 // 100 KB

func LogPayload(ctx context.Context, msg string, payload proto.Message, redactionConf map[string]mask.MaskingStrategy, fields ...zap.Field) {
	marshalledVal, err := protojson.Marshal(payload)
	if err != nil {
		logger.Error(ctx, "Unable to marshal", zap.Error(err))
		return
	}
	redactedData, err := httpcontentredactor.GetInstance().Redact(ctx, marshalledVal, httpcontentredactor.ContentTypeJSON, redactionConf)
	if err != nil {
		logger.Error(ctx, "Unable to redact", zap.Error(err))
		return
	}

	redactLogger.LogSecure(zap.InfoLevel, ctx, msg, []*redactLogger.RedactionCandidateField{redactLogger.NewRedactionCandidateField(httpcontentredactor.ContentTypeJSON, logger.PAYLOAD, &marshalledVal)}, fields...)

	logger.Info(ctx, msg,
		append(fields, zap.ByteString(logger.PAYLOAD, redactedData))...,
	)
}

func LogCallbackRequestData(ctx context.Context, req proto.Message, callbackName string, requestId string, redactionConf map[string]mask.MaskingStrategy) {
	marshalledVal, err := protojson.Marshal(req)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Unable to marshal %v", callbackName))
		return
	}
	redactLogger.LogSecure(zap.InfoLevel, ctx, fmt.Sprintf("Received a callback for %v", callbackName),
		[]*redactLogger.RedactionCandidateField{redactLogger.NewRedactionCandidateField(httpcontentredactor.ContentTypeJSON, logger.PAYLOAD, &marshalledVal)},
		zap.String(logger.REQUEST_ID, requestId))

	redactedCallback, err := httpcontentredactor.GetInstance().Redact(ctx, marshalledVal, httpcontentredactor.ContentTypeJSON, redactionConf)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Unable to redact %v", callbackName))
		return
	}

	logger.Info(ctx, fmt.Sprintf("Received a callback for %v", callbackName),
		zap.String(logger.REQUEST_ID, requestId),
		zap.ByteString(logger.PAYLOAD, redactedCallback))
}

func LogCallbackXMLRawRequestData(ctx context.Context, xmlData []byte, callbackName string, requestId string, redactionConf map[string]mask.MaskingStrategy) {
	getTruncatedPayload := func(payload []byte) []byte {
		if len(payload) > maxPayloadToLog {
			return payload[:maxPayloadToLog]
		}
		return payload
	}
	payloadBytes := getTruncatedPayload(xmlData)
	redactLogger.LogSecure(zap.InfoLevel, ctx, fmt.Sprintf("Received a callback for %v", callbackName),
		[]*redactLogger.RedactionCandidateField{redactLogger.NewRedactionCandidateField(httpcontentredactor.ContentTypeXML, logger.PAYLOAD, &payloadBytes)},
		zap.String(logger.REQUEST_ID, requestId))

	redactedCallback, err := httpcontentredactor.GetInstance().Redact(ctx, xmlData, httpcontentredactor.ContentTypeXML, redactionConf)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Unable to redact %v", callbackName))
		return
	}

	logger.Info(ctx, fmt.Sprintf("Received a callback for %v", callbackName),
		zap.String(logger.REQUEST_ID, requestId),
		zap.ByteString("payload", getTruncatedPayload(redactedCallback)))
}
