package karza

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"

	"github.com/epifi/gamma/api/vendornotification/liveness/karza"
	"github.com/epifi/be-common/pkg/queue"
)

type Service struct{}

type CheckLivenessCallbackPublisher queue.Publisher

var _ karza.LivenessServer = &Service{}

func NewService() *Service {
	return &Service{}
}

func (s *Service) CheckLivenessCallback(_ context.Context, _ *karza.CheckLivenessCallbackRequest) (*empty.Empty, error) {
	return &empty.Empty{}, nil
}
