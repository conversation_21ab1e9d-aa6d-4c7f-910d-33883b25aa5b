package reward

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/google/uuid"
	errorsPkg "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/emptypb"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	fireflyV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	datacollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	vnRewardsPb "github.com/epifi/gamma/api/vendornotification/reward"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/api/vendors/reward"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

const (
	FulfillRewardRequest          = "FulfillRewardRequest"
	GetRewardStatusRequest        = "GetRewardStatusRequest"
	requestPayloadClaimsFieldName = "request_payload"
)

var (
	rewardStatusEnumToVnRewardsStatusMap = map[rewardsPb.RewardStatus]string{
		rewardsPb.RewardStatus_CREATED:            "PROCESSING_PENDING",
		rewardsPb.RewardStatus_PROCESSED:          "PROCESSED",
		rewardsPb.RewardStatus_PROCESSING_PENDING: "PROCESSING_PENDING",
		rewardsPb.RewardStatus_PROCESSING_FAILED:  "PROCESSING_FAILED",
	}

	vendorRewardTypeToRewardTypeEnumMap = map[string]rewardsPb.RewardType{
		"FI_COIN": rewardsPb.RewardType_FI_COINS,
		"CASH":    rewardsPb.RewardType_CASH,
		"VOUCHER": rewardsPb.RewardType_EGV_BASKET,
	}

	vendorOfferTypeToOfferTypeEnumMap = map[string]datacollectorPb.VendorRewardOfferType{
		"MAGNIFI_CVP_FI_COIN":                     datacollectorPb.VendorRewardOfferType_MAGNIFI_CVP_FI_COIN,
		"MAGNIFI_CVP_BILLGEN_BASE_FI_COIN":        datacollectorPb.VendorRewardOfferType_MAGNIFI_CVP_FI_COIN,
		"MAGNIFI_CVP_BILLGEN_ACCELERATED_FI_COIN": datacollectorPb.VendorRewardOfferType_MAGNIFI_CVP_FI_COIN,
		"MAGNIFI_CVP_VOUCHER":                     datacollectorPb.VendorRewardOfferType_MAGNIFI_CVP_VOUCHER,
	}
)

type Service struct {
	vnRewardsPb.UnimplementedRewardServer
	gconf                            *genconf.Config
	vendorRewardFulfillmentPublisher VendorRewardFulfillmentPublisher
	fireflyV2Client                  fireflyV2Pb.FireflyV2Client
	rewardsGenClient                 rewardsPb.RewardsGeneratorClient
	jwtRsaVerifier                   *JwtRsaVerifier
	conf                             *config.Config
}

func NewRewardService(gconf *genconf.Config, conf *config.Config, vendorRewardFulfillmentPublisher VendorRewardFulfillmentPublisher,
	fireflyV2Client fireflyV2Pb.FireflyV2Client, rewardsGenClient rewardsPb.RewardsGeneratorClient) (*Service, error) {
	jwtVerifier, err := NewJWTVerifier(conf)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error creating jwt verifier")
	}
	return &Service{
		gconf:                            gconf,
		vendorRewardFulfillmentPublisher: vendorRewardFulfillmentPublisher,
		fireflyV2Client:                  fireflyV2Client,
		rewardsGenClient:                 rewardsGenClient,
		jwtRsaVerifier:                   jwtVerifier,
		conf:                             conf,
	}, nil
}

type VendorRewardFulfillmentPublisher queue.Publisher

func (s *Service) FulfillReward(ctx context.Context, req *reward.FulfillRewardRequest) (*emptypb.Empty, error) {
	logger.Debug(ctx, fmt.Sprintf("%v token: %v", FulfillRewardRequest, req.GetToken()))

	if err := security.CheckWhiteList(ctx, s.conf.SavenWhiteList, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, epifierrors.ErrUnauthorized
	}
	reqPayloadString, err := s.getRequestPayloadClaimsFromJwtToken(req.GetToken())
	if err != nil {
		logger.Error(ctx, "failed to get request_payload from jwt token for FulfillReward request", zap.Error(err))
		return nil, err
	}

	requestPayloadProto := &reward.FulfillRewardRequestPayload{}
	protoUnmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	err = protoUnmarshaller.Unmarshal([]byte(reqPayloadString), requestPayloadProto)
	if err != nil {
		logger.Error(ctx, "error while unmarshalling request payload for FulfillReward request", zap.Error(err))
		return nil, status.Errorf(codes.FailedPrecondition, "invalid request_payload")
	}
	// log unmarshalled request payload
	redactor.LogCallbackRequestData(ctx, requestPayloadProto, FulfillRewardRequest, requestPayloadProto.GetVendorRefId(), vendorsRedactor.Config)

	rewardTriggerEventTime, err := datetimePkg.ParseStringTimeStampProto(time.RFC3339Nano, requestPayloadProto.GetEventTime())
	if err != nil {
		logger.Error(ctx, "error while parsing eventTime",
			zap.String("EventTime", requestPayloadProto.GetEventTime()),
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.Error(err))
		return nil, status.Errorf(codes.FailedPrecondition, "invalid eventTime format, expecting Iso8601")
	}

	rewardTypeEnum, ok := vendorRewardTypeToRewardTypeEnumMap[requestPayloadProto.GetRewardType()]
	if !ok {
		logger.Error(ctx, "unexpected rewardType in vendor reward fulfillment request",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.String(logger.REWARD_TYPE, requestPayloadProto.GetRewardType()))
		return nil, status.Errorf(codes.FailedPrecondition, "unexpected rewardType")
	}

	offerType, ok := vendorOfferTypeToOfferTypeEnumMap[requestPayloadProto.GetOfferType()]
	if !ok {
		logger.Error(ctx, "unexpected offerType in vendor reward fulfillment request",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.String("OfferType", requestPayloadProto.GetOfferType()))
		return nil, status.Errorf(codes.FailedPrecondition, "unexpected offerType")
	}

	vendor, ok := commonvgpb.Vendor_value[strings.ToUpper(requestPayloadProto.GetVendor())]
	if !ok {
		logger.Error(ctx, "got reward fulfillment request from unknown vendor",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.String(logger.VENDOR, requestPayloadProto.GetVendor()))
		return nil, status.Errorf(codes.Unauthenticated, "unknown vendor")
	}

	if !s.gconf.Reward().DisableDummyApiResponseFlow() {
		return s.fulfillRewardRequestDummyResponse(requestPayloadProto)
	}

	maxAllowedRewardValue, ok := s.gconf.Reward().RewardTypeToMaxRewardValueMap().Load(requestPayloadProto.GetRewardType())
	if !ok {
		logger.Error(ctx, "couldn't find the max allowed rewardValue to RewardType",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.String(logger.VENDOR, requestPayloadProto.GetVendor()),
			zap.String(logger.REWARD_TYPE, requestPayloadProto.GetRewardType()))
		return nil, status.Errorf(codes.Internal, "System error")
	}
	if maxAllowedRewardValue < requestPayloadProto.GetRewardValue() {
		logger.Error(ctx, "rewardValue received in vendor reward fulfillment request is higher than max allowed values",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.String(logger.VENDOR, requestPayloadProto.GetVendor()),
			zap.String(logger.REWARD_TYPE, requestPayloadProto.GetRewardType()),
			zap.Float64("RequestRewardValue", requestPayloadProto.GetRewardValue()),
			zap.Float64("MaxAllowedRewardValue", maxAllowedRewardValue))
		return nil, status.Errorf(codes.FailedPrecondition, "rewardValue can't be greater than max allowed rewardValue")
	}

	getCardsResp, err := s.fireflyV2Client.GetCreditCards(ctx, &fireflyV2Pb.GetCreditCardsRequest{
		Identifier: &fireflyV2Pb.GetCreditCardsRequest_ExternalUserId{
			ExternalUserId: requestPayloadProto.GetUserId(),
		},
		StateFilters: []ccEnumsV2Pb.CardState{ccEnumsV2Pb.CardState_CARD_STATE_CREATED},
	})
	if te := epifigrpc.RPCError(getCardsResp, err); te != nil {
		logger.Error(ctx, "error while fetching credit card for external user id",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.Error(te))
		return nil, status.Errorf(codes.Code(rpcPb.StatusFromError(te).GetCode()), "system error")
	}

	actorId := getCardsResp.GetCreditCards()[0].GetActorId()
	msgId, pubErr := s.vendorRewardFulfillmentPublisher.Publish(ctx, &datacollectorPb.VendorRewardFulfillmentEvent{
		ActorId:         actorId,
		Vendor:          commonvgpb.Vendor(vendor),
		OfferType:       datacollectorPb.VendorRewardOfferType(offerType),
		RewardType:      rewardTypeEnum,
		RewardValue:     requestPayloadProto.GetRewardValue(),
		RewardPackageId: requestPayloadProto.GetRewardPackageId(),
		VendorRefId:     requestPayloadProto.GetVendorRefId(),
		EventTime:       timestamp.New(rewardTriggerEventTime.AsTime().In(datetimePkg.IST)),
	})
	if pubErr != nil {
		logger.Error(ctx, "error while publishing vendor reward fulfillment event",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.Error(pubErr))
		return nil, status.Errorf(codes.Internal, "System error")
	}

	logger.Info(ctx, "published vendor reward fulfillment event",
		zap.String(logger.QUEUE_MESSAGE_ID, msgId),
		zap.String("VendorUserId", requestPayloadProto.GetUserId()),
		zap.String(logger.ACTOR_ID_V2, actorId),
		zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()))

	return &emptypb.Empty{}, nil
}

func (s *Service) GetRewardStatus(ctx context.Context, req *reward.GetRewardStatusRequest) (*reward.GetRewardStatusResponse, error) {
	logger.Debug(ctx, fmt.Sprintf("%v token: %v", GetRewardStatusRequest, req.GetToken()))

	if err := security.CheckWhiteList(ctx, s.conf.SavenWhiteList, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, epifierrors.ErrUnauthorized
	}
	reqPayloadString, err := s.getRequestPayloadClaimsFromJwtToken(req.GetToken())
	if err != nil {
		logger.Error(ctx, "failed to get request_payload from jwt token for FulfillReward request", zap.Error(err))
		return nil, err
	}
	requestPayloadProto := &reward.GetRewardStatusRequestPayload{}
	protoUnmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	err = protoUnmarshaller.Unmarshal([]byte(reqPayloadString), requestPayloadProto)
	if err != nil {
		logger.Error(ctx, "error while unmarshalling request payload for GetRewardStatus request", zap.Error(err))
		return nil, status.Errorf(codes.FailedPrecondition, "invalid request_payload")
	}
	// log unmarshalled request payload
	redactor.LogCallbackRequestData(ctx, requestPayloadProto, GetRewardStatusRequest, requestPayloadProto.GetVendorRefId(), vendorsRedactor.Config)

	if !s.gconf.Reward().DisableDummyApiResponseFlow() {
		return s.gerRewardsStatusRequestDummyResponse(requestPayloadProto)
	}

	getCardsResp, err := s.fireflyV2Client.GetCreditCards(ctx, &fireflyV2Pb.GetCreditCardsRequest{
		Identifier: &fireflyV2Pb.GetCreditCardsRequest_ExternalUserId{
			ExternalUserId: requestPayloadProto.GetUserId(),
		},
		StateFilters: []ccEnumsV2Pb.CardState{ccEnumsV2Pb.CardState_CARD_STATE_CREATED},
	})
	if te := epifigrpc.RPCError(getCardsResp, err); te != nil {
		logger.Error(ctx, "error while fetching credit card for external user id",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.Error(te))
		return nil, status.Errorf(codes.Code(rpcPb.StatusFromError(te).GetCode()), "system error")
	}
	actorId := getCardsResp.GetCreditCards()[0].GetActorId()

	getRewardsResp, err := s.rewardsGenClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId: getCardsResp.GetCreditCards()[0].GetActorId(),
		PageContext: &rpcPb.PageContextRequest{
			PageSize: 2,
		},
		FiltersV2: &rewardsPb.RewardsByActorIdRequest_FiltersV2{
			AndFilter: &rewardsPb.RewardsByActorIdRequest_Filter{
				RefIds: []string{requestPayloadProto.GetVendorRefId()},
			},
		},
	})
	if te := epifigrpc.RPCError(getRewardsResp, err); te != nil {
		logger.Error(ctx, "error while fetching rewards for actorId",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.Error(te))
		return nil, status.Errorf(codes.Code(rpcPb.StatusFromError(te).GetCode()), "system error")
	}
	if len(getRewardsResp.GetRewards()) == 0 {
		logger.Error(ctx, "no rewards found for actor with provided refIds", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()))
		return nil, status.Errorf(codes.NotFound, "reward not found")
	}
	if len(getRewardsResp.GetRewards()) > 1 {
		logger.Error(ctx, "unexpected error, multiple rewards found found for refId",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.Int(logger.COUNT, len(getRewardsResp.GetRewards())))
		return nil, status.Errorf(codes.Internal, "Unexpected error occurred")
	}

	rewardStatus, ok := rewardStatusEnumToVnRewardsStatusMap[getRewardsResp.GetRewards()[0].GetStatus()]
	if !ok {
		logger.Error(ctx, "unexpected vendor reward fulfillment status",
			zap.String("VendorUserId", requestPayloadProto.GetUserId()),
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String("VendorRefId", requestPayloadProto.GetVendorRefId()),
			zap.String(logger.REWARD_ID, getRewardsResp.GetRewards()[0].GetId()),
			zap.String(logger.REWARD_STATUS, getRewardsResp.GetRewards()[0].GetStatus().String()))
		return nil, status.Errorf(codes.Internal, "unexpected reward fulfillment status")
	}

	return &reward.GetRewardStatusResponse{
		VendorRefId:      requestPayloadProto.GetVendorRefId(),
		FulfillmentRefId: getRewardsResp.GetRewards()[0].GetExternalId(),
		Status:           rewardStatus,
	}, nil
}

func (s *Service) getRequestPayloadClaimsFromJwtToken(tokenString string) (string, error) {
	claims, err := s.jwtRsaVerifier.VerifyTokenAndGetClaims(tokenString)
	if err != nil {
		return "", status.Errorf(codes.Unauthenticated, "%v", err)
	}

	requestPayload, ok := claims[requestPayloadClaimsFieldName]
	if !ok {
		return "", status.Errorf(codes.FailedPrecondition, "invalid token claims, could not find request_payload")
	}

	requestPayloadJson, err := json.Marshal(requestPayload)
	if err != nil {
		return "", status.Errorf(codes.FailedPrecondition, "error while marshalling request_payload to JSON string: %v", err)
	}

	return string(requestPayloadJson), nil
}

func (s *Service) fulfillRewardRequestDummyResponse(_ *reward.FulfillRewardRequestPayload) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, nil
}

func (s *Service) gerRewardsStatusRequestDummyResponse(reqPayload *reward.GetRewardStatusRequestPayload) (*reward.GetRewardStatusResponse, error) {
	return &reward.GetRewardStatusResponse{
		VendorRefId:      reqPayload.GetVendorRefId(),
		FulfillmentRefId: uuid.NewString(),
		Status:           s.gconf.Reward().DummyRewardProcessingStatus(),
	}, nil
}
