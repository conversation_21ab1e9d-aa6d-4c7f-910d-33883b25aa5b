package idfc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	panNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	idfcVendorsPb "github.com/epifi/gamma/api/vendors/idfc/lending"
	lendingVendorPb "github.com/epifi/gamma/api/vendors/lending"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/redactor"
)

func (s *Service) VkycProfileStatusUpdate(
	ctx context.Context,
	req *idfcVendorsPb.VkycProfileStatusUpdatePayload,
) (*emptyPb.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, "VkycProfileStatusUpdate", req.GetPayload().GetReqId(), vendorsRedactor.Config)

	payloadBytes, err := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_IDFC,
		},
		LoansNotification: &lendingVendorPb.LoansNotification{
			Details: &lendingVendorPb.LoansNotification_IdfcVkycCallbackNotification{
				IdfcVkycCallbackNotification: req,
			},
		},
	})
	if err != nil {
		logger.Error(ctx, "error in marshalling the payload",
			zap.String(logger.REQUEST_ID, req.GetPayload().GetReqId()),
			zap.Error(err),
		)
		return nil, ErrInvalidCallbackPayload
	}

	// TODO: implement this
	notificationHashId := uuid.New().String()

	initiateResp, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.ProcessVendorNotification),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     notificationHashId,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_IDFC_PL,
			Payload:          payloadBytes,
			QualityOfService: celestialPb.QoS_GUARANTEED,
		},
	})
	if err != nil || !initiateResp.GetStatus().IsSuccess() && !initiateResp.GetStatus().IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate ProcessVendorNotification celestial workflow",
			zap.String(logger.LOAN_REQUEST_ID, req.GetPayload().GetReqId()), zap.Any(logger.RPC_STATUS, initiateResp.GetStatus()),
			zap.Error(err))
		return nil, ErrInternalError
	}

	return &emptyPb.Empty{}, nil
}
