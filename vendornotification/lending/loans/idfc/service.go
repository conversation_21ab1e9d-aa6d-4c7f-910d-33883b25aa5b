package idfc

import (
	"errors"

	celestialPb "github.com/epifi/be-common/api/celestial"
	vnIdfcLendingPb "github.com/epifi/gamma/api/vendornotification/lending/loans/idfc"
	"github.com/epifi/gamma/vendornotification/config"
)

type Service struct {
	conf            *config.Config
	celestialClient celestialPb.CelestialClient
}

var _ vnIdfcLendingPb.IdfcCallbackServer = &Service{}

func NewService(
	conf *config.Config,
	celestialClient celestialPb.CelestialClient,
) *Service {
	return &Service{
		conf:            conf,
		celestialClient: celestialClient,
	}
}

var (
	ErrInvalidCallbackPayload = errors.New("invalid callback payload")
	// ErrInternalError used for errors which are internal to us, and we don't want to expose exact error reason to
	// the vendor due to security reasons.
	ErrInternalError = errors.New("internal error")
)
