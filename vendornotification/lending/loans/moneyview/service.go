package moneyview

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/datetime"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	panNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	moneyviewVnPb "github.com/epifi/gamma/api/vendornotification/lending/loans/moneyview"
	lendingVendorPb "github.com/epifi/gamma/api/vendors/lending"
	moneyviewVendorPb "github.com/epifi/gamma/api/vendors/moneyview"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

const (
	mvDatetimeFormat = "2006-01-02 15:04:05"
)

type Service struct {
	conf            *config.Config
	celestialClient celestialPb.CelestialClient
}

// compile time check to ensure Service implements moneyviewVnPb.MoneyviewCallbackServer
var _ moneyviewVnPb.MoneyviewCallbackServer = &Service{}

func NewService(
	conf *config.Config,
	celestialClient celestialPb.CelestialClient,
) *Service {
	return &Service{
		conf:            conf,
		celestialClient: celestialClient,
	}
}

// ProcessLoanNotification processes loan notifications received from moneyview vendor.
func (s *Service) ProcessLoanNotification(ctx context.Context, req *moneyviewVendorPb.RawVendorLoanNotificationPayload) (*emptyPb.Empty, error) {
	// check if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.MoneyviewWhiteList, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, "MoneyviewProcessLoanNotification", req.GetLeadId(), vendorsRedactor.Config)

	// create moneyview loan notification from vendor notification payload.
	mvLoanNotification, err := createMoneyViewLoanNotificationFromVendorPayload(ctx, req)
	switch {
	case err != nil:
		logger.Error(ctx, "error creating loan notification from moneyview payload", zap.String(logger.REQUEST_ID, req.GetLeadId()), zap.Error(err))
		// intentionally hard-coding error message here as we don't want to expose exact error reason to the vendor due to security reasons.
		return nil, errors.New("transient failure")
	case mvLoanNotification == nil:
		logger.WarnWithCtx(ctx, "unhandled moneyview loan notification", zap.String(logger.REQUEST_ID, req.GetLeadId()))
		return &emptyPb.Empty{}, nil
	}

	callbackWorkflowPayloadBytes, payloadErr := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		LoanHeader: &palPb.LoanHeader{
			Vendor: palPb.Vendor_MONEYVIEW,
		},
		LoansNotification: &lendingVendorPb.LoansNotification{
			Details: &lendingVendorPb.LoansNotification_MoneyviewLoanNotification{
				MoneyviewLoanNotification: mvLoanNotification,
			},
		},
	})
	if payloadErr != nil {
		logger.Error(ctx, "error in marshalling the payload", zap.String(logger.REQUEST_ID, req.GetLeadId()), zap.Error(payloadErr))
		// intentionally hard-coding error message here as we don't want to expose exact error reason to the vendor due to security reasons.
		return nil, errors.New("transient failure")
	}

	// using hash of primary identifiers from notification payload to generate client request id for callback processing workflow so that workflow requests for duplicate callbacks gets de-duped implicitly to avoid re-processing thereby saving on compute resources.
	notificationHashId := crypto.GetSHA256Hash(mvLoanNotification.GetLeadId(), mvLoanNotification.GetNotificationType().String(), mvLoanNotification.GetNotificationStatus().String())
	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.ProcessVendorNotification),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     notificationHashId,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_MONEYVIEW_PL,
			Payload:          callbackWorkflowPayloadBytes,
			QualityOfService: celestialPb.QoS_GUARANTEED,
		},
	})
	if initiateErr != nil || !initiateResp.GetStatus().IsSuccess() && !initiateResp.GetStatus().IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate ProcessVendorNotification celestial workflow", zap.String(logger.LOAN_REQUEST_ID, req.GetLeadId()), zap.Any(logger.RPC_STATUS, initiateResp.GetStatus()), zap.Error(initiateErr))
		// intentionally hard-coding error message here as we don't want to expose exact error reason to the vendor due to security reasons.
		return nil, errors.New("transient failure")
	}

	return &emptyPb.Empty{}, nil
}

func createMoneyViewLoanNotificationFromVendorPayload(ctx context.Context, rawNotificationPayload *moneyviewVendorPb.RawVendorLoanNotificationPayload) (*moneyviewVendorPb.LoanNotification, error) {
	notificationType := vendorEventTypeToLoanNotificationTypeMap[rawNotificationPayload.GetEventType()]
	if notificationType == moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_UNSPECIFIED {
		logger.WarnWithCtx(ctx, "unhandled notification type", zap.String(logger.REQUEST_ID, rawNotificationPayload.GetLeadId()), zap.String(logger.EVENT_TYPE, rawNotificationPayload.GetEventType()))
		return nil, nil
	}

	notificationStatus := vendorNotificationStatusToLoanNotificationStatus[rawNotificationPayload.GetEventStatus()]
	if notificationStatus == moneyviewVendorPb.LoanNotificationStatus_LOAN_NOTIFICATION_STATUS_UNSPECIFIED {
		logger.WarnWithCtx(ctx, "unhandled notification status", zap.String(logger.REQUEST_ID, rawNotificationPayload.GetLeadId()), zap.String(logger.STATUS, rawNotificationPayload.GetEventStatus()))
		return nil, nil
	}

	notificationTimeStamp, err := datetime.ParseStringTimestampProtoInLocation(mvDatetimeFormat, rawNotificationPayload.GetTimestamp(), datetime.IST)
	if err != nil {
		return nil, fmt.Errorf("error parsing notification timestamp from raw notification payload, err : %w", err)
	}

	return &moneyviewVendorPb.LoanNotification{
		LeadId:                rawNotificationPayload.GetLeadId(),
		NotificationType:      notificationType,
		NotificationStatus:    notificationStatus,
		NotificationTimestamp: notificationTimeStamp,
	}, nil
}
