package moneyview

import moneyviewVendorPb "github.com/epifi/gamma/api/vendors/moneyview"

var vendorEventTypeToLoanNotificationTypeMap = map[string]moneyviewVendorPb.LoanNotificationType{
	"NACH":                moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_NACH_STAGE,
	"DISBURSED":           moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_DISBURSEMENT,
	"CLOSED":              moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_CLOSURE,
	"REJECTED":            moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_REJECTION,
	"EXPIRED":             moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_EXPIRY,
	"INCOME_VERIFICATION": moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_INCOME_VERIFICATION_STAGE,
	"REFRESH":             moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_OFFER_REFRESH_STAGE,
	"OFFER_SELECTED":      moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_OFFER_SELECTION_STAGE,
	"KYC":                 moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_KYC_STAGE,
	"BANK_DETAILS":        moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_BANK_DETAILS_ADDITION_STAGE,
	"AGREEMENT_READY":     moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_AGREEMENT_SIGNING_STAGE,
	"COMPLIANCE_REVIEW":   moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_COMPLIANCE_REVIEW_STAGE,
	"SUBMITTED":           moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_SUBMISSION_STAGE,
	"RESUBMITTED":         moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_RESUBMISSION_STAGE,
	"OFFERED":             moneyviewVendorPb.LoanNotificationType_LOAN_NOTIFICATION_TYPE_LOAN_APPLICATION_OFFERED,
}

var vendorNotificationStatusToLoanNotificationStatus = map[string]moneyviewVendorPb.LoanNotificationStatus{
	"IN_PROGRESS": moneyviewVendorPb.LoanNotificationStatus_LOAN_NOTIFICATION_STATUS_IN_PROGRESS,
	"FAILED":      moneyviewVendorPb.LoanNotificationStatus_LOAN_NOTIFICATION_STATUS_FAILED,
	"SUCCESS":     moneyviewVendorPb.LoanNotificationStatus_LOAN_NOTIFICATION_STATUS_SUCCESS,
}
