//nolint:dupl
package fiftyfin

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	panNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	"github.com/epifi/gamma/api/tokenizer"
	fiftyfinVnPb "github.com/epifi/gamma/api/vendornotification/lending/loans/fiftyfin"
	vendorFiftyFinPb "github.com/epifi/gamma/api/vendors/fiftyfin"
	abflVendorPb "github.com/epifi/gamma/api/vendors/lending"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

const (
	MUTUAL_FUNDS_PLEDGE_APPROVED_NOTIFICATION_TYPE = "MUTUAL-FUNDS-PLEDGE-APPROVED"
	KYC_REQUESTED_NOTIFICATION_TYPE                = "KYC-REQUESTED"
	KYC_APPROVED_NOTIFICATION_TYPE                 = "KYC-APPROVED"
	KYC_REJECTED_NOTIFICATION_TYPE                 = "KYC-REJECTED"
	KYC_EXPIRED_NOTIFICATION_TYPE                  = "KYC-EXPIRED"
	LOAN_INITIATED_NOTIFICATION_TYPE               = "LOAN-INITIATED"
	LOAN_CREATED_NOTIFICATION_TYPE                 = "LOAN-CREATED"
	LOAN_AGREEMENT_SENT_NOTIFICATION_TYPE          = "LOAN-AGREEMENT-SENT"
	LOAN_AGREEMENT_SIGNED_NOTIFICATION_TYPE        = "LOAN-AGREEMENT-SIGNED"
	LOAN_MANDATE_SENT_NOTIFICATION_TYPE            = "LOAN-MANDATE-SENT"
	LOAN_APPROVED_NOTIFICATION_TYPE                = "LOAN-APPROVED"
	LOAN_KFS_APPROVED_NOTIFICATION_TYPE            = "LOAN-KFS-APPROVED"
	LOAN_DISBURSEMENT_SENT_NOTIFICATION_TYPE       = "LOAN-DISBURSEMENT-SENT"
	RegisterLamfNotificationRequest                = "RegisterLamfNotification"
)

type Service struct {
	fiftyfinVnPb.UnimplementedFiftyFinCallbackServer
	conf            *config.Config
	celestialClient celestialPb.CelestialClient
}

func NewService(conf *config.Config, celestialClient celestialPb.CelestialClient) *Service {
	return &Service{
		conf:            conf,
		celestialClient: celestialClient,
	}
}

func (s *Service) RegisterLamfNotification(ctx context.Context, req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*emptyPb.Empty, error) {
	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.FiftyfinWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, RegisterLamfNotificationRequest, "", vendorsRedactor.Config)
	err := s.processLoanFiftyfinCallbackEvent(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to process LoansFiftyfinCallbackEvent payload: %w", zap.Error(err))
		return nil, fmt.Errorf("failed to process LoansFiftyfinCallbackEvent payload: %w", err)
	}

	return &emptyPb.Empty{}, nil
}

func (s *Service) processLoanFiftyfinCallbackEvent(ctx context.Context, req *fiftyfinVnPb.RegisterLamfNotificationRequest) error {
	var (
		notification *vendorFiftyFinPb.LoansNotification
		err          error
	)
	switch req.GetNotificationType() {
	case MUTUAL_FUNDS_PLEDGE_APPROVED_NOTIFICATION_TYPE:
		notification, err = s.getMutualFundsPledgedNotificationEvent(req)
	case KYC_REQUESTED_NOTIFICATION_TYPE:
		notification, err = s.getKycRequestNotificationEvent(req)
	case KYC_APPROVED_NOTIFICATION_TYPE:
		notification, err = s.getKycApprovedNotificationEvent(req)
	case KYC_REJECTED_NOTIFICATION_TYPE:
		notification, err = s.getKycRejectedNotificationEvent(req)
	case KYC_EXPIRED_NOTIFICATION_TYPE:
		notification, err = s.getKycExpiredNotificationEvent(req)
	case LOAN_INITIATED_NOTIFICATION_TYPE:
		notification, err = s.getLoanInitiatedNotificationEvent(req)
	case LOAN_CREATED_NOTIFICATION_TYPE:
		notification, err = s.getLoanCreatedNotificationEvent(req)
	case LOAN_AGREEMENT_SENT_NOTIFICATION_TYPE:
		notification, err = s.getLoanAgreementSentNotificationEvent(req)
	case LOAN_AGREEMENT_SIGNED_NOTIFICATION_TYPE:
		notification, err = s.getLoanAgreementSignedNotificationEvent(req)
	case LOAN_MANDATE_SENT_NOTIFICATION_TYPE:
		notification, err = s.getLoanMandateNotificationEvent(req)
	case LOAN_APPROVED_NOTIFICATION_TYPE:
		notification, err = s.getLoanApprovedNotificationEvent(req)
	case LOAN_KFS_APPROVED_NOTIFICATION_TYPE:
		notification, err = s.getLoanKfsApprovedNotificationEvent(req)
	case LOAN_DISBURSEMENT_SENT_NOTIFICATION_TYPE:
		notification, err = s.getLoanDisbursementSentNotificationEvent(req)
	default:
		return fmt.Errorf("unknown notification type received from fiftyfin: %s", req.GetNotificationType())
	}
	if err != nil {
		return fmt.Errorf("error in processing notification %s: %w", req.GetNotificationType(), err)
	}

	payloadBytes, payloadErr := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
			Vendor:      palPb.Vendor_FIFTYFIN,
		},
		LoansNotification: &abflVendorPb.LoansNotification{
			Details: &abflVendorPb.LoansNotification_FiftyfinLamfNotification{
				FiftyfinLamfNotification: notification,
			},
		},
	})
	if payloadErr != nil {
		return fmt.Errorf("error in marshalling the payload %v", zap.Error(payloadErr))
	}
	err = s.initiateCelestialWorkflow(ctx, payloadBytes)
	if err != nil {
		return fmt.Errorf("failed to initiate workflow: %w", err)
	}
	return nil
}

func (s *Service) getMutualFundsPledgedNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	mutualFundPledgeApproved := &vendorFiftyFinPb.MutualFundsPledgeApprovedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), mutualFundPledgeApproved)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}

	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_MUTUAL_FUNDS_PLEDGE_APPROVED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_MutualFundsPledgeApproved{
			MutualFundsPledgeApproved: mutualFundPledgeApproved,
		},
	}, nil
}

func (s *Service) getKycRequestNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	kycRequest := &vendorFiftyFinPb.KycRequestedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), kycRequest)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_KYC_REQUESTED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_KycRequestedDetails{
			KycRequestedDetails: kycRequest,
		},
	}, nil
}

func (s *Service) getKycApprovedNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	kycApproved := &vendorFiftyFinPb.KycApprovedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), kycApproved)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_KYC_APPROVED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_KycApprovedDetails{
			KycApprovedDetails: kycApproved,
		},
	}, nil
}

func (s *Service) getKycRejectedNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	kycRejectedDetails := &vendorFiftyFinPb.KycRejectedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), kycRejectedDetails)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_KYC_REJECTED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_KycRejectedDetails{
			KycRejectedDetails: kycRejectedDetails,
		},
	}, nil
}

func (s *Service) getKycExpiredNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	kycExpiredDetails := &vendorFiftyFinPb.KycExpiredDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), kycExpiredDetails)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_KYC_EXPIRED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_KycExpiredDetails{
			KycExpiredDetails: kycExpiredDetails,
		},
	}, nil
}

func (s *Service) getLoanInitiatedNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	loanInitiated := &vendorFiftyFinPb.LoanInitiatedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), loanInitiated)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_LOAN_INITIATED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_LoanInitiatedDetails{
			LoanInitiatedDetails: loanInitiated,
		},
	}, nil
}

func (s *Service) getLoanCreatedNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	loanCreated := &vendorFiftyFinPb.LoanCreatedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), loanCreated)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_LOAN_CREATED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_LoanCreatedDetails{
			LoanCreatedDetails: loanCreated,
		},
	}, nil
}

func (s *Service) getLoanAgreementSentNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	loanAgreementDetails := &vendorFiftyFinPb.LoanAgreementSentDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), loanAgreementDetails)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_LOAN_AGREEMENT_SENT,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_LoanAgreementSentDetails{
			LoanAgreementSentDetails: loanAgreementDetails,
		},
	}, nil
}

func (s *Service) getLoanAgreementSignedNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	loanAgreementDetails := &vendorFiftyFinPb.LoanAgreementSignedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), loanAgreementDetails)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_LOAN_AGREEMENT_SIGNED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_LoanAgreementSignedDetails{
			LoanAgreementSignedDetails: loanAgreementDetails,
		},
	}, nil
}

func (s *Service) getLoanMandateNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	loanMandateDetails := &vendorFiftyFinPb.LoanMandateSentDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), loanMandateDetails)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_LOAN_MANDATE_SENT,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_LoanMandateSentDetails{
			LoanMandateSentDetails: loanMandateDetails,
		},
	}, nil
}

func (s *Service) getLoanApprovedNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	loanApprovedDetails := &vendorFiftyFinPb.LoanApprovedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), loanApprovedDetails)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_LOAN_APPROVED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_LoanApprovedDetails{
			LoanApprovedDetails: loanApprovedDetails,
		},
	}, nil
}

func (s *Service) getLoanKfsApprovedNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	loanKfsApprovedDetails := &vendorFiftyFinPb.KfsApprovedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), loanKfsApprovedDetails)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_KFS_APPROVED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_KfsApprovedDetails{
			KfsApprovedDetails: loanKfsApprovedDetails,
		},
	}, nil
}

func (s *Service) getLoanDisbursementSentNotificationEvent(req *fiftyfinVnPb.RegisterLamfNotificationRequest) (*vendorFiftyFinPb.LoansNotification, error) {
	dataDict := strings.ReplaceAll(req.GetDataDict(), "'", "\"")
	loanDisbursementSentDetails := &vendorFiftyFinPb.LoanDisbursedDetails{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(dataDict), loanDisbursementSentDetails)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling data_dict: %w", err)
	}
	return &vendorFiftyFinPb.LoansNotification{
		Type:   vendorFiftyFinPb.LoansNotificationType_LOANS_NOTIFICATION_TYPE_LOAN_DISBURSED,
		UserId: req.GetUserId(),
		Details: &vendorFiftyFinPb.LoansNotification_LoanDisbursedDetails{
			LoanDisbursedDetails: loanDisbursementSentDetails,
		},
	}, nil
}

func (s *Service) initiateCelestialWorkflow(ctx context.Context, payloadBytes []byte) error {
	// will use hash value, so that it would not start wf again if vendor sends the same request again
	hashedVal := crypto.GetSHA256Hash(tokenizer.Scope_FI_APP.String(), tokenizer.StorageType_TRANSIENT.String(), string(payloadBytes))

	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: "",
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.ProcessVendorNotification),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     hashedVal,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_FIFTYFIN_LAMF,
			Payload:          payloadBytes,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil && !rpc.StatusFromError(te).IsAlreadyExists() {
		logger.Error(ctx, "workflow initiation failure ", zap.Error(te))
		return fmt.Errorf("workflow initiation failure: %w", te)
	}

	return nil
}
