package abfl

//nolint:depguard
import (
	"context"
	"strings"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	panNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	abflVnPb "github.com/epifi/gamma/api/vendornotification/lending/loans/abfl"
	vendorAbflPb "github.com/epifi/gamma/api/vendors/abfl/lending"
	abflVendorPb "github.com/epifi/gamma/api/vendors/lending"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type Service struct {
	abflVnPb.UnimplementedAbflCallbackServer
	conf            *config.Config
	celestialClient celestialPb.CelestialClient
}

func NewService(conf *config.Config, celestialClient celestialPb.CelestialClient) *Service {
	return &Service{
		conf:            conf,
		celestialClient: celestialClient,
	}
}

//nolint:funlen,dupl
func (s *Service) ProcessAbflBreCallback(ctx context.Context, breDetails *vendorAbflPb.BrePollingResponse) (*vendorAbflPb.CallbackResponse, error) {
	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.AbflWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("IP"),
		}, err
	}

	redactor.LogCallbackRequestData(ctx, breDetails, "ProcessAbflBreCallback", breDetails.GetData().GetAccountId(), vendorsRedactor.Config)

	payloadBytes, payloadErr := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		RequestHeader: &activity.RequestHeader{
			ClientReqId: breDetails.GetData().GetAccountId(),
		},
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_ABFL,
		},
		LoansNotification: &abflVendorPb.LoansNotification{
			Details: &abflVendorPb.LoansNotification_AbflBreCallbackResponse{
				AbflBreCallbackResponse: breDetails,
			},
		},
	})
	if payloadErr != nil {
		logger.Error(ctx, "error in marshalling the payload", zap.Error(payloadErr))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("FAILED"),
		}, nil
	}

	// will use hash value, so that it would not start wf again if vendor sends the same request again
	hashedVal := crypto.GetSHA256Hash(string(payloadBytes))

	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: "",
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.VendorCallbackProcessor),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     hashedVal,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_LOANS_ABFL,
			Payload:          payloadBytes,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil && !rpc.StatusFromError(te).IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate workflow", zap.Error(te))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("INTERNAL"),
		}, nil
	}
	logger.Info(ctx, "initiated VendorCallbackProcessor workflow for ProcessAbflBreCallback", zap.String(logger.WORKFLOW_REQ_ID, initiateResp.GetParams().GetWorkflowRequestId()), zap.String(logger.ACCOUNT_ID, breDetails.GetData().GetAccountId()))

	return &vendorAbflPb.CallbackResponse{
		Status: responseStatus("SUCCESS"),
	}, nil
}

//nolint:funlen,dupl
func (s *Service) ProcessAbflMandateCallback(ctx context.Context, mandateDetails *vendorAbflPb.EmandateStatusResponse) (*vendorAbflPb.CallbackResponse, error) {
	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.AbflWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("IP"),
		}, err
	}

	redactor.LogCallbackRequestData(ctx, mandateDetails, "ProcessAbflMandateCallback", mandateDetails.GetData().GetRazorpayCustId(), vendorsRedactor.Config)
	// TODO(@anupam): Add other terminal cases when available
	if strings.ToLower(mandateDetails.GetData().GetTokenStatus()) != "confirmed" {
		// no need to proceed with non-terminal case as workflow signal is only honoured once
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("SUCCESS"),
		}, nil
	}

	payloadBytes, payloadErr := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		RequestHeader: &activity.RequestHeader{
			ClientReqId: mandateDetails.GetData().GetRazorpayCustId(),
		},
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_ABFL,
		},
		LoansNotification: &abflVendorPb.LoansNotification{
			Details: &abflVendorPb.LoansNotification_AbflMandateCallbackResponse{
				AbflMandateCallbackResponse: mandateDetails,
			},
		},
	})
	if payloadErr != nil {
		logger.Error(ctx, "error in marshalling the payload", zap.Error(payloadErr))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("FAILED"),
		}, nil
	}

	// will use hash value, so that it would not start wf again if vendor sends the same request again
	hashedVal := crypto.GetSHA256Hash(string(payloadBytes))

	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: "",
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.VendorCallbackProcessor),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     hashedVal,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_LOANS_ABFL,
			Payload:          payloadBytes,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil && !rpc.StatusFromError(te).IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate workflow", zap.Error(te))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("INTERNAL"),
		}, nil
	}
	logger.Info(ctx, "initiated VendorCallbackProcessor workflow for ProcessAbflMandateCallback", zap.String(logger.WORKFLOW_REQ_ID, initiateResp.GetParams().GetWorkflowRequestId()), zap.String(logger.CUSTOMER_ID, mandateDetails.GetData().GetRazorpayCustId()))

	return &vendorAbflPb.CallbackResponse{
		Status: responseStatus("SUCCESS"),
	}, nil
}

//nolint:funlen,dupl
func (s *Service) ProcessCkycCallback(ctx context.Context, ckycDetails *vendorAbflPb.PollingStatusCkycResponse) (*vendorAbflPb.CallbackResponse, error) {
	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.AbflWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("IP"),
		}, err
	}

	redactor.LogCallbackRequestData(ctx, ckycDetails, "ProcessCkycCallback", ckycDetails.GetData().GetAccountId(), vendorsRedactor.Config)

	payloadBytes, payloadErr := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		RequestHeader: &activity.RequestHeader{
			ClientReqId: ckycDetails.GetData().GetAccountId(),
		},
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_ABFL,
		},
		LoansNotification: &abflVendorPb.LoansNotification{
			Details: &abflVendorPb.LoansNotification_AbflCkycCallbackResponse{
				AbflCkycCallbackResponse: ckycDetails,
			},
		},
	})
	if payloadErr != nil {
		logger.Error(ctx, "error in marshalling the payload", zap.Error(payloadErr))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("FAILED"),
		}, nil
	}

	// will use hash value, so that it would not start wf again if vendor sends the same request again
	hashedVal := crypto.GetSHA256Hash(string(payloadBytes))

	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: "",
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.VendorCallbackProcessor),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     hashedVal,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_LOANS_ABFL,
			Payload:          payloadBytes,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil && !rpc.StatusFromError(te).IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate workflow", zap.Error(te))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("INTERNAL"),
		}, nil
	}
	logger.Info(ctx, "initiated VendorCallbackProcessor workflow for ProcessCkycCallback", zap.String(logger.WORKFLOW_REQ_ID, initiateResp.GetParams().GetWorkflowRequestId()), zap.String(logger.CUSTOMER_ID, ckycDetails.GetData().GetAccountId()))

	return &vendorAbflPb.CallbackResponse{
		Status: responseStatus("SUCCESS"),
	}, nil
}

//nolint:funlen,dupl
func (s *Service) ProcessAbflUkycCallback(ctx context.Context, req *vendorAbflPb.UnifiedKycStatusResponse) (*vendorAbflPb.CallbackResponse, error) {
	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.AbflWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("IP"),
		}, err
	}

	redactor.LogCallbackRequestData(ctx, req, "ProcessAbflUkycCallback", req.GetData().GetTransactionId(), vendorsRedactor.Config)

	payloadBytes, payloadErr := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		RequestHeader: &activity.RequestHeader{
			ClientReqId: req.GetData().GetTransactionId(),
		},
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_ABFL,
		},
		LoansNotification: &abflVendorPb.LoansNotification{
			Details: &abflVendorPb.LoansNotification_AbflUkycCallbackResponse{
				AbflUkycCallbackResponse: req,
			},
		},
	})
	if payloadErr != nil {
		logger.Error(ctx, "error in marshalling the payload", zap.Error(payloadErr))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("FAILED"),
		}, nil
	}

	// will use hash value, so that it would not start wf again if vendor sends the same request again
	hashedVal := crypto.GetSHA256Hash(string(payloadBytes))

	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: "",
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.VendorCallbackProcessor),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     hashedVal,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_LOANS_ABFL,
			Payload:          payloadBytes,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil && !rpc.StatusFromError(te).IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate workflow", zap.Error(te))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("INTERNAL"),
		}, nil
	}
	logger.Info(ctx, "initiated VendorCallbackProcessor workflow for ProcessAbflUkycCallback", zap.String(logger.WORKFLOW_REQ_ID, initiateResp.GetParams().GetWorkflowRequestId()), zap.String(logger.CUSTOMER_ID, req.GetData().GetTransactionId()))

	return &vendorAbflPb.CallbackResponse{
		Status: responseStatus("SUCCESS"),
	}, nil
}

//nolint:funlen,dupl
func (s *Service) ProcessAbflImpsCallback(ctx context.Context, req *vendorAbflPb.ImpsCallback) (*vendorAbflPb.CallbackResponse, error) {
	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.AbflWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("IP"),
		}, err
	}

	redactor.LogCallbackRequestData(ctx, req, "ProcessAbflImpsCallback", req.GetData().GetUtr(), vendorsRedactor.Config)

	payloadBytes, payloadErr := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		RequestHeader: &activity.RequestHeader{
			ClientReqId: req.GetData().GetUtr(),
		},
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_ABFL,
		},
		LoansNotification: &abflVendorPb.LoansNotification{
			Details: &abflVendorPb.LoansNotification_AbflImpsCallbackResponse{
				AbflImpsCallbackResponse: req,
			},
		},
	})
	if payloadErr != nil {
		logger.Error(ctx, "error in marshalling the payload", zap.Error(payloadErr))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("FAILED"),
		}, nil
	}

	// will use hash value, so that it would not start wf again if vendor sends the same request again
	hashedVal := crypto.GetSHA256Hash(string(payloadBytes))

	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: "",
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.VendorCallbackProcessor),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     hashedVal,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_LOANS_ABFL,
			Payload:          payloadBytes,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil && !rpc.StatusFromError(te).IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate workflow", zap.Error(te))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("INTERNAL"),
		}, nil
	}
	logger.Info(ctx, "initiated VendorCallbackProcessor workflow for ProcessAbflImpsCallback", zap.String(logger.WORKFLOW_REQ_ID, initiateResp.GetParams().GetWorkflowRequestId()), zap.String(logger.CUSTOMER_ID, req.GetData().GetUtr()))

	return &vendorAbflPb.CallbackResponse{
		Status: responseStatus("SUCCESS"),
	}, nil
}

//nolint:funlen,dupl
func (s *Service) ProcessAbflDigiSignCallback(ctx context.Context, digisignDetails *vendorAbflPb.DigitalSignCallbackRequest) (*vendorAbflPb.DigitalSignCallbackResponse, error) {
	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.AbflWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return &vendorAbflPb.DigitalSignCallbackResponse{
			Status: responseStatus("IP"),
		}, err
	}

	redactor.LogCallbackRequestData(ctx, digisignDetails, "ProcessAbflDigiSignCallback", digisignDetails.GetData().GetAccountId(), vendorsRedactor.Config)

	return &vendorAbflPb.DigitalSignCallbackResponse{
		Status: responseStatus("SUCCESS"),
	}, nil
}

//nolint:funlen,dupl
func (s *Service) ProcessAbflPwaJourneyStatusCallback(ctx context.Context, req *vendorAbflPb.PwaJourneyStatusCallbackRequest) (*vendorAbflPb.CallbackResponse, error) {
	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.AbflWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("IP"),
		}, err
	}

	redactor.LogCallbackRequestData(ctx, req, "ProcessAbflPwaJourneyStatusCallback", req.GetCustomerId(), vendorsRedactor.Config)

	payloadBytes, payloadErr := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		RequestHeader: &activity.RequestHeader{
			ClientReqId: req.GetCustomerId(),
		},
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_ABFL,
		},
		LoansNotification: &abflVendorPb.LoansNotification{
			Details: &abflVendorPb.LoansNotification_AbflPwaJourneyStatusCallbackRequest{
				AbflPwaJourneyStatusCallbackRequest: req,
			},
		},
	})
	if payloadErr != nil {
		logger.Error(ctx, "error in marshalling the payload", zap.Error(payloadErr), zap.String(logger.CUSTOMER_ID, req.GetCustomerId()))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("FAILED"),
		}, nil
	}

	// will use hash value, so that it would not start wf again if vendor sends the same request again
	hashedVal := crypto.GetSHA256Hash(string(payloadBytes))

	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: "",
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.VendorCallbackProcessor),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     hashedVal,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_LOANS_ABFL,
			Payload:          payloadBytes,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil && !rpc.StatusFromError(te).IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate workflow", zap.Error(te), zap.String(logger.CUSTOMER_ID, req.GetCustomerId()))
		return &vendorAbflPb.CallbackResponse{
			Status: responseStatus("INTERNAL"),
		}, nil
	}
	logger.Info(ctx, "initiated VendorCallbackProcessor workflow for ProcessAbflPwaJourneyStatusCallback", zap.String(logger.WORKFLOW_REQ_ID, initiateResp.GetParams().GetWorkflowRequestId()), zap.String(logger.CUSTOMER_ID, req.GetCustomerId()))

	return &vendorAbflPb.CallbackResponse{
		Status: responseStatus("SUCCESS"),
	}, nil
}

func responseStatus(resp string) string {
	switch resp {
	case "SUCCESS":
		return "SUCCESS"
	case "FAILED":
		return "FAILED"
	case "IP":
		return "INVALID_IP"
	case "INTERNAL":
		return "INTERNAL_SERVER_ERROR"
	}
	return ""
}
