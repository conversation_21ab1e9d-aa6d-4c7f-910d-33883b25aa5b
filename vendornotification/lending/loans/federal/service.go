package federal

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	structPb "google.golang.org/protobuf/types/known/structpb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	federalPb "github.com/epifi/gamma/api/vendornotification/lending/loans/federal"
	vendorsFederal "github.com/epifi/gamma/api/vendors/federal"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"

	"github.com/golang/protobuf/ptypes/empty"
)

type Service struct {
	federalPb.UnimplementedLoansServer
	conf *config.Config
}

func NewService(conf *config.Config) *Service {
	return &Service{conf: conf}
}

const (
	ProcessLoanInboundTxnRequest   = "ProcessLoanInboundTxnRequest"
	loanCustomerAndAccountCreation = "LoanCustomerAndAccountCreation"
)

var errInternalServer = errors.New("internal server error")

func (s *Service) ProcessLoanInboundTxn(ctx context.Context, req *vendorsFederal.ProcessInboundTxnRequest) (*empty.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, ProcessLoanInboundTxnRequest, req.GetRequestId(), vendorsRedactor.Config)

	if req.SenderCode != s.conf.Secrets.Ids[config.SenderCodeLoans] {
		logger.Error(ctx, fmt.Sprintf("Received invalid sendor code: %s", req.SenderCode))
		return nil, errInternalServer
	}

	return &empty.Empty{}, nil
}

// LoanCustomerAndAccountCreation federal callback api for ntb loans flow which they use for sending callbacks for both customer creation and loan account creation
func (s *Service) LoanCustomerAndAccountCreation(ctx context.Context, req *structPb.Struct) (*empty.Empty, error) {
	marshalledVal, err := req.MarshalJSON()
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s: unable to marshal %v", loanCustomerAndAccountCreation, marshalledVal))
		return nil, errInternalServer
	}

	logger.SecureInfo(ctx, commonvgpb.Vendor_FEDERAL_BANK, fmt.Sprintf("received a callback for %s", loanCustomerAndAccountCreation), zap.ByteString(logger.PAYLOAD, marshalledVal))

	return &empty.Empty{}, nil
}
