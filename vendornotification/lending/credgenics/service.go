// nolint: dupl
package credgenics

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/stream"

	"github.com/epifi/gamma/api/collection"
	collectionTypesPb "github.com/epifi/gamma/api/collection/types"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	credgenicsVnPb "github.com/epifi/gamma/api/vendornotification/lending/credgenics"
	credgenicsTypesPb "github.com/epifi/gamma/api/vendors/credgenics/types"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
	vnTypes "github.com/epifi/gamma/vendornotification/types"
)

type Service struct {
	credgenicsVnPb.UnimplementedCredgenicsWebhookServer
	emailCallbackStream        stream.Producer
	smsCallbackStream          stream.Producer
	whatsappCallbackStream     stream.Producer
	callingCallbackStream      stream.Producer
	voiceMessageCallbackStream stream.Producer
	conf                       *config.Config
	palClient                  preapprovedloan.PreApprovedLoanClient
	collectionClient           collection.CollectionClient
}

var billzyPaymentModeToPaymentProtocol = map[string]payment.PaymentProtocol{
	"UPI":              payment.PaymentProtocol_UPI,
	"DEBIT_CARD":       payment.PaymentProtocol_CARD,
	"INTERNET_BANKING": payment.PaymentProtocol_INTRA_BANK,
}

func NewService(emailCallbackStream vnTypes.EmailCallbackStreamProducer,
	smsCallbackStream vnTypes.SmsCallbackStreamProducer,
	whatsappCallbackStream vnTypes.WhatsappCallbackStreamProducer,
	callingCallbackStream vnTypes.CallingCallbackStreamProducer,
	voiceMessageCallbackStream vnTypes.VoiceMessageCallbackStreamProducer, conf *config.Config, palClient preapprovedloan.PreApprovedLoanClient, collectionClient collection.CollectionClient) *Service {
	return &Service{
		emailCallbackStream:        emailCallbackStream,
		smsCallbackStream:          smsCallbackStream,
		whatsappCallbackStream:     whatsappCallbackStream,
		callingCallbackStream:      callingCallbackStream,
		voiceMessageCallbackStream: voiceMessageCallbackStream,
		conf:                       conf,
		palClient:                  palClient,
		collectionClient:           collectionClient,
	}
}

type commonEventPayload interface {
	GetEventId() string
	GetTimestamp() *timestamppb.Timestamp
	GetLoanId() string
	credgenicsTypesPb.PiiRedactable
	proto.Message
}

const (
	retryDelay             = 20 * time.Second
	stockGuardianCompanyId = "eee5667c-3bd3-4e78-89cb-b52f9c4b9ba6"
	epifiCompanyId         = "5ba28559-55c1-43c2-a91d-214601e1509a"
)

func (s *Service) ProcessEmailEvent(ctx context.Context, req *credgenicsTypesPb.EmailEventPayload) (*emptyPb.Empty, error) {
	if err := s.preprocessRequest(ctx, req, "ProcessEmailEvent"); err != nil {
		return nil, err
	}

	req.Timestamp = timestamppb.Now()

	if err := publishMessageToStream(ctx, req, s.emailCallbackStream); err != nil {
		logger.Error(ctx, "Error publishing email to callback stream", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error publishing email to callback stream: %s", err))
	}
	return &emptyPb.Empty{}, nil
}

func (s *Service) ProcessSmsEvent(ctx context.Context, req *credgenicsTypesPb.SmsEventPayload) (*emptyPb.Empty, error) {
	if err := s.preprocessRequest(ctx, req, "ProcessSmsEvent"); err != nil {
		return nil, err
	}

	req.Timestamp = timestamppb.Now()

	if err := publishMessageToStream(ctx, req, s.smsCallbackStream); err != nil {
		logger.Error(ctx, "Error publishing sms to callback stream", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error publishing sms to callback stream: %s", err))
	}
	return &emptyPb.Empty{}, nil
}

func (s *Service) ProcessWhatsappEvent(ctx context.Context, req *credgenicsTypesPb.WhatsappEventPayload) (*emptyPb.Empty, error) {
	if err := s.preprocessRequest(ctx, req, "ProcessWhatsappEvent"); err != nil {
		return nil, err
	}

	req.Timestamp = timestamppb.Now()

	if err := publishMessageToStream(ctx, req, s.whatsappCallbackStream); err != nil {
		logger.Error(ctx, "Error publishing whatsapp event to callback stream", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error publishing whatsapp event to callback stream: %s", err))
	}
	return &emptyPb.Empty{}, nil
}

func (s *Service) ProcessCallingEvent(ctx context.Context, req *credgenicsTypesPb.CallingEventPayload) (*emptyPb.Empty, error) {
	if err := s.preprocessRequest(ctx, req, "ProcessCallingEvent"); err != nil {
		return nil, err
	}

	req.Timestamp = timestamppb.Now()

	if err := publishMessageToStream(ctx, req, s.callingCallbackStream); err != nil {
		logger.Error(ctx, "Error publishing calling event to callback stream", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error publishing calling event to callback stream: %s", err))
	}
	return &emptyPb.Empty{}, nil
}

func (s *Service) ProcessVoiceMessageEvent(ctx context.Context, req *credgenicsTypesPb.VoiceMessageEventPayload) (*emptyPb.Empty, error) {
	if err := s.preprocessRequest(ctx, req, "ProcessVoiceMessageEvent"); err != nil {
		return nil, err
	}

	req.Timestamp = timestamppb.Now()

	if err := publishMessageToStream(ctx, req, s.voiceMessageCallbackStream); err != nil {
		logger.Error(ctx, "Error publishing voice message event to callback stream", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error publishing voice message event to callback stream: %s", err))
	}
	return &emptyPb.Empty{}, nil
}
func (s *Service) ProcessBillzyPaymentEvent(ctx context.Context, req *credgenicsTypesPb.BillzyPaymentEventPayload) (*emptyPb.Empty, error) {

	// Check if request is coming from whitelisted ip
	if err := security.CheckWhiteList(ctx, s.conf.CredgenicsWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		logger.Error(ctx, "IP whitelisting check failed for credgenics ProcessBillzyPaymentEvent api call", zap.Error(err),
			zap.Any(logger.REFERENCE_ID, req.GetOrderId()))
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, "BillzyPaymentEventPayload", req.GetLoanId(), vendorsRedactor.Config)

	// if payment status is not success no need to inform lender
	if strings.ToLower(req.GetStatus()) != "success" {
		logger.Info(ctx, "payment status is not success")
		return nil, errors.New("payment status is not success")
	}

	// check if request is coming from whitelisted IPs
	dateString := req.GetPgSuccessDatetime()

	layout := "2006-01-02 15:04:05.000000"

	t, err := time.ParseInLocation(layout, dateString, datetime.IST)
	if err != nil {
		logger.Error(ctx, "failed parsing recovery date string field into time object", zap.String("dateString", dateString), zap.Error(err))
		return nil, errors.New("error parsing recovery date field")

	}

	// We are getting diff company id to segregate between the two credgenics accounts of stock guardian and epifi
	productVendor := s.getProductVendorFromCredgenicsCompanyId(req.GetCompanyId())
	if productVendor == commonvgpb.Vendor_VENDOR_UNSPECIFIED {
		logger.Error(ctx, "failed to get the product vendor corresponding to the given company id", zap.String("company_id", req.GetCompanyId()))
		return nil, errors.New("failed to get the product vendor corresponding to the given company id")
	}

	timestamp := timestamppb.New(t)
	leadDetailsRes, err := s.collectionClient.GetLeadDetails(ctx, &collection.GetLeadDetailsRequest{
		VendorReqId: req.GetLoanId(),
		Vendor:      commonvgpb.Vendor_CREDGENICS,
		CollectionHeader: &collectionTypesPb.Header{
			CollectionVendor: commonvgpb.Vendor_CREDGENICS,
			Product:          collectionTypesPb.Product_PRODUCT_LOAN,
			ProductVendor:    productVendor,
		},
	})
	if err = epifigrpc.RPCError(leadDetailsRes, err); err != nil {
		return nil, errors.New("failed fetching lead details")
	}

	palReq := preapprovedloan.ProcessOffAppLoanRepaymentRequest{
		LoanAccountId:      leadDetailsRes.GetLead().GetAccountId(),
		RepaidAmount:       pkgMoney.ParseFloat32(req.GetAmount(), pkgMoney.RupeeCurrencyCode),
		PaymentTime:        timestamp,
		PaymentReferenceId: req.GetOrderId(),
		PaymentProtocol:    billzyPaymentModeToPaymentProtocol[req.GetPaymentMode()],
		PaymentProvenance:  enums.PaymentProvenance_PAYMENT_PROVENANCE_BILLZY,
		TxnSettlementTime:  timestamp,
	}

	loanRepaymentresp, err := s.palClient.ProcessOffAppLoanRepayment(ctx, &palReq)
	if err = epifigrpc.RPCError(loanRepaymentresp, err); err != nil && !loanRepaymentresp.GetStatus().IsAlreadyExists() {
		logger.Error(ctx, "failed calling pal client")
		return nil, errors.New("failed calling pal client")
	}
	return &emptyPb.Empty{}, nil
}
func publishMessageToStream(ctx context.Context, message stream.Message, producer stream.Producer) error {
	_, publishErr := producer.PublishMessage(ctx, message)
	if publishErr == nil {
		return nil
	}
	if strings.Contains(publishErr.Error(), "ProvisionedThroughputExceededException") {
		for attempt := 1; attempt <= 3; attempt++ {
			logger.Info(ctx, fmt.Sprintf("rate limit exceeded. Attempt %d: Retrying after %v ...", attempt, retryDelay))
			time.Sleep(retryDelay)
			_, err := producer.PublishMessage(ctx, message)
			if err == nil {
				return nil
			}
			publishErr = err
		}
	}
	return publishErr
}

func (s *Service) preprocessRequest(ctx context.Context, req commonEventPayload, apiName string) error {
	// check if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.CredgenicsWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		logger.Error(ctx, fmt.Sprintf("IP whitelisting check failed for credgenics %s api call", apiName), zap.Error(err),
			zap.Any(logger.EVENT_ID, req.GetEventId()))
		return err
	}
	redactor.LogCallbackRequestData(ctx, req, "Credgenics"+apiName, req.GetLoanId(), vendorsRedactor.Config)
	req.Redact()
	return nil
}

func (s *Service) getProductVendorFromCredgenicsCompanyId(companyId string) commonvgpb.Vendor {
	switch companyId {
	case stockGuardianCompanyId:
		return commonvgpb.Vendor_STOCK_GUARDIAN_LSP
	case epifiCompanyId:
		return commonvgpb.Vendor_LIQUILOANS
	default:
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED
	}
}
