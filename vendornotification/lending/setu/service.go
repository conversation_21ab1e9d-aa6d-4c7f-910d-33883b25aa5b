package setu

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/crypto"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	panNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	lendingVendorPb "github.com/epifi/gamma/api/vendors/lending"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/api/vendors/setu/lending"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"
)

type Service struct {
	conf            *config.Config
	celestialClient celestialPb.CelestialClient
}

func NewService(conf *config.Config, celestialClient celestialPb.CelestialClient) *Service {
	return &Service{
		conf:            conf,
		celestialClient: celestialClient,
	}
}

func (s *Service) ProcessJourneyStatusCallback(ctx context.Context, req *lending.JourneyStatusCallbackRequest) (*emptyPb.Empty, error) {
	// check if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.conf.SetuWhiteList, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, "ProcessJourneyStatusCallback", req.GetConsentId(), vendorsRedactor.Config)

	callbackWorkflowPayloadBytes, payloadErr := protojson.Marshal(&palWorkflowPb.VendorNotificationsPayload{
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			Vendor:      palPb.Vendor_FEDERAL,
		},
		LoansNotification: &lendingVendorPb.LoansNotification{
			Details: &lendingVendorPb.LoansNotification_SetuCallbackPayload{
				SetuCallbackPayload: req,
			},
		},
	})
	if payloadErr != nil {
		logger.Error(ctx, "error in marshalling the payload", zap.String(logger.REQUEST_ID, req.GetConsentId()), zap.Error(payloadErr))
		// intentionally hard-coding error message here as we don't want to expose exact error reason to the vendor due to security reasons.
		return nil, errors.New("transient failure")
	}

	// hashing the callback payload received from the vendor and using it as client request id, so that workflow execution
	// wouldn't be triggered if same callback is received multiple times
	notificationHashId := crypto.GetSHA256Hash(string(callbackWorkflowPayloadBytes))
	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(panNs.ProcessVendorNotification),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     notificationHashId,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        commontypes.Ownership_FEDERAL_BANK,
			Payload:          callbackWorkflowPayloadBytes,
			QualityOfService: celestialPb.QoS_GUARANTEED,
		},
	})
	if initiateErr != nil || !initiateResp.GetStatus().IsSuccess() && !initiateResp.GetStatus().IsAlreadyExists() {
		logger.Error(ctx, "failed to initiate ProcessVendorNotification celestial workflow", zap.String("SETU CONSENT ID", req.GetConsentId()), zap.Any(logger.RPC_STATUS, initiateResp.GetStatus()), zap.Error(initiateErr))
		// intentionally hard-coding error message here as we don't want to expose exact error reason to the vendor due to security reasons.
		return nil, errors.New("transient failure")
	}

	return &emptyPb.Empty{}, nil
}
