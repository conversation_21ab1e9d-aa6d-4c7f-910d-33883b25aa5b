# Vendor Notification service

Vendor Notification service provides functionality to process the updates from the vendors. These updates can be one of:
* Call backs e.g., change in transaction status at partner bank 
* Notifications e.g., offline transaction at partner bank 

It is a very thin layer that:
 1. Validates a payload
 2. Translates vendor specific payload to domain specific payload
 2. Publishes an event so that the respective service can process it
 3. Responds with an ack message
 
Ideally, no other business logic should be part of this service.

## Implementation  

Given that the consumers of service are going to be vendors that are mostly partner banks, traditional REST/JSON 
based APIs would be exposed for now. Based on the vendors, we may choose to expose GRPC as well on the same port.

All the functionality is exposed via GRPC methods. [GRPC Gateway](https://github.com/grpc-ecosystem/grpc-gateway) 
will be used to translate & redirect the requests to GRPC server.    

## Package structure

// TODO(pruthvi): how it is to be segregated package / vendor
