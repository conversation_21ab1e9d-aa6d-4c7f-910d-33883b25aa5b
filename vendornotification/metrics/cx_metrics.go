package metrics

import (
	chatBotPb "github.com/epifi/gamma/api/vendornotification/cx/chatbot"

	"github.com/prometheus/client_golang/prometheus"
)

type CxMetrics struct {
	chatbotApiStatusCodes                 *prometheus.CounterVec
	freshchatActionsCallbacks             *prometheus.CounterVec
	sentQuestionIDToOzonotalCallIvr       *prometheus.CounterVec
	receivedQuestionIDFromOzonotalCallIvr *prometheus.CounterVec
}

const (
	ChatBotApiNameValidateToken        = "ValidateToken"
	ChatBotApiNameInitiateConversation = "InitiateConversation"
	ChatBotApiNamePushUserMessage      = "PushUserMessage"
	ChatBotApiNameNotifyUser           = "NotifyUser"
	ChatBotApiNameFetchData            = "FetchData"
	ChatBotApiNameExecuteAction        = "ExecuteAction"

	NoError = "Success"
)

var (
	cxMetricsRecorder   = initialiseCxMetrics()
	chatbotApiNamesList = []string{ChatBotApiNameValidateToken, ChatBotApiNameInitiateConversation,
		ChatBotApiNamePushUserMessage, ChatBotApiNameNotifyUser, ChatBotApiNameFetchData, ChatBotApiNameExecuteAction}
)

func initialiseCxMetrics() *CxMetrics {
	cxMetrics := &CxMetrics{
		chatbotApiStatusCodes: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_cx_chatbot_api_status_codes",
			Help: "Status codes returned to vendor by VN chatbot related APIs",
		}, []string{"api_name", "status_code"}),
		freshchatActionsCallbacks: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_cx_freshchat_action_callbacks",
			Help: "Callbacks received for freshchat events/actions",
		}, []string{"action_type", "error"}),
		sentQuestionIDToOzonotalCallIvr: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "sent_question_id_to_ozonotal_in_call_ivr_count",
			Help: "Total count of request question IDs to Ozonotal Risk IVR",
		}, []string{"question_id", "preferred_language"}),
		receivedQuestionIDFromOzonotalCallIvr: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "received_question_id_from_ozonotal_in_call_ivr_count",
			Help: "Total count of response question IDs from Ozonotal Risk IVR",
		}, []string{"question_id", "preferred_language"}),
	}

	// register all metrics
	prometheus.MustRegister(cxMetrics.chatbotApiStatusCodes)
	prometheus.MustRegister(cxMetrics.freshchatActionsCallbacks)
	prometheus.MustRegister(cxMetrics.sentQuestionIDToOzonotalCallIvr)
	prometheus.MustRegister(cxMetrics.receivedQuestionIDFromOzonotalCallIvr)
	initialiseCxCounters(cxMetrics)
	return cxMetrics
}

func initialiseCxCounters(cxMetrics *CxMetrics) {
	for statusCode := range chatBotPb.Status_value {
		for _, apiName := range chatbotApiNamesList {
			cxMetrics.chatbotApiStatusCodes.WithLabelValues(apiName, statusCode)
		}
	}
}

func RecordChatbotApiStatusCode(apiName, statusCode string) {
	cxMetricsRecorder.chatbotApiStatusCodes.WithLabelValues(apiName, statusCode).Inc()
}

func RecordFreshchatActionCallback(actionType, error string) {
	cxMetricsRecorder.freshchatActionsCallbacks.WithLabelValues(actionType, error).Inc()
}

func RecordSentQuestionIDtoOzonotal(questionID, preferredLanguage string) {
	cxMetricsRecorder.sentQuestionIDToOzonotalCallIvr.WithLabelValues(questionID, preferredLanguage).Inc()
}

func RecordReceivedQuestionIdFromOzonotal(questionID, preferredLanguage string) {
	cxMetricsRecorder.receivedQuestionIDFromOzonotalCallIvr.WithLabelValues(questionID, preferredLanguage).Inc()
}
