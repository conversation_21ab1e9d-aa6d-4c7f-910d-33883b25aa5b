package metrics

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	typesPb "github.com/epifi/gamma/api/typesv2"
	moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
	"github.com/epifi/gamma/api/vendors"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"

	"github.com/prometheus/client_golang/prometheus"
)

// VendorNotificationMetrics represents a collection of metrics to be registered on a
// Prometheus metrics registry for VendorNotification service custom-metrics.
type VendorNotificationMetrics struct {
	// counter to measure parsed and generic transactions
	inboundNotification   *prometheus.CounterVec
	aaFiNotification      *prometheus.CounterVec
	aaConsentNotification *prometheus.CounterVec
	aaAccountNotification *prometheus.CounterVec

	// count to measure the number of miscellaneous charges related notifications received in total
	inboundMiscChargeNotificationCount *prometheus.CounterVec

	// counter to measure the area based success/failure status for getUserAttributes api calls
	areaBasedStatusCounterForGetUserAttributes *prometheus.CounterVec

	ozonetelInvalidCallerIdCount         *prometheus.CounterVec
	ozonetelInvalidCampaignDidCount      *prometheus.CounterVec
	ozonetelInvalidAgentPhoneNumberCount *prometheus.CounterVec
	ozonetelInvalidCallStartTimeCount    *prometheus.CounterVec
	ozonetelInvalidCallEndTimeCount      *prometheus.CounterVec

	// counter to measure enach registration auth callbacks with a given status.
	enachMandateRegistrationAuthCallbackStatusCounter *prometheus.CounterVec
	// counter to measure failures while processing enach registration auth callbacks.
	enachMandateRegistrationAuthCallbackProcessingFailureCounter *prometheus.CounterVec
	// count to measure all notification types for cc switch card notifications
	creditCardNotificationTypeCount *prometheus.CounterVec
	// count to measure card switch notifications grouped by txn mode
	debitCardSwitchNotificationTxnModeCount *prometheus.CounterVec
}

const (
	NameSpace           = "vn"
	PaymentSubSystem    = "payment"
	ParsedTransactions  = "PARSED"
	GenericTransactions = "GENERIC"

	GenericSuccessStatus = "Success"
	GenericFailureStatus = "Failed"
)

var vendorNotificationMetricsRecorder = initialiseVendorNotificationMetrics()

func initialiseVendorNotificationMetrics() *VendorNotificationMetrics {
	vendorNotificationMetrics := &VendorNotificationMetrics{
		inboundNotification: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_inbound_notification_total",
			Help: "Total number of inbound notification",
		}, []string{"status"}),
		aaFiNotification: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_inbound_aa_fi_notification_total",
			Help: "Total AA FI notifications by session status and fi status",
		}, []string{"session_status", "fi_status", "bank", "aa_id"}),
		aaConsentNotification: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_inbound_aa_consent_notification_total",
			Help: "Total AA consent notifications by consent status",
		}, []string{"consent_status", "aa_id"}),
		aaAccountNotification: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_inbound_aa_account_notification_total",
			Help: "Total AA account notifications by account status",
		}, []string{"account_status", "aa_id"}),
		inboundMiscChargeNotificationCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Namespace: NameSpace,
			Subsystem: PaymentSubSystem,
			Name:      "inbound_miscellaneous_charge_notification_total",
			Help:      "Total number of miscellaneous charges related notifications received",
		}, []string{"partner_bank"}),
		ozonetelInvalidCallerIdCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_ozonetel_invalid_caller_id_total",
			Help: "Total number of invalid screenpop callerId count",
		}, []string{}),
		ozonetelInvalidCampaignDidCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_ozonetel_invalid_campaign_did_total",
			Help: "Total number of invalid screenpop campaign did count",
		}, []string{}),
		ozonetelInvalidAgentPhoneNumberCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_ozonetel_invalid_agent_phone_number_total",
			Help: "Total number of invalid agent phone number count",
		}, []string{}),
		ozonetelInvalidCallStartTimeCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_ozonetel_invalid_call_start_time_total",
			Help: "Total number of invalid call start time count",
		}, []string{}),
		ozonetelInvalidCallEndTimeCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "vn_ozonetel_invalid_call_end_time_total",
			Help: "Total number of invalid call end time count",
		}, []string{}),
		enachMandateRegistrationAuthCallbackProcessingFailureCounter: prometheus.NewCounterVec(prometheus.CounterOpts{
			Namespace: NameSpace,
			Subsystem: PaymentSubSystem,
			Name:      "enach_mandate_registration_auth_callback_processing_failure_total",
			Help:      "Total count of failures while processing enach registration authorization callback",
		}, []string{"vendor"},
		// vendor denotes the partner/bank from whom the callback is received.
		),
		enachMandateRegistrationAuthCallbackStatusCounter: prometheus.NewCounterVec(prometheus.CounterOpts{
			Namespace: NameSpace,
			Subsystem: PaymentSubSystem,
			Name:      "enach_mandate_registration_auth_callback_status_total",
			Help:      "Total count of enach registration authorization callbacks with a given status",
		}, []string{"vendor", "status"},
		// vendor denotes the partner/bank from whom the callback is received.
		// status denotes the status received in authorization callback payload
		),
		areaBasedStatusCounterForGetUserAttributes: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "area_based_getUserAttributes_status_total",
			Help: "Total count of the area based status for the getUserAttributes rpc calls",
		}, []string{"area", "status"},
		),
		creditCardNotificationTypeCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Namespace: NameSpace,
			Name:      "credit_card_notification_type_total",
			Help:      "Total number of credit card notifications encountered on vendor notification",
		}, []string{"transaction_type"}),
		debitCardSwitchNotificationTxnModeCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Namespace: NameSpace,
			Name:      "debit_card_switch_notification_txn_mode_total",
			Help:      "Total number of card switch notifications callback with various transaction modes",
		}, []string{"transaction_mode"}),
	}
	// register all metrics
	prometheus.MustRegister(vendorNotificationMetrics.inboundMiscChargeNotificationCount)
	prometheus.MustRegister(vendorNotificationMetrics.inboundNotification)
	prometheus.MustRegister(vendorNotificationMetrics.aaFiNotification)
	prometheus.MustRegister(vendorNotificationMetrics.aaConsentNotification)
	prometheus.MustRegister(vendorNotificationMetrics.aaAccountNotification)
	prometheus.MustRegister(vendorNotificationMetrics.ozonetelInvalidCallerIdCount)
	prometheus.MustRegister(vendorNotificationMetrics.ozonetelInvalidCampaignDidCount)
	prometheus.MustRegister(vendorNotificationMetrics.ozonetelInvalidAgentPhoneNumberCount)
	prometheus.MustRegister(vendorNotificationMetrics.ozonetelInvalidCallStartTimeCount)
	prometheus.MustRegister(vendorNotificationMetrics.ozonetelInvalidCallEndTimeCount)
	prometheus.MustRegister(vendorNotificationMetrics.enachMandateRegistrationAuthCallbackProcessingFailureCounter)
	prometheus.MustRegister(vendorNotificationMetrics.enachMandateRegistrationAuthCallbackStatusCounter)
	prometheus.MustRegister(vendorNotificationMetrics.areaBasedStatusCounterForGetUserAttributes)
	prometheus.MustRegister(vendorNotificationMetrics.creditCardNotificationTypeCount)
	prometheus.MustRegister(vendorNotificationMetrics.debitCardSwitchNotificationTxnModeCount)
	initialise(vendorNotificationMetrics)
	return vendorNotificationMetrics
}

// initialise metrics to 0
func initialise(vendorNotificationMetrics *VendorNotificationMetrics) {
	vendorNotificationMetrics.inboundNotification.WithLabelValues(ParsedTransactions)
	vendorNotificationMetrics.inboundNotification.WithLabelValues(GenericTransactions)
	vendorNotificationMetrics.inboundMiscChargeNotificationCount.WithLabelValues(commonvgpb.Vendor_FEDERAL_BANK.String())
	for _, cs := range caPkg.ConsentStatusList {
		for _, aaId := range caPkg.ProdAaIdList {
			vendorNotificationMetrics.aaConsentNotification.WithLabelValues(cs, aaId)
		}
	}
	for _, ss := range caPkg.SessionStatusList {
		for _, fi := range caPkg.FiStatusList {
			for bank := range typesPb.Bank_value {
				for _, aaId := range caPkg.ProdAaIdList {
					vendorNotificationMetrics.aaFiNotification.WithLabelValues(ss, fi, bank, aaId)
				}
			}
		}
	}
	for _, s := range caPkg.AccStatusList {
		for _, aaId := range caPkg.ProdAaIdList {
			vendorNotificationMetrics.aaAccountNotification.WithLabelValues(s, aaId)
		}
	}

	vendorNotificationMetrics.ozonetelInvalidCallerIdCount.WithLabelValues()
	vendorNotificationMetrics.ozonetelInvalidCampaignDidCount.WithLabelValues()
	vendorNotificationMetrics.ozonetelInvalidAgentPhoneNumberCount.WithLabelValues()
	vendorNotificationMetrics.ozonetelInvalidCallStartTimeCount.WithLabelValues()
	vendorNotificationMetrics.ozonetelInvalidCallEndTimeCount.WithLabelValues()

	// initialize counter metrics to zero value
	vendorNotificationMetrics.enachMandateRegistrationAuthCallbackProcessingFailureCounter.WithLabelValues(commonvgpb.Vendor_FEDERAL_BANK.String())
	vendorNotificationMetrics.enachMandateRegistrationAuthCallbackStatusCounter.WithLabelValues(commonvgpb.Vendor_FEDERAL_BANK.String(), GenericSuccessStatus)
	vendorNotificationMetrics.enachMandateRegistrationAuthCallbackStatusCounter.WithLabelValues(commonvgpb.Vendor_FEDERAL_BANK.String(), GenericFailureStatus)
	for _, area := range moengageVnPb.Area_name {
		vendorNotificationMetrics.areaBasedStatusCounterForGetUserAttributes.WithLabelValues(area, GenericSuccessStatus)
		vendorNotificationMetrics.areaBasedStatusCounterForGetUserAttributes.WithLabelValues(area, GenericFailureStatus)
	}
}

var (
	receivedCallbackCount = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "received_callback_total",
			Help: "total number of callbacks received from outside epifi",
		},
		[]string{"callback_type", "vendor"},
	)
	// register metrics without using init
	_ = func() struct{} {
		prometheus.MustRegister(receivedCallbackCount)
		receivedCallbackCount.Reset()
		initCommsCallback()
		return struct{}{}
	}()
)

func initCommsCallback() {
	// initialize sms callback
	for _, sms := range vendors.SmsVendor {
		receivedCallbackCount.WithLabelValues("SMS", sms.String())
	}

	// initialize whatsapp vendor
	for _, wa := range vendors.WhatsappVendor {
		receivedCallbackCount.WithLabelValues("WHATSAPP", wa.String())
	}
}

// RecordInboundNotification will record transaction is parsed or generic one
func RecordInboundNotification(status string) {
	vendorNotificationMetricsRecorder.inboundNotification.WithLabelValues(status).Inc()
}

// RecordInboundMiscChargeNotification will record a misc charge notification for a given partner bank
func RecordInboundMiscChargeNotification(partnerBank commonvgpb.Vendor) {
	vendorNotificationMetricsRecorder.inboundMiscChargeNotificationCount.WithLabelValues(partnerBank.String()).Inc()
}

func IncrementSMSCallbackCount(vendor string) {
	receivedCallbackCount.WithLabelValues("SMS", vendor).Inc()
}

func IncrementWhatsappCallbackCount(vendor string) {
	receivedCallbackCount.WithLabelValues("WHATSAPP", vendor).Inc()
}

func RecordAaFiNotification(sessionStatus, fiStatus, fipId, aaId string) {
	fipMeta, _ := caPkg.GetFipMetaById(fipId)
	if fipMeta != nil {
		vendorNotificationMetricsRecorder.aaFiNotification.WithLabelValues(sessionStatus, fiStatus, fipMeta.Bank.String(), aaId).Inc()
	} else {
		vendorNotificationMetricsRecorder.aaFiNotification.WithLabelValues(sessionStatus, fiStatus, typesPb.Bank_BANK_UNSPECIFIED.String(), aaId).Inc()
	}
}

func RecordAaConsentNotification(consentStatus, aaId string) {
	vendorNotificationMetricsRecorder.aaConsentNotification.WithLabelValues(consentStatus, aaId).Inc()
}

func RecordAaAccountNotification(accStatus, aaId string) {
	vendorNotificationMetricsRecorder.aaAccountNotification.WithLabelValues(accStatus, aaId).Inc()
}

func IncrementInvalidCallerIdCount() {
	vendorNotificationMetricsRecorder.ozonetelInvalidCallerIdCount.WithLabelValues().Inc()
}

func IncrementInvalidCampaignDidCount() {
	vendorNotificationMetricsRecorder.ozonetelInvalidCampaignDidCount.WithLabelValues().Inc()
}

func IncrementInvalidAgentPhoneNumberCount() {
	vendorNotificationMetricsRecorder.ozonetelInvalidAgentPhoneNumberCount.WithLabelValues().Inc()
}

func IncrementInvalidCallStartTimeCount() {
	vendorNotificationMetricsRecorder.ozonetelInvalidCallStartTimeCount.WithLabelValues().Inc()
}

func IncrementCallEndTimeCount() {
	vendorNotificationMetricsRecorder.ozonetelInvalidCallEndTimeCount.WithLabelValues().Inc()
}

func RecordEnachMandateRegistrationAuthCallbackProcessingFailure(vendor commonvgpb.Vendor) {
	vendorNotificationMetricsRecorder.enachMandateRegistrationAuthCallbackProcessingFailureCounter.WithLabelValues(vendor.String()).Inc()
}

func RecordEnachMandateRegistrationAuthCallbackStatus(vendor commonvgpb.Vendor, status string) {
	vendorNotificationMetricsRecorder.enachMandateRegistrationAuthCallbackStatusCounter.WithLabelValues(vendor.String(), status).Inc()
}

func RecordAreaBasedStatusCounterForGetUserAttributes(area string, status string) {
	vendorNotificationMetricsRecorder.areaBasedStatusCounterForGetUserAttributes.WithLabelValues(area, status).Inc()
}

func IncreaseCreditCardNotificationTypeCount(transactionType string) {
	vendorNotificationMetricsRecorder.creditCardNotificationTypeCount.WithLabelValues(transactionType).Inc()
}

func RecordDebitCardSwitchNotificationTxnModeCount(txnMode string) {
	vendorNotificationMetricsRecorder.debitCardSwitchNotificationTxnModeCount.WithLabelValues(txnMode).Inc()
}
