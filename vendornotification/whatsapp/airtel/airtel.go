package airtel

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	commspb "github.com/epifi/gamma/api/comms"
	airtelWpVnPb "github.com/epifi/gamma/api/vendornotification/whatsapp/airtel"
	airtelVpb "github.com/epifi/gamma/api/vendors/airtel"
	federalRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	"google.golang.org/protobuf/types/known/emptypb"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"
)

type Service struct {
	conf                            *config.Config
	airtelWhatsappCallbackPublisher queue.Publisher
}

type AirtelWhatsappCallbackPublisher queue.Publisher

const AirtelWhatsappCallBackRequest = "AirtelWhatsappCallbackRequest"

func NewService(conf *config.Config, airtelWhatsappCallbackPublisher AirtelWhatsappCallbackPublisher) *Service {
	return &Service{
		conf:                            conf,
		airtelWhatsappCallbackPublisher: airtelWhatsappCallbackPublisher,
	}
}

var _ airtelWpVnPb.AirtelCallbackServer = &Service{}

func (s *Service) AirtelWhatsappDLR(ctx context.Context, req *airtelVpb.AirtelWhatsappDLRPayload) (*emptyPb.Empty, error) {
	if err := security.CheckWhiteList(ctx, s.conf.AirtelWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, AirtelWhatsappCallBackRequest, req.GetMessageRequestId(), federalRedactor.Config)

	event := &commspb.ProcessAirtelWhatsappCallbackRequest{
		RequestHeader:         nil,
		CustomerId:            req.GetCustomerId(),
		MessageId:             req.GetMessageId(),
		MessageRequestId:      req.GetMessageRequestId(),
		WaMsgId:               req.GetWaMsgId(),
		SessionDocId:          req.GetSessionDocId(),
		SourceAddress:         req.GetSourceAddress(),
		RecipientAddress:      req.GetRecipientAddress(),
		SourceCountry:         req.GetSourceCountry(),
		MsgStatus:             req.GetMsgStatus(),
		ConversationId:        req.GetConversationId(),
		ConversationType:      req.GetConversationType(),
		ConversationStartTime: req.GetConversationStartTime(),
		MessageParameters: &commspb.MessageParams{
			Interactive: &commspb.Interactive{
				Action: &commspb.Action{},
			},
			Body: &commspb.TextBody{
				Text: req.GetMessageParameters().GetBody().GetText(),
			},
		},
		MsgSort:             req.GetMsgSort(),
		MsgStream:           req.GetMsgStream(),
		ClientCorrelationId: req.GetClientCorrelationId(),
		MessageType:         req.GetMessageType(),
		SessionLogTime:      req.GetSessionLogTime(),
		CreatedDate:         req.GetCreatedDate(),
		UpdatedDate:         req.GetUpdatedDate(),
		ErrorDetails: &commspb.ErrorDetails{
			Code:    req.GetErrorDetails().GetCode(),
			Title:   req.GetErrorDetails().GetTitle(),
			Details: req.GetErrorDetails().GetDetails(),
		},
		WhatsappOrigin: commspb.WhatsappOrigin_WHATSAPP_ORIGIN_MOENGAGE,
	}
	msgId, err := s.airtelWhatsappCallbackPublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error while publishing airtel whatsapp dlr event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}

	metrics.IncrementWhatsappCallbackCount(commonvgpb.Vendor_AIRTEL.String())

	logger.Debug(ctx, "airtel whatsapp dlr request processed successfully", zap.String("message_id", msgId))

	return &emptypb.Empty{}, nil
}
