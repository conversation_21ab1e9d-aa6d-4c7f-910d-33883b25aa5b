package acl

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	aclWAPb "github.com/epifi/gamma/api/vendornotification/whatsapp/acl"
	"github.com/epifi/gamma/vendornotification/metrics"

	federalRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"

	emptyPb "github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	commsPb "github.com/epifi/gamma/api/comms"
)

type Service struct {
	conf                      *config.Config
	whatsappCallbackPublisher queue.Publisher
	whatsappReplyPublisher    queue.Publisher
}

type AclWhatsappCallbackPublisher queue.Publisher
type AclWhatsappReplyPublisher queue.Publisher

func NewService(conf *config.Config, whatsappCallbackPublisher AclWhatsappCallbackPublisher, whatsappReplyPublisher AclWhatsappReplyPublisher) *Service {
	return &Service{
		conf:                      conf,
		whatsappCallbackPublisher: whatsappCallbackPublisher,
		whatsappReplyPublisher:    whatsappReplyPublisher,
	}
}

var _ aclWAPb.WhatsappCallbackServer = &Service{}

const (
	AclWhatsappDLRRequest   = "AclWhatsappDLRRequest"
	AclWhatsappReplyRequest = "AclWhatsappReplyRequest"
)

// DLR is delivery report, a standard term used by all vendors
func (s *Service) AclWhatsappDLR(ctx context.Context, req *aclWAPb.AclWhatsappDLRRequest) (*emptyPb.Empty, error) {
	if err := security.CheckWhiteList(
		ctx, s.conf.AclWhatsappWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix,
	); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, AclWhatsappDLRRequest, req.GetRqstAckId(), federalRedactor.Config)

	event := &commsPb.ProcessAclWhatsappCallBackRequest{
		ResponseId:     req.GetRqstAckId(),
		MobileNumber:   req.GetMobileNumber(),
		DelStatus:      req.GetDelStatus(),
		DelTime:        req.GetDelTime(),
		DelDesc:        req.GetDelDesc(),
		EventTimestamp: timestamppb.Now(),
		WhatsappOrigin: commsPb.WhatsappOrigin_WHATSAPP_ORIGIN_EPIFI,
	}
	sqsMsgId, err := s.whatsappCallbackPublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error publishing whatsapp acl callback event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "message published in queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))
	metrics.IncrementWhatsappCallbackCount(commonvgpb.Vendor_ACL.String())
	return &emptyPb.Empty{}, nil
}

func (s *Service) AclWhatsappReply(ctx context.Context, req *aclWAPb.AclWhatsappReplyRequest) (*emptyPb.Empty, error) {
	if err := security.CheckWhiteList(
		ctx, s.conf.AclWhatsappWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor,
		s.conf.VpcCidrIPPrefix,
	); err != nil {
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, AclWhatsappReplyRequest, req.GetMessage().GetId(), federalRedactor.Config)

	event := &commsPb.ProcessAclWhatsappReplyRequest{
		FromNumber:     req.GetMessage().GetFrom(),
		ToNumber:       req.GetMessage().GetTo(),
		MessageId:      req.GetMessage().GetId(),
		Body:           req.GetMessage().GetText().GetBody(),
		EventTimestamp: timestamppb.Now(),
		SentTimestamp:  req.GetMessage().GetTimestamp(),
	}
	sqsMsgId, err := s.whatsappReplyPublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error publishing whatsapp reply event to queue", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "message published in queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))
	return &emptyPb.Empty{}, nil
}
