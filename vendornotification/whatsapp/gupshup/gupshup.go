package gupshup

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"time"

	"github.com/epifi/gamma/vendornotification/security"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/metrics"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"go.uber.org/zap"

	"google.golang.org/protobuf/types/known/timestamppb"

	commsPb "github.com/epifi/gamma/api/comms"
	gupshupWAPb "github.com/epifi/gamma/api/vendornotification/whatsapp/gupshup"
	gupshupPb "github.com/epifi/gamma/api/vendors/gupshup"
	federalRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/redactor"

	emptyPb "google.golang.org/protobuf/types/known/emptypb"
)

type Service struct {
	conf                      *config.Config
	whatsappCallbackPublisher queue.Publisher
}

type GupshupWhatsappCallbackPublisher queue.Publisher

func NewService(conf *config.Config, whatsappCallbackPublisher GupshupWhatsappCallbackPublisher) *Service {
	return &Service{
		conf:                      conf,
		whatsappCallbackPublisher: whatsappCallbackPublisher,
	}
}

var _ gupshupWAPb.GupshupCallbackServer = &Service{}

const GupshupWhatsappDLRRequest = "GupshupWhatsappDLRRequest" //nolint:gosec

func (s *Service) GupshupWhatsappDLR(ctx context.Context, req *gupshupPb.GupshupWhatsappDLRRequest) (*emptyPb.Empty, error) {
	if err := security.CheckWhiteList(ctx, s.conf.GupshupWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, err
	}

	for _, dlrEvent := range req.GetResponse() {
		redactor.LogCallbackRequestData(ctx, req, GupshupWhatsappDLRRequest, dlrEvent.GetExternalId(), federalRedactor.Config)
		unixMilliseconds := dlrEvent.GetEventTimestamp()
		// Convert milliseconds to seconds
		unixSeconds := unixMilliseconds / 1000
		event := &commsPb.ProcessGupshupWhatsappCallBackRequest{
			SrcAddress:        dlrEvent.GetSrcAddr(),
			Channel:           dlrEvent.GetChannel(),
			TemplateId:        dlrEvent.GetHsmTemplateId(),
			ExternalId:        dlrEvent.GetExternalId(),
			DlrStatus:         dlrEvent.GetEventType(),
			ErrorCode:         dlrEvent.GetErrorCode(),
			DestAddress:       dlrEvent.GetDestAddr(),
			DlrDetailedStatus: dlrEvent.GetCause(),
			SentTimestamp:     timestamppb.New(time.Unix(unixSeconds, 0)),
		}
		msgId, err := s.whatsappCallbackPublisher.Publish(ctx, event)
		if err != nil {
			logger.Error(ctx, "error publishing whatsapp gupshup callback event to queue", zap.Error(err))
			return nil, status.Errorf(codes.Internal, "internal server error")
		}
		logger.Info(ctx, "message published to gupshup queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, msgId))
		metrics.IncrementWhatsappCallbackCount(commonvgpb.Vendor_GUPSHUP.String())
	}
	return &emptyPb.Empty{}, nil
}
