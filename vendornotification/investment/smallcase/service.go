package smallcase

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	emptyPb "github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/investment/mutualfund/external/consumer"
	smallcaseVn "github.com/epifi/gamma/api/vendornotification/investment/smallcase"
	"github.com/epifi/gamma/api/vendors/smallcase"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/security"
)

type Service struct {
	config                                   *config.Config
	smallcaseHoldingWebhookExtendedPublisher queue.ExtendedPublisher
}

func NewService(conf *config.Config, smallcaseHoldingWebhookExtendedPublisher queue.ExtendedPublisher) *Service {
	return &Service{
		config:                                   conf,
		smallcaseHoldingWebhookExtendedPublisher: smallcaseHoldingWebhookExtendedPublisher,
	}
}

func (s *Service) ProcessMFHoldingsWebhook(ctx context.Context, req *smallcase.ProcessMFHoldingsWebhookRequest) (*emptyPb.Empty, error) {
	logger.Info(ctx, fmt.Sprintf("callback received for ProcessMFHoldingsWebhook: %v", req.TransactionId))
	logger.SecureInfo(ctx, commonvgpb.Vendor_SMALL_CASE, fmt.Sprintf("callback received for ProcessMFHoldingsWebhook: %v", req.TransactionId), zap.Any(logger.PAYLOAD, req))
	// checking if request is coming from whitelisted IPs
	if err := security.CheckWhiteList(ctx, s.config.SmallcaseWhitelist, s.config.NumberOfHopsThatAddXForwardedFor,
		s.config.VpcCidrIPPrefix); err != nil {
		return nil, err
	}

	smallcaseMFHoldingsPayload := &smallcaseVn.SmallCaseMFHoldingsPayLoad{
		Data:          req.GetData(),
		TransactionId: req.GetTransactionId(),
	}
	publishReq := &consumer.ProcessMFHoldingsCallbackConsumerRequest{
		Identifier: &consumer.ProcessMFHoldingsCallbackConsumerRequest_SmallcaseMfHoldingsPayload{SmallcaseMfHoldingsPayload: smallcaseMFHoldingsPayload},
	}

	_, publishErr := s.smallcaseHoldingWebhookExtendedPublisher.PublishExtendedMessage(ctx, publishReq)
	if publishErr != nil {
		logger.Error(ctx, "error in publishing smallcaseMFHoldingsPayload", zap.Error(publishErr))
		return &emptyPb.Empty{}, nil
	}
	logger.Info(ctx, "successfully published smallcaseMFHoldingsPayload", zap.String("transactionId", req.GetTransactionId()))
	return &emptyPb.Empty{}, nil
}
