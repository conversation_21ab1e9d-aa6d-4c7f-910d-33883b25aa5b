package moengage

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	mockVendorMapping "github.com/epifi/gamma/api/vendormapping/mocks"
	moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
	mocks "github.com/epifi/gamma/vendornotification/test/mocks/notifications/moengage/userattributesfetcher"
)

func TestService_GetUserAttributes(t *testing.T) {
	var (
		mockUserAttributesFetcherFactory *mocks.MockIUserAttributesFetcherFactory
		mockVendorMappingClient          *mockVendorMapping.MockVendorMappingServiceClient
		mockAttribGetter                 *mocks.MockIUserAttributesFetcher
	)
	type args struct {
		ctx     context.Context
		request *moengageVendorPb.GetUserAttributesRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockUserAttributesFetcherFactory *mocks.MockIUserAttributesFetcherFactory, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient, mockAttribGetter *mocks.MockIUserAttributesFetcher)
		want       *moengageVendorPb.GetUserAttributesResponse
		wantErr    bool
	}{
		{
			name: "should return error if user-id is not present in request",
			args: args{
				ctx: context.Background(),
				request: &moengageVendorPb.GetUserAttributesRequest{
					Areas:           "rewards",
					FieldMask:       "field",
					RequestMetadata: "key:value",
				},
			},
			setupMocks: func(mockUserAttributesFetcherFactory *mocks.MockIUserAttributesFetcherFactory, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient, mockAttribGetter *mocks.MockIUserAttributesFetcher) {
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error if area is not present in request",
			args: args{
				ctx: context.Background(),
				request: &moengageVendorPb.GetUserAttributesRequest{
					UserId:          "user-id",
					FieldMask:       "field",
					RequestMetadata: "key:value",
				},
			},
			setupMocks: func(mockUserAttributesFetcherFactory *mocks.MockIUserAttributesFetcherFactory, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient, mockAttribGetter *mocks.MockIUserAttributesFetcher) {
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error if unsupported area is provided in request",
			args: args{
				ctx: context.Background(),
				request: &moengageVendorPb.GetUserAttributesRequest{
					Areas:           "invalid",
					UserId:          "user-id",
					FieldMask:       "field",
					RequestMetadata: "key:value",
				},
			},
			setupMocks: func(mockUserAttributesFetcherFactory *mocks.MockIUserAttributesFetcherFactory, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient, mockAttribGetter *mocks.MockIUserAttributesFetcher) {
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error if GetActorIdByVendorId returns ISE",
			args: args{
				ctx: context.Background(),
				request: &moengageVendorPb.GetUserAttributesRequest{
					Areas:           "rewards",
					UserId:          "user-id",
					FieldMask:       "field",
					RequestMetadata: "key:value",
				},
			},
			setupMocks: func(mockUserAttributesFetcherFactory *mocks.MockIUserAttributesFetcherFactory, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient, mockAttribGetter *mocks.MockIUserAttributesFetcher) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(
					context.Background(),
					&vendormappingPb.GetActorIdByVendorIdRequest{VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{MoengageId: "user-id"}},
				).Return(&vendormappingPb.GetActorIdByVendorIdResponse{Status: rpc.StatusInternal()}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error if GetUserAttributesFetcher returns error",
			args: args{
				ctx: context.Background(),
				request: &moengageVendorPb.GetUserAttributesRequest{
					Areas:           "rewards",
					UserId:          "user-id",
					FieldMask:       "field",
					RequestMetadata: "key:value",
				},
			},
			setupMocks: func(mockUserAttributesFetcherFactory *mocks.MockIUserAttributesFetcherFactory, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient, mockAttribGetter *mocks.MockIUserAttributesFetcher) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(
					context.Background(),
					&vendormappingPb.GetActorIdByVendorIdRequest{VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{MoengageId: "user-id"}},
				).Return(&vendormappingPb.GetActorIdByVendorIdResponse{Status: rpc.StatusOk(), ActorId: "actor-id"}, nil)
				mockUserAttributesFetcherFactory.EXPECT().GetUserAttributesFetcher(moengageVnPb.Area_REWARDS).Return(nil, fmt.Errorf("error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error if GetAttributes returns error",
			args: args{
				ctx: context.Background(),
				request: &moengageVendorPb.GetUserAttributesRequest{
					Areas:           "rewards",
					UserId:          "user-id",
					FieldMask:       "summary",
					RequestMetadata: "duration:7h",
				},
			},
			setupMocks: func(mockUserAttributesFetcherFactory *mocks.MockIUserAttributesFetcherFactory, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient, mockAttribGetter *mocks.MockIUserAttributesFetcher) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(
					context.Background(),
					&vendormappingPb.GetActorIdByVendorIdRequest{VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{MoengageId: "user-id"}},
				).Return(&vendormappingPb.GetActorIdByVendorIdResponse{Status: rpc.StatusOk(), ActorId: "actor-id"}, nil)
				mockUserAttributesFetcherFactory.EXPECT().GetUserAttributesFetcher(moengageVnPb.Area_REWARDS).Return(mockAttribGetter, nil)
				mockAttribGetter.EXPECT().GetAttributes(gomock.Any(), &userattributesfetcher.GetAttributesRequest{
					ActorId:         "actor-id",
					FieldMask:       []userattributesfetcher.UserAttributesReqField{userattributesfetcher.UserAttributesReqField_Summary},
					RequestMetadata: map[userattributesfetcher.RequestMetadataKey]string{userattributesfetcher.RequestMetadataKey_Duration: "7h"},
				}).Return(nil, fmt.Errorf("error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return successful result when GetAttributes returns no error",
			args: args{
				ctx: context.Background(),
				request: &moengageVendorPb.GetUserAttributesRequest{
					Areas:           "rewards",
					UserId:          "user-id",
					FieldMask:       "summary",
					RequestMetadata: "duration:7h",
				},
			},
			setupMocks: func(mockUserAttributesFetcherFactory *mocks.MockIUserAttributesFetcherFactory, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient, mockAttribGetter *mocks.MockIUserAttributesFetcher) {
				mockVendorMappingClient.EXPECT().GetActorIdByVendorId(
					context.Background(),
					&vendormappingPb.GetActorIdByVendorIdRequest{VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{MoengageId: "user-id"}},
				).Return(&vendormappingPb.GetActorIdByVendorIdResponse{Status: rpc.StatusOk(), ActorId: "actor-id"}, nil)
				mockUserAttributesFetcherFactory.EXPECT().GetUserAttributesFetcher(moengageVnPb.Area_REWARDS).Return(mockAttribGetter, nil)
				mockAttribGetter.EXPECT().GetAttributes(gomock.Any(), &userattributesfetcher.GetAttributesRequest{
					ActorId:         "actor-id",
					FieldMask:       []userattributesfetcher.UserAttributesReqField{userattributesfetcher.UserAttributesReqField_Summary},
					RequestMetadata: map[userattributesfetcher.RequestMetadataKey]string{userattributesfetcher.RequestMetadataKey_Duration: "7h"},
				}).Return(&userattributesfetcher.GetAttributesResponse{
					FieldNameToAttributesMap: map[string]*moengageVendorPb.UserAttribute{
						"key": {
							ValueTypes: &moengageVendorPb.UserAttribute_StringValue{
								StringValue: "val",
							},
						},
					},
				}, nil)
			},
			want: &moengageVendorPb.GetUserAttributesResponse{
				AreaToFieldNameUserAttributes: map[string]*moengageVendorPb.FieldNameToUserAttributeMap{
					"REWARDS": {
						FieldNameToUserAttributes: map[string]*moengageVendorPb.UserAttribute{
							"key": {
								ValueTypes: &moengageVendorPb.UserAttribute_StringValue{
									StringValue: "val",
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockUserAttributesFetcherFactory = mocks.NewMockIUserAttributesFetcherFactory(ctrl)
			mockVendorMappingClient = mockVendorMapping.NewMockVendorMappingServiceClient(ctrl)
			mockAttribGetter = mocks.NewMockIUserAttributesFetcher(ctrl)

			tt.setupMocks(mockUserAttributesFetcherFactory, mockVendorMappingClient, mockAttribGetter)

			s := &Service{
				conf:                         conf,
				userAttributesFetcherFactory: mockUserAttributesFetcherFactory,
				vendorMapping:                mockVendorMappingClient,
			}
			got, err := s.GetUserAttributes(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserAttributes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserAttributes() got = %v, want %v", got, tt.want)
			}
		})
	}
}
