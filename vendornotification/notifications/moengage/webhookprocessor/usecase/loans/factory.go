package loans

import (
	"fmt"

	moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase/loans/dropoff"
)

type LoansUseCaseProcessorFactory struct {
	dropOffOutcallProcessor *dropoff.DropOffOutcallUseCaseProcessor
}

func NewLoansUseCaseProcessorFactory(dropOffOutcallProcessor *dropoff.DropOffOutcallUseCaseProcessor) *LoansUseCaseProcessorFactory {
	return &LoansUseCaseProcessorFactory{
		dropOffOutcallProcessor: dropOffOutcallProcessor,
	}
}

// compile time check to make sure LoansUseCaseProcessorFactory implements IUseCaseProcessorFactory
var _ usecase.IUseCaseProcessorFactory = &LoansUseCaseProcessorFactory{}

func (u *LoansUseCaseProcessorFactory) GetUseCaseProcessor(useCase moengageVnPb.UseCase) (usecase.IUseCaseProcessor, error) {
	switch useCase {
	case moengageVnPb.UseCase_DROP_OFF_OUTCALL:
		return u.dropOffOutcallProcessor, nil
	default:
		return nil, fmt.Errorf("unsupported use case: %v", useCase)
	}
}
