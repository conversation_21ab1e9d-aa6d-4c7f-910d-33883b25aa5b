package dropoff

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	userPb "github.com/epifi/gamma/api/user"
	palPkg "github.com/epifi/gamma/preapprovedloan/pkg"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase"
)

const (
	loansDropOffSalesOutcallIssueCategoryId   = "af23ce6b-4048-5b09-909a-ac15bea8de89"
	loansDropOffServiceOutcallIssueCategoryId = "c57a73be-2c23-584e-a42c-fc78202a9b67"
	loansDropOffOutcallTicketTag              = "LoansDropOffOutcall"
	CreateTicket                              = "CREATE"
	CloseTicket                               = "CLOSE"
	GlobalCloseTicket                         = "CLOSE_ALL"
)

// DropOffOutcallUseCaseProcessor processes DROP_OFF_OUTCALL use case
type DropOffOutcallUseCaseProcessor struct {
	palClient      palPb.PreApprovedLoanClient
	cxTicketClient ticketPb.TicketClient
	usersClient    userPb.UsersClient
}

// NewDropOffOutcallUseCaseProcessor creates a new DropOffOutcallUseCaseProcessor
func NewDropOffOutcallUseCaseProcessor(palClient palPb.PreApprovedLoanClient, cxTicketClient ticketPb.TicketClient, usersClient userPb.UsersClient) *DropOffOutcallUseCaseProcessor {
	return &DropOffOutcallUseCaseProcessor{
		palClient:      palClient,
		cxTicketClient: cxTicketClient,
		usersClient:    usersClient,
	}
}

// Process creates a freshdesk ticket so that the agent can out call the user to proceed from the dropped off stage
func (p *DropOffOutcallUseCaseProcessor) Process(ctx context.Context, request *usecase.ProcessRequest) error {
	dropOffCategory, dropOffStage, err := ParseCampaignName(request.GetCampaignName())
	if err != nil {
		logger.Error(ctx, "error parsing campaign name", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return err
	}

	campaignMetadata, err := ParseCampaignMeta(request.GetCampaignMeta())
	if err != nil {
		logger.Error(ctx, "error parsing campaign meta", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return err
	}

	userRes, userResErr := p.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: request.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(userRes, userResErr); rpcErr != nil {
		return fmt.Errorf("error getting user profile: %w", rpcErr)
	}

	email := userRes.GetUser().GetProfile().GetEmail()
	acquisitionChannel := userRes.GetUser().GetAcquisitionInfo().GetAcquisitionChannel().TrimmedPrefixString()
	switch dropOffCategory {
	case SalesDropOffCategory:
		switch campaignMetadata.GetAction() {
		case "", CreateTicket:
			if err := p.createSalesOutcallTicket(ctx, request.GetActorId(), email, acquisitionChannel, dropOffStage, campaignMetadata); err != nil {
				return fmt.Errorf("error creating sales outcall ticket: %w", err)
			}
		case CloseTicket:
			if err := p.closeSalesOutcallTicket(ctx, request.GetActorId(), email, dropOffStage, campaignMetadata); err != nil {
				return fmt.Errorf("error closing sales outcall ticket: %w", err)
			}
		case GlobalCloseTicket:
			if err := p.closeAllSalesOutcallTickets(ctx, request.GetActorId(), email); err != nil {
				return fmt.Errorf("error globally closing sales outcall tickets: %w", err)
			}
		default:
			return fmt.Errorf("invalid action in campaign metadata: %s", campaignMetadata.GetAction())
		}
	case ServiceDropOffCategory:
		switch campaignMetadata.GetAction() {
		case "", CreateTicket:
			if err := p.createServiceOutcallTicket(ctx, request.GetActorId(), email, acquisitionChannel, dropOffStage); err != nil {
				return fmt.Errorf("error creating service outcall ticket: %w", err)
			}
		case CloseTicket:
			if err := p.closeServiceOutcallTicket(ctx, request.GetActorId(), email, dropOffStage); err != nil {
				return fmt.Errorf("error closing service outcall ticket: %w", err)
			}
		case GlobalCloseTicket:
			if err := p.closeAllServiceOutcallTickets(ctx, request.GetActorId(), email); err != nil {
				return fmt.Errorf("error globally closing service outcall tickets: %w", err)
			}
		default:
			return fmt.Errorf("invalid action in campaign metadata: %s", campaignMetadata.GetAction())
		}
	default:
		return fmt.Errorf("invalid drop off category: %s", dropOffCategory)
	}

	return nil
}

func (p *DropOffOutcallUseCaseProcessor) createSalesOutcallTicket(ctx context.Context, actorId string, emailId string, acquisitionChannel string, dropOffStage palPkg.DropOffStage, campaignMetadata *CampaignMetadata) error {
	getSupportTicketsRes, getSupportTicketsErr := p.cxTicketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			IssueCategoryId: loansDropOffSalesOutcallIssueCategoryId,
			IdentifierType:  ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_EMAIL,
			IdentifierValue: emailId,
			FromTime:        timestamp.New(time.Now().Add(-time.Hour * 24 * 30)),
			StatusList:      ticketPb.GetActiveStatusList(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getSupportTicketsRes, getSupportTicketsErr); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		return fmt.Errorf("error getting support tickets: %w", rpcErr)
	}

	// Check if there's already an active ticket with the same stage and loan program
	if len(getSupportTicketsRes.GetTickets()) > 0 {
		for _, ticket := range getSupportTicketsRes.GetTickets() {
			if ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanProgram() == campaignMetadata.GetLoanProgram() && ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanVendor() == campaignMetadata.GetLoanVendor() && lo.Every(ticket.GetTags(), dropOffStage.GetStageTags()) {
				logger.Info(ctx, "ticket already exists for the user with the same loan program and stage",
					zap.String(logger.ACTOR_ID_V2, actorId),
					zap.String("loan_program", campaignMetadata.GetLoanProgram()),
					zap.Strings("stage_tags", dropOffStage.GetStageTags()))
				return nil
			}
		}
	}

	lv := campaignMetadata.GetLoanVendor()
	lp := campaignMetadata.GetLoanProgram()
	tags := []string{loansDropOffOutcallTicketTag}
	tags = append(tags, lv, lp)
	tags = append(tags, dropOffStage.GetStageTags()...)
	err := validatedStageTags(tags)
	if err != nil {
		return fmt.Errorf("error validating stage tags: %w", err)
	}

	createTicketAsyncRes, createTicketAsyncErr := p.cxTicketClient.CreateTicketAsync(ctx, &ticketPb.CreateTicketAsyncRequest{
		Ticket: &ticketPb.Ticket{
			CustomFields: &ticketPb.CustomFields{
				LoanOutcallMetadata: &ticketPb.LoanOutcallMetadata{
					LoanVendor:         lv,
					LoanProgram:        lp,
					DropOffStage:       string(dropOffStage),
					AcquisitionChannel: acquisitionChannel,
				},
			},
			Subject:         dropOffStage.GetStageName(),
			Description:     dropOffStage.GetStageDescription(),
			Email:           emailId,
			Priority:        ticketPb.Priority_PRIORITY_MEDIUM,
			Status:          ticketPb.Status_STATUS_OPEN,
			Tags:            tags,
			Group:           ticketPb.Group_GROUP_LOAN_OUTBOUND_CALL,
			ActorId:         actorId,
			IssueCategoryId: loansDropOffSalesOutcallIssueCategoryId,
		},
	})
	if rpcErr := epifigrpc.RPCError(createTicketAsyncRes, createTicketAsyncErr); rpcErr != nil {
		return fmt.Errorf("error creating ticket: %w", rpcErr)
	}
	return nil
}

func (p *DropOffOutcallUseCaseProcessor) createServiceOutcallTicket(ctx context.Context, actorId string, emailId string, acquisitionChannel string, dropOffStage palPkg.DropOffStage) error {
	// todo: get loan request id from moengage connector if feasible, considering the latest active loan request for now
	getActiveLoanRes, getActiveLoanErr := p.palClient.GetActiveLoanRequests(ctx, &palPb.GetActiveLoanRequestsRequest{
		ActorId:         actorId,
		LoanRequestType: palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
	})
	if rpcErr := epifigrpc.RPCError(getActiveLoanRes, getActiveLoanErr); rpcErr != nil {
		if getActiveLoanRes.GetStatus().IsRecordNotFound() {
			return nil
		}
		return fmt.Errorf("error getting active loan: %w", rpcErr)
	}
	if len(getActiveLoanRes.GetActiveLoanRequests()) == 0 {
		return nil
	}
	latestActiveLoanReq := getActiveLoanRes.GetActiveLoanRequests()[0]

	getSupportTicketsRes, getSupportTicketsErr := p.cxTicketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			IssueCategoryId: loansDropOffServiceOutcallIssueCategoryId,
			IdentifierType:  ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_EMAIL,
			IdentifierValue: emailId,
			FromTime:        timestamp.New(time.Now().Add(-time.Hour * 24 * 30)),
			StatusList:      ticketPb.GetActiveStatusList(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getSupportTicketsRes, getSupportTicketsErr); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		return fmt.Errorf("error getting support tickets: %w", rpcErr)
	}

	// Check if there's already a ticket with the same loan request ID and stage
	if len(getSupportTicketsRes.GetTickets()) > 0 {
		for _, ticket := range getSupportTicketsRes.GetTickets() {
			if ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanRequestId() == latestActiveLoanReq.GetId() && lo.Every(ticket.GetTags(), dropOffStage.GetStageTags()) {
				logger.Info(ctx, "ticket already exists for the user with the same loan request id and stage",
					zap.String(logger.ACTOR_ID_V2, actorId),
					zap.String("loan_request_id", latestActiveLoanReq.GetId()),
					zap.Strings("stage_tags", dropOffStage.GetStageTags()))
				return nil
			}
		}
	}

	lv := latestActiveLoanReq.GetVendor().String()
	lp := latestActiveLoanReq.GetLoanProgram().TrimmedPrefixString()
	lrId := latestActiveLoanReq.GetId()
	tags := []string{loansDropOffOutcallTicketTag}
	tags = append(tags, lv, lp)
	tags = append(tags, dropOffStage.GetStageTags()...)
	err := validatedStageTags(tags)
	if err != nil {
		return fmt.Errorf("error validating stage tags: %w", err)
	}

	createTicketAsyncRes, createTicketAsyncErr := p.cxTicketClient.CreateTicketAsync(ctx, &ticketPb.CreateTicketAsyncRequest{
		Ticket: &ticketPb.Ticket{
			CustomFields: &ticketPb.CustomFields{
				LoanOutcallMetadata: &ticketPb.LoanOutcallMetadata{
					LoanVendor:         lv,
					LoanProgram:        lp,
					LoanRequestId:      lrId,
					DropOffStage:       string(dropOffStage),
					AcquisitionChannel: acquisitionChannel,
				},
			},
			Subject:         dropOffStage.GetStageName(),
			Description:     dropOffStage.GetStageDescription(),
			Email:           emailId,
			Priority:        ticketPb.Priority_PRIORITY_MEDIUM,
			Status:          ticketPb.Status_STATUS_OPEN,
			Tags:            tags,
			Group:           ticketPb.Group_GROUP_LOAN_OUTBOUND_CALL,
			ActorId:         actorId,
			IssueCategoryId: loansDropOffServiceOutcallIssueCategoryId,
		},
	})
	if rpcErr := epifigrpc.RPCError(createTicketAsyncRes, createTicketAsyncErr); rpcErr != nil {
		return fmt.Errorf("error creating ticket: %w", rpcErr)
	}
	return nil
}

// nolint:dupl
func (p *DropOffOutcallUseCaseProcessor) closeSalesOutcallTicket(ctx context.Context, actorId string, emailId string, dropOffStage palPkg.DropOffStage, campaignMetadata *CampaignMetadata) error {
	getSupportTicketsRes, getSupportTicketsErr := p.cxTicketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			IssueCategoryId: loansDropOffSalesOutcallIssueCategoryId,
			IdentifierType:  ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_EMAIL,
			IdentifierValue: emailId,
			FromTime:        timestamp.New(time.Now().Add(-time.Hour * 24 * 30)),
			StatusList:      ticketPb.GetActiveStatusList(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getSupportTicketsRes, getSupportTicketsErr); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		return fmt.Errorf("error getting support tickets: %w", rpcErr)
	}

	var errors []error
	if len(getSupportTicketsRes.GetTickets()) > 0 {
		for _, ticket := range getSupportTicketsRes.GetTickets() {
			if campaignMetadata != nil && ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanProgram() == campaignMetadata.GetLoanProgram() && ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanVendor() == campaignMetadata.GetLoanVendor() && lo.Every(ticket.GetTags(), dropOffStage.GetStageTags()) {
				logger.Info(ctx, "ticket exists for the user", zap.String(logger.ACTOR_ID_V2, actorId))
				if ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition1() == "" {
					ticket.Status = ticketPb.Status_STATUS_CLOSED
					ticket.GetCustomFields().GetLoanOutcallMetadata().Disposition1 = string(palPkg.Autoclosed)
				}
				updateTicketRes, updateTicketErr := p.cxTicketClient.UpdateTicketAsync(ctx, &ticketPb.UpdateTicketAsyncRequest{
					Ticket: ticket,
				})
				if rpcErr := epifigrpc.RPCError(updateTicketRes, updateTicketErr); rpcErr != nil {
					// Log the error but continue processing
					logger.Error(ctx, "error updating ticket", zap.Int64("ticket_id", ticket.GetId()), zap.Error(rpcErr))
					errors = append(errors, fmt.Errorf("error updating ticket %d: %w", ticket.GetId(), rpcErr))
					continue
				}
			}
		}
	}

	// After processing all tickets, check if there were any errors
	if len(errors) > 0 {
		return fmt.Errorf("error failed to update: %v", errors)
	}
	return nil
}

// closeAllSalesOutcallTickets closes all sales outcall tickets for a user
func (p *DropOffOutcallUseCaseProcessor) closeAllSalesOutcallTickets(ctx context.Context, actorId string, emailId string) error {
	getSupportTicketsRes, getSupportTicketsErr := p.cxTicketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			IssueCategoryId: loansDropOffSalesOutcallIssueCategoryId,
			IdentifierType:  ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_EMAIL,
			IdentifierValue: emailId,
			FromTime:        timestamp.New(time.Now().Add(-time.Hour * 24 * 30)),
			StatusList:      ticketPb.GetActiveStatusList(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getSupportTicketsRes, getSupportTicketsErr); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		return fmt.Errorf("error getting support tickets for global close: %w", rpcErr)
	}

	var errorsList []error
	for _, ticket := range getSupportTicketsRes.GetTickets() {
		// Close ticket if Disposition1 is not already set (i.e., agent hasn't handled it)
		if ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition1() == "" {
			ticket.Status = ticketPb.Status_STATUS_CLOSED
			ticket.GetCustomFields().GetLoanOutcallMetadata().Disposition1 = string(palPkg.Autoclosed)
			updateTicketRes, updateTicketErr := p.cxTicketClient.UpdateTicketAsync(ctx, &ticketPb.UpdateTicketAsyncRequest{
				Ticket: ticket,
			})
			if rpcErr := epifigrpc.RPCError(updateTicketRes, updateTicketErr); rpcErr != nil {
				logger.Error(ctx, "error updating ticket during global close", zap.Int64("ticket_id", ticket.GetId()), zap.Error(rpcErr))
				errorsList = append(errorsList, fmt.Errorf("error updating ticket %d: %w", ticket.GetId(), rpcErr))
				continue
			}
			logger.Info(ctx, "Successfully closed sales ticket during global close", zap.Int64("ticket_id", ticket.GetId()), zap.String(logger.ACTOR_ID_V2, actorId))
		}
	}

	if len(errorsList) > 0 {
		return fmt.Errorf("encountered errors while globally closing sales tickets: %v", errorsList)
	}
	return nil
}

// nolint:dupl
func (p *DropOffOutcallUseCaseProcessor) closeServiceOutcallTicket(ctx context.Context, actorId string, emailId string, dropOffStage palPkg.DropOffStage) error {
	getActiveLoanRes, getActiveLoanErr := p.palClient.GetActiveLoanRequests(ctx, &palPb.GetActiveLoanRequestsRequest{
		ActorId:         actorId,
		LoanRequestType: palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
	})
	if rpcErr := epifigrpc.RPCError(getActiveLoanRes, getActiveLoanErr); rpcErr != nil {
		if errors.Is(rpcErr, epifierrors.ErrRecordNotFound) {
			return nil
		}
		return fmt.Errorf("error getting active loan: %w", rpcErr)
	}
	if len(getActiveLoanRes.GetActiveLoanRequests()) == 0 {
		return nil
	}
	latestActiveLoanReq := getActiveLoanRes.GetActiveLoanRequests()[0]

	getSupportTicketsRes, getSupportTicketsErr := p.cxTicketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			IssueCategoryId: loansDropOffServiceOutcallIssueCategoryId,
			IdentifierType:  ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_EMAIL,
			IdentifierValue: emailId,
			FromTime:        timestamp.New(time.Now().Add(-time.Hour * 24 * 30)),
			StatusList:      ticketPb.GetActiveStatusList(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getSupportTicketsRes, getSupportTicketsErr); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		return fmt.Errorf("error getting support tickets: %w", rpcErr)
	}
	// if we find any existing ticket with the same loan request id, tag and stage, update the existing ticket's status and disposition (D1) if action is not taken by agent
	var errors []error
	for _, ticket := range getSupportTicketsRes.GetTickets() {
		if ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanRequestId() == latestActiveLoanReq.GetId() && lo.Every(ticket.GetTags(), dropOffStage.GetStageTags()) {
			logger.Info(ctx, "ticket already exists for the user with the same loan request id", zap.String(logger.ACTOR_ID_V2, actorId))
			if ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition1() == "" {
				ticket.Status = ticketPb.Status_STATUS_CLOSED
				ticket.GetCustomFields().GetLoanOutcallMetadata().Disposition1 = string(palPkg.Autoclosed)
			}
			updateTicketRes, updateTicketErr := p.cxTicketClient.UpdateTicketAsync(ctx, &ticketPb.UpdateTicketAsyncRequest{
				Ticket: ticket,
			})
			if rpcErr := epifigrpc.RPCError(updateTicketRes, updateTicketErr); rpcErr != nil {
				// Log the error but continue processing
				logger.Error(ctx, "error updating ticket", zap.Int64("ticket_id", ticket.GetId()), zap.Error(rpcErr))
				errors = append(errors, fmt.Errorf("error updating ticket %d: %w", ticket.GetId(), rpcErr))
				continue
			}
		}
	}

	// After processing all tickets, check if there were any errors
	if len(errors) > 0 {
		return fmt.Errorf("error failed to update: %v", errors)
	}
	return nil
}

// closeAllServiceOutcallTickets closes all service outcall tickets for a loan application
func (p *DropOffOutcallUseCaseProcessor) closeAllServiceOutcallTickets(ctx context.Context, actorId string, emailId string) error {
	getCompletedLoanRes, getCompletedLoanErr := p.palClient.GetCompletedLoanRequests(ctx, &palPb.GetCompletedLoanRequestsRequest{
		ActorId:         actorId,
		LoanRequestType: palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
	})
	if rpcErr := epifigrpc.RPCError(getCompletedLoanRes, getCompletedLoanErr); rpcErr != nil {
		if getCompletedLoanRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "No completed loan request found for global service ticket closure", zap.String(logger.ACTOR_ID_V2, actorId))
			return nil
		}
		return fmt.Errorf("error getting completed loan requests for global close: %w", rpcErr)
	}
	if len(getCompletedLoanRes.GetCompletedLoanRequests()) == 0 {
		logger.Info(ctx, "No completed loan requests returned for global service ticket closure", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil
	}
	loanRequestIds := lo.Map(getCompletedLoanRes.GetCompletedLoanRequests(), func(lr *palPb.LoanRequest, _ int) string {
		return lr.GetId()
	})

	getSupportTicketsRes, getSupportTicketsErr := p.cxTicketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			IssueCategoryId: loansDropOffServiceOutcallIssueCategoryId,
			IdentifierType:  ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_EMAIL,
			IdentifierValue: emailId,
			FromTime:        timestamp.New(time.Now().Add(-time.Hour * 24 * 30)),
			StatusList:      ticketPb.GetActiveStatusList(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getSupportTicketsRes, getSupportTicketsErr); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		return fmt.Errorf("error getting support tickets for global close: %w", rpcErr)
	}

	var errorsList []error
	for _, ticket := range getSupportTicketsRes.GetTickets() {
		// Check if the ticket is for any completed loan request and disposition is not already set
		if lo.Contains(loanRequestIds, ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanRequestId()) && ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition1() == "" {
			loanRequestId := ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanRequestId()
			ticket.Status = ticketPb.Status_STATUS_CLOSED
			ticket.GetCustomFields().GetLoanOutcallMetadata().Disposition1 = string(palPkg.Autoclosed)
			updateTicketRes, updateTicketErr := p.cxTicketClient.UpdateTicketAsync(ctx, &ticketPb.UpdateTicketAsyncRequest{
				Ticket: ticket,
			})
			if rpcErr := epifigrpc.RPCError(updateTicketRes, updateTicketErr); rpcErr != nil {
				logger.Error(ctx, "error updating service ticket during global close", zap.Int64("ticket_id", ticket.GetId()), zap.String("loan_request_id", loanRequestId), zap.Error(rpcErr))
				errorsList = append(errorsList, fmt.Errorf("error updating ticket %d for loan %s: %w", ticket.GetId(), loanRequestId, rpcErr))
				continue
			}
			logger.Info(ctx, "Successfully closed service ticket during global close", zap.Int64("ticket_id", ticket.GetId()), zap.String("loan_request_id", loanRequestId), zap.String(logger.ACTOR_ID_V2, actorId))
		}
	}

	if len(errorsList) > 0 {
		return fmt.Errorf("encountered errors while globally closing service tickets for loan %v: %v", loanRequestIds, errorsList)
	}
	return nil
}

func validatedStageTags(tags []string) error {
	var invalidTags []string
	for _, tag := range tags {
		if len(tag) > 32 {
			invalidTags = append(invalidTags, tag)
		}
	}
	if len(invalidTags) > 0 {
		return fmt.Errorf("the following tags exceed 32 characters: %v", invalidTags)
	}
	return nil
}
