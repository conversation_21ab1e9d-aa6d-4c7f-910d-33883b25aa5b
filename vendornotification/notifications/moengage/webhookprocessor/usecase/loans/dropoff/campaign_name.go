package dropoff

import (
	"fmt"
	"strings"

	palPkg "github.com/epifi/gamma/preapprovedloan/pkg"
)

/*
Structure of campaign name for drop off out calling use case will be -
1. DropOffCategory
2. DropOffStage

All above fields will be joined by underscore.
*/

func ParseCampaignName(campaignName string) (DropOffCategory, palPkg.DropOffStage, error) {
	// Split campaign name by underscore and get fields
	campaignNameComponents := strings.Split(strings.ToUpper(strings.TrimSpace(campaignName)), "_")
	if len(campaignNameComponents) != 2 {
		return UnspecifiedDropOffCategory, palPkg.UnspecifiedDropOffStage, fmt.Errorf("invalid campaign name: %s", campaignName)
	}

	// convert the string to enums and add a validation check
	dropOffCategory := DropOffCategory(campaignNameComponents[0])
	dropOffStage := palPkg.DropOffStage(campaignNameComponents[1])
	if dropOffCategory == "" || dropOffStage == "" {
		return UnspecifiedDropOffCategory, palPkg.UnspecifiedDropOffStage, fmt.Errorf("invalid drop off category: %s or drop off stage: %s", campaignNameComponents[0], campaignNameComponents[1])
	}

	return dropOffCategory, dropOffStage, nil
}
