package area

import moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"

type ProcessRequest struct {
	ActorId      string
	Area         moengageVnPb.Area
	UseCase      moengageVnPb.UseCase
	CampaignName string
	CampaignMeta string
}

func (p *ProcessRequest) GetActorId() string {
	if p == nil {
		return ""
	}
	return p.ActorId
}

func (p *ProcessRequest) GetArea() moengageVnPb.Area {
	if p == nil {
		return moengageVnPb.Area_AREA_UNSPECIFIED
	}
	return p.Area
}

func (p *ProcessRequest) GetUseCase() moengageVnPb.UseCase {
	if p == nil {
		return moengageVnPb.UseCase_USE_CASE_UNSPECIFIED
	}
	return p.UseCase
}

func (p *ProcessRequest) GetCampaignName() string {
	if p == nil {
		return ""
	}
	return p.CampaignName
}

func (p *ProcessRequest) GetCampaignMeta() string {
	if p == nil {
		return ""
	}
	return p.CampaignMeta
}
