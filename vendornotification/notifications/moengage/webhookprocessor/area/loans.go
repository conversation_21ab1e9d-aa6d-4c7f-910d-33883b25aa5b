package area

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase"
	loansUsecase "github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase/loans"
)

// LoansAreaProcessor processes webhooks for the LOANS area
type LoansAreaProcessor struct {
	useCaseFactory *loansUsecase.LoansUseCaseProcessorFactory
}

// NewLoansAreaProcessor creates a new LoansAreaProcessor
func NewLoansAreaProcessor(useCaseFactory *loansUsecase.LoansUseCaseProcessorFactory) *LoansAreaProcessor {
	return &LoansAreaProcessor{
		useCaseFactory: useCaseFactory,
	}
}

// Process performs loan area specific processing and then delegates to the use case processor
func (p *LoansAreaProcessor) Process(ctx context.Context, request *ProcessRequest) error {
	useCaseProcessor, err := p.useCaseFactory.GetUseCaseProcessor(request.UseCase)
	if err != nil {
		logger.Error(ctx, "error getting use case processor",
			zap.String("area", request.Area.String()),
			zap.String("useCase", request.UseCase.String()),
			zap.Error(err))
		return fmt.Errorf("unsupported use case: %s for area: %s", request.UseCase.String(), request.Area.String())
	}

	return useCaseProcessor.Process(ctx, &usecase.ProcessRequest{
		ActorId:      request.ActorId,
		Area:         request.Area,
		UseCase:      request.UseCase,
		CampaignName: request.CampaignName,
		CampaignMeta: request.CampaignMeta,
	})
}
