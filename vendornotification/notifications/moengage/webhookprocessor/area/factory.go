package area

import (
	"fmt"

	"github.com/google/wire"

	moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
)

var AreaProcessorFactoryWireSet = wire.NewSet(NewAreaProcessorFactory, wire.Bind(new(IAreaProcessorFactory), new(*AreaProcessorFactory)))

// IAreaProcessorFactory creates area processors based on area name
type IAreaProcessorFactory interface {
	GetAreaProcessor(area moengageVnPb.Area) (IAreaProcessor, error)
}

type AreaProcessorFactory struct {
	loansAreaProcessor *LoansAreaProcessor
}

func NewAreaProcessorFactory(loansAreaProcessor *LoansAreaProcessor) *AreaProcessorFactory {
	return &AreaProcessorFactory{
		loansAreaProcessor: loansAreaProcessor,
	}
}

// compile time check to make sure AreaProcessorFactory implements IAreaProcessorFactory
var _ IAreaProcessorFactory = &AreaProcessorFactory{}

func (a *AreaProcessorFactory) GetAreaProcessor(area moengageVnPb.Area) (IAreaProcessor, error) {
	switch area {
	case moengageVnPb.Area_PRE_APPROVED_LOAN:
		return a.loansAreaProcessor, nil
	default:
		return nil, fmt.Errorf("unsupported area: %v", area)
	}
}
