package stocks

import (
	"context"
	"fmt"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	usStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	moengageStocksPb "github.com/epifi/gamma/api/vendors/moengage/stocks"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	userAttributeFetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
	vnTypes "github.com/epifi/gamma/vendornotification/types"
)

var StocksAttributeProviderWireSet = wire.NewSet(NewStocksAttributeProvider, wire.Bind(new(IStocksAttributeProvider), new(*StocksAttributeProvider)))

const MarketIndexCacheKeyPrefix = "STOCKS:MarketIdx"

type IStocksAttributeProvider interface {
	GetMarketIndexSummary(ctx context.Context, requestMeta map[userAttributeFetcher.RequestMetadataKey]string) (*moengageStocksPb.MarketIndexSummary, error)
	GetStockDetails(ctx context.Context, requestMeta map[userAttributeFetcher.RequestMetadataKey]string) (*moengageStocksPb.Stock, error)
}

func NewStocksAttributeProvider(
	ussCatalogClient usStocksCatalogPb.CatalogManagerClient,
	ussCache vnTypes.USStocksCacheStorage,
	timeHelper datetime.Time,
) *StocksAttributeProvider {
	return &StocksAttributeProvider{
		ussCache:         ussCache,
		ussCatalogClient: ussCatalogClient,
		timeHelper:       timeHelper,
	}
}

type StocksAttributeProvider struct {
	ussCache         cache.CacheStorage
	ussCatalogClient usStocksCatalogPb.CatalogManagerClient
	timeHelper       datetime.Time
}

// GetMarketIndexSummary returns latest available summary for a stock market index
func (s *StocksAttributeProvider) GetMarketIndexSummary(ctx context.Context, requestMeta map[userAttributeFetcher.RequestMetadataKey]string) (*moengageStocksPb.MarketIndexSummary, error) {
	indexName, ok := requestMeta[userAttributeFetcher.RequestMetadataKey_StockMarketIndex]
	if !ok {
		return nil, fmt.Errorf("missing mandatory key %v", userAttributeFetcher.RequestMetadataKey_StockMarketIndex)
	}

	indexSummary, err := s.getMarketIndexSummaryFromCache(ctx, indexName)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to get market index summary from cache: %w", err)
	}
	if indexSummary != nil {
		return indexSummary, nil
	}

	indexSummary, err = s.getMarketIndexSummary(ctx, indexName)
	if err != nil {
		return nil, fmt.Errorf("failed to get market index summary: %w", err)
	}

	return indexSummary, nil
}

func (s *StocksAttributeProvider) getMarketIndexSummary(ctx context.Context, indexName string) (*moengageStocksPb.MarketIndexSummary, error) {
	indexRes, err := s.ussCatalogClient.GetMarketIndex(ctx, &usStocksCatalogPb.GetMarketIndexRequest{IndexName: indexName, TopGainersListSize: 1, TopLosersListSize: 1})
	if err = epifigrpc.RPCError(indexRes, err); err != nil {
		return nil, fmt.Errorf("failed to get market index details %s: %w", indexName, err)
	}

	indexMsg := moengageStocksPb.NewUsStockIndexMessageParam(indexRes.GetIndex())

	var topGainerMsg, topLooserMsg *moengageStocksPb.Stock

	if len(indexRes.GetTopGainerStocks()) > 0 {
		topGainerMsg, err = moengageStocksPb.NewUsStockMessageParam(indexRes.GetTopGainerStocks()[0])
		if err != nil {
			return nil, fmt.Errorf("failed to create top gainer stock moengage message: %w", err)
		}
	}
	if len(indexRes.GetTopLoserStocks()) > 0 {
		topLooserMsg, err = moengageStocksPb.NewUsStockMessageParam(indexRes.GetTopLoserStocks()[0])
		if err != nil {
			return nil, fmt.Errorf("failed to create top gainer stock moengage message: %w", err)
		}
	}

	indexSummary := &moengageStocksPb.MarketIndexSummary{
		Index:     indexMsg,
		TopGainer: topGainerMsg,
		TopLooser: topLooserMsg,
	}

	err = s.setMarketIndexSummaryInCache(ctx, indexName, indexSummary)
	if err != nil {
		return nil, fmt.Errorf("failed to set market index sumamry in cache: %w", err)
	}
	return indexSummary, nil
}

func marketIndexStatusCacheKey(indexName string) string {
	return fmt.Sprintf("%s:%s", MarketIndexCacheKeyPrefix, indexName)
}

func (s *StocksAttributeProvider) getMarketIndexSummaryFromCache(
	ctx context.Context,
	indexName string,
) (*moengageStocksPb.MarketIndexSummary, error) {
	cachedMarketStatusByt, err := s.ussCache.Get(ctx, marketIndexStatusCacheKey(indexName))
	if err != nil {
		return nil, fmt.Errorf("failed to get market summary from cache: %w", err)
	}
	cacheMsg := &moengageStocksPb.MarketIndexSummaryCacheMessage{}
	err = proto.Unmarshal([]byte(cachedMarketStatusByt), cacheMsg)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshall cached market index summary: %w", err)
	}

	if s.timeHelper.Now().Sub(cacheMsg.GetUpdatedAt().AsTime()) > 5*time.Minute {
		cloneCtx := epificontext.CloneCtx(ctx)
		goroutine.Run(cloneCtx, 30*time.Second, func(ctx context.Context) {
			_, err := s.getMarketIndexSummary(ctx, indexName)
			if err != nil {
				logger.Error(ctx, "failed to refresh market index summary in background", zap.Error(err))
			}
		})
	}

	return cacheMsg.GetMarketIndexSummary(), nil
}

func (s *StocksAttributeProvider) setMarketIndexSummaryInCache(
	ctx context.Context,
	indexName string,
	indexSummary *moengageStocksPb.MarketIndexSummary,
) error {
	cacheMsg := &moengageStocksPb.MarketIndexSummaryCacheMessage{
		MarketIndexSummary: indexSummary,
		UpdatedAt:          timestampPb.New(s.timeHelper.Now()),
	}
	buf, err := proto.Marshal(cacheMsg)
	if err != nil {
		return fmt.Errorf("failed to marshall market summary: %w", err)
	}

	err = s.ussCache.Set(ctx, marketIndexStatusCacheKey(indexName), string(buf), 10*time.Minute)
	if err != nil {
		return fmt.Errorf("faield to set market summary in cache: %w", err)
	}
	return nil
}
