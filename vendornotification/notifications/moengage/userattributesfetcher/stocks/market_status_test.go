package stocks

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	usstockscatalogpb "github.com/epifi/gamma/api/usstocks/catalog"
	moengageStocksPb "github.com/epifi/gamma/api/vendors/moengage/stocks"
	userAttributeFetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
)

func TestStocksAttributeProvider_GetMarketIndexSummary(t *testing.T) {
	logger.Init(cfg.TestEnv)
	tests := []struct {
		name        string
		requestMeta map[userAttributeFetcher.RequestMetadataKey]string
		before      func(m *mockFields)
		want        *moengageStocksPb.MarketIndexSummary
		wantErr     bool
	}{
		{
			name: "Successfully get market index summary",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockMarketIndex: "Nasdaq100",
			},
			before: func(m *mockFields) {
				m.mocksUssCacheStorage.EXPECT().Get(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100")).Return("", epifierrors.ErrRecordNotFound)
				m.mockUssCatalogClient.EXPECT().GetMarketIndex(gomock.Any(), &usstockscatalogpb.GetMarketIndexRequest{
					IndexName:          "Nasdaq100",
					TopGainersListSize: 1, TopLosersListSize: 1,
				}).Return(&usstockscatalogpb.GetMarketIndexResponse{
					Status: rpcPb.StatusOk(),
					Index: &usstockscatalogpb.MarketIndex{
						Name:         "Nasdaq100",
						DisplayName:  "Nasdaq 100",
						Constituents: []string{"GOOG", "APPL", "META", "INTC"},
						Performance: &usstockscatalogpb.IndexPerformance{
							DailyPercentChange: 2.7861,
						},
					},
					TopLoserStocks: []*usstockscatalogpb.Stock{
						{
							Id:     "id2",
							Symbol: "META",
							StockBasicDetails: &usstockscatalogpb.StockBasicDetails{
								Name: &usstockscatalogpb.CompanyName{ShortName: "Meta"},
							},
							DailyPerformance: &usstockscatalogpb.DailyPerformance{DailyPercentChange: -6.79887},
						},
					},
					TopGainerStocks: []*usstockscatalogpb.Stock{
						{
							Id:     "id3",
							Symbol: "GOOG",
							StockBasicDetails: &usstockscatalogpb.StockBasicDetails{
								Name: &usstockscatalogpb.CompanyName{ShortName: "Google"},
							},
							DailyPerformance: &usstockscatalogpb.DailyPerformance{DailyPercentChange: 2.712},
						},
					},
				}, nil)

				m.mockTime.EXPECT().Now().Return(time.Date(2024, 03, 1, 0, 0, 0, 0, datetime.IST))
				m.mocksUssCacheStorage.EXPECT().Set(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100"),
					gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &moengageStocksPb.MarketIndexSummary{
				Index: &moengageStocksPb.Index{
					DisplayName: "Nasdaq 100",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "2.78",
						Value:        2.7861,
					},
				},
				TopGainer: &moengageStocksPb.Stock{
					Symbol:            "GOOG",
					DisplayName:       "Google",
					Base64DeeplinkUrl: "fi://screen?data=CPQCygsHCgNpZDMYAQ==",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "2.71",
						Value:        2.712,
					},
				},
				TopLooser: &moengageStocksPb.Stock{
					Symbol:            "META",
					DisplayName:       "Meta",
					Base64DeeplinkUrl: "fi://screen?data=CPQCygsHCgNpZDIYAQ==",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "-6.79",
						Value:        -6.79887,
					},
				},
			},
		},
		{
			name: "Successfully get market index summary with no stock in top looser",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockMarketIndex: "Nasdaq100",
			},
			before: func(m *mockFields) {
				m.mocksUssCacheStorage.EXPECT().Get(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100")).Return("", epifierrors.ErrRecordNotFound)
				m.mockUssCatalogClient.EXPECT().GetMarketIndex(gomock.Any(), &usstockscatalogpb.GetMarketIndexRequest{
					IndexName:          "Nasdaq100",
					TopGainersListSize: 1, TopLosersListSize: 1,
				}).Return(&usstockscatalogpb.GetMarketIndexResponse{
					Status: rpcPb.StatusOk(),
					Index: &usstockscatalogpb.MarketIndex{
						Name:         "Nasdaq100",
						DisplayName:  "Nasdaq 100",
						Constituents: []string{"GOOG"},
						Performance: &usstockscatalogpb.IndexPerformance{
							DailyPercentChange: 2.7861,
						},
					},
					TopGainerStocks: []*usstockscatalogpb.Stock{
						{
							Id:     "id1",
							Symbol: "GOOG",
							StockBasicDetails: &usstockscatalogpb.StockBasicDetails{
								Name: &usstockscatalogpb.CompanyName{ShortName: "Google"},
							},
							DailyPerformance: &usstockscatalogpb.DailyPerformance{DailyPercentChange: 2.712},
						},
					},
				}, nil)

				m.mockTime.EXPECT().Now().Return(time.Date(2024, 03, 1, 0, 0, 0, 0, datetime.IST))
				m.mocksUssCacheStorage.EXPECT().Set(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100"),
					gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &moengageStocksPb.MarketIndexSummary{
				Index: &moengageStocksPb.Index{
					DisplayName: "Nasdaq 100",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "2.78",
						Value:        2.7861,
					},
				},
				TopGainer: &moengageStocksPb.Stock{
					Symbol:            "GOOG",
					DisplayName:       "Google",
					Base64DeeplinkUrl: "fi://screen?data=CPQCygsHCgNpZDEYAQ==",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "2.71",
						Value:        2.712,
					},
				},
			},
		},
		{
			name: "Successfully get market index summary with no stock in top gainer",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockMarketIndex: "Nasdaq100",
			},
			before: func(m *mockFields) {
				m.mocksUssCacheStorage.EXPECT().Get(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100")).Return("", epifierrors.ErrRecordNotFound)
				m.mockUssCatalogClient.EXPECT().GetMarketIndex(gomock.Any(), &usstockscatalogpb.GetMarketIndexRequest{
					IndexName:          "Nasdaq100",
					TopGainersListSize: 1, TopLosersListSize: 1,
				}).Return(&usstockscatalogpb.GetMarketIndexResponse{
					Status: rpcPb.StatusOk(),
					Index: &usstockscatalogpb.MarketIndex{
						Name:         "Nasdaq100",
						DisplayName:  "Nasdaq 100",
						Constituents: []string{"META"},
						Performance: &usstockscatalogpb.IndexPerformance{
							DailyPercentChange: 2.7861,
						},
					},
					TopLoserStocks: []*usstockscatalogpb.Stock{
						{
							Id:     "id1",
							Symbol: "META",
							StockBasicDetails: &usstockscatalogpb.StockBasicDetails{
								Name: &usstockscatalogpb.CompanyName{ShortName: "Meta"},
							},
							DailyPerformance: &usstockscatalogpb.DailyPerformance{DailyPercentChange: -6.79887},
						},
					},
				}, nil)

				m.mockTime.EXPECT().Now().Return(time.Date(2024, 03, 1, 0, 0, 0, 0, datetime.IST))
				m.mocksUssCacheStorage.EXPECT().Set(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100"),
					gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &moengageStocksPb.MarketIndexSummary{
				Index: &moengageStocksPb.Index{
					DisplayName: "Nasdaq 100",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "2.78",
						Value:        2.7861,
					},
				},
				TopLooser: &moengageStocksPb.Stock{
					Symbol:            "META",
					DisplayName:       "Meta",
					Base64DeeplinkUrl: "fi://screen?data=CPQCygsHCgNpZDEYAQ==",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "-6.79",
						Value:        -6.79887,
					},
				},
			},
		},
		{
			name: "Successfully get market index summary from cache",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockMarketIndex: "Nasdaq100",
			},
			before: func(m *mockFields) {
				buf, _ := proto.Marshal(&moengageStocksPb.MarketIndexSummaryCacheMessage{
					MarketIndexSummary: &moengageStocksPb.MarketIndexSummary{
						Index: &moengageStocksPb.Index{
							DisplayName: "Nasdaq 100",
							DailyPercentageChange: &moengageStocksPb.NumericMetric{
								DisplayValue: "2.78",
								Value:        2.7861,
							},
						},
						TopLooser: &moengageStocksPb.Stock{
							Symbol:      "META",
							DisplayName: "Meta",
							DailyPercentageChange: &moengageStocksPb.NumericMetric{
								DisplayValue: "-6.79",
								Value:        -6.79887,
							},
						},
					},
					UpdatedAt: timestampPb.New(time.Date(2024, 03, 1, 0, 0, 0, 0, datetime.IST)),
				})
				m.mockTime.EXPECT().Now().Return(time.Date(2024, 03, 1, 0, 0, 0, 0, datetime.IST))
				m.mocksUssCacheStorage.EXPECT().Get(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100")).Return(string(buf), nil)
			},
			want: &moengageStocksPb.MarketIndexSummary{
				Index: &moengageStocksPb.Index{
					DisplayName: "Nasdaq 100",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "2.78",
						Value:        2.7861,
					},
				},
				TopLooser: &moengageStocksPb.Stock{
					Symbol:      "META",
					DisplayName: "Meta",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "-6.79",
						Value:        -6.79887,
					},
				},
			},
		},
		{
			name: "Successfully get market index summary from cache and background refresh started",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockMarketIndex: "Nasdaq100",
			},
			before: func(m *mockFields) {
				buf, _ := proto.Marshal(&moengageStocksPb.MarketIndexSummaryCacheMessage{
					MarketIndexSummary: &moengageStocksPb.MarketIndexSummary{
						Index: &moengageStocksPb.Index{
							DisplayName: "Nasdaq 100",
							DailyPercentageChange: &moengageStocksPb.NumericMetric{
								DisplayValue: "2.78",
								Value:        2.7861,
							},
						},
						TopLooser: &moengageStocksPb.Stock{
							Symbol:      "META",
							DisplayName: "Meta",
							DailyPercentageChange: &moengageStocksPb.NumericMetric{
								DisplayValue: "-6.79",
								Value:        -6.79887,
							},
						},
					},
					UpdatedAt: timestampPb.New(time.Date(2024, 03, 1, 0, 0, 0, 0, datetime.IST)),
				})
				m.mockTime.EXPECT().Now().Return(time.Date(2024, 03, 2, 0, 0, 0, 0, datetime.IST)).MinTimes(1)
				m.mocksUssCacheStorage.EXPECT().Get(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100")).Return(string(buf), nil)

				m.mockUssCatalogClient.EXPECT().GetMarketIndex(gomock.Any(), &usstockscatalogpb.GetMarketIndexRequest{
					IndexName:          "Nasdaq100",
					TopGainersListSize: 1, TopLosersListSize: 1,
				}).Return(&usstockscatalogpb.GetMarketIndexResponse{
					Status: rpcPb.StatusOk(),
					Index: &usstockscatalogpb.MarketIndex{
						Name:         "Nasdaq100",
						DisplayName:  "Nasdaq 100",
						Constituents: []string{"GOOG"},
					},
				}, nil).MaxTimes(1)
				m.mocksUssCacheStorage.EXPECT().Set(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100"),
					gomock.Any(), gomock.Any()).Return(nil).MaxTimes(1)
			},
			want: &moengageStocksPb.MarketIndexSummary{
				Index: &moengageStocksPb.Index{
					DisplayName: "Nasdaq 100",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "2.78",
						Value:        2.7861,
					},
				},
				TopLooser: &moengageStocksPb.Stock{
					Symbol:      "META",
					DisplayName: "Meta",
					DailyPercentageChange: &moengageStocksPb.NumericMetric{
						DisplayValue: "-6.79",
						Value:        -6.79887,
					},
				},
			},
		},
		{
			name: "Error in getting index details should lead to error",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockMarketIndex: "Nasdaq100",
			},
			before: func(m *mockFields) {
				m.mocksUssCacheStorage.EXPECT().Get(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100")).Return("", epifierrors.ErrRecordNotFound)
				m.mockUssCatalogClient.EXPECT().GetMarketIndex(gomock.Any(), &usstockscatalogpb.GetMarketIndexRequest{
					IndexName:          "Nasdaq100",
					TopGainersListSize: 1, TopLosersListSize: 1,
				}).Return(&usstockscatalogpb.GetMarketIndexResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Error in getting market index status from cache should lead to error",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockMarketIndex: "Nasdaq100",
			},
			before: func(m *mockFields) {
				m.mocksUssCacheStorage.EXPECT().Get(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100")).Return("", fmt.Errorf("some err"))
			},
			wantErr: true,
		},
		{
			name: "Error in unmarshalling market index status from cache should lead to error",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockMarketIndex: "Nasdaq100",
			},
			before: func(m *mockFields) {
				m.mocksUssCacheStorage.EXPECT().Get(gomock.Any(), marketIndexStatusCacheKey("Nasdaq100")).Return("invalidproto", nil)
			},
			wantErr: true,
		},
		{
			name:        "Missing stock market index key in request meta should lead to error",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{},
			before:      func(m *mockFields) {},
			wantErr:     true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.before(m)
			s := NewStocksAttributeProvider(m.mockUssCatalogClient, m.mocksUssCacheStorage, m.mockTime)
			got, err := s.GetMarketIndexSummary(context.Background(), tt.requestMeta)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMarketIndexSummary() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("GetMarketIndexSummary() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}
