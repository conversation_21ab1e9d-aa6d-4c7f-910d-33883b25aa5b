package stocks

import (
	"github.com/golang/mock/gomock"

	mockUssStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog/mocks"
	mockCache "github.com/epifi/be-common/pkg/cache/mocks"
	mockDateTime "github.com/epifi/be-common/pkg/datetime/mocks"
)

type mockFields struct {
	mockUssCatalogClient *mockUssStocksCatalogPb.MockCatalogManagerClient
	mocksUssCacheStorage *mockCache.MockCacheStorage
	mockTime             *mockDateTime.MockTime
}

func initMocks(ctl *gomock.Controller) *mockFields {
	return &mockFields{
		mockUssCatalogClient: mockUssStocksCatalogPb.NewMockCatalogManagerClient(ctl),
		mocksUssCacheStorage: mockCache.NewMockCacheStorage(ctl),
		mockTime:             mockDateTime.NewMockTime(ctl),
	}
}
