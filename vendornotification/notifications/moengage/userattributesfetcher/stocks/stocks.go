package stocks

import (
	"context"
	"fmt"

	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
	userAttributeFetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
)

type StocksAttributeFetcher struct {
	stocksAttributeProvider IStocksAttributeProvider
}

func NewStocksAttributeFetcher(stocksAttributeProvider IStocksAttributeProvider) *StocksAttributeFetcher {
	return &StocksAttributeFetcher{
		stocksAttributeProvider: stocksAttributeProvider,
	}
}

func (s *StocksAttributeFetcher) GetAttributes(ctx context.Context, req *userAttributeFetcher.GetAttributesRequest) (*userAttributeFetcher.GetAttributesResponse, error) {
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case userAttributeFetcher.UserAttributesReqField_Summary:
			summary, err := s.stocksAttributeProvider.GetMarketIndexSummary(ctx, req.GetRequestMetadata())
			if err != nil {
				return nil, fmt.Errorf("failed to get market index summary: %w", err)
			}
			fieldToUserAttributesMap[string(field)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_MarketIndexSummary{
					MarketIndexSummary: summary,
				},
			}
		case userAttributeFetcher.UserAttributesReqField_StockDetails:
			stock, err := s.stocksAttributeProvider.GetStockDetails(ctx, req.GetRequestMetadata())
			if err != nil {
				return nil, fmt.Errorf("failed to get stock details: %w", err)
			}
			fieldToUserAttributesMap[string(field)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_StockDetails{
					StockDetails: stock,
				},
			}
		default:
			return nil, fmt.Errorf("unsupported parameter field encountered in stocks attributes fetcher %v", field)
		}
	}
	return &userAttributeFetcher.GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}
