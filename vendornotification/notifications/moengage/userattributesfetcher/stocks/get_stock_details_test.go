package stocks

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	rpcPb "github.com/epifi/be-common/api/rpc"

	usstockscatalogpb "github.com/epifi/gamma/api/usstocks/catalog"
	moengageStocksPb "github.com/epifi/gamma/api/vendors/moengage/stocks"
	userAttributeFetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
)

func TestStocksAttributeProvider_GetStockDetails(t *testing.T) {
	tests := []struct {
		name        string
		requestMeta map[userAttributeFetcher.RequestMetadataKey]string
		before      func(m *mockFields)
		want        *moengageStocksPb.Stock
		wantErr     bool
	}{
		{
			name: "Successfully get stock details",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockId: "stockId",
			},
			before: func(m *mockFields) {
				m.mockUssCatalogClient.EXPECT().GetStocks(gomock.Any(), &usstockscatalogpb.GetStocksRequest{
					Identifiers: &usstockscatalogpb.GetStocksRequest_StockIds{
						StockIds: &usstockscatalogpb.RepeatedStrings{Ids: []string{"stockId"}},
					},
				}).Return(&usstockscatalogpb.GetStocksResponse{
					Status: rpcPb.StatusOk(),
					Stocks: map[string]*usstockscatalogpb.Stock{
						"stockId": {
							Id:     "stockId",
							Symbol: "GOOG",
							StockBasicDetails: &usstockscatalogpb.StockBasicDetails{
								Name: &usstockscatalogpb.CompanyName{ShortName: "Google"},
							},
							DailyPerformance: &usstockscatalogpb.DailyPerformance{
								DailyPercentChange: 2.712,
								Close:              &moneyPb.Money{CurrencyCode: "USD", Units: 10, Nanos: *********},
							},
							EstimatesInfo: &usstockscatalogpb.EstimatesInfo{
								AnalystRecommendations: &usstockscatalogpb.AnalystRecommendations{
									Buy:          2,
									Outperform:   1,
									Hold:         2,
									Underperform: 3,
									Sell:         0,
									NoOpinion:    0,
								},
								AnalystEstimates: &usstockscatalogpb.AnalystEstimates{
									TargetPriceEstimates: &usstockscatalogpb.TargetPriceEstimates{
										PeriodicTargetPriceEstimates: []*usstockscatalogpb.PeriodicTargetPriceEstimates{
											{
												High:           150,
												Low:            120,
												Mean:           130,
												Median:         132,
												NumOfEstimates: 8,
											},
										},
									},
								},
							},
						},
					},
				}, nil)

				m.mockUssCatalogClient.EXPECT().GetHistoricalStockPrices(gomock.Any(),
					&usstockscatalogpb.GetHistoricalStockPricesRequest{StockId: "stockId"}).Return(&usstockscatalogpb.GetHistoricalStockPricesResponse{
					Status: rpcPb.StatusOk(),
					StockPricesForPeriods: []*usstockscatalogpb.StockPricesForPeriod{
						{
							Period:                "1D",
							PercentagePriceChange: 1.34,
						},
						{
							Period:                "1W",
							PercentagePriceChange: -2.5,
						},
						{
							Period:                "1M",
							PercentagePriceChange: 4.11,
						},
						{
							Period:                "1Y",
							PercentagePriceChange: 20.78,
						},
						{
							Period:                "5Y",
							PercentagePriceChange: 100.24,
						},
					},
				}, nil)
			},
			want: &moengageStocksPb.Stock{
				Symbol:            "GOOG",
				DisplayName:       "Google",
				Base64DeeplinkUrl: "fi://screen?data=CPQCygsLCgdzdG9ja0lkGAE=",
				DailyPercentageChange: &moengageStocksPb.NumericMetric{
					DisplayValue: "2.71",
					Value:        2.712,
				},
				LastKnownPrice: &moengageStocksPb.NumericMetric{
					DisplayValue: "10.23",
					Value:        10.23,
				},
				AnalystRating: &moengageStocksPb.AnalystRating{
					PercentageBuy: &moengageStocksPb.NumericMetric{
						DisplayValue: "37.5",
						Value:        37.5,
					},
					TargetPrice: &moengageStocksPb.AnalystRating_TargetPrice{
						UpperBound: &moengageStocksPb.NumericMetric{
							DisplayValue: "150",
							Value:        150,
						},
						LowerBound: &moengageStocksPb.NumericMetric{
							DisplayValue: "120",
							Value:        120,
						},
					},
				},
				HistoricalPerformance: &moengageStocksPb.HistoricalPerformance{
					OneDay: &moengageStocksPb.PastPerformance{
						PercentageChange: &moengageStocksPb.NumericMetric{
							DisplayValue: "1.34",
							Value:        1.34,
						},
					},
					OneWeek: &moengageStocksPb.PastPerformance{
						PercentageChange: &moengageStocksPb.NumericMetric{
							DisplayValue: "-2.5",
							Value:        -2.5,
						},
					},
					OneMonth: &moengageStocksPb.PastPerformance{
						PercentageChange: &moengageStocksPb.NumericMetric{
							DisplayValue: "4.11",
							Value:        4.11,
						},
					},
					OneYear: &moengageStocksPb.PastPerformance{
						PercentageChange: &moengageStocksPb.NumericMetric{
							DisplayValue: "20.78",
							Value:        20.78,
						},
					},
					FiveYear: &moengageStocksPb.PastPerformance{
						PercentageChange: &moengageStocksPb.NumericMetric{
							DisplayValue: "100.24",
							Value:        100.24,
						},
					},
				},
			},
		},
		{
			name: "Successfully get stock details with only 1 day historical performances available",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockId: "stockId",
			},
			before: func(m *mockFields) {
				m.mockUssCatalogClient.EXPECT().GetStocks(gomock.Any(), &usstockscatalogpb.GetStocksRequest{
					Identifiers: &usstockscatalogpb.GetStocksRequest_StockIds{
						StockIds: &usstockscatalogpb.RepeatedStrings{Ids: []string{"stockId"}},
					},
				}).Return(&usstockscatalogpb.GetStocksResponse{
					Status: rpcPb.StatusOk(),
					Stocks: map[string]*usstockscatalogpb.Stock{
						"stockId": {
							Id:     "stockId",
							Symbol: "GOOG",
							StockBasicDetails: &usstockscatalogpb.StockBasicDetails{
								Name: &usstockscatalogpb.CompanyName{ShortName: "Google"},
							},
							DailyPerformance: &usstockscatalogpb.DailyPerformance{DailyPercentChange: 2.712},
							EstimatesInfo: &usstockscatalogpb.EstimatesInfo{
								AnalystRecommendations: &usstockscatalogpb.AnalystRecommendations{
									Buy:          2,
									Outperform:   1,
									Hold:         2,
									Underperform: 3,
									Sell:         0,
									NoOpinion:    0,
								},
								AnalystEstimates: &usstockscatalogpb.AnalystEstimates{
									TargetPriceEstimates: &usstockscatalogpb.TargetPriceEstimates{
										PeriodicTargetPriceEstimates: []*usstockscatalogpb.PeriodicTargetPriceEstimates{
											{
												High:           150,
												Low:            120,
												Mean:           130,
												Median:         132,
												NumOfEstimates: 8,
											},
										},
									},
								},
							},
						},
					},
				}, nil)

				m.mockUssCatalogClient.EXPECT().GetHistoricalStockPrices(gomock.Any(),
					&usstockscatalogpb.GetHistoricalStockPricesRequest{StockId: "stockId"}).Return(&usstockscatalogpb.GetHistoricalStockPricesResponse{
					Status: rpcPb.StatusOk(),
					StockPricesForPeriods: []*usstockscatalogpb.StockPricesForPeriod{
						{
							Period:                "1D",
							PercentagePriceChange: 1.34,
						},
					},
				}, nil)
			},
			want: &moengageStocksPb.Stock{
				Symbol:            "GOOG",
				DisplayName:       "Google",
				Base64DeeplinkUrl: "fi://screen?data=CPQCygsLCgdzdG9ja0lkGAE=",
				DailyPercentageChange: &moengageStocksPb.NumericMetric{
					DisplayValue: "2.71",
					Value:        2.712,
				},
				AnalystRating: &moengageStocksPb.AnalystRating{
					PercentageBuy: &moengageStocksPb.NumericMetric{
						DisplayValue: "37.5",
						Value:        37.5,
					},
					TargetPrice: &moengageStocksPb.AnalystRating_TargetPrice{
						UpperBound: &moengageStocksPb.NumericMetric{
							DisplayValue: "150",
							Value:        150,
						},
						LowerBound: &moengageStocksPb.NumericMetric{
							DisplayValue: "120",
							Value:        120,
						},
					},
				},
				HistoricalPerformance: &moengageStocksPb.HistoricalPerformance{
					OneDay: &moengageStocksPb.PastPerformance{
						PercentageChange: &moengageStocksPb.NumericMetric{
							DisplayValue: "1.34",
							Value:        1.34,
						},
					},
				},
			},
		},
		{
			name: "Successfully get stock details with missing historical performances",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockId: "stockId",
			},
			before: func(m *mockFields) {
				m.mockUssCatalogClient.EXPECT().GetStocks(gomock.Any(), &usstockscatalogpb.GetStocksRequest{
					Identifiers: &usstockscatalogpb.GetStocksRequest_StockIds{
						StockIds: &usstockscatalogpb.RepeatedStrings{Ids: []string{"stockId"}},
					},
				}).Return(&usstockscatalogpb.GetStocksResponse{
					Status: rpcPb.StatusOk(),
					Stocks: map[string]*usstockscatalogpb.Stock{
						"stockId": {
							Id:     "stockId",
							Symbol: "GOOG",
							StockBasicDetails: &usstockscatalogpb.StockBasicDetails{
								Name: &usstockscatalogpb.CompanyName{ShortName: "Google"},
							},
							DailyPerformance: &usstockscatalogpb.DailyPerformance{DailyPercentChange: 2.712},
							EstimatesInfo: &usstockscatalogpb.EstimatesInfo{
								AnalystRecommendations: &usstockscatalogpb.AnalystRecommendations{
									Buy:          2,
									Outperform:   1,
									Hold:         2,
									Underperform: 3,
									Sell:         0,
									NoOpinion:    0,
								},
								AnalystEstimates: &usstockscatalogpb.AnalystEstimates{
									TargetPriceEstimates: &usstockscatalogpb.TargetPriceEstimates{
										PeriodicTargetPriceEstimates: []*usstockscatalogpb.PeriodicTargetPriceEstimates{
											{
												High:           150,
												Low:            120,
												Mean:           130,
												Median:         132,
												NumOfEstimates: 8,
											},
										},
									},
								},
							},
						},
					},
				}, nil)

				m.mockUssCatalogClient.EXPECT().GetHistoricalStockPrices(gomock.Any(),
					&usstockscatalogpb.GetHistoricalStockPricesRequest{StockId: "stockId"}).Return(&usstockscatalogpb.GetHistoricalStockPricesResponse{
					Status:                rpcPb.StatusOk(),
					StockPricesForPeriods: []*usstockscatalogpb.StockPricesForPeriod{},
				}, nil)
			},
			want: &moengageStocksPb.Stock{
				Symbol:            "GOOG",
				DisplayName:       "Google",
				Base64DeeplinkUrl: "fi://screen?data=CPQCygsLCgdzdG9ja0lkGAE=",
				DailyPercentageChange: &moengageStocksPb.NumericMetric{
					DisplayValue: "2.71",
					Value:        2.712,
				},
				AnalystRating: &moengageStocksPb.AnalystRating{
					PercentageBuy: &moengageStocksPb.NumericMetric{
						DisplayValue: "37.5",
						Value:        37.5,
					},
					TargetPrice: &moengageStocksPb.AnalystRating_TargetPrice{
						UpperBound: &moengageStocksPb.NumericMetric{
							DisplayValue: "150",
							Value:        150,
						},
						LowerBound: &moengageStocksPb.NumericMetric{
							DisplayValue: "120",
							Value:        120,
						},
					},
				},
			},
		},
		{
			name: "Successfully get stock details with missing target price estimates and historical performances",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockId: "stockId",
			},
			before: func(m *mockFields) {
				m.mockUssCatalogClient.EXPECT().GetStocks(gomock.Any(), &usstockscatalogpb.GetStocksRequest{
					Identifiers: &usstockscatalogpb.GetStocksRequest_StockIds{
						StockIds: &usstockscatalogpb.RepeatedStrings{Ids: []string{"stockId"}},
					},
				}).Return(&usstockscatalogpb.GetStocksResponse{
					Status: rpcPb.StatusOk(),
					Stocks: map[string]*usstockscatalogpb.Stock{
						"stockId": {
							Id:     "stockId",
							Symbol: "GOOG",
							StockBasicDetails: &usstockscatalogpb.StockBasicDetails{
								Name: &usstockscatalogpb.CompanyName{ShortName: "Google"},
							},
							DailyPerformance: &usstockscatalogpb.DailyPerformance{DailyPercentChange: 2.712},
							EstimatesInfo: &usstockscatalogpb.EstimatesInfo{
								AnalystRecommendations: &usstockscatalogpb.AnalystRecommendations{
									Buy:          2,
									Outperform:   1,
									Hold:         2,
									Underperform: 3,
									Sell:         0,
									NoOpinion:    0,
								},
							},
						},
					},
				}, nil)

				m.mockUssCatalogClient.EXPECT().GetHistoricalStockPrices(gomock.Any(),
					&usstockscatalogpb.GetHistoricalStockPricesRequest{StockId: "stockId"}).Return(&usstockscatalogpb.GetHistoricalStockPricesResponse{
					Status:                rpcPb.StatusOk(),
					StockPricesForPeriods: []*usstockscatalogpb.StockPricesForPeriod{},
				}, nil)
			},
			want: &moengageStocksPb.Stock{
				Symbol:            "GOOG",
				DisplayName:       "Google",
				Base64DeeplinkUrl: "fi://screen?data=CPQCygsLCgdzdG9ja0lkGAE=",
				DailyPercentageChange: &moengageStocksPb.NumericMetric{
					DisplayValue: "2.71",
					Value:        2.712,
				},
				AnalystRating: &moengageStocksPb.AnalystRating{
					PercentageBuy: &moengageStocksPb.NumericMetric{
						DisplayValue: "37.5",
						Value:        37.5,
					},
				},
			},
		},
		{
			name: "Successfully get stock details without analyst rating and historical performances",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockId: "stockId",
			},
			before: func(m *mockFields) {
				m.mockUssCatalogClient.EXPECT().GetStocks(gomock.Any(), &usstockscatalogpb.GetStocksRequest{
					Identifiers: &usstockscatalogpb.GetStocksRequest_StockIds{
						StockIds: &usstockscatalogpb.RepeatedStrings{Ids: []string{"stockId"}},
					},
				}).Return(&usstockscatalogpb.GetStocksResponse{
					Status: rpcPb.StatusOk(),
					Stocks: map[string]*usstockscatalogpb.Stock{
						"stockId": {
							Id:     "stockId",
							Symbol: "GOOG",
							StockBasicDetails: &usstockscatalogpb.StockBasicDetails{
								Name: &usstockscatalogpb.CompanyName{ShortName: "Google"},
							},
							DailyPerformance: &usstockscatalogpb.DailyPerformance{DailyPercentChange: 2.712},
						},
					},
				}, nil)

				m.mockUssCatalogClient.EXPECT().GetHistoricalStockPrices(gomock.Any(),
					&usstockscatalogpb.GetHistoricalStockPricesRequest{StockId: "stockId"}).Return(&usstockscatalogpb.GetHistoricalStockPricesResponse{
					Status:                rpcPb.StatusOk(),
					StockPricesForPeriods: []*usstockscatalogpb.StockPricesForPeriod{},
				}, nil)
			},
			want: &moengageStocksPb.Stock{
				Symbol:            "GOOG",
				DisplayName:       "Google",
				Base64DeeplinkUrl: "fi://screen?data=CPQCygsLCgdzdG9ja0lkGAE=",
				DailyPercentageChange: &moengageStocksPb.NumericMetric{
					DisplayValue: "2.71",
					Value:        2.712,
				},
			},
		},
		{
			name: "Failure to get historical prices should lead to error",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockId: "stockId",
			},
			before: func(m *mockFields) {
				m.mockUssCatalogClient.EXPECT().GetStocks(gomock.Any(), &usstockscatalogpb.GetStocksRequest{
					Identifiers: &usstockscatalogpb.GetStocksRequest_StockIds{
						StockIds: &usstockscatalogpb.RepeatedStrings{Ids: []string{"stockId"}},
					},
				}).Return(&usstockscatalogpb.GetStocksResponse{
					Status: rpcPb.StatusOk(),
					Stocks: map[string]*usstockscatalogpb.Stock{
						"stockId": {
							Symbol: "GOOG",
						},
					},
				}, nil).MaxTimes(1)

				m.mockUssCatalogClient.EXPECT().GetHistoricalStockPrices(gomock.Any(),
					&usstockscatalogpb.GetHistoricalStockPricesRequest{StockId: "stockId"}).Return(&usstockscatalogpb.GetHistoricalStockPricesResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Failure to get stock should lead to error",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockId: "stockId",
			},
			before: func(m *mockFields) {
				m.mockUssCatalogClient.EXPECT().GetStocks(gomock.Any(), &usstockscatalogpb.GetStocksRequest{
					Identifiers: &usstockscatalogpb.GetStocksRequest_StockIds{
						StockIds: &usstockscatalogpb.RepeatedStrings{Ids: []string{"stockId"}},
					},
				}).Return(&usstockscatalogpb.GetStocksResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)

				m.mockUssCatalogClient.EXPECT().GetHistoricalStockPrices(gomock.Any(),
					&usstockscatalogpb.GetHistoricalStockPricesRequest{StockId: "stockId"}).Return(&usstockscatalogpb.GetHistoricalStockPricesResponse{
					Status: rpcPb.StatusOk(),
				}, nil).MaxTimes(1)
			},
			wantErr: true,
		},
		{
			name:        "Missing stock id in metadata key should lead to error",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{},
			before:      func(m *mockFields) {},
			wantErr:     true,
		},
		{
			name: "Missing stockId key in stocks map should lead to error",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockId: "stockId",
			},
			before: func(m *mockFields) {
				m.mockUssCatalogClient.EXPECT().GetStocks(gomock.Any(), &usstockscatalogpb.GetStocksRequest{
					Identifiers: &usstockscatalogpb.GetStocksRequest_StockIds{
						StockIds: &usstockscatalogpb.RepeatedStrings{Ids: []string{"stockId"}},
					},
				}).Return(&usstockscatalogpb.GetStocksResponse{
					Status: rpcPb.StatusOk(),
					Stocks: map[string]*usstockscatalogpb.Stock{},
				}, nil)

				m.mockUssCatalogClient.EXPECT().GetHistoricalStockPrices(gomock.Any(),
					&usstockscatalogpb.GetHistoricalStockPricesRequest{StockId: "stockId"}).Return(&usstockscatalogpb.GetHistoricalStockPricesResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Nil stocks map should lead to error",
			requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
				userAttributeFetcher.RequestMetadataKey_StockId: "stockId",
			},
			before: func(m *mockFields) {
				m.mockUssCatalogClient.EXPECT().GetStocks(gomock.Any(), &usstockscatalogpb.GetStocksRequest{
					Identifiers: &usstockscatalogpb.GetStocksRequest_StockIds{
						StockIds: &usstockscatalogpb.RepeatedStrings{Ids: []string{"stockId"}},
					},
				}).Return(&usstockscatalogpb.GetStocksResponse{
					Status: rpcPb.StatusOk(),
					Stocks: map[string]*usstockscatalogpb.Stock{},
				}, nil)

				m.mockUssCatalogClient.EXPECT().GetHistoricalStockPrices(gomock.Any(),
					&usstockscatalogpb.GetHistoricalStockPricesRequest{StockId: "stockId"}).Return(&usstockscatalogpb.GetHistoricalStockPricesResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.before(m)
			s := NewStocksAttributeProvider(m.mockUssCatalogClient, m.mocksUssCacheStorage, m.mockTime)
			got, err := s.GetStockDetails(context.Background(), tt.requestMeta)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStockDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("GetStockDetails() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}
