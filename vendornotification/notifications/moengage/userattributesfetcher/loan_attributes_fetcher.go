package userattributesfetcher

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"

	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/vendors/moengage"
)

type LoanAttributesFetcher struct {
	palClient preapprovedloan.PreApprovedLoanClient
}

func NewLoanAttributesFetcher(palClient preapprovedloan.PreApprovedLoanClient) *LoanAttributesFetcher {
	return &LoanAttributesFetcher{palClient: palClient}
}

var vendorNamesMap = map[string]string{
	"FEDERAL":            "Federal Bank",
	"LIQUILOANS":         "Liquiloans",
	"IDFC":               "IDFC First Bank",
	"FIFTYFIN":           "Bajaj Finance Limited",
	"ABFL":               "Aditya Birla Finance Limited",
	"MONEYVIEW":          "Moneyview",
	"STOCK_GUARDIAN_LSP": "Fi Loans",
	"LENDEN":             "LenDenClub",
}

// nolint dupl
func (c *LoanAttributesFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	var (
		actorId = req.GetActorId()
	)
	fieldToUserAttributesMap := map[string]*moengage.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_Summary:
			loanSummary, err := c.getLoanSummary(ctx, actorId)
			if err != nil {
				logger.Error(ctx, "failed to get loan summary for content api", zap.Error(err))
				return nil, errors.Wrap(err, "failed to get loan summary for content api")
			}
			fieldToUserAttributesMap[string(UserAttributesReqField_Summary)] = &moengage.UserAttribute{
				ValueTypes: &moengage.UserAttribute_LoanSummary{
					LoanSummary: loanSummary,
				},
			}
		case UserAttributesReqField_LoanOfferDetails:
			loanOfferDetails, err := c.getLoanOfferDetails(ctx, actorId)
			if err != nil {
				logger.Error(ctx, "failed to get loan offer details for content api", zap.Error(err))
				return nil, errors.Wrap(err, "failed to get loan offer details for content api")
			}
			fieldToUserAttributesMap[string(UserAttributesReqField_LoanOfferDetails)] = &moengage.UserAttribute{
				ValueTypes: &moengage.UserAttribute_LoanOfferDetails{
					LoanOfferDetails: loanOfferDetails,
				},
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in loan attributes fetcher", zap.String("field", string(field)))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (c *LoanAttributesFetcher) getLoanSummary(ctx context.Context, actorId string) (*moengage.LoanSummary, error) {
	laRes, err := c.palClient.GetLoanAccountDetails(ctx, &preapprovedloan.GetLoanAccountDetailsRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(laRes, err); err != nil {
		return nil, err
	}

	if len(laRes.GetLoanDetails()) < 1 {
		return nil, fmt.Errorf("unexpected length of loan accounts, len: %v", len(laRes.GetLoanDetails()))
	}
	// GetLoanAccountDetails() will give all loan accounts across different vendors, with closed and active status
	// since we need only active loan account added that check  here.
	var loanDetails = laRes.GetLoanDetails()[0]
	for _, la := range laRes.GetLoanDetails() {
		if la.GetStatus() == preapprovedloan.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			loanDetails = la
			break
		}
	}
	var (
		emiAmount       = loanDetails.GetInstallmentDetail().GetNextEmiAmount()
		emiAmountString = moneyPkg.ToDisplayStringWithPrecision(emiAmount, 2)

		dueDate = datetimePkg.TimeToDateInLoc(loanDetails.GetInstallmentDetail().GetNextDueInstallmentTime().AsTime(), datetimePkg.IST)
		// Do not use dd-mm-yyyy format because whatsapp detects it as phone number
		dueDateString = datetimePkg.DateToString(dueDate, "02 Jan, 2006", datetimePkg.IST)

		currentDate         = datetimePkg.TimeToDateInLoc(time.Now(), datetimePkg.IST)
		daysTillDueDate     = -1
		dpd                 = -1
		bankAccountDetails  = loanDetails.GetBankAccountDetails()
		accNoLastFourDigits string
		bankDetail          *moengage.BankDetails
	)

	if bankAccountDetails != nil && bankAccountDetails.GetAccountNumber() != "" && len(bankAccountDetails.GetAccountNumber()) > 4 {
		accNoLastFourDigits = bankAccountDetails.GetAccountNumber()[len(bankAccountDetails.GetAccountNumber())-4:]
		bankDetail = &moengage.BankDetails{
			BankAccountNumber: "XXXXXXX" + accNoLastFourDigits,
			BankName:          bankAccountDetails.GetBankName(),
		}
	} else {
		bankDetail = &moengage.BankDetails{
			BankAccountNumber: "registered",
			BankName:          "bank",
		}
	}

	// Event will be triggered for users who have passed due date, for others it would fail
	if datetimePkg.IsDateAfter(dueDate, currentDate) {
		daysTillDueDate = len(datetimePkg.GetDatesBetween(currentDate, dueDate))
	} else {
		dpd = len(datetimePkg.GetDatesBetween(dueDate, currentDate))
	}

	loanSummary := &moengage.LoanSummary{
		EmiAmount:       emiAmountString,
		EmiDate:         dueDateString,
		Dpd:             int32(dpd),
		DaysTillNextDue: int32(daysTillDueDate),
		BankDetails:     bankDetail,
	}
	return loanSummary, nil
}

func (c *LoanAttributesFetcher) getLoanOfferDetails(ctx context.Context, actorId string) (*moengage.LoanOfferDetails, error) {
	res, err := c.palClient.GetLoanOffers(ctx, &preapprovedloan.GetLoanOffersRequest{
		ActorId:      actorId,
		GetBestOffer: true,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, err
	}
	loanOfferDetails := res.GetBestOffer()
	if loanOfferDetails == nil {
		return nil, epifierrors.ErrRecordNotFound
	}
	var (
		loanOfferAmount       = moneyPkg.ToDisplayStringWithPrecision(loanOfferDetails.GetOfferConstraints().GetMaxLoanAmount(), 0)
		interestRateYearly    = strconv.FormatFloat(loanOfferDetails.GetProcessingInfo().GetInterestRate()[0].GetPercentage(), 'f', -1, 64)
		lender                = loanOfferDetails.GetVendor().String()
		offerExpiryDate       = datetimePkg.TimeToDateInLoc(loanOfferDetails.GetValidTill().AsTime(), datetimePkg.IST)
		offerExpiryDateString = datetimePkg.DateToString(offerExpiryDate, "02 January", datetimePkg.IST)
	)
	loanOfferDetail := &moengage.LoanOfferDetails{
		LoanOfferAmount:    loanOfferAmount,
		InterestRateYearly: interestRateYearly,
		Lender:             vendorNamesMap[lender],
		OfferExpiryDate:    offerExpiryDateString,
	}
	return loanOfferDetail, nil
}

func getLoanProgramFromMetaData(metadata map[RequestMetadataKey]string) (preapprovedloan.LoanProgram, preapprovedloan.Vendor) {
	loanProgram, hasLoanProgram := metadata[RequestMetadataKey_Program]
	loanVendor, hasLoanVendor := metadata[RequestMetadataKey_Vendor]

	lp := preapprovedloan.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
	lv := preapprovedloan.Vendor_VENDOR_UNSPECIFIED
	switch {
	case hasLoanProgram:
		lp = preapprovedloan.LoanProgram(preapprovedloan.LoanProgram_value[loanProgram])
		fallthrough
	case hasLoanVendor:
		lv = preapprovedloan.Vendor(preapprovedloan.Vendor_value[loanVendor])
	}
	return lp, lv
}
