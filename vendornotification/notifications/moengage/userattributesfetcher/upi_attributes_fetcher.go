package userattributesfetcher

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
	fePkgUpi "github.com/epifi/gamma/frontend/pkg/upi"
)

type UpiAttributesFetcher struct {
	upiOnboardingClient upiOnbPb.UpiOnboardingClient
	accountPiClient     accountPiPb.AccountPIRelationClient
	savingsClient       savingsPb.SavingsClient
}

func NewUpiAttributesFetcher(
	upiOnboardingClient upiOnbPb.UpiOnboardingClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	savingsClient savingsPb.SavingsClient,
) *UpiAttributesFetcher {
	return &UpiAttributesFetcher{
		upiOnboardingClient: upiOnboardingClient,
		accountPiClient:     accountPiClient,
		savingsClient:       savingsClient,
	}
}

func (u *UpiAttributesFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_UpiId:
			upiId, err := u.getUpiId(ctx, req.GetActorId())
			if err != nil {
				return nil, fmt.Errorf("failed to get UPI ID: %w", err)
			}
			if upiId != "" {
				return &GetAttributesResponse{
					FieldNameToAttributesMap: map[string]*moengageVendorPb.UserAttribute{
						string(field): {
							ValueTypes: &moengageVendorPb.UserAttribute_StringValue{
								StringValue: upiId,
							},
						},
					},
				}, nil
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in UPI attributes fetcher", zap.String("field", string(field)))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (u *UpiAttributesFetcher) getUpiId(ctx context.Context, actorId string) (string, error) {
	// GetVPAForActor will try to fetch the user's VPA. It first checks for a Federal Bank savings account VPA,
	// and if not found, falls back to checking for a primary UPI account's VPA
	vpa, _, err := fePkgUpi.GetVPAForActor(ctx, actorId, u.upiOnboardingClient, u.accountPiClient, u.savingsClient)
	if err != nil {
		return "", fmt.Errorf("error getting VPA: %w", err)
	}
	return vpa, nil
}
