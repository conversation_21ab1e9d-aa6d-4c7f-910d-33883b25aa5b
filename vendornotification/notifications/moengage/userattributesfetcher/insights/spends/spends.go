package spends

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	userAttributeFetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
)

type SpendsAttributesFetcher struct {
	summaryGenerator ISummaryGenerator
}

func NewSpendsAttributesFetcher(summaryGenerator ISummaryGenerator) *SpendsAttributesFetcher {
	return &SpendsAttributesFetcher{
		summaryGenerator: summaryGenerator,
	}
}

func (s *SpendsAttributesFetcher) GetAttributes(ctx context.Context, req *userAttributeFetcher.GetAttributesRequest) (*userAttributeFetcher.GetAttributesResponse, error) {
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case userAttributeFetcher.UserAttributesReqField_Summary:
			summary, err := s.summaryGenerator.GetSpendsSummary(ctx, req.GetActorId(), req.GetRequestMetadata())
			if err != nil {
				return nil, fmt.Errorf("failed to get spends sumamry: %w", err)
			}
			fieldToUserAttributesMap[string(field)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_SpendsSummary{
					SpendsSummary: summary,
				},
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in insights attributes fetcher", zap.String("field", string(field)))
		}
	}
	return &userAttributeFetcher.GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func extractAndParseDate(requestMeta map[userAttributeFetcher.RequestMetadataKey]string, dateKey userAttributeFetcher.RequestMetadataKey) (time.Time, error) {
	dateStr, ok := requestMeta[dateKey]
	if !ok {
		return time.Time{}, fmt.Errorf("missing metadata key %s", dateKey)
	}

	date, err := time.ParseInLocation("02-Jan-2006", dateStr, datetime.IST)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse date from string: %w", err)
	}

	return date.In(datetime.IST), nil
}
