package spends

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/google/wire"
	goCache "github.com/patrickmn/go-cache"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	txnaggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	moengageInsightsPb "github.com/epifi/gamma/api/vendors/moengage/insights"
	"github.com/epifi/gamma/pkg/dmf/datafetcher"
	"github.com/epifi/gamma/pkg/dmf/txnaggregates"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	userAttributeFetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
)

var WireSummaryGeneratorSet = wire.NewSet(NewSummaryGenerator, wire.Bind(new(ISummaryGenerator), new(*SummaryGenerator)))

type ISummaryGenerator interface {
	GetSpendsSummary(ctx context.Context, actorId string, requestMeta map[userAttributeFetcher.RequestMetadataKey]string) (*moengageInsightsPb.SpendsSummary, error)
}

var selfTransferCategorySet = map[categorizerPb.DisplayCategory]struct{}{
	categorizerPb.DisplayCategory_SELF_TRANSFER_DEBIT:  {},
	categorizerPb.DisplayCategory_SELF_TRANSFER_CREDIT: {},
	categorizerPb.DisplayCategory_SELF_CREDIT:          {},
	categorizerPb.DisplayCategory_SELF_DEBIT:           {},
	categorizerPb.DisplayCategory_SELF_TRANSFER:        {},
}

type SummaryGenerator struct {
	actorAccounts       datafetcher.ActorAccounts
	txnAggregatesHelper txnaggregates.TxnAggregates
	categorizerClient   categorizerPb.TxnCategorizerClient
	categoryInfoCache   *goCache.Cache
}

func NewSummaryGenerator(
	actorAccounts datafetcher.ActorAccounts,
	txnAggregatesHelper txnaggregates.TxnAggregates,
	categorizerClient categorizerPb.TxnCategorizerClient,
) *SummaryGenerator {
	return &SummaryGenerator{
		actorAccounts:       actorAccounts,
		txnAggregatesHelper: txnAggregatesHelper,
		categorizerClient:   categorizerClient,
		categoryInfoCache:   goCache.New(36*time.Hour, time.Hour),
	}
}

// GetSpendsSummary computes summary of spends across all savings accounts for the actor
func (s *SummaryGenerator) GetSpendsSummary(ctx context.Context, actorId string, requestMeta map[userAttributeFetcher.RequestMetadataKey]string) (*moengageInsightsPb.SpendsSummary, error) {
	fromDate, err := extractAndParseDate(requestMeta, userAttributeFetcher.RequestMetaadataKey_FromDate)
	if err != nil {
		return nil, fmt.Errorf("error in extracting %s: %w", userAttributeFetcher.RequestMetaadataKey_FromDate, err)
	}

	toDate, err := extractAndParseDate(requestMeta, userAttributeFetcher.RequestMetaadataKey_ToDate)
	if err != nil {
		return nil, fmt.Errorf("error in extracting %s: %w", userAttributeFetcher.RequestMetaadataKey_ToDate, err)
	}
	toDate = toDate.AddDate(0, 0, 1)

	acctId, err := s.actorAccounts.FetchAccountIds(ctx, actorId, datafetcher.ForFiAccounts(), datafetcher.ForAaAccounts())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch account ids for actor: %w", err)
	}
	catAggs, err := s.txnAggregatesHelper.GetTopCategoriesAggs(ctx, actorId, timestampPb.New(fromDate), timestampPb.New(toDate), acctId.FiAccountIds, acctId.AaAccountIds, []txnaggregates.AggregationType{
		txnaggregates.AmountSum,
	}, paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED)
	if err != nil {
		return nil, fmt.Errorf("failed to get category aggs : %w", err)
	}

	summary, err := s.prepareSummary(ctx, catAggs)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare spends summary: %w", err)
	}

	return summary, nil
}

// fetchCategoryInfo fetch category info from either cache or from categorizer
func (s *SummaryGenerator) fetchCategoryInfo(ctx context.Context, category categorizerPb.DisplayCategory) (*categorizerPb.CategoryInfo, error) {
	if value, ok := s.categoryInfoCache.Get(category.String()); ok {
		info, ok := value.(*categorizerPb.CategoryInfo)
		if !ok {
			return nil, fmt.Errorf("failed to cast category info of type %T to %T", info, &categorizerPb.CategoryInfo{})
		}
		return info, nil
	}

	res, err := s.categorizerClient.GetCategoriesInfo(ctx, &categorizerPb.GetCategoriesInfoRequest{})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, fmt.Errorf("faield to get categories info: %w", err)
	}

	var categoryInfo *categorizerPb.CategoryInfo
	for _, info := range res.GetCategoriesInfo() {
		if info.GetDisplayCategory() == category {
			categoryInfo = info
		}
		s.categoryInfoCache.Set(info.GetDisplayCategory().String(), info, goCache.DefaultExpiration)
	}

	if categoryInfo == nil {
		return nil, fmt.Errorf("unable to get category info for %v", category)
	}

	return categoryInfo, nil
}

// prepareSummary prepare spends summary from category aggregates
func (s *SummaryGenerator) prepareSummary(ctx context.Context, aggregates []*txnaggregatesPb.Aggregate) (*moengageInsightsPb.SpendsSummary, error) {
	moneyIn := money.ZeroINR().GetPb()
	moneyOut := money.ZeroINR().GetPb()
	spends := money.ZeroINR().GetPb()
	investments := money.ZeroINR().GetPb()
	income := money.ZeroINR().GetPb()

	for _, agg := range aggregates {
		var err error
		switch agg.GetCategoryL0() {
		case categorizerPb.L0_L0_SPEND, categorizerPb.L0_L0_DEBT_SETTLEMENT:
			if _, ok := selfTransferCategorySet[agg.GetDisplayCategory()]; !ok {
				spends, err = money.Sum(spends, agg.GetSumAmount().GetBeMoney())
				if err != nil {
					return nil, fmt.Errorf("failed to sum spends: %w", err)
				}
			}
			moneyOut, err = money.Sum(moneyOut, agg.GetSumAmount().GetBeMoney())
			if err != nil {
				return nil, fmt.Errorf("failed to sum money out: %w", err)
			}
		case categorizerPb.L0_L0_INVESTMENTS:
			investments, err = money.Sum(investments, agg.GetSumAmount().GetBeMoney())
			if err != nil {
				return nil, fmt.Errorf("failed to sum investments: %w", err)
			}
			moneyOut, err = money.Sum(moneyOut, agg.GetSumAmount().GetBeMoney())
			if err != nil {
				return nil, fmt.Errorf("failed to sum money out investments: %w", err)
			}
		case categorizerPb.L0_L0_INCOME, categorizerPb.L0_L0_LOAN:
			if _, ok := selfTransferCategorySet[agg.GetDisplayCategory()]; !ok {
				income, err = money.Sum(income, agg.GetSumAmount().GetBeMoney())
				if err != nil {
					return nil, fmt.Errorf("failed to sum income: %w", err)
				}
			}
			moneyIn, err = money.Sum(moneyIn, agg.GetSumAmount().GetBeMoney())
			if err != nil {
				return nil, fmt.Errorf("failed to sum money in: %w", err)
			}
		case categorizerPb.L0_L0_UNSPECIFIED, categorizerPb.L0_L0_UNKNOWN:
			logger.WarnWithCtx(ctx, fmt.Sprintf("unknown category l0 %v", agg.GetCategoryL0()))
		default:
			return nil, fmt.Errorf("unhandled category l0 %v", agg.GetCategoryL0())
		}
	}

	categoryAggregates, err := s.convertToCategoryAggregates(ctx, aggregates)
	if err != nil {
		return nil, fmt.Errorf("failed to convert to category aggregates: %w", err)
	}

	return &moengageInsightsPb.SpendsSummary{
		MoneyIn:       s.convertMoneyToAmount(moneyIn),
		MoneyOut:      s.convertMoneyToAmount(moneyOut),
		Income:        s.convertMoneyToAmount(income),
		Spends:        s.convertMoneyToAmount(spends),
		Investments:   s.convertMoneyToAmount(investments),
		TopCategories: categoryAggregates,
	}, nil
}

func (s *SummaryGenerator) convertMoneyToAmount(amount *moneyPb.Money) *moengageInsightsPb.Amount {
	displayValue := money.ToDisplayStringInIndianFormat(amount, 0, true)
	value, _ := money.ToDecimal(amount).Float64()
	return &moengageInsightsPb.Amount{
		DisplayValue: displayValue,
		Value:        value,
	}
}

// convertToCategoryAggregates transform analyser categpry aggregate to insight category aggregate
func (s *SummaryGenerator) convertToCategoryAggregates(ctx context.Context, aggs []*txnaggregatesPb.Aggregate) ([]*moengageInsightsPb.CategoryAggregate, error) {
	// sort in descending order
	sort.Slice(aggs, func(i, j int) bool {
		amount1 := aggs[i].GetSumAmount()
		amount2 := aggs[j].GetSumAmount()
		if amount1.GetUnits() == amount2.GetUnits() {
			return amount1.GetDecimals() > amount2.GetDecimals()
		}
		return amount1.GetUnits() > amount2.GetUnits()
	})

	var categoryAggregates []*moengageInsightsPb.CategoryAggregate
	for _, agg := range aggs {
		// skip self transfer and non spend/settlement categories
		if _, ok := selfTransferCategorySet[agg.GetDisplayCategory()]; ok ||
			!(agg.GetCategoryL0() == categorizerPb.L0_L0_SPEND || agg.GetCategoryL0() == categorizerPb.L0_L0_DEBT_SETTLEMENT) {
			continue
		}
		categoryInfo, err := s.fetchCategoryInfo(ctx, agg.GetDisplayCategory())
		if err != nil {
			return nil, fmt.Errorf("failed to get category info for %v: %w", agg.GetDisplayCategory(), err)
		}
		categoryAggregates = append(categoryAggregates, &moengageInsightsPb.CategoryAggregate{
			DisplayName: categoryInfo.GetDisplayName(),
			Amount:      s.convertMoneyToAmount(agg.GetSumAmount().GetBeMoney()),
		})
	}
	return categoryAggregates, nil
}
