package userattributesfetcher

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	balancePb "github.com/epifi/gamma/api/accounts/balance"
	balanceEnums "github.com/epifi/gamma/api/accounts/balance/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
)

type BalanceAttributesFetcher struct {
	balanceClient balancePb.BalanceClient
	savingsClient savingsPb.SavingsClient
}

func NewBalanceAttributesFetcher(balanceClient balancePb.BalanceClient, savingsClient savingsPb.SavingsClient) *BalanceAttributesFetcher {
	return &BalanceAttributesFetcher{
		balanceClient: balanceClient,
		savingsClient: savingsClient,
	}
}

//nolint:dupl
func (s *BalanceAttributesFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_Balance:
			availableBalance, err := s.GetBalance(ctx, req.GetActorId(), req.GetRequestMetadata())
			if err != nil {
				return nil, fmt.Errorf("failed to get balance: %w", err)
			}
			fieldToUserAttributesMap[string(field)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_StringValue{
					StringValue: availableBalance,
				},
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in balance fetcher", zap.String("field", string(field)))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (s *BalanceAttributesFetcher) GetBalance(ctx context.Context, actorId string, requestMeta map[RequestMetadataKey]string) (string, error) {

	savingsRes, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err != nil || savingsRes == nil {
		statusFromErr, ok := status.FromError(err)
		if !ok {
			return "", fmt.Errorf("error getting savings account details for user : %w cannot get status from err", err)
		}
		if statusFromErr.Code() == codes.NotFound {
			return "", epifierrors.ErrRecordNotFound
		}
		return "", fmt.Errorf("error getting savings account details for user : %w", err)
	}
	if savingsRes.GetAccount().GetId() == "" {
		return "", epifierrors.ErrRecordNotFound
	}

	accountBalanceRes, accountBalanceErr := s.balanceClient.GetAccountBalance(ctx, &balancePb.GetAccountBalanceRequest{
		ActorId:       actorId,
		DataFreshness: balanceEnums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
		Identifier: &balancePb.GetAccountBalanceRequest_Id{
			Id: savingsRes.GetAccount().GetId(),
		},
	})

	if err = epifigrpc.RPCError(accountBalanceRes, accountBalanceErr); err != nil {
		return "", fmt.Errorf("error in getting account balance for actor %w", err)
	}

	return money.ToDisplayString(accountBalanceRes.GetAvailableBalance()), nil
}
