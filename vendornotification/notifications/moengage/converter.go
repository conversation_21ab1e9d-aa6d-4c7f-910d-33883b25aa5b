package moengage

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	goutils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"

	moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
)

func getAreaEnumFromString(ctx context.Context, areaString string) (moengageVnPb.Area, error) {
	areaUpperStr := strings.ToUpper(strings.TrimSpace(areaString))
	areaEnum := goutils.Enum(areaUpperStr, moengageVnPb.Area_value, moengageVnPb.Area_AREA_UNSPECIFIED)
	if areaEnum == moengageVnPb.Area_AREA_UNSPECIFIED {
		logger.Error(ctx, "unsupported area provided", zap.String("area", areaString))
		return moengageVnPb.Area_AREA_UNSPECIFIED, fmt.Errorf("unsupported area provided, area: %s", areaString)
	}
	return areaEnum, nil
}

func getUseCaseEnumFromString(ctx context.Context, useCaseString string) (moengageVnPb.UseCase, error) {
	useCaseUpperStr := strings.ToUpper(strings.TrimSpace(useCaseString))
	useCaseEnum := goutils.Enum(useCaseUpperStr, moengageVnPb.UseCase_value, moengageVnPb.UseCase_USE_CASE_UNSPECIFIED)
	if useCaseEnum == moengageVnPb.UseCase_USE_CASE_UNSPECIFIED {
		logger.Error(ctx, "unsupported use case provided", zap.String("usecase", useCaseString))
		return moengageVnPb.UseCase_USE_CASE_UNSPECIFIED, fmt.Errorf("unsupported use case provided, area: %s", useCaseString)
	}
	return useCaseEnum, nil
}
