package videosdk

import (
	"context"
	"fmt"

	"github.com/golang/protobuf/ptypes/empty" // nolint: depguard

	videosdkPb "github.com/epifi/gamma/api/vendornotification/videocall/videosdk"
	"github.com/epifi/gamma/api/vendors/vkyccall/videosdk"
	"github.com/epifi/be-common/pkg/logger"
)

type Service struct {
}

func NewService() *Service {
	return &Service{}
}

var _ videosdkPb.VideoSdkCallbackServer = &Service{}

func (s *Service) ParticipantLeftEvent(ctx context.Context, req *videosdk.PariticipantLeftEventRequest) (*empty.Empty, error) {
	logger.Info(ctx, fmt.Sprintf("participant left event request received, MeetingId participantId %v %v", req.GetPariticipantLeftEventData().GetParticipantId(), req.GetPariticipantLeftEventData().GetMeetingId()))
	return &empty.Empty{}, nil
}
