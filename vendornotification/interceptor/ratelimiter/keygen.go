package ratelimiter

import (
	"context"

	"github.com/epifi/be-common/pkg/epifigrpc/interceptors/ratelimit/keygen"
	"github.com/epifi/gamma/vendornotification/interceptor/ratelimiter/namespace"
)

// KeyGenerator implements ratelimit.IKeyGenerator.
type KeyGenerator struct {
	namespaceFactory namespace.IFactory
}

func NewKeyGenerator(namespaceFactory namespace.IFactory) keygen.IKeyGenerator {
	return &KeyGenerator{
		namespaceFactory: namespaceFactory,
	}
}

// compile time check to make sure KeyGenerator is of IKeyGenerator type
var _ keygen.IKeyGenerator = &KeyGenerator{}

// GenerateKey We might want to implement different rate limits based on some of the request params (eg: client_a and client_b from the client_id field).
// This provides a way for RPCs to append a key to distinguish targets for rate limit.
// The rpc rate limits should be set on this key in the config file.
// **Note** : If we make changes to this key gen logic then rate limit keys in config file should also be updated according to the new logic.
func (v *KeyGenerator) GenerateKey(ctx context.Context, req interface{}, fullGrpcMethodName string) (string, error) {
	rateLimitKey := keygen.TransformGrpcMethodName(fullGrpcMethodName)

	// check if namespacing is applicable
	namespaceGenerator := v.namespaceFactory.GetGenerator(fullGrpcMethodName)
	if namespaceGenerator == nil {
		return rateLimitKey, nil
	}

	keyNamespace, err := namespaceGenerator.GenerateNamespace(ctx, req)
	if err != nil {
		return "", err
	}

	rateLimitKey = rateLimitKey + "_" + keyNamespace
	return rateLimitKey, nil
}
