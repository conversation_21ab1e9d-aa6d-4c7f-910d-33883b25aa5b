package namespace

type IFactory interface {
	GetGenerator(fullGrpcMethodName string) IGenerator
}

type DefaultFactory struct {
	clientIdFromReqNamespaceGenerator *ClientIdFromReqNamespaceGenerator
	clientIdFromCtxNamespaceGenerator *ClientIdFromCtxNamespaceGenerator
}

func NewDefaultFactory(clientIdFromReqNamespaceGenerator *ClientIdFromReqNamespaceGenerator, clientIdFromCtxNamespaceGenerator *ClientIdFromCtxNamespaceGenerator) IFactory {
	return &DefaultFactory{
		clientIdFromReqNamespaceGenerator: clientIdFromReqNamespaceGenerator,
		clientIdFromCtxNamespaceGenerator: clientIdFromCtxNamespaceGenerator,
	}
}

var _ IFactory = &DefaultFactory{}

func (d *DefaultFactory) GetGenerator(fullGrpcMethodName string) IGenerator {
	switch fullGrpcMethodName {
	case "/vendornotification.auth.Auth/GetAuthToken":
		return d.clientIdFromReqNamespaceGenerator
	case "/leads.external.Lead/CreateLoansLead":
		return d.clientIdFromCtxNamespaceGenerator
	default:
		return nil
	}
}
