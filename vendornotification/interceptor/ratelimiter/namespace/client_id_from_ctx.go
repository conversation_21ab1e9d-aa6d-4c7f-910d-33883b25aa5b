package namespace

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epificontext"
)

type ClientIdFromCtxNamespaceGenerator struct {
}

func NewClientIdFromCtxNamespaceGenerator() *ClientIdFromCtxNamespaceGenerator {
	return &ClientIdFromCtxNamespaceGenerator{}
}

var _ IGenerator = &ClientIdFromCtxNamespaceGenerator{}

func (i *ClientIdFromCtxNamespaceGenerator) GenerateNamespace(ctx context.Context, req interface{}) (string, error) {
	clientId := epificontext.ClientIdFromContext(ctx)
	if clientId == epificontext.UnknownClientId {
		return "", fmt.Errorf("ctx does not have client_id key")
	}

	return clientId, nil
}
