package namespace

import "context"

type IGenerator interface {
	// GenerateNamespace returns a unique string for a given rpc that will be used to set up a different rate limit configuration based on that string
	// Eg: It can return client_A and client_B to set up different rate limits for different clients based on the client_id in the request
	// The same value needs to be appended to the full grpc names in the rate limit config yaml file
	GenerateNamespace(ctx context.Context, req interface{}) (string, error)
}
