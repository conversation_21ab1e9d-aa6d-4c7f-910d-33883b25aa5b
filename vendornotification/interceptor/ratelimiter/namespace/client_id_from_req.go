package namespace

import (
	"context"
	"fmt"
)

type ReqWithClientId interface {
	GetClientId() string
}

type ClientIdFromReqNamespaceGenerator struct {
}

func NewClientIdFromReqNamespaceGenerator() *ClientIdFromReqNamespaceGenerator {
	return &ClientIdFromReqNamespaceGenerator{}
}

var _ IGenerator = &ClientIdFromReqNamespaceGenerator{}

func (i *ClientIdFromReqNamespaceGenerator) GenerateNamespace(ctx context.Context, req interface{}) (string, error) {
	reqWithClientId, ok := req.(ReqWithClientId)
	if !ok {
		return "", fmt.Errorf("req does not implement ReqWithClientId")
	}

	return reqWithClientId.GetClientId(), nil
}
