package dao

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCkycPb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

//go:generate mockgen -source=./dao.go -destination=./../test/mocks/dao/mock_dao.go -package=mock_dao
//go:generate dao_metrics_gen impl
type OnboardingDetailsDao interface {
	Create(ctx context.Context, onboardingDetails *woPb.OnboardingDetails) (*woPb.OnboardingDetails, error)
	Update(ctx context.Context, onboardingDetails *woPb.OnboardingDetails, updateMasks []woPb.OnboardingDetailsFieldMask) error
	GetById(ctx context.Context, id string) (*woPb.OnboardingDetails, error)
	GetByIds(ctx context.Context, ids []string) ([]*woPb.OnboardingDetails, error)
	GetByActorIdAndOnbType(ctx context.Context, actorId string, onbType woPb.OnboardingType) (*woPb.OnboardingDetails, error)

	// BatchGetByActorIdsAndOnbType returns the details of a particular onboarding type for a multiple users (max 100)
	// Any users whose records are not found are ignored
	BatchGetByActorIdsAndOnbType(ctx context.Context, actorIds []string, onbType woPb.OnboardingType) ([]*woPb.OnboardingDetails, error)

	GetOrCreate(ctx context.Context, actorId string, onbType woPb.OnboardingType, newOnbDetails *woPb.OnboardingDetails) (*woPb.OnboardingDetails, bool, error)
}

type OnboardingStepDetailsDao interface {
	Create(ctx context.Context, onboardingStepDetails *woPb.OnboardingStepDetails) (*woPb.OnboardingStepDetails, error)
	UpdateById(ctx context.Context, onboardingStepDetails *woPb.OnboardingStepDetails, updateMasks []woPb.OnboardingStepDetailsFieldMask) error
	GetById(ctx context.Context, id string) (*woPb.OnboardingStepDetails, error)
	GetByOnboardingDetailsIdAndStep(ctx context.Context, onboardingDetailsId string, step woPb.OnboardingStep) (*woPb.OnboardingStepDetails, error)
	GetByOnboardingDetailsId(ctx context.Context, onboardingDetailsId string) ([]*woPb.OnboardingStepDetails, error)
	GetOrCreate(ctx context.Context, onbDetailsId string, step woPb.OnboardingStep, newOnbStepDetails *woPb.OnboardingStepDetails) (*woPb.OnboardingStepDetails, bool, error)
}

type ESignDetailsDao interface {
	Create(ctx context.Context, eSignDetails *woPb.ESignDetails) (*woPb.ESignDetails, error)
	Get(ctx context.Context, eSignId string) (*woPb.ESignDetails, error)
	UpdateById(ctx context.Context, details *woPb.ESignDetails, updateMasks []woPb.ESignDetailsFieldMask) error
}

type VendorRequestDao interface {
	Create(ctx context.Context, vendorRequest *woPb.VendorRequest) error
	GetByActorId(ctx context.Context, actorId string) ([]*woPb.VendorRequest, error)
}

type ManualReviewDao interface {
	Create(ctx context.Context, review *woPb.ManualReview) (*woPb.ManualReview, error)
	GetById(ctx context.Context, id string) (*woPb.ManualReview, error)
	UpdateById(ctx context.Context, review *woPb.ManualReview, mask []woPb.ManualReviewFieldMask) error
}

type CkycDataDao interface {
	Create(ctx context.Context, ckycData *woCkycPb.CkycData) (*woCkycPb.CkycData, error)
	GetByActorIdAndType(ctx context.Context, actorId string, dataType woCkycPb.CkycDataType) (*woCkycPb.CkycData, error)
}

type UserDao interface {
	Create(ctx context.Context, wealthUser *userPb.User) (*userPb.User, error)
	Update(ctx context.Context, wealthUser *userPb.User, updateMasks []userPb.UserFieldMask) error
	GetById(ctx context.Context, id string) (*userPb.User, error)
	GetByActorId(ctx context.Context, actorId string) (*userPb.User, error)
}

type OnboardingSummary interface {
	GetByFilters(ctx context.Context, pageToken *pagination.PageToken, filters ...storagev2.FilterOption) ([]*woPb.OnboardingSummary, *rpc.PageContextResponse, error)
}
