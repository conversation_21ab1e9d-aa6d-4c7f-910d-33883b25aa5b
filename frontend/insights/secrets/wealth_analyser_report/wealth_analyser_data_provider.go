package wealth_analyser_report

import (
	"context"
	"errors"

	"github.com/google/wire"

	"github.com/epifi/gamma/api/frontend/deeplink"
	feSecretsPb "github.com/epifi/gamma/api/frontend/insights/secrets"
	secretsBeFePb "github.com/epifi/gamma/api/insights/secrets/frontend"
)

type IWealthAnalyserReport interface {
	GetWealthAnalyserReport(ctx context.Context, req *WealthAnalyserReportRequest) (*WealthAnalyserReportResponse, error)
}

type WealthAnalyserReportRequest struct {
	ActorId           string
	ReportType        secretsBeFePb.WealthAnalyserReportType
	SecretLandingPage []*feSecretsPb.SecretLibraryCollection
}

type WealthAnalyserReportResponse struct {
	WealthAnalyserReport *feSecretsPb.WealthAnalyserReport
	RedirectDeeplink     *deeplink.Deeplink
}

type WealthAnalyserDataProviderImpl struct {
	MfDataProvider    *MfDataProvider
	EpfDataProvider   *EpfDataProvider
	AssetDataProvider *AssetDataProvider
}

func NewWealthAnalyserReport(
	mfDataProvider *MfDataProvider,
	epfDataProvider *EpfDataProvider,
	assetDataProvider *AssetDataProvider,
) *WealthAnalyserDataProviderImpl {
	return &WealthAnalyserDataProviderImpl{
		MfDataProvider:    mfDataProvider,
		EpfDataProvider:   epfDataProvider,
		AssetDataProvider: assetDataProvider,
	}
}

var WealthAnalyserReportWireSet = wire.NewSet(NewWealthAnalyserReport, wire.Bind(new(IWealthAnalyserReport), new(*WealthAnalyserDataProviderImpl)))

func (w *WealthAnalyserDataProviderImpl) GetWealthAnalyserReport(ctx context.Context, req *WealthAnalyserReportRequest) (*WealthAnalyserReportResponse, error) {
	switch req.ReportType {
	case secretsBeFePb.WealthAnalyserReportType_WEALTH_ANALYSER_REPORT_TYPE_MUTUAL_FUND:
		return w.MfDataProvider.GetWealthAnalyserReport(ctx, req)
	case secretsBeFePb.WealthAnalyserReportType_WEALTH_ANALYSER_REPORT_TYPE_EPF:
		return w.EpfDataProvider.GetWealthAnalyserReport(ctx, req)
	case secretsBeFePb.WealthAnalyserReportType_WEALTH_ANALYSER_REPORT_TYPE_ASSETS:
		return w.AssetDataProvider.GetWealthAnalyserReport(ctx, req)
	}
	return nil, errors.New("invalid report type")
}
