package valuegenerator

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/samber/lo"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	secretErrors "github.com/epifi/gamma/frontend/insights/secrets/errors"
	"github.com/epifi/gamma/frontend/insights/secrets/valuegenerator"
)

type NewtorthAtAgeDataContainer struct {
	timeOfBirth       time.Time
	currentAge        int
	currentNetworth   *moneyPb.Money
	targetAge         int
	targetNetworth    *moneyPb.Money
	networtCalculator NetworthCalculator
	monthlyNetworths  []*NetworthAtAge
}

type NetworthAtAge struct {
	age string
	*PredictedNetworth
}

func (n *NetworthAtAge) GetSliderLabels() []string {
	return []string{
		money.ToDisplayStringWithSuffixAndPrecisionV2(n.value, true, true, 2, false, money.IndianNumberSystem),
		n.age,
	}
}

func (n *NewtorthAtAgeDataContainer) GetInfoTitle() string {
	return fmt.Sprintf("Your networth at age %d", n.targetAge)
}

func (n *NewtorthAtAgeDataContainer) GetInfoValue() string {
	if n.targetNetworth == nil {
		return "Insufficient Data"
	}
	return money.ToDisplayStringWithSuffixAndPrecisionV2(n.targetNetworth, true, true, 2, false, money.IndianNumberSystem)
}

func (n *NewtorthAtAgeDataContainer) GetNumberCardTitle() string {
	return fmt.Sprintf("Your potential networth at age %d", n.targetAge)
}

func (n *NewtorthAtAgeDataContainer) GetNumberCardValue() string {
	return n.GetInfoValue()
}

func (n *NewtorthAtAgeDataContainer) GetSummaryTitle() string {
	return n.GetNumberCardTitle()
}

func (n *NewtorthAtAgeDataContainer) GetSummaryValue() string {
	return n.GetNumberCardValue()
}

func (n *NewtorthAtAgeDataContainer) GetLinePoints() []LinePointData {
	return lo.Map(n.monthlyNetworths, func(predictedNetworth *NetworthAtAge, _ int) LinePointData {
		return predictedNetworth
	})
}

func (n *NewtorthAtAgeDataContainer) GetHorizontalAxisLabels() []HorizontalAxisLabelData {
	if len(n.monthlyNetworths) < 2 {
		return nil
	}
	firstPoint := n.monthlyNetworths[0]
	firstPointXPosition, _ := firstPoint.GetXYCoordinates()
	lastPoint := n.monthlyNetworths[len(n.monthlyNetworths)-1]
	lastPointXPosition, _ := lastPoint.GetXYCoordinates()

	return []HorizontalAxisLabelData{
		&valuegenerator.HorizontalAxisLabel{
			XPosition: firstPointXPosition,
			Label:     "Today",
		},
		&valuegenerator.HorizontalAxisLabel{
			XPosition: lastPointXPosition,
			Label:     fmt.Sprintf("%s YRS", lastPoint.age),
		},
	}
}

type NetworthAtAgeProjection struct {
	ageLabel string
	networth string
}

func (n *NetworthAtAgeProjection) GetLeftHeading() string {
	return n.ageLabel
}

func (n *NetworthAtAgeProjection) GetRightHeading() string {
	return n.networth
}

func (n *NetworthAtAgeProjection) GetIconUrl() string {
	return "https://epifi-icons.pointz.in/insights/secrets/calender_icon_amber.png"
}

func (n *NewtorthAtAgeDataContainer) GetLineItemsData() ([]LineItemData, error) {
	if n.currentNetworth.GetUnits() < MinNetworth || len(n.monthlyNetworths) == 0 {
		return nil, nil
	}
	var networthAtAgeProjections []LineItemData
	networthAtAgeProjections = append(networthAtAgeProjections, &NetworthAtAgeProjection{
		ageLabel: "Today",
		networth: money.ToDisplayStringWithSuffixAndPrecisionV2(n.currentNetworth, true, true, 2, false, money.IndianNumberSystem),
	})

	ages := []int{30, 40, 50, 60}
	for _, age := range ages {
		if age <= n.currentAge {
			continue
		}
		estimatedNetworth, err := n.networtCalculator.GetEstimatedNetworthAtAge(&GetEstimatedNetworthAtAgeRequest{
			CurrentNetworth: n.currentNetworth,
			TimeOfBirth:     n.timeOfBirth,
			CurrentAge:      n.currentAge,
			TargetAge:       age,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to get estimated networth at age %d: %w", age, err)
		}
		networthAtAgeProjections = append(networthAtAgeProjections, &NetworthAtAgeProjection{
			ageLabel: fmt.Sprintf("At age %d", age),
			networth: money.ToDisplayStringWithSuffixAndPrecisionV2(estimatedNetworth, true, true, 2, false, money.IndianNumberSystem),
		})
	}

	return networthAtAgeProjections, nil
}

type NetworthAtAgeDataProvider struct {
	*UnimplementedAssetsSecretDataProvider
	networtCalculator NetworthCalculator
	timeImpl          datetime.Time
}

func NewNetworthAtAgeDataProvider(networtCalculator NetworthCalculator, timeImpl datetime.Time) *NetworthAtAgeDataProvider {
	return &NetworthAtAgeDataProvider{
		networtCalculator: networtCalculator,
		timeImpl:          timeImpl,
	}
}

func (n *NetworthAtAgeDataProvider) GetSummaryData(ctx context.Context, req *DataProviderRequest) (valuegenerator.SummaryDataWithTitle, error) {
	return n.getDataContainer(ctx, req)
}

func (n *NetworthAtAgeDataProvider) GetNumberCardData(ctx context.Context, req *DataProviderRequest) (valuegenerator.NumberCardDataWithTitle, error) {
	dataContainer, err := n.getDataContainer(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get data container: %w", err)
	}
	if len(dataContainer.monthlyNetworths) != 0 {
		// number card is build only when there are no monthly networths to build area chart
		return nil, SkipNumberCardErr
	}
	return dataContainer, nil
}

func (n *NetworthAtAgeDataProvider) GetLineItemsData(ctx context.Context, req *DataProviderRequest) ([]LineItemData, error) {
	dataContainer, err := n.getDataContainer(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get data container: %w", err)
	}
	return dataContainer.GetLineItemsData()
}

func (n *NetworthAtAgeDataProvider) GetAreaChartData(ctx context.Context, req *DataProviderRequest) (AreaChartData, error) {
	return n.getDataContainer(ctx, req)
}

func (n *NetworthAtAgeDataProvider) getDataContainer(ctx context.Context, req *DataProviderRequest) (*NewtorthAtAgeDataContainer, error) {
	currentNetworth, err := req.Store.GetTotalAssetValue()
	if err != nil {
		return nil, fmt.Errorf("failed to get total asset value: %w", err)
	}
	dob, err := req.Store.GetVerifiedOrUnverifiedUserDob()
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, errors.Join(err, secretErrors.NoDataToBuildSecretPostFilters)
		}
		return nil, fmt.Errorf("failed to get actor's age: %w", err)
	}
	age := datetime.AgeAt(dob, n.timeImpl.Now())
	targetAge, err := n.getNextAgeMilestone(age)
	if err != nil {
		return nil, fmt.Errorf("failed to get next age milestone: %w", err)
	}

	dataContainer := &NewtorthAtAgeDataContainer{
		currentAge:      age,
		timeOfBirth:     dob,
		targetAge:       targetAge,
		currentNetworth: currentNetworth,
	}
	if currentNetworth.GetUnits() < MinNetworth {
		return dataContainer, nil
	}

	estimatedNetworth, err := n.networtCalculator.GetEstimatedNetworthAtAge(&GetEstimatedNetworthAtAgeRequest{
		CurrentNetworth: currentNetworth,
		TimeOfBirth:     dob,
		CurrentAge:      age,
		TargetAge:       targetAge,
	})
	if err != nil {
		if errors.Is(err, LowMonthlyInvestmentProjectionErr) {
			return dataContainer, nil
		}
		return nil, fmt.Errorf("failed to get estimated networth at age: %w", err)
	}

	monthlyContr, err := n.networtCalculator.GetEstimatedMonthlyContributionOfActor(ctx, &GetEstimatedMonthlyContributionOfActorRequest{
		ActorId:         req.ActorId,
		CurrentNetworth: currentNetworth,
		TimeOfBirth:     dob,
	})
	if err != nil {
		if errors.Is(err, LowMonthlyInvestmentProjectionErr) {
			return dataContainer, nil
		}
		return nil, fmt.Errorf("failed to get monthly contribution: %w", err)

	}

	monthlyNetworths, err := n.networtCalculator.GetEstimatedMonthlyNetworthValues(&GetEstimatedMonthlyNetworthValues{
		StartingNetworth:                 currentNetworth,
		TargetNetworth:                   estimatedNetworth,
		StartTime:                        n.timeImpl.Now(),
		MonthlyInvestment:                monthlyContr,
		ExpectedMonthlyReturnPercentage:  monthlyGrowthPercentage,
		YearlyInvestmentStepUpPercentage: yearlySetupPercentage,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get monthly networth values: %w", err)
	}

	networthAtAges := lo.Map(monthlyNetworths, func(predictedNetworth *PredictedNetworth, _ int) *NetworthAtAge {
		return &NetworthAtAge{
			age:               strconv.Itoa(datetime.AgeAt(dob, predictedNetworth.date)),
			PredictedNetworth: predictedNetworth,
		}
	})

	return &NewtorthAtAgeDataContainer{
		currentAge:        age,
		timeOfBirth:       dob,
		targetAge:         targetAge,
		targetNetworth:    estimatedNetworth,
		currentNetworth:   currentNetworth,
		networtCalculator: n.networtCalculator,
		monthlyNetworths:  networthAtAges,
	}, nil
}

func (n *NetworthAtAgeDataProvider) getNextAgeMilestone(currentAge int) (int, error) {
	switch {
	case currentAge < 40:
		return 40, nil
	case currentAge < 50:
		return 50, nil
	case currentAge < 60:
		return 60, nil
	default:
		return 0, secretErrors.NoDataToBuildSecretPostFilters
	}
}
