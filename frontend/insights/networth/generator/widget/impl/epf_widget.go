package impl

import (
	"context"
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	feNetworthUi "github.com/epifi/gamma/api/frontend/insights/networth/ui"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/api/insights/epf/model"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/insights/networth/data_fetcher"
	networthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"
	"github.com/epifi/gamma/frontend/insights/networth/generator/widget"
	"github.com/epifi/gamma/frontend/insights/networth/generator/widget/impl/helper"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	epfRefreshMaxDuration = 30 * time.Minute
)

type EpfWidgetGenerator struct {
	config            *genconf.Config
	epfPassbookParams *genconf.EpfPassbookParams
	widgetConfig      *networthFePb.WidgetDetails
	userClient        userPb.UsersClient
	epfClient         beEpfPb.EpfClient
	releaseEvaluator  release.IEvaluator
	deeplinkBuilder   deeplink_builder.IDeeplinkBuilder
}

func NewEpfWidgetGenerator(
	config *genconf.Config,
	epfPassbookImportParams *genconf.EpfPassbookParams,
	widgetConfig *networthFePb.WidgetDetails,
	userClient userPb.UsersClient,
	epfClient beEpfPb.EpfClient,
	releaseEvaluator release.IEvaluator,
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder,
) *EpfWidgetGenerator {
	return &EpfWidgetGenerator{
		config:            config,
		epfPassbookParams: epfPassbookImportParams,
		widgetConfig:      widgetConfig,
		userClient:        userClient,
		epfClient:         epfClient,
		releaseEvaluator:  releaseEvaluator,
		deeplinkBuilder:   deeplinkBuilder,
	}
}

func (g *EpfWidgetGenerator) GenerateWidget(ctx context.Context, actorId string,
	categoryData *data_fetcher.CategoryData, categoryStatus networthFePb.NetworthCategoryStatus, widgetState networthFePb.NetworthWidgetState,
	refreshDetails *networthBePb.InstrumentRefreshDetails, onlyManualAssetsForRefresh bool) (*networthFePb.Widget, error) {
	var epfDeeplink *deeplinkPb.Deeplink
	if categoryStatus == networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_UNINITIALIZED {
		if g.epfPassbookParams.IsEpfRedirectionEnabled() {
			var err error
			epfDeeplink, err = g.getEpfImportDeeplink(ctx, actorId)
			if err != nil {
				return nil, fmt.Errorf("failed to get epf import deeplink: %w", err)
			}
		} else {
			widgetState = networthFePb.NetworthWidgetState_NETWORTH_WIDGET_STATE_COMING_SOON
		}
	} else if (categoryStatus == networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_INITIALIZED) &&
		g.epfPassbookParams.IsEpfRedirectionEnabled() {
		epfDeeplink = networthDeeplink.EpfDashboardDeeplink()
	} else if categoryStatus == networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_NOT_APPLICABLE &&
		g.epfPassbookParams.IsEpfRedirectionEnabled() {
		epfDeeplink = deeplink_builder.GetEpfUanListScreenDeeplink("")
	}

	widget, err := helper.GenerateBasicCardWidget(g.widgetConfig, categoryData, categoryStatus, epfDeeplink, widgetState, g.getWidgetTag(refreshDetails))
	if err != nil {
		return nil, fmt.Errorf("failed to generate card widget for epf: %w", err)
	}

	return widget, nil
}

func (g *EpfWidgetGenerator) getEpfImportDeeplink(ctx context.Context, actorId string) (*deeplinkPb.Deeplink, error) {
	phoneNumbers, err := g.getUserPhoneNumbers(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get phone number for actor: %w", err)
	}
	if len(phoneNumbers) == 0 {
		return nil, fmt.Errorf("no phone number found for actor")
	}
	isUanLookupDone, err := g.isUanLookupDone(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to check if uan lookup if complete for actor: %w", err)
	}
	var epfDeeplink *deeplinkPb.Deeplink
	// if uan lookup is already done for user, redirect to connect uan dashboard
	if isUanLookupDone {
		epfDeeplink, err = networthDeeplink.EpfConnectUanDashboard(phoneNumbers[0])
		if err != nil {
			return nil, fmt.Errorf("failed to get connect uan dashboard: %w", err)
		}
	} else {
		// if no uan present in table, redirect to epf confirm phone number screen
		epfDeeplink, err = g.deeplinkBuilder.EPFConfirmPhoneNumberScreen(ctx, phoneNumbers, actorId, "")
		if err != nil {
			return nil, fmt.Errorf("failed to get epf uan import deeplink: %w", err)
		}
	}
	return epfDeeplink, nil
}

func (g *EpfWidgetGenerator) getUserPhoneNumbers(ctx context.Context, actorId string) ([]*commontypes.PhoneNumber, error) {
	userResp, errResp := g.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(userResp, errResp); err != nil {
		return nil, fmt.Errorf("failed to get user details: %w", err)
	}

	return []*commontypes.PhoneNumber{userResp.GetUser().GetProfile().GetPhoneNumber()}, nil
}

func (g *EpfWidgetGenerator) isUanLookupDone(ctx context.Context, actorId string) (bool, error) {
	var (
		getUansResp              *beEpfPb.GetUANAccountsResponse
		getEpfPassbookRequestRes *beEpfPb.GetEpfPassbookRequestsForActorResponse
	)

	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		var err error
		getUansResp, err = g.epfClient.GetUANAccounts(gctx, &beEpfPb.GetUANAccountsRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(getUansResp, err); rpcErr != nil && !getUansResp.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("failed to get uan accounts for actor: %w", rpcErr)
		}
		return nil
	})
	grp.Go(func() error {
		var err2 error
		getEpfPassbookRequestRes, err2 = g.epfClient.GetEpfPassbookRequestsForActor(gctx, &beEpfPb.GetEpfPassbookRequestsForActorRequest{
			ActorId:   actorId,
			StartTime: timestamppb.New(time.Now().Add(-g.config.InsightsParams().EpfConfig().RecentEpfPassbookRequestFetchDuration())),
			EndTime:   timestamppb.New(time.Now()),
		})
		if rpcErr2 := epifigrpc.RPCError(getEpfPassbookRequestRes, err2); rpcErr2 != nil && !getEpfPassbookRequestRes.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("failed to get epf passbook requests for actor: %w", rpcErr2)
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return false, fmt.Errorf("failed to get uan account and epf passbook request for actor: %w", err)
	}
	if len(getUansResp.GetUanAccounts()) > 0 || len(getEpfPassbookRequestRes.GetEpfPassbookRequests()) > 0 {
		return true, nil
	}
	return false, nil
}

func (g *EpfWidgetGenerator) getWidgetTag(refreshDetails *networthBePb.InstrumentRefreshDetails) *ui.IconTextComponent {
	var (
		widgetTag *ui.IconTextComponent
	)

	if refreshDetails.GetRefreshStatus() == networthBePb.RefreshStatus_REFRESH_STATUS_IN_PROCESS {
		widgetTag = ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(PROCESSING, colors.ColorSupportingAmber900, commontypes.FontStyle_OVERLINE_2XS_CAPS)).
			WithContainer(0, 0, 9, colors.ColorSupportingAmber100).
			WithContainerPadding(2, 8, 2, 8)
	} else {
		widgetTag = helper.GetWidgetTag(refreshDetails)
	}

	return widgetTag
}

// isRefreshInProgress returns true if there is a uan in last epfRefreshMaxDuration which is still in passbook_fetch_initiated state
func (g *EpfWidgetGenerator) isRefreshInProgress(requestsStatusMap map[string]*beEpfPb.EpfPassbookRequestWithStatus) bool {
	for _, requestStatus := range requestsStatusMap {
		if requestStatus.GetEpfPassbookRequest().GetRequestStatus() == model.RequestStatus_REQUEST_STATUS_PASSBOOK_FETCH_INITIATED {
			if requestStatus.GetEpfPassbookRequest().GetUpdatedAt().AsTime().After(time.Now().Add(-epfRefreshMaxDuration)) {
				return true
			}
		}
	}
	return false
}

func (g *EpfWidgetGenerator) GenerateWealthBuilderLandingWidget(ctx context.Context, actorId string, widgetDataDetails *widget.WidgetDataDetails) (*feNetworthUi.WidgetV2, error) {
	var (
		wbWidget *feNetworthUi.WidgetV2
		err      error
	)
	deeplink := networthDeeplink.EpfDashboardDeeplink()
	bottomTag := getWidgetBottomTag(widgetDataDetails)

	wbWidget, err = widget.NewBasicWealthBuilderWidget(widgetDataDetails, deeplink, getRefreshStatus(widgetDataDetails), bottomTag)
	if err != nil {
		return nil, fmt.Errorf("failed to generate wealth builder widget for epf: %w", err)
	}
	return wbWidget, nil
}

func (g *EpfWidgetGenerator) GenerateZeroStateWealthBuilderLandingWidget(ctx context.Context, actorId string, widgetDataDetails *widget.WidgetDataDetails) (*feNetworthUi.WidgetV2, error) {
	epfDeeplink, err := g.getEpfImportDeeplink(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get epf import deeplink: %w", err)
	}
	wbWidget, err := widget.NewBasicWealthBuilderWidget(widgetDataDetails, epfDeeplink, nil, getZeroStateBottomTag(widgetDataDetails.WidgetWidth))
	if err != nil {
		return nil, fmt.Errorf("failed to generate wealth builder zero widget for epf: %w", err)
	}
	return wbWidget, nil
}
