package validation_types

import (
	"testing"

	wrappersPb "google.golang.org/protobuf/types/known/wrapperspb"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	"github.com/epifi/gamma/api/frontend/insights/networth/enums"
)

func TestTotalSumValidation_ValidateDataForMultiEditOption(t *testing.T) {
	type args struct {
		data       *networthFePb.NetWorthManualInputData
		validation *networthFePb.MultiEditOptionValidation
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "unhandled data type, return error",
			args: args{
				data: &networthFePb.NetWorthManualInputData{
					DataType: enums.NetworthManualFormInputDataType_NETWORTH_MANUAL_FORM_INPUT_DATA_TYPE_STRING,
				},
			},
			wantErr: true,
		},
		{
			name: "total sum equal, return nil",
			args: args{
				data: &networthFePb.NetWorthManualInputData{
					DataType: enums.NetworthManualFormInputDataType_NETWORTH_MANUAL_FORM_INPUT_DATA_TYPE_DOUBLE,
					Input: &networthFePb.NetWorthManualInputData_MultiEditableOptions{
						MultiEditableOptions: getMultiEditableOptionsForSumValidation(&wrappersPb.DoubleValue{Value: 50}, &wrappersPb.DoubleValue{Value: 40}),
					},
				},
				validation: &networthFePb.MultiEditOptionValidation{
					Validations: &networthFePb.MultiEditOptionValidation_SumValidation{
						SumValidation: &networthFePb.TotalSumValidation{
							TotalSum:        90,
							PossibleAbsDiff: 0.001,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "total sum is less than possible absolute difference, return nil",
			args: args{
				data: &networthFePb.NetWorthManualInputData{
					DataType: enums.NetworthManualFormInputDataType_NETWORTH_MANUAL_FORM_INPUT_DATA_TYPE_DOUBLE,
					Input: &networthFePb.NetWorthManualInputData_MultiEditableOptions{
						MultiEditableOptions: getMultiEditableOptionsForSumValidation(&wrappersPb.DoubleValue{Value: 49.99}, &wrappersPb.DoubleValue{Value: 40}),
					},
				},
				validation: &networthFePb.MultiEditOptionValidation{
					Validations: &networthFePb.MultiEditOptionValidation_SumValidation{
						SumValidation: &networthFePb.TotalSumValidation{
							TotalSum:        90,
							PossibleAbsDiff: 0.1,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "total sum not equal and not in range of possible absolute difference, return error",
			args: args{
				data: &networthFePb.NetWorthManualInputData{
					DataType: enums.NetworthManualFormInputDataType_NETWORTH_MANUAL_FORM_INPUT_DATA_TYPE_DOUBLE,
					Input: &networthFePb.NetWorthManualInputData_MultiEditableOptions{
						MultiEditableOptions: getMultiEditableOptionsForSumValidation(&wrappersPb.DoubleValue{Value: 50}, &wrappersPb.DoubleValue{Value: 40}),
					},
				},
				validation: &networthFePb.MultiEditOptionValidation{
					Validations: &networthFePb.MultiEditOptionValidation_SumValidation{
						SumValidation: &networthFePb.TotalSumValidation{
							TotalSum:        100,
							PossibleAbsDiff: 0.001,
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s := NewTotalSumValidationHandler()
			err := s.ValidateDataForMultiEditOption(tt.args.data, tt.args.validation)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateDataForMultiEditOption() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func getMultiEditableOptionsForSumValidation(data1, data2 *wrappersPb.DoubleValue) *networthFePb.MultipleEditableOptions {
	return &networthFePb.MultipleEditableOptions{
		Options: []*networthFePb.MultiEditOption{
			{
				InputOptionData: &networthFePb.InputOptionData{
					InputValue: &networthFePb.InputOptionValue{
						Value: &networthFePb.InputOptionValue_DoubleData{
							DoubleData: &networthFePb.DoubleType{
								Data: data1,
							},
						},
					},
				},
			},
			{
				InputOptionData: &networthFePb.InputOptionData{
					InputValue: &networthFePb.InputOptionValue{
						Value: &networthFePb.InputOptionValue_DoubleData{
							DoubleData: &networthFePb.DoubleType{
								Data: data2,
							},
						},
					},
				},
			},
		},
	}
}
