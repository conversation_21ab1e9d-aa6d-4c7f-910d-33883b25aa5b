package conv

import (
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/search"
	"github.com/epifi/gamma/api/frontend/search/auto_suggestions"
	search2 "github.com/epifi/gamma/api/search"
	auto_suggestions2 "github.com/epifi/gamma/api/search/auto_suggestions"
)

func ConvertBEAutoSuggestionsToFEAutoSuggestions(resp *search2.GetAutoSuggestionsV2Response) *search.GetAutoSuggestionsResponse {
	if resp == nil {
		return nil
	}
	var feAutoSuggestionBundle []*auto_suggestions.AutoSuggestionBundle
	for _, eachBeAutoSuggestionBundle := range resp.GetAutoSuggestions().GetAutoSuggestionBundles() {
		feAutoSuggestionBundle = append(feAutoSuggestionBundle, &auto_suggestions.AutoSuggestionBundle{
			Type: auto_suggestions.AutoSuggestionType(eachBeAutoSuggestionBundle.GetType()),
			Rows: ConvertBEAutoSuggestionRowsToFEAutoSuggestionRows(eachBeAutoSuggestionBundle.GetRows()),
		})
	}
	return &search.GetAutoSuggestionsResponse{
		Status: resp.GetStatus(),
		RespHeader: &header.ResponseHeader{
			Status: resp.GetStatus(),
		},
		AutoSuggestions: &auto_suggestions.AutoSuggestions{
			AutoSuggestionBundles: feAutoSuggestionBundle,
		},
	}
}

func ConvertBEAutoSuggestionRowsToFEAutoSuggestionRows(rows []*auto_suggestions2.AutoSuggestionRow) []*auto_suggestions.AutoSuggestionRow {
	if rows == nil {
		return nil
	}
	var ret []*auto_suggestions.AutoSuggestionRow
	for _, eachRow := range rows {
		ret = append(ret, &auto_suggestions.AutoSuggestionRow{
			Title:             eachRow.GetTitle(),
			SubTitle:          eachRow.GetSubTitle(),
			PrimaryImg:        eachRow.GetPrimaryImg(),
			SecondaryImg:      eachRow.GetSecondaryImg(),
			Deeplink:          eachRow.GetDeeplink(),
			HashedPhoneNumber: eachRow.GetHashedPhoneNumber(),
		})
	}
	return ret
}
