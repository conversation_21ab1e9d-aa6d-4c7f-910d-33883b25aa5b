package search

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/rpc/code"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/actor/mocks"
	feDeeplink "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/search/widget"
	beSearchPb "github.com/epifi/gamma/api/search"
	actionbarPb "github.com/epifi/gamma/api/search/actionbar"
	"github.com/epifi/gamma/api/search/txn"
)

func Test_getTxnReceiptDeeplink(t *testing.T) {
	testCases := []struct {
		name       string
		inpOrderId string
		inpAaTxnId string
		exp        *widget.DeepLinkElement
	}{
		{
			name:       "Order ID is present and AA Txn ID is not present",
			inpOrderId: "o1",
			inpAaTxnId: "",
			exp: &widget.DeepLinkElement{
				Link: &feDeeplink.Deeplink{
					Screen: feDeeplink.Screen_TRANSACTION_RECEIPT,
					ScreenOptions: &feDeeplink.Deeplink_TransactionReceiptScreenOptions{
						TransactionReceiptScreenOptions: &feDeeplink.TransactionReceiptScreenOptions{
							OrderId: "o1",
							Identifier: &feDeeplink.TransactionReceiptScreenOptions_OrdersId{
								OrdersId: "o1",
							},
						},
					},
				},
			},
		},
		{
			name:       "Order ID is present and AA Txn ID is present",
			inpOrderId: "o1",
			inpAaTxnId: "a1",
			exp: &widget.DeepLinkElement{
				Link: &feDeeplink.Deeplink{
					Screen: feDeeplink.Screen_TRANSACTION_RECEIPT,
					ScreenOptions: &feDeeplink.Deeplink_TransactionReceiptScreenOptions{
						TransactionReceiptScreenOptions: &feDeeplink.TransactionReceiptScreenOptions{
							OrderId: "o1",
							Identifier: &feDeeplink.TransactionReceiptScreenOptions_AaTxnId{
								AaTxnId: "o1",
							},
						},
					},
				},
			},
		},
		{
			name:       "Order ID and AA Txn ID is present",
			inpOrderId: "o1",
			inpAaTxnId: "a1",
			exp: &widget.DeepLinkElement{
				Link: &feDeeplink.Deeplink{
					Screen: feDeeplink.Screen_TRANSACTION_RECEIPT,
					ScreenOptions: &feDeeplink.Deeplink_TransactionReceiptScreenOptions{
						TransactionReceiptScreenOptions: &feDeeplink.TransactionReceiptScreenOptions{
							OrderId: "o1",
							Identifier: &feDeeplink.TransactionReceiptScreenOptions_AaTxnId{
								AaTxnId: "o1",
							},
						},
					},
				},
			},
		},
		{
			name:       "Order ID is null and AA Txn ID is present",
			inpOrderId: "",
			inpAaTxnId: "a1",
			exp:        nil,
		},
		{
			name:       "Order ID is nil",
			inpOrderId: "",
			exp:        nil,
		},
	}
	for _, tt := range testCases {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := getTxnReceiptDeeplink(tt.inpOrderId, tt.inpAaTxnId, nil)
			if !reflect.DeepEqual(got, tt.exp) {
				t.Errorf("Expected %v, Got %v", tt.exp, got)
			}
		})
	}
}

func Test_getSectionForRelatedQueries(t *testing.T) {
	type args struct {
		relatedQueries []*beSearchPb.RelatedQueryDetail
	}
	tests := []struct {
		name  string
		args  args
		want  []*widget.UnitRelatedQuery
		want1 []*widget.UnitRelatedQuery
	}{
		{
			name: "no related queries",
			args: args{
				relatedQueries: nil,
			},
			want:  nil,
			want1: nil,
		},
		{
			name: "2 related queries",
			args: args{
				relatedQueries: []*beSearchPb.RelatedQueryDetail{
					{
						RelatedQuery: "related query 1",
					},
					{
						RelatedQuery: "related query 2",
					},
				},
			},
			want: []*widget.UnitRelatedQuery{
				{
					Text: "related query 1",
				},
				{
					Text: "related query 2",
				},
			},
			want1: nil,
		},
		{
			name: "3 related queries",
			args: args{
				relatedQueries: []*beSearchPb.RelatedQueryDetail{
					{
						RelatedQuery: "related query 1",
					},
					{
						RelatedQuery: "related query 2",
					}, {
						RelatedQuery: "related query 3",
					},
				},
			},
			want: []*widget.UnitRelatedQuery{
				{
					Text: "related query 1",
				},
				{
					Text: "related query 2",
				},
			},
			want1: []*widget.UnitRelatedQuery{
				{
					Text: "related query 3",
				},
			},
		},
		{
			name: "5 related queries",
			args: args{
				relatedQueries: []*beSearchPb.RelatedQueryDetail{
					{
						RelatedQuery: "related query 1",
					},
					{
						RelatedQuery: "related query 2",
					}, {
						RelatedQuery: "related query 3",
					},
					{
						RelatedQuery: "related query 4",
					},
					{
						RelatedQuery: "related query 5",
					},
				},
			},
			want: []*widget.UnitRelatedQuery{
				{
					Text: "related query 1",
				},
				{
					Text: "related query 2",
				},
				{
					Text: "related query 3",
				},
			},
			want1: []*widget.UnitRelatedQuery{
				{
					Text: "related query 4",
				},
				{
					Text: "related query 5",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := getSectionForRelatedQueries(tt.args.relatedQueries)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getSectionForRelatedQueries() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("getSectionForRelatedQueries() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestService_getActorIdToIconUrlMap(t *testing.T) {
	type mockGetEntityDetailsRes struct {
		res *actor.GetEntityDetailsResponse
		err error
	}
	type args struct {
		quickInfoResponses []*actionbarPb.QuickInfoResponse
	}
	tests := []struct {
		name    string
		args    args
		rpcResp *mockGetEntityDetailsRes
		want    map[string]string
	}{
		{
			name: "When GetEntityDetails() RPC does not return any error",
			args: args{
				quickInfoResponses: []*actionbarPb.QuickInfoResponse{
					{
						Transactions: []*txn.TransactionView{
							{
								SecondActorId: "SecondaryActorId1",
							},
							{
								SecondActorId: "SecondaryActorId2",
							},
						},
					},
				},
			},
			rpcResp: &mockGetEntityDetailsRes{
				res: &actor.GetEntityDetailsResponse{
					Status: &rpc.Status{
						Code: uint32(code.Code_OK),
					},
					EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{
						{
							ActorId:         "SecondaryActorId1",
							ProfileImageUrl: "ProfileImageUrl1",
						},
						{
							ActorId:         "SecondaryActorId2",
							ProfileImageUrl: "ProfileImageUrl2",
						},
					},
				},
				err: nil,
			},
			want: map[string]string{
				"SecondaryActorId1": "ProfileImageUrl1",
				"SecondaryActorId2": "ProfileImageUrl2",
			},
		},
		{
			name: "When GetEntityDetails() RPC returns error",
			args: args{
				quickInfoResponses: []*actionbarPb.QuickInfoResponse{
					{
						Transactions: []*txn.TransactionView{
							{
								SecondActorId: "SecondaryActorId1",
							},
							{
								SecondActorId: "SecondaryActorId2",
							},
						},
					},
				},
			},
			rpcResp: &mockGetEntityDetailsRes{
				res: nil,
				err: errors.New("error occurred"),
			},
			want: map[string]string{},
		},
		{
			name: "When quickInfoResponses is nil",
			args: args{
				quickInfoResponses: nil,
			},
			rpcResp: &mockGetEntityDetailsRes{},
			want:    map[string]string{},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockActorClient := mocks.NewMockActorClient(ctr)
			mockActorClient.EXPECT().GetEntityDetails(gomock.Any(), gomock.Any()).Return(tt.rpcResp.res, tt.rpcResp.err).AnyTimes()
			s := &Service{
				actorClient: mockActorClient,
			}
			got := s.getActorIdToIconUrlMap(context.Background(), tt.args.quickInfoResponses)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getActorIdToIconUrlMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_removeDuplicateStringsFromList(t *testing.T) {
	type args struct {
		strings []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "when duplicates exist",
			args: args{
				strings: []string{"A", "B", "A"},
			},
			want: []string{"A", "B"},
		},
		{
			name: "when duplicates don't exist",
			args: args{
				strings: []string{"A", "B"},
			},
			want: []string{"A", "B"},
		},
		{
			name: "when list is nil",
			args: args{
				strings: nil,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			if got := removeDuplicateStringsFromList(tt.args.strings); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("removeDuplicateStringsFromList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_sliceListIntoBatches(t *testing.T) {
	type args struct {
		list      []string
		batchSize int
	}
	tests := []struct {
		name string
		args args
		want [][]string
	}{
		{
			name: "when len of list > batch size",
			args: args{
				list:      []string{"a", "b", "c", "d"},
				batchSize: 3,
			},
			want: [][]string{{"a", "b", "c"}, {"d"}},
		},
		{
			name: "when len of list < batch size",
			args: args{
				list:      []string{"a", "b", "c", "d"},
				batchSize: 5,
			},
			want: [][]string{{"a", "b", "c", "d"}},
		},
		{
			name: "when len of list = batch size",
			args: args{
				list:      []string{"a", "b", "c", "d"},
				batchSize: 4,
			},
			want: [][]string{{"a", "b", "c", "d"}},
		},
		{
			name: "when list is nil",
			args: args{
				list:      nil,
				batchSize: 4,
			},
			want: nil,
		},
		{
			name: "when batch size is < 1",
			args: args{
				list:      []string{"a", "b", "c", "d"},
				batchSize: 0,
			},
			want: [][]string{{"a", "b", "c", "d"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := sliceListIntoBatches(tt.args.list, tt.args.batchSize); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("sliceListIntoBatches() = %v, want %v", got, tt.want)
			}
		})
	}
}
