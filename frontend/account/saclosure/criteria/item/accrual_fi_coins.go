package item

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	accrualPb "github.com/epifi/gamma/api/accrual"
	"github.com/epifi/gamma/api/frontend/account/enums"
	saClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	rewardsPb "github.com/epifi/gamma/api/rewards"

	"github.com/epifi/gamma/frontend/config/genconf"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

type FiCoinsItemMaker struct {
	conf          *genconf.Config
	accrualClient accrualPb.AccrualClient
}

func NewFiCoinsItemMaker(conf *genconf.Config, accrualClient accrualPb.AccrualClient) *FiCoinsItemMaker {
	return &FiCoinsItemMaker{
		conf:          conf,
		accrualClient: accrualClient,
	}
}

func (i *FiCoinsItemMaker) CollectData(ctx context.Context, actorId string) (map[enums.SaClosureCriteriaItem]*saClosurePb.CriteriaItemMeta, error) {
	criteriaMetas := make(map[enums.SaClosureCriteriaItem]*saClosurePb.CriteriaItemMeta)
	itemEnum := enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_FI_COIN_BALANCE

	resp, err := i.accrualClient.GetAccountDetails(ctx, &accrualPb.GetAccountDetailsRequest{
		ActorId:     actorId,
		AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "failed to fetch accrual account details for user", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		criteriaMetas[itemEnum] = &saClosurePb.CriteriaItemMeta{
			FetchFailed:  true,
			SoftCriteria: true,
		}
		return criteriaMetas, nil
	}

	if resp.GetAvailableBalance() == 0 {
		return nil, nil
	}

	criteriaMetas[itemEnum] = &saClosurePb.CriteriaItemMeta{
		SoftCriteria: true,
		Value:        &saClosurePb.CriteriaItemMeta_FiCoins{FiCoins: resp.GetAvailableBalance()},
	}
	return criteriaMetas, nil
}

func (i *FiCoinsItemMaker) BuildItem(item enums.SaClosureCriteriaItem, meta *saClosurePb.CriteriaItemMeta) (saClosurePb.ICriteriaItem, error) {
	itemConf, ok := i.conf.SavingsAccountClosure().DisplayValues().CriteriaScreen().CriteriaItems().Load(item.String())
	if !ok {
		return nil, fmt.Errorf("no display config found for %s item", item.String())
	}

	return makeItem(meta, itemConf, item)
}
