package screen

import (
	"context"

	"github.com/pkg/errors"

	cfg "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/gamma/api/frontend/deeplink"
	saClosurePb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/sa_closure"
	"github.com/epifi/gamma/api/typesv2/info"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type FeedbackTicketSubmittedScreenBuilder struct {
	conf *genconf.Config
}

func NewFeedbackTicketSubmittedScreenBuilder(conf *genconf.Config) *FeedbackTicketSubmittedScreenBuilder {
	return &FeedbackTicketSubmittedScreenBuilder{conf: conf}
}

func (b *FeedbackTicketSubmittedScreenBuilder) BuildScreen(ctx context.Context, params *BuildScreenParams) (*deeplink.Deeplink, error) {
	displayValues := b.conf.SavingsAccountClosure().DisplayValues().FeedbackTicketSubmittedScreen()

	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&saClosurePb.SaClosureRequestSubmittedScreenOptions{
		FullScreenInfoView: &info.FullScreenInfoView{
			Image: cfg.NewVisualElementImagePb(displayValues.Icon()),
			Title: cfg.NewTextPb(displayValues.Title(), ""),
			Body:  cfg.NewTextPb(displayValues.SubTitle(), ""),
			Ctas: []*deeplink.Cta{
				{
					Type: deeplink.Cta_CUSTOM,
					Text: SupportTicketCtaText,
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CX_TICKET_LIST_SCREEN,
					},
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
			BackIcon:              cfg.NewVisualElementImagePb(displayValues.BackIcon()),
			BackgroundColor:       displayValues.BgColor(),
			ArrangeCtasVertically: true,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to build FullScreenInfoViewScreenOptions")
	}

	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_SA_CLOSURE_REQUEST_SUBMITTED_SCREEN,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}
