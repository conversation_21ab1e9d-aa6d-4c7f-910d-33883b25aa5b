package signup

import (
	"context"
	"fmt"
	"strconv"

	"go.uber.org/zap"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	afuDlPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/afu"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user"

	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

//nolint:dupl
func (s *Service) getAFUScreenOptionsDetailsV2(ctx context.Context, profile *user.ProfileEssentials, popUp deeplinkPb.AFUConfirmStartOptions_PopUpType, actorId string, selectedEmail string,
	selectedPh *commontypes.PhoneNumber) (*anyPb.Any, error) {
	var (
		screenOptions *anyPb.Any
		err           error
		maskedEmail   string
	)
	switch popUp {
	case deeplinkPb.AFUConfirmStartOptions_EMAIL_UPDATE_CONFIRMATION:
		maskedEmail, err = maskEmail(profile.GetEmail())
		if err != nil {
			logger.Error(ctx, "error in masking email", zap.Error(err))
			_, _, err = s.addOAuthErrorResponseWithMetadata(rpc.StatusInternal(), err, actorId)
			return nil, err
		}
		screenOptions, err = deeplinkV3.GetScreenOptionV2(&afuDlPb.GetAfuWarningBottomUpSheet{
			PopUpType: afuDlPb.GetAfuWarningBottomUpSheet_EMAIL_UPDATE_CONFIRMATION,
			Img:       &commontypes.Image{ImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/reoobe/chaticon", Width: 72, Height: 68},
			Title:     commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Continue with "+selectedEmail+"?", "#313234", commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER),
			Subtitle:  commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Your account is linked to another email ID "+maskedEmail+". Do you want to update it?", "#313234", commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER),
			InfoSection: &ui.IconTextComponent{
				LeftIcon:          &commontypes.Image{ImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/reoobe/infoicon", Width: 24, Height: 24},
				LeftImgTxtPadding: 8,
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					CornerRadius:  16,
					BorderColor:   "#B2B5B9",
					BorderWidth:   1,
					LeftPadding:   16,
					RightPadding:  16,
					TopPadding:    16,
					BottomPadding: 16,
				},
				Texts: []*commontypes.Text{
					commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("You won’t be able to go back to your old email ID or change it again for the next "+"<b>7 days</b>"), "#6A6D70", commontypes.FontStyle_BODY_S),
				},
			},
			Ctas: []*deeplinkPb.Cta{
				{
					Text:         "Go back to old email ID",
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Type:         deeplinkPb.Cta_LOGOUT,
				},
				{
					Text:         "Continue & update email ID",
					DisplayTheme: deeplinkPb.Cta_SECONDARY,
				},
			},
		})
	case deeplinkPb.AFUConfirmStartOptions_PHONE_UPDATE_CONFIRMATION:
		maskedPhone := mask.GetMaskedString(mask.DontMaskLastFourChars, strconv.FormatUint(profile.GetPhoneNumber().GetNationalNumber(), 10))
		var nationalNumberStr = strconv.FormatUint(selectedPh.GetNationalNumber(), 10)
		screenOptions, err = deeplinkV3.GetScreenOptionV2(&afuDlPb.GetAfuWarningBottomUpSheet{
			PopUpType: afuDlPb.GetAfuWarningBottomUpSheet_PHONE_UPDATE_CONFIRMATION,
			Img:       &commontypes.Image{ImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/reoobe/chaticon", Width: 72, Height: 68},
			Title:     commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Continue with "+nationalNumberStr+"?", "#313234", commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER),
			Subtitle:  commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Your account is linked to another mobile number "+maskedPhone+". Do you want to update it?", "#313234", commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER),
			InfoSection: &ui.IconTextComponent{
				LeftIcon:          &commontypes.Image{ImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/reoobe/infoicon", Width: 24, Height: 24},
				LeftImgTxtPadding: 8,
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					CornerRadius:  16,
					BorderColor:   "#B2B5B9",
					BorderWidth:   1,
					LeftPadding:   16,
					RightPadding:  16,
					TopPadding:    16,
					BottomPadding: 16,
				},
				Texts: []*commontypes.Text{
					commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("You won’t be able to go back to your old number or change it again for the next "+"<b>6 months</b>"), "#6A6D70", commontypes.FontStyle_BODY_S),
				},
			},
			Ctas: []*deeplinkPb.Cta{
				{
					Text:         "Restart with old number",
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Type:         deeplinkPb.Cta_LOGOUT,
				},
				{
					Text:         "Continue & update number",
					DisplayTheme: deeplinkPb.Cta_SECONDARY,
				},
			},
		})
	}
	if err != nil {
		return nil, err
	}
	return screenOptions, nil
}
