package manager

import (
	"context"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/consent"
)

type AckManager interface {
	AckProc(ctx context.Context, req *AckProcRequest) (*AckProcResponse, error)
}

type AckProcRequest struct {
	AckType consent.AckType
	AckId   string
	ActorId string
}

type AckProcResponse struct {
	NextAction *deeplink.Deeplink
}
