// nolint: dupl
//
//go:generate mockgen -source=line_items.go -destination=./mocks/line_items.go package=mocks
package visualcomponents

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	pb "github.com/epifi/gamma/api/frontend/analyser"
)

type LineItemHeaderGenerator interface {
	GenerateHeader(ctx context.Context) (*pb.LineItemHeader, error)
}

type LineItemsGenerator[T any] struct {
	items            []T
	generateLineItem func(ctx context.Context, item T) (*pb.LineItem, error)
	headerGenerator  LineItemHeaderGenerator
}

func NewLineItemsGenerator[T any](items []T,
	lineItemGenerator func(ctx context.Context, item T) (*pb.LineItem, error), headerGenerator LineItemHeaderGenerator) *LineItemsGenerator[T] {
	return &LineItemsGenerator[T]{
		items:            items,
		generateLineItem: lineItemGenerator,
		headerGenerator:  headerGenerator,
	}
}

func (p *LineItemsGenerator[T]) Generate(ctx context.Context) (*pb.VisualComponent, error) {
	var lineItems []*pb.LineItem
	for _, item := range p.items {
		lineItem, err := p.generateLineItem(ctx, item)
		if err != nil {
			return nil, fmt.Errorf("error generating line item : %w", err)
		}
		lineItems = append(lineItems, lineItem)
	}
	// if no line items were generated, return nil
	if len(lineItems) == 0 {
		return nil, nil
	}
	var header *pb.LineItemHeader
	if p.headerGenerator != nil {
		var err error
		header, err = p.headerGenerator.GenerateHeader(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to generate line item header : %w", err)
		}
	}
	return &pb.VisualComponent{
		Type: pb.VisualComponentType_VISUAL_COMPONENT_TYPE_LINE_ITEMS,
		Component: &pb.VisualComponent_LineItems{
			LineItems: &pb.LineItems{
				Header: header,
				Items:  lineItems,
			},
		},
	}, nil
}

type ItemDetails struct {
	Identifier string
}

type LineItemGeneratorV2 interface {
	GenerateLineItem(ctx context.Context, item *ItemDetails) (*pb.LineItem, error)
}

type LineItemsGeneratorV2 struct {
	items             []*ItemDetails
	lineItemGenerator LineItemGeneratorV2
}

func NewLineItemsGeneratorV2(items []*ItemDetails,
	lineItemGenerator LineItemGeneratorV2) *LineItemsGeneratorV2 {
	return &LineItemsGeneratorV2{
		items:             items,
		lineItemGenerator: lineItemGenerator,
	}
}

func (p *LineItemsGeneratorV2) Generate(ctx context.Context) (*pb.VisualComponent, error) {
	var lineItems []*pb.LineItem
	for _, item := range p.items {
		lineItem, err := p.lineItemGenerator.GenerateLineItem(ctx, item)
		if err != nil {
			return nil, fmt.Errorf("error generating line item : %w", err)
		}
		lineItems = append(lineItems, lineItem)
	}
	// if no line items were generated, return nil
	if len(lineItems) == 0 {
		return nil, nil
	}
	return &pb.VisualComponent{
		Type: pb.VisualComponentType_VISUAL_COMPONENT_TYPE_LINE_ITEMS,
		Component: &pb.VisualComponent_LineItems{
			LineItems: &pb.LineItems{
				Items: lineItems,
			},
		},
	}, nil
}

type StandardLineItemHeaderGenerator struct {
	leftTitle, rightTitle string
}

func NewStandardLineItemHeaderGenerator(leftTitle, rightTitle string) *StandardLineItemHeaderGenerator {
	return &StandardLineItemHeaderGenerator{
		leftTitle:  leftTitle,
		rightTitle: rightTitle,
	}
}

func (l *StandardLineItemHeaderGenerator) GenerateHeader(ctx context.Context) (*pb.LineItemHeader, error) {
	if l.leftTitle == "" && l.rightTitle == "" {
		return nil, fmt.Errorf("both left and right title cannot be empty")
	}
	return &pb.LineItemHeader{
		LeftSection: &pb.HeaderSection{
			HeaderItems: []*pb.HeaderItem{
				{
					HeaderItemType: pb.HeaderItemType_HEADER_ITEM_TYPE_TEXT,
					Item: &pb.HeaderItem_Text{
						Text: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: l.leftTitle,
							},
						},
					},
				},
			},
		},
		RightSection: &pb.HeaderSection{
			HeaderItems: []*pb.HeaderItem{
				{
					HeaderItemType: pb.HeaderItemType_HEADER_ITEM_TYPE_TEXT,
					Item: &pb.HeaderItem_Text{
						Text: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: l.rightTitle,
							},
						},
					},
				},
			},
		},
	}, nil
}
