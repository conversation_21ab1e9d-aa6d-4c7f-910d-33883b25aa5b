Application:
  Environment: "staging"
  Name: "frontend"
  EnableDeviceIntegrityCheck: false
  EnableLocationInterceptor: true
  MaxGRPCTimeout: "1m"

Server:
  Ports:
    GrpcPort: 8082
    GrpcSecurePort: 9509
    HttpPort: 9999
    HttpPProfPort: 9990

SecureLogging:
  EnablePartnerLog: false
  EnableSecureLog: true
  PartnerLogPath: "/var/log/frontend/partner/partner.log"
  SecureLogPath: "/var/log/frontend/secure.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 0
    ClientName: home

LegalDocuments:
  FiTncUrl: "https://web.staging.pointz.in/T&C"
  FederalBankTncUrl: "https://www.federalbank.co.in/epifi-tandc#CASA"
  FiPrivacyPolicyUrl: "https://web.staging.pointz.in/privacy"
  FiWealthTncUrl: "https://web.staging.pointz.in/wealth/TnC"
  OpenSourceLicenses:
    Firebase: ""
    Cronet: ""
    ChromeWebView: ""

#json file path
PayErrorViewJson: "./mappingJson/errorViewMapping.json"
CardErrorViewJson: "./mappingJson/cardErrorViewMapping.json"
DisplayCategoryMappingJson: "./mappingJson/displayCategoryMapping.json"

Flags:
  SkipAddMoney: false
  TrimDebugMessageFromStatus: false
  EnableCardTracking: true
  EnableCardBlock: true
  EnableCardQRCodeScan: true
  EnableCardOnlineTxnEnabledTile: true
  EnableVKYCOnlyForInternalUsers: true
  EnableVkycScheduleFlow: true
  EnableCardOffers: true
  SkipUserRevokeStateCheck: false
  EnableCardTrackingAndActivationChanges:
    IsEnableOnAndroid: true
    MinAndroidVersion: 117
    IsEnableOnIos: true
    MinIosVersion: 200
  EnableCheckForAccessRevoke: true
  EnableAutoInvestComponentOnInvestLanding:
    MinAndroidVersion: 231
    MinIOSVersion: 333
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableUSStocksInstrumentCardFlag: true
  EnableMFInstrumentCardFlag: true
  EnableSDInstrumentCardFlag: true
  EnableFDInstrumentCardFlag: true
  EnableJumpInstrumentCardFlag: true
  EnableInvestmentLandingQuickLinksComponent:
    MinAndroidVersion: 231
    MinIOSVersion: 999
    FallbackToEnableFeature: false
    DisableFeature: false
  EnablePartnersComponentInvestLanding:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  UnsupportedCustomerDeviceModels: [ ]
  UseAppsFlyerClientKeyV2: false
  EnableCCBillDashboardV2FeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 281
    IsEnableOnIos: true
    MinIosVersion: 1674
  EnableSecuredCardsRewardsDashboard:
    IsEnableOnAndroid: true
    MinAndroidVersion: 281
    IsEnableOnIos: true
    MinIosVersion: 381
  EnableGenericRewardsDashboard:
    IsEnableOnAndroid: false
    MinAndroidVersion: 3000
    IsEnableOnIos: false
    MinIosVersion: 3000
  EnableInhouseBiometricLiveness:
    MinAndroidVersion: 1
    MinIOSVersion: 1
    FallbackToEnableFeature: true
    DisableFeature: false
  ReOOBECooldownBottomSheetScreenOptionsV2:
    MinAndroidVersion: 417
    MinIOSVersion: 2630
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableCardDesignEnhancement:
    MinAndroidVersion: 1
    MinIOSVersion: 1
    FallbackToEnableFeature: false
    DisableFeature: false

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

RewardsFrontendMeta:
  AndroidAppVersionsNotSupportingMultiChoiceRewards: [ 0 ]
  MinAndroidAppVersionWithAllRewardTileStatesClickable: 0
  MinAndroidAppVersionSupportingGiftHamperReward: 87
  MinIosAppVersionSupportingGiftHamperReward: 51
  MinAndroidAppVersionSupportingCBRV2: 143
  MinAndroidVersionSupportingLockingOfRewardsForMinKycUsers: 181
  MinIosVersionSupportingLockingOfRewardsForMinKycUsers: 154
  MinAndroidVersionForEmptyAddressesGracefulHandling: 9999
  MinIosVersionForEmptyAddressesGracefulHandling: 9999
  MinAndroidVersionSupportingBoosterFields: 206
  MinIosVersionSupportingBoosterFields: 188
  MinAndroidAppVersionSupportingUnredeemableOfferLabel: 194
  MinIosAppVersionSupportingUnredeemableOfferLabel: 999
  MinAndroidAppVersionSupportingComingSoonOffer: 194
  MinIosAppVersionSupportingComingSoonOffer: 999
  MinAndroidAppVersionSupportingInventoryExhaustedOffer: 194
  MinIosAppVersionSupportingInventoryExhaustedOffer: 999
  MinAndroidAppVersionSupportingThriweBenefitsPackageOffers: 10000
  MinIosAppVersionSupportingThriweBenefitsPackageOffers: 1318
  MinAndroidAppVersionSupportingSalaryExclusiveOffer: 166
  MinIosAppVersionSupportingSalaryExclusiveOffer: 133
  MinAndroidAppVersionSupportingYourRewardsCardV2: 0
  MinIosAppVersionSupportingYourRewardsCardV2: 0
  MinAndroidAppVersionSupportingDefaultOfferType: 0
  MinIosAppVersionSupportingDefaultOfferType: 0
  MinAndroidAppVersionSupportingCardOfferCatalogV2: 150
  MinIosAppVersionSupportingCardOfferCatalogV2: 150
  MinIosVersionSupportingMyRewardsV2Screen: 1487
  # todo(divyadeep): update this with correct version
  MinIosVersionWithRedeemedOffersScreenPaginationFix: 9999
  IsWebPageWithCardDetailsScreenEnabled: false
  MinAndroidVersionForWebPageWithCardDetailsScreen: 5620
  MinIosVersionForWebPageWithCardDetailsScreen: 2118
  WaysToEarnRewardsScreenVersionToRender: 2
  RewardClaimFlowAllowedNextScreenCTAs:
    SALARY_PROGRAM: true
    TIER: true
  OffersCatalogPageConfig:
    ForceUpgradeFeatureConfig:
      MinAndroidVersion: 466
      MinIOSVersion:  2900

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    RudderWriteKey: "staging/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "staging/gcloud/profiling-service-account-key"
    RudderClientApiKey: "staging/rudder/android_write_key"
    AppsFlyerClientKey: "staging/appsflyer/client_key"
    OneMoneyClientSecret: "staging/onemoney/client_secret"
    DeviceIdsEnabledForSafetyNetV2: "staging/frontend-auth/v2_safetynet_enabled_device_ids"
    MoengageAppSdkKey: "staging/moengage/app-sdk-key"
    ActiveDeviceIntegrityTokenSigningKey: "staging/frontend-auth/device-integrity-token-signing-key"
    MiAmpPushSecretJson: "staging/mi_amp_push/mi_secret_json"
    AppsFlyerClientKeyV2: "staging/appsflyer/client_key_2"
    RudderIosClientApiKey: "staging/rudder/ios_write_key"
    RazorpaySecrets: "staging/frontend/razorpay-federal-secured-cards-api-key"

DeviceIntegrity:
  EnableWhitelistedTokens: true
  SkipAsyncDeviceIntegrityChecks: false
  WhitelistedTokensList: [ "DUMMY_TOKEN" ]
  DefaultHighRiskDeviceConsentDuration: "24h"
  MaxHighRiskDeviceConsentDuration: "1080h" # 45 days
  AsyncDeviceIntegrityCheck:
    DisableFeature: false
    MinAndroidVersion: 180
    MinIOSVersion: 10000

VKYC:
  PopupTileDuration: 12 # in hours
  PopupTileNonDismissableAfter: 60 # in minutes
  AccountFreezePopupNonDismissibleWithinDays: 30 # in days
  AccountFreezeThreshold: "4320h" # 180 days
  SavingsBalanceLimitPercent: 70
  CreditBalanceLimitPercent: 70
  PerformEkycAfter: "72h"
  SlotDays: 5
  MorningStart: 8
  SplitMorning: 12
  SplitAfternoon: 15
  SplitLateAfternoon: 23
  EveningEnd: 23
  ScheduleToLiveCutOffMinutes: 2
  # redmit 8A, Samsumng S20+, Samsung note10+, galaxy note 8 for testing
  NewPopupTileAccountCreationTimeLimit: 180 # in days
  NewPopupTileDuration: 84 # in hours
  HomeBannerColorHex: "#383838"
  ABFeatureReleaseConfig:
    FeatureConstraints:
      - VKYC_NEW_REVIEW_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 0
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99

InsightsParams:
  GetInsightConfig:
    MarkNoticedAfter: 2
  SmsReadConsentPocAndroidAppVersion: 392
  EnableDailyChangeComponentViaSegment: false
  EpfConfig:
    AndroidAppVersionSkipEpfDeeplink: 463

Card:
  MinAndroidVersionForFMAuth: 56
  CardDynamicTileDuration:
    ViewCardDeliveryTracking: "5m"
    QRCodeAsPrimaryTile: "15m"
    ViewCardOnlineTxnEnabledTile: "5m"
    ViewQRCodeScanTime: "10m"
    ViewSecurePinActivationTime: "20m"
  EnableSecurePinActivationFlow:
    IsEnableOnAndroid: true
    MinAndroidVersion: 250
    IsEnableOnIos: true
    MinIosVersion: 250
  MinAndroidVersionForCardOffers: 79
  MinAndroidVersionForCardTracking: 83
  MinAndroidVersionForSecurePinValidation: 200
  MinIOSVersionForSecurePinValidation: 200
  EnableSecurePinValidationAuth: false
  AllowedUserGrpForSecurePinValidationAuth:
    - 1 # INTERNAL
  OffersDynamicTileExpiryTime: "31-08-2022T23:59:00"
  EnableCardOffersInformativeDynamicTile: true
  PrintingToDispatchDynamicTileDuration: "10m"
  DashboardV2Config:
    IsQuestCheckEnabledForDashboardV2: true
    IsDashboardV2EnabledByQuest: true
    SectionsConfig:
      CardSectionConfig:
        ShowTapnPaySettingOnHome:
          IsEnableOnAndroid: true
          MinAndroidVersion: 395
          IsEnableOnIos: true
          MinIosVersion: 2495
      ShortcutsSectionConfig:
        PlanTravelBudgetUrl: "https://web.staging.pointz.in/travel-budget"

Comms:
  NotificationsPageSize: 3

Screening:
  EnableScreenerV2: false
  EmpVerificationCheckStatusPollIntervalInSecs: 5
  CheckCreditReportAvailabilityStatusPollIntervalInSecs: 5
  CheckCreditReportVerificationStatusPollIntervalInSecs: 5

Referrals:
  LandingPageVersion: 2 # referrals-v1
  IsReferralsViaFiniteCodeAllowed: true
  MinAndroidVersionSupportingNewHomeReferralsWidget: 195
  MinIosVersionSupportingNewHomeReferralsWidget: 210
  MinIosVersionSupportingCtasFixForHomeV1EntryPoint: 211
  LandingScreenPromotionalBanner:
    BgColor: "#4F71AB"
    Cta:
      BgColor: "#00B899"
      ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
      IsEnabled: false
      IsVisible: false
      Text: ""
      TextColor: "#FFFFFF"
    ExpandedStateCta:
      BgColor: ""
      ImageUrl: ""
      IsEnabled: false
      IsVisible: true
      Text: ""
      TextColor: ""
    ExpandedStateDesc: "Earn ₹300 to ₹3000 for each referral"
    ExpandedStateDescColor: "#FFFFFF"
    ExpandedStateHeading1: ""
    ExpandedStateHeading2: "Reward T&Cs"
    ExpandedStateIconUrl: "https://epifi-icons.pointz.in/referrals/promo_banner_icon.png"
    ExpandedStateInfos1: "You need to maintain an average balance of ₹1000 or more across your Savings Account and Deposits for at least 10 days to unlock referrals\nOnce you unlock referrals, share your unique referral code with your friends\nWhen a friend opens an account on Fi and adds at least ₹3000 in their savings account you get a reward\nYou will win assured cash reward of up to ₹3000 and your friend earns at least ₹100 in cash rewards. You will receive any amount between ₹300 to ₹3000 per successful referral"
    ExpandedStateInfos2: "To qualify you have to maintain an average balance of ₹1000 or more across your Savings Account and Deposits for at least 10 days\nYour friend has to use your unique referral code when opening their account\nYou will get a reward only when your friend adds at least ₹3000 to their savings account within 7 days of opening an account\nA maximum of 40 referral rewards can be earned by you in a financial year\nThis offer is valid till 30 Nov'22\nReward amount is determined based on the reward offer active when your referee creates a Fi account and not according to the offer active when you shared your referral code\nReward programs can be terminated without prior notice at the Company's discretion\nPlease also note that users of our Platform residing in the State of Tamil Nadu are not eligible to participate in specific rewards/offers as per the applicable law in Tamil Nadu, thus users residing in Tamil Nadu are requested not to participate in offers relating to cash backs etc"
    ExpandedStateTitle: "Offer Details"
    ExpandedStateTitleColor: "#FFFFFF"
    ImageUrl: "https://epifi-icons.pointz.in/referrals/1L-people-img.png"
    ShowBanner: true
    Title: "Referral Rush : Activated 🔑\n\nRefer more & unlock ₹500 per referral \nHurry! Offer ends on 6 Aug"
    TitleColor: "#FFFFFF"
  InfoDuringOnboarding:
    OfferCodes:
      - CODE_1:
          IsEnabled: true
          Icon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/FI200-offer-icon.png"
          DisplayCode: "FI200"
          UnderlyingFiniteCode: "VNJT9V27WJ"
          Offer: "Get up to ₹200 cashback"
          Desc: "When you add money to your account"
          CtaText: "Apply now"
          CodeAppliedPopupDetails:
            Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
            Title: "Yay! “FI200” applied"
            Subtitle: "You get up to ₹200 cashback"
            Desc: "Sign up & add funds to your Savings Account to claim this offer"
            CanDismiss: true
          Identifier: "OnboardingOfferCode1"
      - CODE_2:
          IsEnabled: true
          Icon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/FiCoins-offer-icon.png"
          DisplayCode: "FI2500"
          UnderlyingFiniteCode: "VNJT9V27WJ" # todo: change to some other code
          Offer: "Get flat 2,500 Fi-Coins"
          Desc: "When you complete 3 UPI payments using Fi"
          CtaText: "Apply now"
          CodeAppliedPopupDetails:
            Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
            Title: "Yay! “FI2500” applied"
            Subtitle: "You get flat 2,500 Fi-Coins"
            Desc: "Sign up & complete 3 UPI payments using Fi to claim this offer"
            CanDismiss: true
          Identifier: "OnboardingOfferCode2"
    ReferralCodeAppliedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
      Title: "It’s a great start!"
      Subtitle: "You get cashback up to ₹160"
      Desc: "Sign up and make P2M transactions to claim rewards"
      CanDismiss: false
    Fi200ReferralCodeAppliedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
      Title: "Yay! Code applied"
      Subtitle: "You'll get up to ₹200 cashback"
      Desc: "Sign up & add funds to your Savings Account to claim this offer"
      CanDismiss: false
    ReferralCodeClaimFailedPopup:
      MinAndroidVersion: 1
      MinIOSVersion: 1
    MaxReferralCodeClaimAttempts: 100
  FeatureRestrictionParams:
    AgeRestriction:
      Enable: false
      AgeLowerBound: 30

ReferralsV1:
  MinAndroidVersionSupportingInviteContactsSection: 227
  MinIosVersionSupportingInviteContactsSection: 213
  MinAndroidVersionForImplicitCopyCodeButton: 236
  MinIosVersionForImplicitCopyCodeButton: 99999
  RefereeStageRemindScripts:
    - "NEEDS_TO_SIGN_UP": "Hi, I remember recommending Fi app to you. Did you try it?\n\nIf not, I highly recommend giving it a shot.\n\nI've been using it myself to save, invest, and track my money regularly, and it's been a game changer. And, they are also giving ₹100 joining bonus for a limited time."
    - "NEEDS_TO_ADD_FUNDS": "Hey, Just wanted to check in and see how you're liking the Fi app so far. I hope you're finding it helpful!\n\nBy the way, if you haven't already, don't forget to add at least ₹3,000 to your account so that you can claim your joining bonus. It’s a pretty sweet deal! Thank me later!"
    - "NEEDS_TO_RECEIVE_SALARY": "Hey, Just wanted to check in and see how you're liking the Fi app so far. I hope you're finding it helpful!\n\nBy the way, if you haven't already, don't forget to make Fi your Salary account so that you can claim your ₹500 Amazon voucher. It’s a pretty sweet deal! Thank me later!"
  ReferralLinkGenerationInfoConf:
    LinkValidForSinceGeneration: 5m
    OnelinkTemplateId: "5jZl"
    AfChannel: "referrals-channel"
    Campaign: "referrals-campaign"
    OnelinkGenerationParams:
      - deep_link_sub5: "FINITE_CODE_PLACEHOLDER"
      - pid: "referrals-pid"
  AppUpdateHardNudgeConf:
    ShowNudgeTillAppVersionAndroid: 8999
    ShowNudgeTillAppVersionIos: 8999
    UserBucketStart: 0
    UserBucketEnd: 99
  AppUpdateSoftNudgeConf:
    ShowNudgeTillAppVersionAndroid: 8999
    ShowNudgeTillAppVersionIos: 8999
    UserBucketStart: 0
    UserBucketEnd: 99
  StackedRewards:
    StartDateTime: "2023-08-10T15:04:05+05:30"
    EndDateTime: "2033-08-10T15:04:05+05:30"
    ShowReferralHistoryEarningSummary: true
    RefereeSignupActionExpiryDuration: 15m

SalaryProgram:
  HealthInsuranceOnsurityPolicyFlowsConfig:
    DisableFeature: false
    MinAndroidVersion: 370
    MinIOSVersion: 2350
  LandingPageCommsInfoSectionFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 100
    MinIOSVersion: 100
  LandingPageQuickLinkTilesV1FeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 100
    MinIOSVersion: 100
  SalaryLiteConfig:
    MinReqDurationSinceLastActivationForBenefitsExpiryComms: 5m
    IsEnabledForInternalActiveUsers: true
    IsSalaryLiteMandateSetupDropOffFeedbackFlowEnabled: true
    IsSalaryLiteEnachDropOffFeedbackFlowEnabled: true
  SalaryWinningSectionPrioritizedMonthlyIntervals:
    StartDate: 6
    EndDate: 23

  HomeCard:
    ShowCard: true
    HeaderTitle: "Fi Salary Benefits"
    BenefitsInactiveTitle: "Get flat 10% of your UPI spends as Fi-Coins"
    RegCompleteBenefitsInactiveTitle: "Get flat 10% of your UPI spends as Fi-Coins"
    BenefitsActiveTitle: "Get flat 10% of your UPI spends as Fi-Coins"
    BenefitsInactiveCta:
      BgColor: "#7FBECE"
      IsVisible: true
      Text: "Get Salary Account"
      TextColor: "#FFFFFF"
    RegCompleteBenefitsInactiveCta:
      BgColor: "#7FBECE"
      IsVisible: true
      Text: "Upgrade Now"
      TextColor: "#FFFFFF"
    BenefitsActiveCta:
      BgColor: "#7FBECE"
      IsVisible: true
      Text: "View All Benefits"
      TextColor: "#FFFFFF"
    BenefitsInactiveTag:
      BgColor: "#DEEEF2"
      Tag: ""
      TextColor: "#4E9199"
    RegCompleteBenefitsInactiveTag:
      BgColor: "#DEEEF2"
      Tag: ""
      TextColor: "#4E9199"
    BenefitsActiveTag:
      BgColor: "#DEEEF2"
      Tag: ""
      TextColor: "#4E9199"
    BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/shopping-bag.png"
    RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/shopping-bag.png"
    BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/shopping-bag.png"
    BenefitsInactiveBgColor: "#C0DAE0"
    RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
    BenefitsActiveBgColor: "#C0DAE0"
  HomeCardsTimeBound:
    Custom1:
      ActiveFrom: "2022-11-03T00:00:00+05:30"
      ActiveTill: "2022-11-09T23:59:59+05:30"
      ShowCard: true
      HeaderTitle: "Fi Salary Benefits"
      BenefitsInactiveTitle: "Redeem ₹200 Amazon voucher using your bonus Fi-Coins"
      RegCompleteBenefitsInactiveTitle: "Redeem ₹200 Amazon voucher using your bonus Fi-Coins"
      BenefitsActiveTitle: "Earn more rewards when your colleagues open a Fi Salary Account"
      BenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Email your HR"
        TextColor: "#FFFFFF"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "View All Benefits"
        TextColor: "#FFFFFF"
      BenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsActiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
      BenefitsInactiveBgColor: "#C0DAE0"
      RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
      BenefitsActiveBgColor: "#C0DAE0"
    Custom2:
      ActiveFrom: "2022-10-27T00:00:00+05:30"
      ActiveTill: "2022-10-31T23:59:59+05:30"
      ShowCard: true
      HeaderTitle: "Fi Salary Benefits"
      BenefitsInactiveTitle: "Employees from over 1000 companies trust Fi"
      RegCompleteBenefitsInactiveTitle: "Employees from over 1000 companies trust Fi with their salaries"
      BenefitsActiveTitle: "15% cashback on DMart, Hotstar & more with your debit card"
      BenefitsInactiveCta:
        BgColor: "#9287BD"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#9287BD"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "View Benefits"
        TextColor: "#FFFFFF"
      BenefitsInactiveTag:
        BgColor: "#CDC6E8"
        Tag: ""
        TextColor: "#6F62A4"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#CDC6E8"
        Tag: ""
        TextColor: "#6F62A4"
      BenefitsActiveTag:
        BgColor: "#CDC6E8"
        Tag: "LIMITED TIME"
        TextColor: "#6F62A4"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner_checkbox"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner_checkbox"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/curated_vouchers.png"
      BenefitsInactiveBgColor: "#C0B7E1"
      RegCompleteBenefitsInactiveBgColor: "#C0B7E1"
      BenefitsActiveBgColor: "#C0B7E1"
  HomeCardsB2BTimeBound:
    Custom1:
      ActiveFrom: "2022-11-03T00:00:00+05:30"
      ActiveTill: "2022-11-09T23:59:59+05:30"
      ShowCard: true
      HeaderTitle: "Fi Salary Benefits"
      BenefitsInactiveTitle: "Redeem ₹200 Amazon voucher using your bonus Fi-Coins"
      RegCompleteBenefitsInactiveTitle: "Redeem ₹200 Amazon voucher using your bonus Fi-Coins"
      BenefitsActiveTitle: "Win assured cash rewards from ₹200 to ₹250,000"
      BenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Email your HR"
        TextColor: "#FFFFFF"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Play with Fi-Coins"
        TextColor: "#FFFFFF"
      BenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsActiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
      BenefitsInactiveBgColor: "#C0DAE0"
      RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
      BenefitsActiveBgColor: "#C0DAE0"
    Custom2:
      ActiveFrom: "2022-10-27T00:00:00+05:30"
      ActiveTill: "2022-10-31T23:59:59+05:30"
      ShowCard: true
      HeaderTitle: "Fi Salary Benefits"
      BenefitsInactiveTitle: "Employees from over 1000 companies trust Fi"
      RegCompleteBenefitsInactiveTitle: "Employees from over 1000 companies trust Fi with their salaries"
      BenefitsActiveTitle: "15% cashback on DMart, Hotstar & more with your debit card"
      BenefitsInactiveCta:
        BgColor: "#9287BD"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#9287BD"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "View Benefits"
        TextColor: "#FFFFFF"
      BenefitsInactiveTag:
        BgColor: "#CDC6E8"
        Tag: ""
        TextColor: "#6F62A4"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#CDC6E8"
        Tag: ""
        TextColor: "#6F62A4"
      BenefitsActiveTag:
        BgColor: "#CDC6E8"
        Tag: "LIMITED TIME"
        TextColor: "#6F62A4"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner_checkbox"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner_checkbox"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/curated_vouchers.png"
      BenefitsInactiveBgColor: "#C0B7E1"
      RegCompleteBenefitsInactiveBgColor: "#C0B7E1"
      BenefitsActiveBgColor: "#C0B7E1"
  EntryPointSectionInfo:
    BenefitsActivePromoBannerTileInfo:
      IsVisible: true
      Title: "Get flat 10% of salary as Fi-Coins"
      TitleColor: "#313234"
      BgColor: "#C0DAE0"
      ImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      Tag:
        BgColor: "#CDC6E8"
        Tag: ""
        TextColor: "#6F62A4"
      Cta:
        Deeplink:
          Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
    RegCompletedBenefitsInActivePromoBannerTileInfo:
      IsVisible: true
      Title: "Get flat 10% of salary as Fi-Coins"
      TitleColor: "#313234"
      BgColor: "#C0DAE0"
      ImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      Tag:
        BgColor: ""
        Tag: ""
        TextColor: ""
      Cta:
        Deeplink:
          Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
    RegNotCompletedPromoBannerTileInfo:
      IsVisible: true
      Title: "Get flat 10% of salary as Fi-Coins"
      TitleColor: "#313234"
      BgColor: "#C0DAE0"
      ImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      Tag:
        BgColor: ""
        Tag: ""
        TextColor: ""
      Cta:
        Deeplink:
          Screen: "SALARY_PROGRAM_INTRO_SCREEN"
  EntryPointSectionInfoTimeBound:
    Custom1:
      ActiveFrom: "2023-01-06T00:00:00+05:30"
      ActiveTill: "2023-01-10T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-referral-speaker.png"
        IsVisible: true
        Tag:
          BgColor: "#CDC6E8"
          Tag: ""
          TextColor: "#6F62A4"
        Title: "Want 5X rewards? Invite your colleagues now!"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
        IsVisible: true
        Tag:
          BgColor: "#9287BD"
          Tag: "LIMITED TIME"
          TextColor: "#CDC6E8"
        Title: "Limited offer: Get ₹1,000 Amazon gift card"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#FFD6BF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Group+*********.png"
        IsVisible: true
        Title: "{daysLeftForCampaignCompletion} days left to win a ₹1000 gift"
        TitleColor: "#A7583F"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
        IsVisible: true
        Tag:
          BgColor: "#9287BD"
          Tag: "LIMITED TIME"
          TextColor: "#CDC6E8"
        Title: "Limited offer: Get ₹1,000 Amazon gift card"
        TitleColor: "#383838"
      RegNotCompletedStatusTileInfo:
        BgColor: "#FFD6BF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Group+*********.png"
        IsVisible: false
        Title: "{daysLeftForCampaignCompletion} days left to win a ₹1000 gift"
        TitleColor: "#A7583F"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
    Custom2:
      ActiveFrom: "2023-01-11T00:00:00+05:30"
      ActiveTill: "2023-01-15T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "OFFERS_LANDING_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/curated_vouchers.png"
        IsVisible: true
        Tag:
          BgColor: "#7FBECE"
          Tag: ""
          TextColor: "#DEEEF2"
        Title: "New Products added: Redeem your Fi-Coins now"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
        IsVisible: true
        Tag:
          BgColor: "#7FBECE"
          Tag: "LIMITED TIME"
          TextColor: "#DEEEF2"
        Title: "Last few days left: Get ₹1,000 Amazon gift card"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#F4E7BF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/calendar_yellow-bg+(1).png"
        IsVisible: true
        Title: "{daysLeftForCampaignCompletion} days left to win a ₹1000 gift"
        TitleColor: "#AC7C44"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
        IsVisible: true
        Tag:
          BgColor: "#7FBECE"
          Tag: "LIMITED TIME"
          TextColor: "#DEEEF2"
        Title: "Last few days left: Get ₹1,000 Amazon gift card"
        TitleColor: "#383838"
      RegNotCompletedStatusTileInfo:
        BgColor: "#F4E7BF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/calendar_yellow-bg+(1).png"
        IsVisible: false
        Title: "{daysLeftForCampaignCompletion} days left to win a ₹1000 gift"
        TitleColor: "#AC7C44"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
    Custom3:
      ActiveFrom: "2023-01-16T00:00:00+05:30"
      ActiveTill: "2023-01-19T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#C0B7E0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png"
        IsVisible: true
        Tag:
          BgColor: "#87BA6B"
          Tag: ""
          TextColor: "#D9F2CC"
        Title: "Launching Soon: Free 3X Health insurance top-up"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0B7E0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png"
        IsVisible: true
        Tag:
          BgColor: "#87BA6B"
          Tag: ""
          TextColor: "#D9F2CC"
        Title: "Launching Soon: Free 3X Health insurance top-up"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#F4E7BF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png"
        IsVisible: true
        Title: "Your salary has not arrived yet"
        TitleColor: "#AC7C44"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0B7E0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png"
        IsVisible: true
        Tag:
          BgColor: "#87BA6B"
          Tag: ""
          TextColor: "#D9F2CC"
        Title: "Launching Soon: Free 3X Health insurance top-up"
        TitleColor: "#383838"
      RegNotCompletedStatusTileInfo:
        BgColor: "#F4E7BF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png"
        IsVisible: false
        Title: "Your salary has not arrived yet"
        TitleColor: "#AC7C44"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
    Custom4:
      ActiveFrom: "2023-01-20T00:00:00+05:30"
      ActiveTill: "2023-01-24T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-referral-speaker.png"
        IsVisible: true
        Tag:
          BgColor: "#CDC6E8"
          Tag: ""
          TextColor: "#6F62A4"
        Title: "Get your Colleagues, take 50% higher rewards"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
        IsVisible: true
        Tag:
          BgColor: "#9287BD"
          Tag: ""
          TextColor: "#CDC6E8"
        Title: "Get rewards upto ₹10,000 this year"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#FAD0D0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning_triangle_pink.png"
        IsVisible: true
        Title: "Your salary has not arrived yet"
        TitleColor: "#9E5A57"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
        IsVisible: true
        Tag:
          BgColor: "#9287BD"
          Tag: ""
          TextColor: "#CDC6E8"
        Title: "Get rewards upto ₹10,000 this year"
        TitleColor: "#383838"
      RegNotCompletedStatusTileInfo:
        BgColor: "#FAD0D0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning_triangle_pink.png"
        IsVisible: false
        Title: "Your salary has not arrived yet"
        TitleColor: "#9E5A57"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
    Custom5:
      ActiveFrom: "2023-01-25T00:00:00+05:30"
      ActiveTill: "2023-01-30T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        IsVisible: true
        Tag:
          BgColor: "#CDC6E8"
          Tag: ""
          TextColor: "#6F62A4"
        Title: "Get 5X rewards when your colleagues open salary accounts"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        IsVisible: true
        Tag:
          BgColor: "#9287BD"
          Tag: ""
          TextColor: "#CDC6E8"
        Title: "Get a 10% bonus every month"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#F4E7BF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png"
        IsVisible: true
        Title: "Your salary has not arrived yet"
        TitleColor: "#AC7C44"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        IsVisible: true
        Tag:
          BgColor: "#9287BD"
          Tag: ""
          TextColor: "#CDC6E8"
        Title: "Get a 10% bonus every month"
        TitleColor: "#383838"
      RegNotCompletedStatusTileInfo:
        BgColor: "#F4E7BF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png"
        IsVisible: false
        Title: "Your salary has not arrived yet"
        TitleColor: "#AC7C44"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
  EntryPointSectionInfoB2BTimeBound:
    Custom1:
      ActiveFrom: "2023-01-06T00:00:00+05:30"
      ActiveTill: "2023-01-10T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#EAD8A3"
        Cta:
          Deeplink:
            Screen: "OFFERS_LANDING_SCREEN"
            offerCatalogScreenOptions:
              displayFirstOfferIds:
                - 4769d6b1-35cf-4ca1-9a1f-6cc4bb83e903
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Gift-Box-1.png"
        IsVisible: true
        Tag:
          BgColor: "#CDC6E8"
          Tag: ""
          TextColor: "#6F62A4"
        Title: "Get a gift from Daily Objects with your Fi-Coins"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/curated_vouchers.png"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Want 4X rewards? Share your account details with HR"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#FAD0D0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning_triangle_pink.png"
        IsVisible: true
        Title: "Your salary has not arrived yet"
        TitleColor: "#9E5A57"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/curated_vouchers.png"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Want 4X rewards? Share your account details with HR"
        TitleColor: "#383838"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
    Custom2:
      ActiveFrom: "2023-01-11T00:00:00+05:30"
      ActiveTill: "2023-01-15T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "OFFERS_LANDING_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/curated_vouchers.png"
        IsVisible: true
        Tag:
          BgColor: "#CDC6E8"
          Tag: ""
          TextColor: "#6F62A4"
        Title: "New products added: Redeem your Fi-Coins now"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "OFFERS_LANDING_SCREEN"
            offerCatalogScreenOptions:
              displayFirstOfferIds:
                - 578f4dfe-6db8-47f8-af11-8726cd4b49e2
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Redeem ₹200 Amazon voucher using your bonus Fi-Coins"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#F4E7BF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png"
        IsVisible: true
        Title: "Your salary has not arrived yet"
        TitleColor: "#AC7C44"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "OFFERS_LANDING_SCREEN"
            offerCatalogScreenOptions:
              displayFirstOfferIds:
                - 578f4dfe-6db8-47f8-af11-8726cd4b49e2
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Redeem ₹200 Amazon voucher using your bonus Fi-Coins"
        TitleColor: "#383838"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
    Custom3:
      ActiveFrom: "2023-01-16T00:00:00+05:30"
      ActiveTill: "2023-01-19T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#C0B7E0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png"
        IsVisible: true
        Tag:
          BgColor: "#CDC6E8"
          Tag: ""
          TextColor: "#6F62A4"
        Title: "Launching Soon: Free 3X Health insurance top-up"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0B7E0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Launching Soon: Free 3X Health insurance top-up"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#FAD0D0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning_triangle_pink.png"
        IsVisible: true
        Title: "Your salary has not arrived yet"
        TitleColor: "#9E5A57"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0B7E0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Launching Soon: Free 3X Health insurance top-up"
        TitleColor: "#383838"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
    Custom4:
      ActiveFrom: "2023-01-20T00:00:00+05:30"
      ActiveTill: "2023-01-24T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#EFC0C0"
        Cta:
          Deeplink:
            Screen: "OFFERS_LANDING_SCREEN"
            offerCatalogScreenOptions:
              displayFirstOfferIds:
                - 6968ff01-9a46-4137-b29a-8ca77c4d8c8f
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner/Perfora_tooth.png"
        IsVisible: true
        Tag:
          BgColor: "#CDC6E8"
          Tag: ""
          TextColor: "#6F62A4"
        Title: "Get a perfora electric toothbrush"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Get rewards upto ₹10,000 this year"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#FAD0D0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning_triangle_pink.png"
        IsVisible: true
        Title: "Your salary has not arrived yet"
        TitleColor: "#9E5A57"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0DAE0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Get rewards upto ₹10,000 this year"
        TitleColor: "#383838"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
    Custom5:
      ActiveFrom: "2023-01-25T00:00:00+05:30"
      ActiveTill: "2023-01-30T23:59:59+05:30"
      IsVisible: true
      BenefitsActivePromoBannerTileInfo:
        BgColor: "#EAD8A3"
        Cta:
          Deeplink:
            Screen: "OFFERS_LANDING_SCREEN"
            offerCatalogScreenOptions:
              displayFirstOfferIds:
                - 1f804ffa-766d-4937-a112-03347b03242d
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Gift-Box-1.png"
        IsVisible: true
        Tag:
          BgColor: "#CDC6E8"
          Tag: ""
          TextColor: "#6F62A4"
        Title: "Redeem a GIVA deer heart silver neclace"
        TitleColor: "#383838"
      BenefitsActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Get a 10% bonus every month"
        TitleColor: "#383838"
      RegCompletedBenefitsInActiveStatusTileInfo:
        BgColor: "#FAD0D0"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/warning_triangle_pink.png"
        IsVisible: true
        Title: "Your salary has not arrived yet"
        TitleColor: "#9E5A57"
      RegCompletedBenefitsInActiveSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "View All"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
      RegNotCompletedPromoBannerTileInfo:
        BgColor: "#C0B7E1"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        IsVisible: true
        Tag:
          BgColor: ""
          Tag: ""
          TextColor: ""
        Title: "Get a 10% bonus every month"
        TitleColor: "#383838"
      RegNotCompletedSummaryTileInfo:
        BgColor: "#FFFFFF"
        Cta:
          Deeplink:
            Screen: "SALARY_PROGRAM_INTRO_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-honey.png"
          Text: "Try it"
          TextColor: "#D2AC3D" #Honey
          BgColor: "#FFF8CE"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/star.png"
        IsVisible: true
        Title: "Salary\nBenefits"
        TitleColor: "#2A496F"
  B2BEmployersMap:
    5a7ac603-69e9-4876-88db-0e288fbe8c49: "Epifi Tech"
  SalaryReferralEmployersBlockedMap: [ ]
  InviteColleaguesInvitationMsg: "Hey there,\nI have been living a super rewarding life with the Fi Salary Program! Sign-up and you can get benefits up to ₹30,000 every year including flat 2%% cashback on all spends (including UPI), 2x health insurance top-up and vouchers from Myntra, Swiggy, Uber, etc.\nThere's more, you also get a ₹500 Amazon voucher on joining!\nHere's my referral code %s. Download the Fi app and gear up for the rewards!\nhttps://invite.fi.money/ZjEQ/salaryaccount"
  ShareDetailsOnMailV1FeatureConfig:
    DisableFeature: false
  SalaryBenefitsLandingPageBannerSectionInfo:
    IsVisible: true
    MinAndroidVersionSupportingBannerSection: 10
    MinIOSVersionSupportingBannerSection: 10
    Title:
      Content: "Earn higher rewards when your colleagues open a salary account with us"
      FontColor: "#FFFFFF"
    BgColor: "#383838" #charcoal
    ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-referral-speaker.png"
    Cta:
      IsVisible: true
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-white.png"
      BgColor: "#646464"
      Deeplink:
        Screen: "SALARY_PROGRAM_REFERRALS_LANDING_SCREEN"
  SalaryBenefitsLandingPageQuickLinksSection:
    IsVisible: true
    MinAndroidVersionSupportingQuickLinksSection: 100
    MinIOSVersionSupportingQuickLinksSection: 156
    QuickLinksTiles:
      Link1:
        BgColor: "#FAD0D0"
        CTA:
          BgColor: "#EFC0C0"
          Deeplink:
            Screen: "STORY_SCREEN"
            StoryScreenOptions:
              StoryId: "e24825d2-740b-4fce-9aba-6a1ecc47d0ef"
              StoryTitle: "Fi-Coins Intro"
              StoryUrl: "https://stories.fi.money/stories/fi-coins-intro"
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          IsVisible: true
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        TileRank: 1
        Title: "Get a free chequebook"
        TitleColor: "#333333"
      Link2:
        BgColor: "#D9F2CC"
        CTA:
          BgColor: "#C5E9B2"
          Deeplink:
            Screen: "STATEMENT_REQUEST_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          IsVisible: true
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        TileRank: 2
        Title: "Download your bank statement"
        TitleColor: "#333333"
      Link3:
        BgColor: "#FAD0D0"
        CTA:
          BgColor: "#EFC0C0"
          Deeplink:
            Screen: "STORY_SCREEN"
            StoryScreenOptions:
              StoryId: "e24825d2-740b-4fce-9aba-6a1ecc47d0ef"
              StoryTitle: "Fi-Coins Intro"
              StoryUrl: "https://stories.fi.money/stories/fi-coins-intro"
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          IsVisible: true
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        TileRank: 3
        Title: "Get a free chequebook - 2"
        TitleColor: "#333333"
      Link4:
        BgColor: "#D9F2CC"
        CTA:
          BgColor: "#C5E9B2"
          Deeplink:
            Screen: "STATEMENT_REQUEST_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          IsVisible: true
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        TileRank: 4
        Title: "Download your bank statement - 2"
        TitleColor: "#333333"
  HelpSectionInfo:
    IsVisible: true
    MinAndroidVersionSupportingHelpSection: 100
    MinIOSVersionSupportingHelpSection: 156
  SalaryProgramFAQsCategoryId: "***********"
  LandingPageTopSectionCommsInfoMap:
    ReferralB2CPromotions:
      IsEnabled: true
      OnlyForSalaryProgramActiveUsers: true
      OnlyForB2BEmployers: false
      OnlyForB2CEmployers: true
      MinAndroidAppVersionSupported: 1000
      MinIosAppVersionSupported: 162
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/fi-coins-salary-top-scn.png"
      Title: "Upto 50% of your salary in Fi-Coins"
      TitleColor: "#FFFFFF"
      Desc: "Multiply salary account benefits, when colleagues from your company join"
      DescColor: "#CED2D6"
      PrimaryCta:
        Text: "Find out how"
        TextColor: "#00B899"
        BgColor: "#383838"
        IsVisible: true
        Deeplink:
          Screen: "SALARY_PROGRAM_REFERRALS_LANDING_SCREEN"
    ReferralB2CPromotionsUpdateApp:
      IsEnabled: true
      OnlyForSalaryProgramActiveUsers: true
      OnlyForB2BEmployers: false
      OnlyForB2CEmployers: true
      MaxAndroidAppVersionAllowed: 0
      MaxIosAppVersionAllowed: 161
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/fi-coins-salary-top-scn.png"
      Title: "Upto 50% of your salary in Fi-Coins"
      TitleColor: "#FFFFFF"
      Desc: "Multiply salary account benefits, when colleagues from your company join"
      DescColor: "#CED2D6"
      PrimaryCta:
        Text: "Update app to learn more"
        TextColor: "#00B899"
        BgColor: "#383838"
        IsVisible: true
        Deeplink:
          Screen: "UPDATE_APP_SCREEN"

  EarlySalaryBenefitConfig:
    EarlySalaryBenefitRewardOfferId: "05613a9f-0015-4a44-9c35-b080f50f9318"
  HealthInsuranceRewardOfferIdToPolicyConfigMap:
    d8ea6922-3643-4e06-a512-06703bc92838:
      HealthInsurancePolicyFAQsDocS3Path: "healthinsurance/docs/healthInsuranceFAQs.pdf"
      HealthInsurancePolicyClaimProcessDocS3Path: "healthinsurance/docs/claimsProcessDoc.pdf"
      HealthInsuranceCashlessHospitalListDocS3Path: "healthinsurance/docs/cashlessHospitalsDoc.pdf"
      HealthInsuranceTncsDocS3Path: "healthinsurance/docs/healthInsuranceTncs.pdf"
      HealthInsurancePolicyType: "SUPER_TOP_UP_INSURANCE"
    64502d97-6638-45b7-9f52-3254c8d068ce:
      HealthInsurancePolicyFAQsDocS3Path: "healthinsurance/docs/healthInsuranceFAQs.pdf"
      HealthInsurancePolicyClaimProcessDocS3Path: "healthinsurance/docs/claimsProcessDoc.pdf"
      HealthInsuranceCashlessHospitalListDocS3Path: "healthinsurance/docs/cashlessHospitalsDoc.pdf"
      HealthInsuranceTncsDocS3Path: "healthinsurance/docs/healthInsuranceTncs.pdf"
      HealthInsurancePolicyType: "BASE_HEALTH_INSURANCE"
    opd-1:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_2A2C"
    opd-2:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_2A"
    opd-3:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_1A"
  SalaryAccountBenefitsSectionTopBannerInfo:
    Title: "Calculate the total annual value of your own salary benefits here"
    TitleFontColor: "#FFFFFF"
    BgColor: "#383838"
    LeftIconUrl: ""
    RightIconUrl: "https://epifi-icons.pointz.in/salaryprogram/right-arrow.png"
    IsVisible: true
  MinReqDurationSinceLastActivationForBenefitsExpiryComms: 5m # 5 minutes for testing
  MinAndroidAppVersionSupportingBenefitsSectionV1: 10000
  MinIosAppVersionSupportingBenefitsSectionV1: 10000
  MinAndroidAppVersionHandlingRpcResponseErrorView: 200

IPInterceptorParams:
  EnableIPInterceptor: true
  BlockedIPAddresses: [ "**************" ]

Signup:
  ThemeBasedInfoAckScreen:
    MinAndroidVersion: 10
    MinIOSVersion: 10
  EnableKnowMoreAccountClosureFlowIOS:
    MinIOSVersion: 2000
    MinAndroidVersion: 1
    FallbackToEnableFeature: true
  BlockOnboardingDueToUnlinkedPANAndAadhaar: true
  BabaPhoneNumberHashes:
    2af8052055013fdbdd89b0e2a967e5fa8b93542b: true # aditya bhardwaj

ConnectedAccount:
  DisableWealthOnboardingMinAndroidVersion: 147 #min version on android on which wonb is disabled to use connected account
  DisableWealthOnboardingMinIosVersion: 69 #min version on ios on which wonb is disabled to use connected account
  FinvuAccountDiscoveryTimeoutSeconds: 12
  OnemoneyAccountDiscoveryTimeoutSeconds: 12
  FinvuDisconnectUrl: "https://sherlock.staging.pointz.in/developer/actions" # need to run manual dev action to revoke
  HomeBannerCutoffDays: 10000
  IsConnectedAccountEnabled: true
  MinVersionCheckForNewBanks: 158
  HomeEntryPoint:
    Enabled: true
    Text: "Search across all your other account spends"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/home_entry_1.png"
  SearchEntryPoint:
    Enabled: true
    Text: "Connect Account"
    LogoUrl: "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
  AnalyserEntryPoint:
    Enabled: true
    Text: "Connect\naccounts"
    LogoUrl: "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
  ProfileEntryPoint:
    Enabled: true
    Text: "Track all your other accounts accurately & safely in one place"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/home_entry_1.png"
  AccountManagerEntryPoint:
    Enabled: true
    Text: "Connect more accounts"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/ca_entry_plus_circle_1.png"
  AllTransactionsEntryPoint:
    Enabled: true
    Text: "Connect Accounts"
    LogoUrl: "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
  SearchBannerEntryPoint:
    Enabled: true
    Text: "Track all your other accounts accurately & safely in one place"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/home_entry_1.png"
  V2FlowParams:
    UseV2Flow: true
    AccountDiscoveryTitleText: "Select the accounts you want to link & track on Fi"
    AccountDiscoverySubtitleText: "We found these accounts linked to %s"
    AccountDiscoverySubtitleSearchingText: "One moment! We're searching for your accounts linked to %s."
    AccountDiscoveryCtaText: "Continue"
    MinVersionAndroid: 1
    MinVersionIos: 10000
    AccountDiscoveryLoadingText: "Searching for your accounts"
    RegisterOtherAccountsText: "Can't see your accounts?"
  MinimumDurationRequiredToPermitDisconnect: "72h"
  FinvuAsyncDiscovery:
    AndroidMinVersion: 40000
    IosMinVersion: 40000
  EnableConsentRenewalSegmentNonProdTest: true
  EnableAccountManagerConsentRenewalEntryPoint: true

Tiering:
  NewTiersConstraints:
    AaSalary:
      IsEnableOnAndroid: true
      MinAndroidVersion: 1
      IsEnableOnIos: true
      MinIosVersion: 1
  HeroBenefits:
    MinVersionAndroid: 1
    MinVersionIos: 1
  TierIntroduction:
    ReleaseConstraints:
      MinVersionAndroid: 1
      MinVersionIos: 1
    LaunchAnimationInactivitySeconds: 0
  OnbAddFundsSuccessVersionConstraints:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: true
    MinIosVersion: 1
  IsTierAllPlansDropOffFeedbackFlowEnabled: true
  EnableHeroV3Component: true
  HeroBenefitsV3Constraints:
    MinVersionAndroid: 1
    MinVersionIos: 10000
  TierAllPlansV2ConfigParams:
    ShouldShowEntryBanner: true
  TierDropOffBottomSheet:
    ShouldShowUSStocks: true
    ShouldShowFixedDeposit: false
    ShouldShowAddFunds: true

ConnectedAccountUserGroupParams:
  IsConnectedAccountRestricted: false
  AllowedUserGrps:
    - 1 # INTERNAL
    - 2 # FNF
    - 7 # CONNECTED_ACCOUNT

Fittt:
  MinAndroidVersionToSupportFeaturedRulesBanner: 1000
  MinIOSVersionToSupportFeaturedRulesBanner: 117
  AutoPayCollectionBannerAllowedUserGroups:
    - 1 # INTERNAL = user belongs to epiFi

Tracing:
  Enable: true

Investment:
  MfNavChart:
    WebUrl: "https://web.staging.pointz.in/fin-charts/line-chart"
    LastUpdatedAt: ********** # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  MinAndroidVersionToSupportMINKYCCheckForPurchase: 137
  MinIOSVersionToSupportMINKYCCheckForPurchase: 169
  MinAndroidVersionToUploadPan: 137
  IsMFNAVChartEnabled: true
  MinIosVersionToUploadPan: 169
  ISKYCCheckOnPurchaseEnabledForIOS: false
  ISKYCCheckOnPurchaseEnabledForAndroid: true
  FundActivityManualInterventionSupport:
    IsEnableOnAndroid: 1
    MinAndroidVersion: 164
    IsEnableOnIos: 1
    MinIosVersion: 169
  MinAndroidVersionForNextOnboardingStep: 161
  MinIOSVersionForNextOnboardingStep: 100
  MFLandingPageCollectionIDForCuratedFunds: "CONSISTENCY_IS_KEY"
  MFLandingPageCollectionNameForCuratedFunds: "Consistency is Key"
  EnableAggregatorNotificationAlertFlag: true
  MinAndroidVersionToSupportGraph: 10000
  MinIOSVersionToSupportGraph: 285
  InvestmentHomeElementParams:
    InvestmentSegmentABExperimentParams:
      - 0:
          SegmentExpression: "IsMember('AWS_test-segment')"
          ABExperimentVariant: "DEFAULT"
          UseCase: "US_STOCKS_1"
      - 1:
          SegmentExpression: "IsMember('AWS_test-segment')"
          ABExperimentVariant: "INVESTMENT_HOME_COMPONENT_CONTROL_1"
          UseCase: "US_STOCKS_1"
  InvestmentLandingRecommendations:
    SegmentExpressionToRecommendationDetailsMap:
      - "IsMember('AWS_test-segment')":
          RecommendationID: "USSTOCK_LIVE"
          InstrumentType: 5 # 5 represents usstocks recommendation
    FallBackRecommendationDetails:
      RecommendationID: "USSTOCK_LIVE"
      InstrumentType: 5 # represent usstocks
    MFRecommendationIDToDetailsMap:
      - "STABLE_AND_ABLE_COLLECTION":
          CollectionID: "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA=="
          HeaderDisplayString: "Stable And Able"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
      - "POPULAR_IN_MF":
          FilterIDs:
            - "INDEX_FUND_TYPE"
            - "UPTO_POINT5_PER_EXPENSE"
            - "1001_TO_5000CR_FUND_SIZE"
            - "5001_TO_10KCR_FUND_SIZE"
            - "MORE_THAN_10KCR_FUND_SIZE"
          HeaderDisplayString: "Popular in Mutual funds"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    FallBackMFRecommendationDetails:
      CollectionID: "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA=="
      HeaderDisplayString: "Stable And Able"
      SubtitleString: "Based on what people invest"
      HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    USStocksRecommendationIDToDetailsMap:
      - "USSTOCK_LIVE":
          CollectionID: "COLLECTION_NAME_TOP_GAINERS_ACROSS_ALL_INDEX"
          HeaderDisplayString: "Top Gainers"
          SubtitleString: "Discover top gainers of NASDAQ & S&P500"
          HeaderImageUrl: "https://epifi-icons.pointz.in/top-gainers.png"
    FallBackUSStocksRecommendationDetails:
      CollectionID: "COLLECTION_NAME_TOP_GAINERS_ACROSS_ALL_INDEX"
      HeaderDisplayString: "Top Gainers"
      SubtitleString: "Discover top gainers of NASDAQ & S&P500"
      HeaderImageUrl: "https://epifi-icons.pointz.in/top-gainers.png"
    HybridRecommendationIDToDetailsMap:
      - "INTERNAL":
          #          TitleString: "Popular on Fi"
          #          SubtitleString: "Based on what people invest"
          #          TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
          TitleString: "Mutual Funds collections"
          SubtitleString: "Save time with expert-picked funds"
          CTAForInstrumentType: 1
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
          CollectionRecommendationCards:
            "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
              InstrumentType: 1
              Rank: 1
              Title: "SIP with ₹500"
              Subtitle: "Invest regularly with these top funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
            "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
              InstrumentType: 1
              Rank: 2
              Title: "Tax saver"
              Subtitle: "Funds to save tax under section 80C"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
            "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
              InstrumentType: 1
              Rank: 3
              Title: "Better than FD"
              Subtitle: "Returns up to 7.8% at lower risk"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
            "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
              InstrumentType: 1
              Rank: 4
              Title: "Index funds"
              Subtitle: "Invest in top Indian companies"
              ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
      - "HYBRID_1":
          #          TitleString: "Popular on Fi"
          #          SubtitleString: "Based on what people invest"
          #          TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
          TitleString: "Mutual Funds collections"
          SubtitleString: "Save time with expert-picked funds"
          CTAForInstrumentType: 1
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
          CollectionRecommendationCards:
            "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
              InstrumentType: 1
              Rank: 1
              Title: "SIP with ₹500"
              Subtitle: "Invest regularly with these top funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
            "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
              InstrumentType: 1
              Rank: 2
              Title: "Tax saver"
              Subtitle: "Funds to save tax under section 80C"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
            "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
              InstrumentType: 1
              Rank: 3
              Title: "Better than FD"
              Subtitle: "Returns up to 7.8% at lower risk"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
            "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
              InstrumentType: 1
              Rank: 4
              Title: "Index funds"
              Subtitle: "Invest in top Indian companies"
              ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
    FallBackHybridRecommendationDetails:
      #      TitleString: "Popular in Fi"
      #      SubtitleString: "Based on what people invest"
      #      TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
      TitleString: "Mutual Funds collections"
      SubtitleString: "Save time with expert-picked funds"
      CTAForInstrumentType: 1
      TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
      CollectionRecommendationCards:
        "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
          InstrumentType: 1
          Rank: 1
          Title: "SIP with ₹500"
          Subtitle: "Invest regularly with these top funds"
          ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
        "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
          InstrumentType: 1
          Rank: 2
          Title: "Tax saver"
          Subtitle: "Funds to save tax under section 80C"
          ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
        "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
          InstrumentType: 1
          Rank: 3
          Title: "Better than FD"
          Subtitle: "Returns up to 7.8% at lower risk"
          ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
        "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
          InstrumentType: 1
          Rank: 4
          Title: "Index funds"
          Subtitle: "Invest in top Indian companies"
          ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
    MinIosVersionForV2Recommendations: 0
    MinAndroidVersionForV2Recommendations: 0
    AndroidVersionForSubtitleSwapFix: 10000
    EnableMultipleRecommendation: true
    MultipleHybridRecommendationIDToDetailsMap:
      - "HYBRID_1":
          Recommendations:
            "1":
              TitleString: "Popular on Fi"
              SubtitleString: "Based on what people invest"
              TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
              USStockRecommendationCards:
                - "1":
                    Id: "USS230105jtc+1hQ0QAio048mZutSyQ=="
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "US STOCKS"
                    Rank: 9
                - "2":
                    Id: "USS5sknExOBTGORbhwWrupJuQ230522=="
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "US STOCKS"
                    Rank: 10
              MFRecommendationCards:
                - "1":
                    Id: "MF220622XboJDGq7RHG9oR0ButrRyQ=="
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "MUTUAL FUNDS"
                    Rank: 1
                - "2":
                    Id: "MF220411EeRofPZwTIusYZUOko4eig=="
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "MUTUAL FUNDS"
                    Rank: 5
              FixedDepositRecommendationCards:
                - "1":
                    Id: "FD725"
                    Tag:
                      FontColor: "#9287BD"
                      BgColor: "#CDC6E8"
                      Content: "FIXED DEPOSIT"
                    Rank: 4
                    Title: "Bumper jar - Lock safe returns"
                    Info: "15 months | 7.25% p.a. "
              SmartDepositRecommendationCards:
                - "1":
                    Id: "SD4"
                    Tag:
                      FontColor: "#87BA6B"
                      BgColor: "#D9F2CC"
                      Content: "SMART DEPOSIT"
                    Rank: 6
                    Title: "Monthly autosave in a Smart deposit"
                    Info: "₹100 only | 6.8% p.a."
              P2PRecommendationCards:
                - "SCHEME_NAME_LL_BOOSTER":
                    Tag:
                      FontColor: "#D3B250"
                      BgColor: "#F4E7BF"
                      Content: "JUMP"
                    Rank: 2
                    Title: "Booster plan - Invest up to ₹10L"
                    Info: "Limited period"
                - "SCHEME_NAME_LL_SHORT_TERM":
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "JUMP"
                    Rank: 6
                    Title: "Short term plan Start with ₹10,000"
                    Info: "Join 10k+ investors"
            "2":
              TitleString: "Mutual Funds collections"
              CTAForInstrumentType: 1
              SubtitleString: "Save time with expert-picked funds"
              TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
              CollectionRecommendationCards:
                "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
                  InstrumentType: 1
                  Rank: 1
                  Title: "SIP with ₹500"
                  Subtitle: "Invest regularly with these top funds"
                  ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
                "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
                  InstrumentType: 1
                  Rank: 2
                  Title: "Tax saver"
                  Subtitle: "Funds to save tax under section 80C"
                  ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
                "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
                  InstrumentType: 1
                  Rank: 3
                  Title: "Better than FD"
                  Subtitle: "Returns up to 7.8% at lower risk"
                  ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
                "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
                  InstrumentType: 1
                  Rank: 4
                  Title: "Index funds"
                  Subtitle: "Invest in top Indian companies"
                  ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
    FallBackMultipleHybridRecommendationDetails:
      Recommendations:
        "1":
          TitleString: "Popular on Fi"
          SubtitleString: "Based on what people invest"
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
          USStockRecommendationCards:
            - "1":
                Id: "USS230105jtc+1hQ0QAio048mZutSyQ=="
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "US STOCKS"
                Rank: 9
            - "2":
                Id: "USS5sknExOBTGORbhwWrupJuQ230522=="
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "US STOCKS"
                Rank: 10
          MFRecommendationCards:
            - "1":
                Id: "MF220622XboJDGq7RHG9oR0ButrRyQ=="
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "MUTUAL FUNDS"
                Rank: 1
            - "2":
                Id: "MF220411EeRofPZwTIusYZUOko4eig=="
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "MUTUAL FUNDS"
                Rank: 5
          FixedDepositRecommendationCards:
            - "1":
                Id: "FD725"
                Tag:
                  FontColor: "#9287BD"
                  BgColor: "#CDC6E8"
                  Content: "FIXED DEPOSIT"
                Rank: 4
                Title: "Bumper jar - Lock safe returns"
                Info: "15 months | 7.25% p.a. "
          SmartDepositRecommendationCards:
            - "1":
                Id: "SD4"
                Tag:
                  FontColor: "#87BA6B"
                  BgColor: "#D9F2CC"
                  Content: "SMART DEPOSIT"
                Rank: 6
                Title: "Monthly autosave in a Smart deposit"
                Info: "₹100 only | 6.8% p.a."
          P2PRecommendationCards:
            - "SCHEME_NAME_LL_BOOSTER":
                Tag:
                  FontColor: "#D3B250"
                  BgColor: "#F4E7BF"
                  Content: "JUMP"
                Rank: 2
                Title: "Booster plan - Invest up to ₹10L"
                Info: "Limited period"
            - "SCHEME_NAME_LL_SHORT_TERM":
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "JUMP"
                Rank: 6
                Title: "Short term plan Start with ₹10,000"
                Info: "Join 10k+ investors"
        "2":
          TitleString: "Mutual Funds collections"
          CTAForInstrumentType: 1
          SubtitleString: "Save time with expert-picked funds"
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
          CollectionRecommendationCards:
            "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
              InstrumentType: 1
              Rank: 1
              Title: "SIP with ₹500"
              Subtitle: "Invest regularly with these top funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
            "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
              InstrumentType: 1
              Rank: 2
              Title: "Tax saver"
              Subtitle: "Funds to save tax under section 80C"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
            "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
              InstrumentType: 1
              Rank: 3
              Title: "Better than FD"
              Subtitle: "Returns up to 7.8% at lower risk"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
            "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
              InstrumentType: 1
              Rank: 4
              Title: "Index funds"
              Subtitle: "Invest in top Indian companies"
              ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
  EnableInvestmentDigestTileDedupe: true
  EnableHardLockinCalculator: true

Deposit:
  Preclosure:
    ConfirmationNudge:
      FaqCategoryId: "82000180883" # currently its save category, also category id is different for prod and non prod
  MaturityAmountVisibility: # deposit's maturity amount feature flags belong here
    Global: true # if false, maturity amount will be hidden everywhere irrespective of the screen
    GlobalAllowedUserGroups: [ ] # allowed user groups
    SDCreation: true  # if false, maturity amount will be hidden in SD creation flow
    FDCreation: true # if false, maturity amount will be hidden in FD creation flow
    SDDetails: true # if false, maturity amount will be hidden in SD details screen
    FDDetails: true # if false, maturity amount will be hidden in FD details screen
    SDAddFunds: true # if false, maturity amount will be hidden in SD add funds flow
  Goals:
    GoalDetailsInDepositList:
      Enable: true
    GoalDetailsInDepositDetails:
      Enable: true
  AutoSave:
    PostCreationFlow:
      Enable: false
      GlobalAllowedUserGroups: [ ] # allowed user groups
    DetailsFlow:
      Enable: true
      GlobalAllowedUserGroups: [ ] # allowed user groups
      EnableAutoSaveSuggestions: true
      EnableAutoSaveRuleList: true
    PreCreationFlow:
      Enable: true
      AllowedUserGroups: [ ] # allowed user groups
  Statement:
    Enable: true
    AllowedUserGroups: [ ] # allowed user groups
  TaxSaving:
    Enable: true
    MinAndroidVersion: 10
    MinIosVersion: 10
  Summary:
    MinIosVersionForAddMoneyBottomSheet: 0
    MinAndroidVersionForAddMoneyBottomSheet: 0

AnalyserParams:
  ShowAnalyser: true
  GetAnalyserGenerateDummyResponse: false
  AnalyserConfigJson: "./mappingJson/analyserConfig.json"
  AddFundsBannerConf:
    EndOfMonthDays: 10
    StartOfMonthDays: 7
  AnalyserFeedbackParams:
    ExitAnalyserFeedbackCoolOff: "1s"
    OnScreenLensFeedbackCoolOffDuration: "1s"
    AcrossAnalyserFeedbackCoolOff: "1s"
    AnalyserFeedbackRateLimit:
      TimePeriod: "1s"
      MaxFeedbackAskLimit: 100
  AnalyserReleaseConfig:
    - ANALYSER_NAME_SPEND_TOP_CATEGORIES:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 100
            IsFeatureRestricted: false
          IOSReleaseConfig:
            MinAppVersion: 100
            IsFeatureRestricted: false
        LensReleaseConfigs:
          - LENS_NAME_TOP_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 100
                  IsFeatureRestricted: false
                IOSReleaseConfig:
                  MinAppVersion: 100
                  IsFeatureRestricted: false
          - LENS_NAME_TOP_SPENDS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 1000
                  IsFeatureRestricted: true
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: true
                  AllowedUserGroups:
                    - "INTERNAL"
          - LENS_NAME_TOP_INVESTMENTS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 1000
                  IsFeatureRestricted: true
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: true
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_SPEND_TOP_MERCHANTS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 189
            IsFeatureRestricted: false
          IOSReleaseConfig:
            MinAppVersion: 275
            IsFeatureRestricted: false
        LensReleaseConfigs:
          - LENS_NAME_TOP_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 189
                  IsFeatureRestricted: false
                IOSReleaseConfig:
                  MinAppVersion: 275
                  IsFeatureRestricted: false
          - LENS_NAME_TOP_PEOPLE_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
          - LENS_NAME_TOP_MERCHANTS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_SPEND_TIME:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 1
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 1
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_INCREMENTAL_DISTRIBUTION:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_CREDIT_SCORE:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 1
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 1
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_CREDIT_SCORE_SUMMARY:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_GROWTH:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 1
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 1
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_PORTFOLIO_GROWTH:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_TOP_FUNDS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 215
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 306
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_TOP_MUTUAL_FUNDS:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 215
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 306
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_ASSET_CLASS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 215
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 306
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_MUTUAL_FUNDS_ASSET_CLASS:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 215
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 306
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_EQUITY:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 215
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 306
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_MUTUAL_FUNDS_EQUITY_MARKET_CAP:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 215
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 306
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
  AnalyserLandingPageReleaseConfig:
    - ANALYSER_LANDING_PAGE_NAME_MUTUAL_FUND:
        AndroidReleaseConfig:
          MinAppVersion: 0
          IsFeatureRestricted: false
          AllowedUserGroups:
            - "INTERNAL"
        IOSReleaseConfig:
          MinAppVersion: 0
          IsFeatureRestricted: false
          AllowedUserGroups:
            - "INTERNAL"
  AnalyserHubConfig:
    Experiments:
      - EXPERIMENT_NAME_CTA_BOTTOM_LEFT_WITHOUT_TEXT
  CreditReportParams:
    DownloadProcessExpiry: 30m
  CreditScoreAnalyserConfig:
    AutoRefreshCoolOffDurationInDays: 30
    AutoRefreshPollerTimeoutDuration: 5s
    ExperianV2InsightsConfig:
      IsEnabled: true
      ActiveFrom: "2024-04-01T00:00:00+05:30"
      ActiveTill: "2024-06-30T23:59:59+05:30"
  MfAnalyserRefreshBannerReleaseConfig:
    IsFeatureRestricted: true
    AllowedUserGroups:
      - "INTERNAL"

ABFeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_US_FUNDS_IN_INVEST_LANDING_PAGE:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          StickyPercentageConstraintConfig:
            RolloutPercentage: 100
        Buckets:
          - ONE:
              Start: 0
              End: 100
    - SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
          RolloutPercentage: 100
    - INVESTMENT_HOME_COMPONENT:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 0
            MinIOSVersion: 0
        Buckets:
          - INVESTMENT_HOME_COMPONENT_CONTROL_1:
              Start: 1
              End: 50
    - ASK_FI_HOME_SEARCH_BAR:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 0
            MinIOSVersion: 0
        Buckets:
          - CONTROL:
              Start: 70
              End: 74
          - TYPE_1:
              Start: 75
              End: 79
          - TYPE_2:
              Start: 80
              End: 84
          - TYPE_3:
              Start: 85
              End: 89
    - HOME_SEARCH_BAR_CHIPS:
        Buckets:
          - TYPE_1:
              Start: 0
              End: 90
    - INVESTMENT_LANDING_COMPONENTS_ORDERING:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - CONTROL_1:
              Start: 10
              End: 13
    - INVESTMENT_LANDING_MUTUAL_FUNDS_DEEPLINK:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - INVESTMENT_LANDING_MUTUAL_FUNDS_INSTRUMENT_DEEPLINK_CONTROL_1:
              Start: 48
              End: 59
    - JUMP_DASHBOARD_FOR_NON_INVESTED_USERS:
        Buckets:
          - ZERO_STATE_DASHBOARD_VARIANT_ENABLED:
              Start: -1
              End: -1
    - FIXED_DEPOSIT_INTEREST_RATES:
        Buckets:
          - FIXED_DEPOSIT_INTEREST_RATES_EXPERIMENT_MIN_1_YEAR:
              Start: -1
              End: -1
    - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM:
        Buckets:
          - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM_EXPERIMENT_15_MONTHS:
              Start: -1
              End: -1
    - REFERRAL_SCREEN_DURING_ONBOARDING_V1:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 243
            MinIOSVersion: 1318
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - UPDATED_BENEFITS_FOR_AFFLUENT_USER_ONB_ADD_FUNDS:
        ConstraintConfig:
          AppVersionConstraintConfig: # this should be the same as the app version for AFFLUENT_USER_BONUS_TRANSITION_SCREEN in user-staging.yml
            MinAndroidVersion: 99999
            MinIOSVersion: 99999
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets: # this should be the same as the buckets for AFFLUENT_USER_BONUS_TRANSITION_SCREEN in user-staging.yml
          - ONE:
              Start: 0
              End: 0
    - APP_UPDATE_HARD_NUDGE_REFERRALS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 2 # FNF
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - APP_UPDATE_SOFT_NUDGE_REFERRALS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - REFERRALS_V1_LANDING_PAGE_STACKED_REWARDS_COMPONENT:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - PHONE_NUMBER_AS_REFERRAL_CODE:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
              - 2 # FNF
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - FEATURE_TIERING_PITCH_IN_PAYMENT_OPTIONS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 471
            MinIOSVersion: 3060
          StickyPercentageConstraintConfig:
              RolloutPercentage: 100
        Buckets:
          - ENABLED:
              Start: 0
              End: 99

FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_DISPUTE_COMPLAINT_SUMMARY_INPUT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 400
          MinIOSVersion: 600
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SHOW_UPDATED_BOTTOM_SHEET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 480
          MinIOSVersion: 1000000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WB_MAGIC_IMPORT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 468
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_INTERACTIVE_TALK_TO_AI_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CA_BANK_SELECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CC_DASHBOARD_BENEFITS_WIDGET_UNSECURED_CARD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 441
          MinIOSVersion: 2000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SALARY_LITE_PROGRAM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
    - TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
    - PRIME_SMS_PARSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 400
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INVESTMENT_MF_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 2 # FNF
            - 9 # FIT_INVESTMENT
    - MF_ADVANCE_FILTER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 153
          MinIOSVersion: 98
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_TEXT_SEARCH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 160
          MinIOSVersion: 117
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_SIP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_NEW_OTI_PAYMENT_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MERCHANT_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - TIME_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 196
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - AA_FINVU_TOKEN_AUTHENTICATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 185
          MinIOSVersion: 185
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 12 # P2P_INVESTMENT_INTERNAL
    - CATEGORY_ANALYSER_ADD_FUNDS_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CREDIT_SCORE_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_FEEDBACK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FI_MINUTE_HUB_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - HOME_PAGE_LAYOUT_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - REFERRALS_V1_LANDING_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 213
          MinIOSVersion: 202
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - REFERRAL_LINK_GENERATION_AT_CLIENT_SIDE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 235
          MinIOSVersion: 218
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - US_STOCK_LANDING_PAGE_UI:
        # US_STOCK_LANDING_PAGE_UI feature controls visibility of landing page to the user to invest in us stocks
        # Pre-launch page is shown instead of landing for users not allowed for this feature
        AppVersionConstraintConfig:
          MinAndroidVersion: 201
          MinIOSVersion: 188
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - SALARY_PROGRAM_HEALTH_INSURANCE_BENEFIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_RENEWAL_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ML_KIT_QR:
        AppVersionConstraintConfig:
          MinAndroidVersion: 228
          MinIOSVersion: 1000000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ENABLE_GET_VKYC_NEXT_ACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - CATEGORY_ANALYSER_ACCOUNT_FILTER_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 230
          MinIOSVersion: 330
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_DASHBOARD_FOR_NON_INVESTED_USERS:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - TIME_ANALYSER_UPCOMING_TRANSACTIONS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 247
          MinIOSVersion: 343
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_ZERO_STATES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 249
          MinIOSVersion: 343
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CA_CONNECT_FI_TO_FI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - AA_CONSENT_RENEWAL:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - JUMP_MATURITY_CONSENT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_ALL_ACTIVITY_DEEPLINK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 100
    - HEALTH_ENGINE_FOR_PAYMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: ********
          MinIOSVersion: **********
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_JUMP_INVEST_PAGE_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_HOLDINGS_IMPORT_PHONE_NUMBER_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - POST_PAYMENT_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 260
          MinIOSVersion: 365
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_BIOMETRIC_REVALIDATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 2000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - INVESTMENT_RETENTION_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 280
          MinIOSVersion: 281
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_VIA_PHONE_NUMBER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000000
          MinIOSVersion: 1000000000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_INVESTMENT_CALCULATOR_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 289
          MinIOSVersion: 373
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_NAV_GRAPH_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 289
          MinIOSVersion: 373
    - CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NETWORTH_IND_SECURITIES_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - AA_EQUITY_ACC_HOME_SUMMARY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - MF_HOLDINGS_IMPORT_V2_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_SECURED_LOANS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ACTIVATE_BENEFICIARY_VIA_LIVENESS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_INVEST_MORE_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_INVEST_SURPLUS_BALANCE_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_EARLY_SALARY_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_SET_REMINDER_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ONB_ADD_FUNDS_TIERING_SUCCESS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_ASK_FI_SEARCH_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CREDIT_CARD_TPAP_PAYMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - AA_PERMITTED_FIP_CONFIG_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 7 # connected_account
    - ADD_FUNDS_V3:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ADD_FUNDS_V4:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - DEPOSIT_AUTO_RENEW_CTA:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ALFRED_SAVINGS_ACC_SIGN_UPDATE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_ETF:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_REIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_INVIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_UAN_EPF_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 328
          MinIOSVersion: 473
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NETWORTH_REFRESH_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 337
          MinIOSVersion: 491

    - FEATURE_MS_CLARITY_SDK_ENABLED:
        AppVersionConstraintConfig:
          MinAndroidVersion: 368
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]

    - FEATURE_MOENGAGE_INAPP_ENABLED:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]

    - LOANS_IDFC_VKYC_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 328
          MinIOSVersion: 473
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - DEBIT_CARD_OFFER_WIDGET_HOME:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL

    - NSDL_PAN_FLOW_V2_MF_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 343
          MinIOSVersion: 484
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_EMPLOYEE_STOCK_OPTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 351
          MinIOSVersion: 506
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_HUB_FI_TO_FI_INTEGRATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ASSET_LANDING_PAGE_FOR_MANUAL_ASSET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 400
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_OFF_APP_ENACH_CANCELLATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - FEATURE_ADD_FUNDS_USS_DOUBLE_PIN_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MONEY_SECRET_HOME_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 367
          MinIOSVersion: 520
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_HOME_SINGLE_RPC:
        AppVersionConstraintConfig: # change to make default dashboard work
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_HOME_DESIGN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_SMS_PARSER_PARTNER_SDK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 400
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 40 # SMS_PARSER_INTERNAL
    - FEATURE_CX_HELP_RECENT_ACTIVITY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 377
          MinIOSVersion: 2387
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_RECEIPT_NEW_ERROR_VIEW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_ENABLE_PAYMENT_OPTIONS_V1:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_IN_APP_ISSUE_REPORTING_NON_FCR_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ISSUE_REPORTING_ASK_FI_INTEGRATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
    - FEATURE_IN_APP_ISSUE_REPORTING_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 371
          MinIOSVersion: 2360
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_NETWORTH_DASHBOARD_SECRET_SUMMARIES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
    - FEATURE_HOME_BOTTOM_NAV_BAR_NETWORTH_SECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
    - FEATURE_EPF_GENERIC_ERROR_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 396
          MinIOSVersion: 547
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_US_STOCKS_LIMIT_ORDER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_MONEY_SECRET_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 555
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SHARE_POST_PAYMENT_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 405
          MinIOSVersion: 2570
    - FEATURE_MONEY_SECRET_PEER_COMPARISON:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 557
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - UPI_MAPPER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 28
    - SELF_TRANSFER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 28 # NEW_VPA_HANDLE
    - FEATURE_SALARY_REPORT_MONEY_SECRET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_MANDATES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MONEY_SECRET_FOOTER_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 417
          MinIOSVersion: 5257
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_ASSET_IMPORT_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_ASSET_DASHBOARD_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WEALTH_BUILDER_NETWORTH_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_QR_SCAN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - FEATURE_UPI_QUICK_LINK_BANNER_PAY_LANDING_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 230
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_LOANS_PREQUAL_OFFER_FLOW:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
    - FEATURE_SHOULD_SHOW_PIN_SCREEN_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_AMB_ENTRYPOINT_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CX_NEW_LANDING_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WB_DASHBOARD_LIABILITIES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
    - FEATURE_REWARDS_CATALOG_MERGED_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_WEEKLY_PORTFOLIO_TRACKER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_PAY_LANDING_BANNER_RUPAY_CC_BE_DRIVEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 456
          MinIOSVersion: 619
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - FEATURE_FI_MCP_TOTP_CODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
    - FEATURE_CREDIT_REPORT_MONEY_SECRET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_TIERING_UPGRADE_PLAN_IN_EXPLORE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 1
        MinIOSVersion: 1
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    - FEATURE_TIERING_PITCH_IN_PROFILE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_NUGGET_ACCOUNT_FREEZE_CHATBOT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_FI_COINS_TO_FI_POINTS_PRE_MIGRATION_PHASE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_FI_COINS_TO_FI_POINTS_POST_MIGRATION_PHASE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_FI_COINS_TO_FI_POINTS_DURING_MIGRATION_PHASE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_NET_WORTH_GOLD_DISTRIBUTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_NET_WORTH_AI_ICONS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_NUGGET_TRANSACTION_CHATBOT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MOBILE_RECHARGES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_NUGGET_WEALTH_CHATBOT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_NUGGET_CLASSIFIER_CHATBOT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_HOME_DESIGN_ENHANCEMENTS_WEALTH_USER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100


P2PInvestment:
  Activity:
    AppVersionConstraintConfig:
      MinAndroidVersion: 161
      MinIOSVersion: 126
  RecentActivity:
    AppVersionConstraintConfig:
      MinAndroidVersion: 10
      MinIosVersion: 10
  WithdrawalDowntime:
    IsEnable: true
    StartTime: "23:30"
    EndTime: "05:30"
  CloseJump:
    IsEnable: true
    StartDate: "2024-08-16"
    EndDate: "2025-11-30"
    Icon: "https://epifi-icons.pointz.in/p2pinvestment/blocker.png"
    Title: "Jump is unavailable for further investments"
    SubTitle: "We would be back soon, until then check our other wealth building products such as US Stocks and Mutual Funds"
  DeeplinkV2CompatibilityAndroidVersion: 254
  MinIosVersionForConsentCardV2: 100
  MinAndroidVersionForConsentCardV2: 100
  DisableFlexiSchemeBanners: true

Goals:
  GoalDiscovery:
    Enable: true
    AllowedGroups: [ ] # allowed user groups
  GoalDiscoveryInExistingInvestmentInstrument:
    Enable: true
    AllowedGroups: [ ] # allowed user groups

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

QrDeeplinkParams:
  MinAndroidVersionForAmountScreenDeeplink: 1
  MinIosVersionForAmountScreenDeeplink: 1

UserProfile:
  EnableEditEmployment: true
  ProfileHeaderV2MinVersion:
    MinVersionAndroid: 1
    MinVersionIos: 1
  IsEmailEditableConfig:
    DisableFeature: false
    MinAndroidVersion: 1
    MinIOSVersion: 1

SharedConfig:
  EnableV2SafetyNetFlow: true

USStocks:
  WithdrawFundsBlockedDuration: "1m"
  IsDoublePinAddFundFlowEnabled: true
  EnableWalletForAllUsers: true
  ETF:
    IsEnabled: true
  MinIOSAppVersionToSupportSuitabilityBottomSheet: 2190
  MinAndroidAppVersionToSupportSuitabilityBottomSheet: 332
  MinAndroidAppVersionToSupportInlineErrDuringBuyFlow: 241
  MinIOSAppVersionToSupportInlineErrDuringBuyFlow: 337
  VersionSupport:
    MinAndroidAppVersionToSupportOnboardingPreRequisites: 100
    MinIOSAppVersionToSupportOnboardingPreRequisites: 100
    MinAndroidAppVersionToSupportVkycCheck: 10000
    MinIOSAppVersionToSupportVkycCheck: 10000
    MinIOSAppVersionToSupportPanAadhaarLinkCheck: 10000
    MinAndroidAppVersionToSupportPanAadhaarLinkCheck: 10000
    MinIOSAppVersionToSupportAnnouncementsInSymbolDetails: 100
    MinAndroidAppVersionToSupportAnnouncementsInSymbolDetails: 100
    MinAndroidAppVersionToSupportFiLite: 100
    MinIOSAppVersionToSupportFiLite: 100
    MinAndroidAppVersionToSupportDropdownForCurrentlyInvestedInstruments: 10_000
    MinIOSAppVersionToSupportDropdownForCurrentlyInvestedInstruments: 2015
    MinAndroidAppVersionToSupportHiddenActivityTab: 10_000
  BuyTimeoutInMilliseconds: 60000
  PriceGraphURL: "https://web.staging.pointz.in/fin-charts/line-chart"
  RatioGraphURL: "https://web.staging.pointz.in/fin-charts/multi-chart"
  PriceGraphUpdatedAt: ********** # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  RatioGraphUpdatedAt: ********** # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  A2FormURL: "https://web.staging.pointz.in/assets/pages/a2-form/v2"
  IsBuyDisabled: false
  IsSellDisabled: false
  EnableStockPageUsingSecuritiesService: true

# MerchantIdCapabilityMap contains merchant id for which we need to
# enable special capabilities on timeline like pay and request
MerchantIdTimelineCapabilityMap:
  "855e89c5-5034-4518-973d-0d4016a37dee":
    "TIMELINE_OPTION_PAY": true
    "TIMELINE_OPTION_REQUEST": true

QuestSdk:
  Disable: false

AutoInvestStoryConfig:
  StoryId: "5c0ca238-488a-476e-9516-17e42e7c7cb3"

AddFundsParams:
  IsTierMovementDropOffFeedbackFlowEnabled: true
  AddFundsV3Params:
    ImageWithTextConstraints:
      IsEnableOnAndroid: true
      MinAndroidVersion: 1
      IsEnableOnIos: true
      MinIosVersion: 1
  IntentNavigateToPayStatusAndroidVersion: 1
  CollectNavigateToPayStatusAndroidVersion: 10000

AddFundsV2Params:
  WhitelistedActorIds: [ "AC220901EPxCmIlCTSq5V1ttIhGgvw==" ]
  OnboardingAddFundsV2ScreenDetails:
    ShowManualAccountBalanceRefreshCta: true
    SalaryB2BSignupUrl: "https://web.staging.pointz.in/signup"

HomeRevampParams:
  NreSavingsDashboardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 0
    IsEnableOnIos: true
    MinIosVersion: 0
  NroSavingsDashboardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 10000
    IsEnableOnIos: true
    MinIosVersion: 10000
  MutualfundDashboardCardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 418
    IsEnableOnIos: true
    MinIosVersion: 5262
  CreditScoreDashboardCardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 418
    IsEnableOnIos: true
    MinIosVersion: 5262
  EpfDashboardCardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 418
    IsEnableOnIos: true
    MinIosVersion: 5262
  DcInternationalWidgetReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 418
    IsEnableOnIos: true
    MinIosVersion: 3012
  ShortcutIconTypeToDetailsMap:
    SHORTCUT_FI_STORE:
      Title: "Fi\nStore"
      ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/fi-store-explore.png"
      Deeplink: '{"screen":"WEB_PAGE", "webPageScreenOptions":{"webpageTitle":"Fi Store", "webpageUrl":"https://fimoney.staging.dpanda.io/", "disableHardwareBackPress": true}}'
    SHORTCUT_FI_STORE_GIFT_CARDS:
      Deeplink: '{"screen":"WEB_PAGE", "webPageScreenOptions":{"webpageTitle":"Fi Store", "webpageUrl":"https://sandbox-fimoney.poshvine.com/gvms", "disableHardwareBackPress": true}}'
  HomeNudgeParams:
    TieringParams:
      ToUseV2States: false
      DowngradeWindowDuration: 24h
  ToShowTieringInfoInSavingsDashboard: true
  AllHomeIcons:
    "qr_code-69c444b6-cd1e-4b2d-8be3-7d9b2e2a25bb":
      IconWithVersionConstraints:
        - VersionConstraints:
            IsEnableOnAndroid: true
            MinAndroidVersion: 231
            IsEnableOnIos: true
            MinIosVersion: 1285
          IconType: "QR_CODE"
          ImageUrl: "https://epifi-icons.pointz.in/home-v2/QRCodeWhite.png"
          OnclickImageUrl: ""
          Title: "Scan & Pay"
          FontColour: "#FFFFFF"
          FontStyle: "BUTTON_S"
        # The default icon parameters do not require a version constraint check
        - IconType: "QR_CODE"
          ImageUrl: "https://epifi-icons.pointz.in/home-v2/QRCode.png"
          OnclickImageUrl: ""
          Title: ""
  HomeLayoutConfigurationV2Params:
    SectionScreenElementsConfig:
      BottomNavBarSection:
        ScreenElementsMapping:
          usstocks-1:
            IconWithVersionConstraints:
              VersionConstraints:
                IsEnableOnAndroid: true
                MinAndroidVersion: 523
                IsEnableOnIos: true
                MinIosVersion: 2658
  LayoutBySegFeatureRelease:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: true
    MinIosVersion: 1
  HomeLayoutUIRevamp:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: true
    MinIosVersion: 1
  HomeLayoutV2Params:
    HomeElementAttributes:
      # This document contains the logic for the different layouts listed below. - https://docs.google.com/document/d/1DPhkfXSf-8COI4eNfUuXLMPsy_FdfcV5Qbv0TwQ0CIg/edit
      ElementIdToSegmentExpressionScores:
        "card-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "wealth-builder-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 3
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 2
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "discover-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 1
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "card-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 4
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 4
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "loans-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
          - Expression: "!IsMember('AWS_test-loan-eligible')" # cc active but sa account frozen
            Score: -1
        "usstocks-1":
          - Expression: "IsMember('AWS_test-usstocks')" # for users not invested in any invest product or just invested in usstocks
            Score: 1
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "card-1", "wealth-builder-1", "discover-1" ]
          slot_2: [ "rewards-4" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "shortcuts-2" ]
        slot_2: [ "dc-international-widget" ]
        slot_3: [ "promotionalbanner-1" ]
        slot_4: [ "primary-feature-1" ]
        slot_5: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_6: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_7: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_8: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_9: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_10: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_11: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_12: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "help-1" ]
        slot_16: [ "refer-2" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [ "invest-3", "usstocks-1" ]
        slot_4: [ "wealth-builder-1","card-2" ]
        slot_5: [ "wealth-builder-1", "discover-1", "loans-2" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "" ]
  HomeLayoutV2D0To7Params:
    HomeElementAttributes:
      # This document contains the logic for the different layouts listed below. - https://docs.google.com/document/d/1DPhkfXSf-8COI4eNfUuXLMPsy_FdfcV5Qbv0TwQ0CIg/edit
      ElementIdToSegmentExpressionScores:
        "card-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "wealth-builder-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 3
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 2
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "discover-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 1
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "card-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 4
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 4
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "loans-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
          - Expression: "!IsMember('AWS_test-loan-eligible')" # cc active but sa account frozen
            Score: -1
        "usstocks-1":
          - Expression: "IsMember('AWS_test-usstocks')" # for users not invested in any invest product or just invested in usstocks
            Score: 1
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "card-1", "wealth-builder-1", "discover-1" ]
          slot_2: [ "rewards-4" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "shortcuts-2" ]
        slot_2: [ "dc-international-widget" ]
        slot_3: [ "promotionalbanner-1" ]
        slot_4: [ "primary-feature-1" ]
        slot_5: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_6: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_7: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_8: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_9: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_10: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_11: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_12: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "help-1" ]
        slot_16: [ "refer-2" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [ "invest-3", "usstocks-1" ]
        slot_4: [ "wealth-builder-1","card-2" ]
        slot_5: [ "wealth-builder-1", "discover-1", "loans-2" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "" ]
  HomeLayoutV2D8To14Params:
    HomeElementAttributes:
      # This document contains the logic for the different layouts listed below. - https://docs.google.com/document/d/1DPhkfXSf-8COI4eNfUuXLMPsy_FdfcV5Qbv0TwQ0CIg/edit
      ElementIdToSegmentExpressionScores:
        "card-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "wealth-builder-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 3
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 2
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "discover-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 1
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "card-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 4
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 4
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "loans-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
          - Expression: "!IsMember('AWS_test-loan-eligible')" # cc active but sa account frozen
            Score: -1
        "usstocks-1":
          - Expression: "IsMember('AWS_test-usstocks')" # for users not invested in any invest product or just invested in usstocks
            Score: 1
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "card-1", "wealth-builder-1", "discover-1" ]
          slot_2: [ "rewards-4" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "shortcuts-2" ]
        slot_2: [ "dc-international-widget" ]
        slot_3: [ "promotionalbanner-1" ]
        slot_4: [ "primary-feature-1" ]
        slot_5: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_6: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_7: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_8: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_9: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_10: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_11: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_12: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "help-1" ]
        slot_16: [ "refer-2" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [ "invest-3", "usstocks-1" ]
        slot_4: [ "wealth-builder-1","card-2" ]
        slot_5: [ "wealth-builder-1", "discover-1", "loans-2" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "" ]
  HomeLayoutV2D15To28Params:
    HomeElementAttributes:
      # This document contains the logic for the different layouts listed below. - https://docs.google.com/document/d/1DPhkfXSf-8COI4eNfUuXLMPsy_FdfcV5Qbv0TwQ0CIg/edit
      ElementIdToSegmentExpressionScores:
        "card-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "wealth-builder-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 3
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 2
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "discover-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 1
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "card-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 4
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 4
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "loans-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
          - Expression: "!IsMember('AWS_test-loan-eligible')" # cc active but sa account frozen
            Score: -1
        "usstocks-1":
          - Expression: "IsMember('AWS_test-usstocks')" # for users not invested in any invest product or just invested in usstocks
            Score: 1
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "card-1", "wealth-builder-1", "discover-1" ]
          slot_2: [ "rewards-4" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "shortcuts-2" ]
        slot_2: [ "dc-international-widget" ]
        slot_3: [ "promotionalbanner-1" ]
        slot_4: [ "primary-feature-1" ]
        slot_5: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_6: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_7: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_8: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_9: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_10: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_11: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_12: [ "suggestedforyou-1", "recentupcomingactivities-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "help-1" ]
        slot_16: [ "refer-2" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [ "invest-3", "usstocks-1" ]
        slot_4: [ "wealth-builder-1","card-2" ]
        slot_5: [ "wealth-builder-1", "discover-1", "loans-2" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "" ]
  HomeLayoutV2WealthAnalyserParams:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "usstocks-1":
          - Expression: "IsMember('AWS_test-usstocks')" # for users not invested in any stocks and users invested only in US Stocks, usstocks-1 will be shown
            Score: 1
NonTpapPspHandles: [ "fede" ]

Lending:
  PreApprovedLoan:
    LoanDetailsSelectionV2Flow:
      IsEnabled: true
      IsRewardsEnabled: false
      SkipAmountSelectionScreen: false
      ShowMultipleOfferSelection: false
      ShowCollapsedBreakdownView: false
      ShowCongratulatoryText: false
      ShowDiscountedPf: false
      EnableLoanPrograms:
        - IDFC
        - FEDERAL_BANK
        - LOAN_PROGRAM_PRE_APPROVED_LOAN
        - LOAN_PROGRAM_FLDG
      DefaultAmountPercentage:
        - "LIQUILOANS": 0.75
        - "IDFC": 1
        - "FEDERAL": 0.75
    RestrictShowingMultipleOffers: false

Alfred:
  ServiceRequestHistoryPageSize: 2
  EnableVRH:
    MinAndroidVersion: 250
    MinIOSVersion: 1443
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableRequestChoiceBottomSheet:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableCopyTrackingUrl:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableSavingsAccSignUpdate:
    MinAndroidVersion: 1
    MinIOSVersion: 1
    FallbackToEnableFeature: true
    DisableFeature: false

CreditCard:
  CCDashboardV2Config:
    CCDashboardSessionBasedIconAppVersionConfig:
      MinAndroidVersion: 1
      MinIOSVersion: 1
  AppVersionSupport:
    MinIosVersionForCreditCard: 330
    MinAndroidVersionForCreditCard: 228
    MinIosVersionForCreditCardIntroV2: 349
    MinAndroidVersionForCreditCardIntroV2: 250
  OnboardingRetryAttemptCutoff: 10
  EnableCCAllTxnPagination: false
  PaymentSuccessBannerTimeInMinutes: 5
  ShowCreditCardTabByDefaultFromCardTab: true
  WorkflowConstraints:
    - "CARD_REQUEST_WORKFLOW_CARD_ACTIVATION":
        AppVersionConstraintConfig:
          MinIOSVersion: 9999
    # to be used for secured card onboarding.
    - "CARD_REQUEST_WORKFLOW_CARD_ONBOARDING":
        AppVersionConstraintConfig:
          MinAndroidVersion: 276
          MinIOSVersion: 369
  AllEligibleCcScreenConfig:
    CardComponentTemplateVersion: 1
  IsCcChoicesComponentListViewEnabled: false
  CcNetworkSelectionScreenVersionCheck:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  FiLiteBottomCtaConfigs:
    EnableFiLiteBottomCtaVersionCheckFlag:
      IsEnableOnAndroid: false
      MinAndroidVersion: 1000
      IsEnableOnIos: false
      MinIosVersion: 1000
    IsCcChoicesComponentListViewEnabled: true
  EnableDashboardSegmentationCarousels: false
  SegmentIdToCarouselObjectMap:
    AWS_test-segment: # for actor ID ACcJM2HRR/TYCr4plZ5iPWTA230829== (only in staging)
      InfoIcon: "https://epifi-icons.pointz.in/credit_card_images/welcome_offers_claim_img.png"
      InfoTitle: "This is a sample banner for testing"
      InfoDescription: "This is a sample banner for testing"
      DeeplinkScreenName: 489

PaymentOptionsConfig:
  IntentOptionsConfig:
    UpiAppsAndroidPackages:
      - PackageId: "com.phonepe.app"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/phone_pe.png"
        AppName: "Phone Pe"
      - PackageId: "com.google.android.apps.nbu.paisa.user"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/google_pay.png"
        AppName: "Google Pay"
      - PackageId: "net.one97.paytm"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/pay_tm.png"
        AppName: "Paytm"
      - PackageId: "com.epifi.paisa.staging"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Fi Staging"
      - PackageId: "in.org.npci.upiapp"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "NPCI upi app"
      - PackageId: "com.idfcfirstbank.optimus"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "IDFC"
      - PackageId: "com.axis.mobile"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "AXIS"
      - PackageId: "com.flipkart.android"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Flipkart"
      - PackageId: "org.altruist.BajajExperia"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Bajaj"

NetworthConfig:
  ConfigPath: "./mappingJson/networthConfig.json"
  DebugActorIdsForDailyReport:
    - "AC3N7kkBRMEm250423": true # Sainath's actorId for debugging purposes
  AppVersionsForWBDashboardV2:
    MinVersionAndroid: 464
    MinVersionIos: 1
  AppVersionsForConnectMoreAssetsScreenV2:
    MinVersionAndroid: 470
    MinVersionIos: 1
  HomeBottomNavBarHighlights:
    NavId: "networth-navbar-animation"
    HighlightLottieUrl: "https://epifi-icons.pointz.in/home-v2/networth4.json"
    MaxSessionCount: 5000
    MaxClicksCount: 10000
    RepeatCount: 3
    EnableHighlights:
      MinAndroidVersion: 1
      MinIOSVersion: 1
      DisableFeature: false

ShowAutoRenewCta: true

TpapUnifiedFlowReleaseVersions:
  MinAndroidVersion: 320
  MinIOSVersion: 2044

OnAppEnachRedirectionDebug:
  ReturnRedirectionUrl: true
  AllowedActors:
    ALL: true # for allowing debugging for all actor-ids at once

VpaMigrationScreenParams:
  OldVpaHandle: "fede"

MsClarityConfig:
  IsEnabled: true
  AllowedScreenNames: [ ]
  AllowedActivityNames:
    - "com.epifi.paisa.home.HomeActivity"
    - "com.epifi.paisa.ui.MainActivity"
    - "com.epifi.paisa.accounttiering.TieringActivity" # Following Activity names are temporarily enabled until Android single activity flag is turned on for all of these
    - "com.epifi.paisa.analyser.AnalyserActivity"
    - "com.epifi.paisa.analyser.mutualfundimport.ImportMutualFundActivity"
    - "com.epifi.paisa.commonflows.chat.CustomerSupportCallUsActivity"
    - "com.epifi.paisa.commonflows.chat.CustomerSupportChatActivity"
    - "com.epifi.paisa.commonflows.feedback.feedbackvote.FeedbackVoteActivity"
    - "com.epifi.paisa.commonflows.feedbackengine.FeedbackEngineActivity"
    - "com.epifi.paisa.commonflows.generate_deposit_statement.GenerateDepositStatementActivity"
    - "com.epifi.paisa.commonflows.goals.GoalsActivity"
    - "com.epifi.paisa.commonflows.information_popup.InformationPopupActivity"
    - "com.epifi.paisa.commonflows.investment_retention.InvestmentRetentionActivity"
    - "com.epifi.paisa.help.HelpActivity"
    - "com.epifi.paisa.investments.InvestmentsActivity"
    - "com.epifi.paisa.nominee.NomineeActivity"
    - "com.epifi.paisa.onboarding.login.LoginActivity"
    - "com.epifi.paisa.onboarding.login.safetynet.SafetynetConsentActivity"
    - "com.epifi.paisa.pay.PayActivity"
    - "com.epifi.paisa.profile.ProfileActivity"
    - "com.epifi.paisa.ui.authorize.notification.CxUserAuthenticationActivity"
    - "com.epifi.paisa.ui.deeplink.DeeplinkActivity"
    - "com.epifi.paisa.ui.fullscreen.FullScreenNotificationActivity"
    - "com.epifi.paisa.ui.splash.SplashActivity"
    - "com.epifi.paisa.usstocks.UsStocksActivity"
    - "com.epifi.paisa.videokyc.OverlayActivity"
    - "com.epifi.paisa.videokyc.VideoKycActivity"
    - "com.epifi.paisa.wealth.WealthActivity"
    - "com.epifi.paisa.wealth.WealthOnboardingActivity"

EnableGetPaymentOptionsV1: true

SavingsAccountClosure:
  FullGroupCriteiaItemMinAppVersions:
    MinVersionAndroid: 1000
    MinVersionIos: 1000

HomeExploreConfig:
  EnableAskFiSection: true
  EnableFeedbackSection: true

MoneySecrets:
  MfStocksBreakdown:
    MaxStocks: 50

MoneySecretsConfig:
  CreditScoreSecretsConfig:
    CoolOffDurationForRnfInMinutes: "2m"

PayLandingScreenParams:
  IOSMinVersionForDesignFixit: 3038
  AndroidMinVersionForDesignFixit: 441

Cx:
  IsLLMSearchDropOffSurveyEnabled: true
  CxLandingPageV2Config:
    MaxNumberOfOpenIndividualTickets: 2
    MaxNumberOfTicketsToFetch: 50
    HelpSectionConfig:
      FAQDetails:
        - FAQId: ***********
          FAQType: "ARTICLE"
          Priority: 3
        - FAQId: ***********
          FAQType: "ARTICLE"
          Priority: 2
        - FAQId: ***********
          FAQType: "ARTICLE"
          Priority: 1
        - FAQId: ***********
          FAQType: "CATEGORY"
          Priority: 3
        - FAQId: ***********
          FAQType: "CATEGORY"
          Priority: 2
        - FAQId: 82000180884
          FAQType: "CATEGORY"
          Priority: 1
