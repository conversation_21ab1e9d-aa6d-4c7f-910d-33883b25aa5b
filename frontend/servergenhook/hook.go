package servergenhook

import (
	"fmt"

	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/config/genconf"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifiserver"
	"github.com/epifi/be-common/pkg/logger"

	frontendConfig "github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/configsvc"
	"github.com/epifi/gamma/frontend/pay/transaction"
	payErrorPkg "github.com/epifi/gamma/pkg/pay/payerrorcode"
)

func FrontendBeforeServerStartHook() (func(), error) {
	var cleanupFn = func() {}

	frontendConf, err := frontendConfig.Load()
	if err != nil {
		return cleanupFn, err
	}

	// TODO(Sundeep): Fix this duplication of config by utilizing gconf.SecureLogging()
	if frontendConf.SecureLogging != nil && frontendConf.SecureLogging.EnableSecureLog {
		logger.InitPartnerLogger(&logger.LogConfig{
			Filenameandpath: frontendConf.SecureLogging.PartnerLogPath,
			MaxSize:         frontendConf.SecureLogging.MaxSizeInMBs,
			MaxBackups:      frontendConf.SecureLogging.MaxBackups,
		})
		cleanupFn = func() { _ = logger.PartnerLogger.Sync() }
	}

	// Here we are loading pay error views to be shown in case of any error, but now we are having three different files containing the same data,
	// Ultimately we will be moving/using only one file for this purpose i.e one inside "pkg/pay/" directory.
	err = transaction.LoadPayErrorView(frontendConf.PayErrorViewJson)
	if err != nil {
		logger.Panic("error loading error views", zap.Error(err))
	}

	err = payErrorPkg.LoadPayErrorView()
	if err != nil {
		logger.Panic("error loading pay error view", zap.Error(err))
	}

	err = configsvc.LoadSMSScannerConfig(frontendConf.SMSScannerConfigJsonFilePath, frontendConf.SMSTemplatesConfigJsonFilePath)
	if err != nil {
		logger.Panic("error loading sms scanning config", zap.Error(err))
	}

	return cleanupFn, nil
}

func StartFrontendServer(s *grpc.Server, gconf *genconf.Config, initNotifier chan<- cfg.ServerName) (func(), error) {
	cleanupFn := func() {}
	initNotifier <- cfg.FRONTEND_SERVER

	ss := epifigrpc.NewSecureProxyServer(string(gconf.Name()), fmt.Sprintf("localhost:%d", gconf.ServerPorts().GrpcPort), prometheus.DefBuckets, gconf.GrpcServerConfig())
	epifiserver.StartWithSecurePort(s, ss, gconf.ServerPorts(), string(gconf.Name()))
	return cleanupFn, nil
}
