package attributes

import (
	"context"
	"fmt"
	"regexp"

	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"

	"github.com/pkg/errors"

	"github.com/Knetic/govaluate"

	"github.com/epifi/be-common/api/rpc"
	typesPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/frontend/config"
)

var (
	crossAttachRegex = regexp.MustCompile(`IsCrossAttachIntent\('(.+?)'\)`)
)

type crossAttachAttributeEvaluator struct {
	crossAttachClient crossAttachPb.CrossAttachClient
}

func NewCrossAttachAttributeEvaluator(crossAttachClient crossAttachPb.CrossAttachClient) *crossAttachAttributeEvaluator {
	return &crossAttachAttributeEvaluator{
		crossAttachClient: crossAttachClient,
	}
}

var _ IAttributeEvaluator = &crossAttachAttributeEvaluator{}

// EvaluateAttribute will evaluate cross-sell info expression attribute by combining all relevant expressions and returning feature lifecycle map
func (c *crossAttachAttributeEvaluator) EvaluateAttribute(ctx context.Context, actorId string, attributes *config.HomeElementAttributes) (any, error) {
	if attributes == nil || attributes.ElementIdToCrossAttachExpressionScores == nil {
		return &crossAttachPb.GetCrossSellInfoResponse{}, nil
	}

	var allExpressions []string
	for _, expressionScores := range attributes.ElementIdToCrossAttachExpressionScores {
		for _, expressionScore := range expressionScores {
			allExpressions = append(allExpressions, expressionScore.Expression)
		}
	}
	if len(allExpressions) == 0 {
		// if no expressions are present, then return canCrossSell as false without calling any RPC
		return &crossAttachPb.GetCrossSellInfoResponse{
			Status:       rpc.StatusOk(),
			CanCrossSell: typesPb.BooleanEnum_FALSE,
		}, nil
	}
	err := validateCohortsFromExpressions(allExpressions)
	if err != nil {
		return nil, errors.Wrap(err, "error while validating cohorts from expressions")
	}

	// call RPC to get cross-sell info
	crossSellInfoResponse, err := c.crossAttachClient.GetCrossSellInfo(ctx, &crossAttachPb.GetCrossSellInfoRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(crossSellInfoResponse, err); rpcErr != nil {
		logger.Error(ctx, "GetCrossSellInfo rpc call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return &crossAttachPb.GetCrossSellInfoResponse{}, nil
	}

	return crossSellInfoResponse, nil
}

// AssignEvaluatedScore assigns evaluated score to each element.
//  1. if expression evaluation fails, we return error
//  2. if expression evaluation succeeds, we assign corresponding score
//
// nolint: dupl
func (c *crossAttachAttributeEvaluator) AssignEvaluatedScore(ctx context.Context, elementIdToScoreMap map[string]float32, attributes *config.HomeElementAttributes, evaluatedValue any) (map[string]float32, error) {
	// if this attributes config is nil, return default elementIdToScoreMap as no evaluation may happen due to empty HomeElementAttributes config.
	if attributes == nil || attributes.ElementIdToCrossAttachExpressionScores == nil {
		return elementIdToScoreMap, nil
	}
	crossSellInfoResp, ok := evaluatedValue.(*crossAttachPb.GetCrossSellInfoResponse)
	if !ok {
		return nil, fmt.Errorf("evaluated value is not of type *crossAttachPb.GetCrossSellInfoResponse")
	}

	expressionFunctionMap := getExpressionFunctionMapForCrossAttach(crossSellInfoResp)
	for elementId, expressionScores := range attributes.ElementIdToCrossAttachExpressionScores {
		for _, expressionScore := range expressionScores {
			isExpressionTrue, err := evaluateExpression(expressionScore.Expression, expressionFunctionMap)
			if err != nil {
				return nil, fmt.Errorf("error while evaluating cross-attach expression, expression: %s, err: %w", expressionScore.Expression, err)
			}

			// if the element already has a negative score, then skip updating the score
			if val, exist := elementIdToScoreMap[elementId]; exist && val < 0 {
				continue
			}

			if isExpressionTrue {
				elementIdToScoreMap[elementId] = expressionScore.Score
			}
		}
	}

	return elementIdToScoreMap, nil
}

func validateCohortsFromExpressions(expressions []string) error {
	cohortsMap := make(map[string]bool, 0)
	invalidExpressions := make([]string, 0)

	for _, expression := range expressions {
		// Extract all regex matches from the expression
		reMatches := crossAttachRegex.FindAllStringSubmatch(expression, -1)
		for _, reMatch := range reMatches {
			for idx, reGroup := range reMatch {
				if idx == 0 {
					// Skip the full match; only consider capture groups
					continue
				}
				if reGroup != "" {
					// Add the matched cohort to the map to ensure uniqueness
					cohortsMap[reGroup] = true

					// Validate if the extracted cohort is a valid ProductType
					// If not found in the enum and not explicitly "PRODUCT_TYPE_UNSPECIFIED," flag it as invalid
					if product.ProductType_value[reGroup] == 0 && reGroup != "PRODUCT_TYPE_UNSPECIFIED" {
						invalidExpressions = append(invalidExpressions, expression)
					}
				}
			}
		}
	}

	if len(invalidExpressions) > 0 {
		return fmt.Errorf("invalid product name found in expressions: %v", invalidExpressions)
	}

	return nil
}

func getExpressionFunctionMapForCrossAttach(crossSellInfoResponse *crossAttachPb.GetCrossSellInfoResponse) map[string]govaluate.ExpressionFunction {
	return map[string]govaluate.ExpressionFunction{
		"IsCrossAttachIntent": func(args ...interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, fmt.Errorf("IsCrossAttachIntent function expects 1 argument")
			}
			cohortTypeString, ok := args[0].(string)
			if !ok {
				return nil, fmt.Errorf("IsCrossAttachIntent function expects string argument")
			}
			cohortType := product.ProductType(product.ProductType_value[cohortTypeString])

			// if cross-sell flag is false, then return false as default
			if crossSellInfoResponse.GetCanCrossSell() == typesPb.BooleanEnum_FALSE {
				return false, nil
			}
			// NOTE: The following logic is based on the assumption that the API returns products to pitch in a prioritized order,
			// even if the user is not part of a cohort. To ensure the most relevant product is pitched,
			// we check if the user belongs to the highest-priority cohort.
			// If the first product in the prioritized list matches the given cohort type, we return true.
			// This prevents returning true for every product, ensuring that the user's top-priority product is pitched first.
			// Additionally, the current home layout configuration setup is designed to support only one product at a time (which is a product call to show one at a time).
			if len(crossSellInfoResponse.GetPrioritizedProductsToPitch()) > 0 && crossSellInfoResponse.GetPrioritizedProductsToPitch()[0] == cohortType {
				return true, nil
			}
			return false, nil
		},
	}
}
