// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/github.com/epifi/gamma/frontend/tiering/data_collector/data_collector.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	employment "github.com/epifi/gamma/api/employment"
	kyc "github.com/epifi/gamma/api/kyc"
	rewards "github.com/epifi/gamma/api/rewards"
	tiering "github.com/epifi/gamma/api/tiering"
	external "github.com/epifi/gamma/api/tiering/external"
	data_collector "github.com/epifi/gamma/frontend/tiering/data_collector"
	helper "github.com/epifi/gamma/frontend/tiering/helper"
	gomock "github.com/golang/mock/gomock"
	money "google.golang.org/genproto/googleapis/type/money"
)

// MockDataCollector is a mock of DataCollector interface.
type MockDataCollector struct {
	ctrl     *gomock.Controller
	recorder *MockDataCollectorMockRecorder
}

// MockDataCollectorMockRecorder is the mock recorder for MockDataCollector.
type MockDataCollectorMockRecorder struct {
	mock *MockDataCollector
}

// NewMockDataCollector creates a new mock instance.
func NewMockDataCollector(ctrl *gomock.Controller) *MockDataCollector {
	mock := &MockDataCollector{ctrl: ctrl}
	mock.recorder = &MockDataCollectorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDataCollector) EXPECT() *MockDataCollectorMockRecorder {
	return m.recorder
}

// GetAaSalaryData mocks base method.
func (m *MockDataCollector) GetAaSalaryData(ctx context.Context, actorId string) (*data_collector.AaSalaryData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAaSalaryData", ctx, actorId)
	ret0, _ := ret[0].(*data_collector.AaSalaryData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAaSalaryData indicates an expected call of GetAaSalaryData.
func (mr *MockDataCollectorMockRecorder) GetAaSalaryData(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAaSalaryData", reflect.TypeOf((*MockDataCollector)(nil).GetAaSalaryData), ctx, actorId)
}

// GetBalance mocks base method.
func (m *MockDataCollector) GetBalance(ctx context.Context, actorId string) (*money.Money, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalance", ctx, actorId)
	ret0, _ := ret[0].(*money.Money)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalance indicates an expected call of GetBalance.
func (mr *MockDataCollectorMockRecorder) GetBalance(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalance", reflect.TypeOf((*MockDataCollector)(nil).GetBalance), ctx, actorId)
}

// GetCashRewardAggregate mocks base method.
func (m *MockDataCollector) GetCashRewardAggregate(ctx context.Context, actorId string, tier external.Tier, fromTime, toTime time.Time) (float32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCashRewardAggregate", ctx, actorId, tier, fromTime, toTime)
	ret0, _ := ret[0].(float32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCashRewardAggregate indicates an expected call of GetCashRewardAggregate.
func (mr *MockDataCollectorMockRecorder) GetCashRewardAggregate(ctx, actorId, tier, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCashRewardAggregate", reflect.TypeOf((*MockDataCollector)(nil).GetCashRewardAggregate), ctx, actorId, tier, fromTime, toTime)
}

// GetChequeBookRefundDetails mocks base method.
func (m *MockDataCollector) GetChequeBookRefundDetails(ctx context.Context, actorId string) (*data_collector.ChequeBookOrderData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChequeBookRefundDetails", ctx, actorId)
	ret0, _ := ret[0].(*data_collector.ChequeBookOrderData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChequeBookRefundDetails indicates an expected call of GetChequeBookRefundDetails.
func (mr *MockDataCollectorMockRecorder) GetChequeBookRefundDetails(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChequeBookRefundDetails", reflect.TypeOf((*MockDataCollector)(nil).GetChequeBookRefundDetails), ctx, actorId)
}

// GetCustomRewardProjectionsAggregate mocks base method.
func (m *MockDataCollector) GetCustomRewardProjectionsAggregate(ctx context.Context, actorId string, rewardType rewards.RewardType, tier external.Tier, month *data_collector.MonthYear) (float32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomRewardProjectionsAggregate", ctx, actorId, rewardType, tier, month)
	ret0, _ := ret[0].(float32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomRewardProjectionsAggregate indicates an expected call of GetCustomRewardProjectionsAggregate.
func (mr *MockDataCollectorMockRecorder) GetCustomRewardProjectionsAggregate(ctx, actorId, rewardType, tier, month interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomRewardProjectionsAggregate", reflect.TypeOf((*MockDataCollector)(nil).GetCustomRewardProjectionsAggregate), ctx, actorId, rewardType, tier, month)
}

// GetDebitCardOrderCharges mocks base method.
func (m *MockDataCollector) GetDebitCardOrderCharges(ctx context.Context, actorId string) (*data_collector.DebitCardOrderChargesData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDebitCardOrderCharges", ctx, actorId)
	ret0, _ := ret[0].(*data_collector.DebitCardOrderChargesData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDebitCardOrderCharges indicates an expected call of GetDebitCardOrderCharges.
func (mr *MockDataCollectorMockRecorder) GetDebitCardOrderCharges(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDebitCardOrderCharges", reflect.TypeOf((*MockDataCollector)(nil).GetDebitCardOrderCharges), ctx, actorId)
}

// GetEmploymentInfo mocks base method.
func (m *MockDataCollector) GetEmploymentInfo(ctx context.Context, actorId string) (*employment.EmployerInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmploymentInfo", ctx, actorId)
	ret0, _ := ret[0].(*employment.EmployerInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmploymentInfo indicates an expected call of GetEmploymentInfo.
func (mr *MockDataCollectorMockRecorder) GetEmploymentInfo(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmploymentInfo", reflect.TypeOf((*MockDataCollector)(nil).GetEmploymentInfo), ctx, actorId)
}

// GetFiCoinsRewardAggregate mocks base method.
func (m *MockDataCollector) GetFiCoinsRewardAggregate(ctx context.Context, actorId string, tier external.Tier, fromTime, toTime time.Time) (float32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFiCoinsRewardAggregate", ctx, actorId, tier, fromTime, toTime)
	ret0, _ := ret[0].(float32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFiCoinsRewardAggregate indicates an expected call of GetFiCoinsRewardAggregate.
func (mr *MockDataCollectorMockRecorder) GetFiCoinsRewardAggregate(ctx, actorId, tier, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFiCoinsRewardAggregate", reflect.TypeOf((*MockDataCollector)(nil).GetFiCoinsRewardAggregate), ctx, actorId, tier, fromTime, toTime)
}

// GetForexRefundAtTier mocks base method.
func (m *MockDataCollector) GetForexRefundAtTier(ctx context.Context, actorId string, tier external.Tier, fromTime, toTime time.Time) (*money.Money, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForexRefundAtTier", ctx, actorId, tier, fromTime, toTime)
	ret0, _ := ret[0].(*money.Money)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForexRefundAtTier indicates an expected call of GetForexRefundAtTier.
func (mr *MockDataCollectorMockRecorder) GetForexRefundAtTier(ctx, actorId, tier, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForexRefundAtTier", reflect.TypeOf((*MockDataCollector)(nil).GetForexRefundAtTier), ctx, actorId, tier, fromTime, toTime)
}

// GetHealthInsuranceDetails mocks base method.
func (m *MockDataCollector) GetHealthInsuranceDetails(ctx context.Context, actorId string) (*data_collector.HealthInsuranceDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHealthInsuranceDetails", ctx, actorId)
	ret0, _ := ret[0].(*data_collector.HealthInsuranceDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHealthInsuranceDetails indicates an expected call of GetHealthInsuranceDetails.
func (mr *MockDataCollectorMockRecorder) GetHealthInsuranceDetails(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHealthInsuranceDetails", reflect.TypeOf((*MockDataCollector)(nil).GetHealthInsuranceDetails), ctx, actorId)
}

// GetKycLevel mocks base method.
func (m *MockDataCollector) GetKycLevel(ctx context.Context, actorId string) (kyc.KYCLevel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKycLevel", ctx, actorId)
	ret0, _ := ret[0].(kyc.KYCLevel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKycLevel indicates an expected call of GetKycLevel.
func (mr *MockDataCollectorMockRecorder) GetKycLevel(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKycLevel", reflect.TypeOf((*MockDataCollector)(nil).GetKycLevel), ctx, actorId)
}

// GetMonthlyForexRefundAtTier mocks base method.
func (m *MockDataCollector) GetMonthlyForexRefundAtTier(ctx context.Context, actorId string, tier external.Tier, monthsToFetch []*data_collector.MonthYear) ([]*data_collector.MonthlyForexData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMonthlyForexRefundAtTier", ctx, actorId, tier, monthsToFetch)
	ret0, _ := ret[0].([]*data_collector.MonthlyForexData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMonthlyForexRefundAtTier indicates an expected call of GetMonthlyForexRefundAtTier.
func (mr *MockDataCollectorMockRecorder) GetMonthlyForexRefundAtTier(ctx, actorId, tier, monthsToFetch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonthlyForexRefundAtTier", reflect.TypeOf((*MockDataCollector)(nil).GetMonthlyForexRefundAtTier), ctx, actorId, tier, monthsToFetch)
}

// GetRewardHistoryForEarnedBenefits mocks base method.
func (m *MockDataCollector) GetRewardHistoryForEarnedBenefits(ctx context.Context, actorId string, tier external.Tier, monthsToFetch []*data_collector.MonthYear) ([]*data_collector.MonthlyRewardsData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardHistoryForEarnedBenefits", ctx, actorId, tier, monthsToFetch)
	ret0, _ := ret[0].([]*data_collector.MonthlyRewardsData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardHistoryForEarnedBenefits indicates an expected call of GetRewardHistoryForEarnedBenefits.
func (mr *MockDataCollectorMockRecorder) GetRewardHistoryForEarnedBenefits(ctx, actorId, tier, monthsToFetch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardHistoryForEarnedBenefits", reflect.TypeOf((*MockDataCollector)(nil).GetRewardHistoryForEarnedBenefits), ctx, actorId, tier, monthsToFetch)
}

// GetRewardsByActorId mocks base method.
func (m *MockDataCollector) GetRewardsByActorId(ctx context.Context, actorId string, tier external.Tier) (*rewards.RewardsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardsByActorId", ctx, actorId, tier)
	ret0, _ := ret[0].(*rewards.RewardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardsByActorId indicates an expected call of GetRewardsByActorId.
func (mr *MockDataCollectorMockRecorder) GetRewardsByActorId(ctx, actorId, tier interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardsByActorId", reflect.TypeOf((*MockDataCollector)(nil).GetRewardsByActorId), ctx, actorId, tier)
}

// GetRewardsDataForEarnedBenefits mocks base method.
func (m *MockDataCollector) GetRewardsDataForEarnedBenefits(ctx context.Context, actorId string, tier, higherTierToPitch external.Tier, timeRangesInTier []*tiering.TimeRange) (*data_collector.RewardsDataForEarnedBenefits, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardsDataForEarnedBenefits", ctx, actorId, tier, higherTierToPitch, timeRangesInTier)
	ret0, _ := ret[0].(*data_collector.RewardsDataForEarnedBenefits)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardsDataForEarnedBenefits indicates an expected call of GetRewardsDataForEarnedBenefits.
func (mr *MockDataCollectorMockRecorder) GetRewardsDataForEarnedBenefits(ctx, actorId, tier, higherTierToPitch, timeRangesInTier interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardsDataForEarnedBenefits", reflect.TypeOf((*MockDataCollector)(nil).GetRewardsDataForEarnedBenefits), ctx, actorId, tier, higherTierToPitch, timeRangesInTier)
}

// GetSalaryStatus mocks base method.
func (m *MockDataCollector) GetSalaryStatus(ctx context.Context, actorId string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSalaryStatus", ctx, actorId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSalaryStatus indicates an expected call of GetSalaryStatus.
func (mr *MockDataCollectorMockRecorder) GetSalaryStatus(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSalaryStatus", reflect.TypeOf((*MockDataCollector)(nil).GetSalaryStatus), ctx, actorId)
}

// GetTierTimeRange mocks base method.
func (m *MockDataCollector) GetTierTimeRange(ctx context.Context, actorId string, tier external.Tier) ([]*tiering.TimeRange, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTierTimeRange", ctx, actorId, tier)
	ret0, _ := ret[0].([]*tiering.TimeRange)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTierTimeRange indicates an expected call of GetTierTimeRange.
func (mr *MockDataCollectorMockRecorder) GetTierTimeRange(ctx, actorId, tier interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTierTimeRange", reflect.TypeOf((*MockDataCollector)(nil).GetTierTimeRange), ctx, actorId, tier)
}

// GetTieringEssentials mocks base method.
func (m *MockDataCollector) GetTieringEssentials(ctx context.Context, actorId string) (*helper.TieringFeEssentials, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTieringEssentials", ctx, actorId)
	ret0, _ := ret[0].(*helper.TieringFeEssentials)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTieringEssentials indicates an expected call of GetTieringEssentials.
func (mr *MockDataCollectorMockRecorder) GetTieringEssentials(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTieringEssentials", reflect.TypeOf((*MockDataCollector)(nil).GetTieringEssentials), ctx, actorId)
}
