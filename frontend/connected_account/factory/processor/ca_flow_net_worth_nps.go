package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/pkg/errors"

	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifigrpc"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	beUserPb "github.com/epifi/gamma/api/user"
	facModel "github.com/epifi/gamma/frontend/connected_account/factory/model"
)

type CAFlowNetWorthNpsProcessor struct {
	beUserClient    beUserPb.UsersClient
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder
}

func NewCAFlowNetWorthNpsProcessor(beUserClient beUserPb.UsersClient, deeplinkBuilder deeplink_builder.IDeeplinkBuilder) *CAFlowNetWorthNpsProcessor {
	return &CAFlowNetWorthNpsProcessor{
		beUserClient:    beUserClient,
		deeplinkBuilder: deeplinkBuilder,
	}
}

// nolint: dupl
func (c *CAFlowNetWorthNpsProcessor) GetDlForPostConsentApproval(ctx context.Context, caFlowRequestData *facModel.CaFlowRequestData) (*deeplinkPb.Deeplink, error) {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CA_POST_CONSENT_POLLING_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_CaPostConsentPollingScreenOptions{
			CaPostConsentPollingScreenOptions: &deeplinkPb.CAPostConsentPollingScreenOptions{
				Heading: &commontypes.Text{
					FontColor:    HeadingFontColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Linking your NPS account to Fi"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_XL,
					},
				},
				LoaderTexts: &deeplinkPb.CAPostConsentPollingScreenOptions_LoaderTexts{
					FetchAccText: &commontypes.Text{
						FontColor:    LoaderFontColor,
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Fetching your account details"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_S,
						},
					},
					ImportTxnsText: &commontypes.Text{
						FontColor:    LoaderFontColor,
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Importing your NPS data"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_S,
						},
					},
				},
				BgColor:              widgetPb.GetBlockBackgroundColour(PostConsenScreenBgColor),
				MaxRetryCountAllowed: int32(MaxRetryNetworkErrorCase),
				NetworkErrorDeeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_CA_POST_CONSENT_TERMINAL_STATE_SCREEN,
					ScreenOptions: GetCAConsentTerminalScreenOptions(NetworkFailureScreenHeading, NetworkFailureScreenSubHeading, ConnectAccountFailureScreenIcon, &deeplinkPb.Cta{
						Type:         deeplinkPb.Cta_DONE,
						Text:         NetworkFailureScreenCta,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
					}, false),
				},
			},
		},
	}, nil
}

func (c *CAFlowNetWorthNpsProcessor) GetDlForConsentFailure(ctx context.Context, caFlowRequestData *facModel.CaFlowRequestData) (*deeplinkPb.Deeplink, error) {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CA_POST_CONSENT_TERMINAL_STATE_SCREEN,
		ScreenOptions: GetCAConsentTerminalScreenOptions("Couldn't link NPS account to Fi",
			"There was an issue connecting your Demat accounts. Please try again.", ConnectAccountFailureScreenIcon, &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_DONE,
				Text:         ImportDataPendingScreenCta,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			}, false),
	}, nil
}

func (c *CAFlowNetWorthNpsProcessor) GetDlForDataPullSuccess(ctx context.Context, caFlowRequestData *facModel.CaFlowRequestData) (*deeplinkPb.Deeplink, error) {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CA_POST_CONSENT_TERMINAL_STATE_SCREEN,
		ScreenOptions: GetCAConsentTerminalScreenOptions("NPS account successfully linked to Fi",
			"You can now easily track your NPS", ConnectAccountSuccessScreenIcon, &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_DONE,
				Text:         ImportDataPendingScreenCta,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			}, true),
	}, nil
}

func (c *CAFlowNetWorthNpsProcessor) GetDlForDataPullFailure(ctx context.Context, caFlowRequestData *facModel.CaFlowRequestData) (*deeplinkPb.Deeplink, error) {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CA_POST_CONSENT_TERMINAL_STATE_SCREEN,
		ScreenOptions: GetCAConsentTerminalScreenOptions("NPS account successfully linked to Fi", ImportDataPendingScreenSubHeading, ConnectAccountSuccessScreenIcon, &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_DONE,
			Text:         ImportDataPendingScreenCta,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		}, false),
	}, nil
}

func (c *CAFlowNetWorthNpsProcessor) GetDlForExitScreen(ctx context.Context, caFlowRequestData *facModel.CaFlowRequestData) (*deeplinkPb.Deeplink, error) {
	switch {
	case isNoAccountDiscoveredFailureCase(caFlowRequestData.SdkExitOperation, caFlowRequestData.SdkExitReason):
		userResp, userErr := c.beUserClient.GetUser(ctx, &beUserPb.GetUserRequest{Identifier: &beUserPb.GetUserRequest_ActorId{ActorId: caFlowRequestData.ActorId}})
		if userErr = epifigrpc.RPCError(userResp, userErr); userErr != nil {
			return nil, errors.Wrapf(userErr, "Error getting user details for actor %s", caFlowRequestData.ActorId)
		}
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_CA_POST_CONSENT_TERMINAL_STATE_SCREEN,
			ScreenOptions: GetCAConsentTerminalScreenOptions(NetWorthNoNpsAccDiscoveredTitleText,
				fmt.Sprintf(NetWorthNoNpsAccDiscoveredSubTitleText, userResp.GetUser().GetProfile().GetPhoneNumber().ToStringInMobileFormat()),
				ConnectAccountFailureScreenIcon, &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_DONE,
					Text:         ImportDataPendingScreenCta,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				}, false),
		}, nil
	default:
		return nil, nil
	}
}
