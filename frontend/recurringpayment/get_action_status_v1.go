package recurringpayment

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	feRpPb "github.com/epifi/gamma/api/frontend/recurringpayment"
	beRpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) GetRecurringPaymentActionStatusV1(ctx context.Context, req *feRpPb.GetRecurringPaymentActionStatusV1Request) (*feRpPb.GetRecurringPaymentActionStatusV1Response, error) {
	var (
		res = &feRpPb.GetRecurringPaymentActionStatusV1Response{}
	)
	// helper function to return the response
	actionStatusResponse := func(status *rpc.Status, errorView *errors.ErrorView, dl *deeplink.Deeplink) (*feRpPb.GetRecurringPaymentActionStatusV1Response, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		res.Deeplink = dl
		return res, nil
	}

	actionStatusRes, actionStatusResErr := s.recurringPaymentClient.GetActionStatusV1(ctx, &beRpPb.GetActionStatusV1Request{
		ClientRequestId: req.GetClientRequestId(),
		PollAttempt:     req.GetPollAttempt(),
	})

	if err := epifigrpc.RPCError(actionStatusRes, actionStatusResErr); err != nil {
		logger.Error(ctx, "failed to fetch the action status for the given client request id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return actionStatusResponse(rpc.StatusInternal(), genericErrorView, nil)
	}
	return actionStatusResponse(rpc.StatusOk(), nil, actionStatusRes.GetNextStepDeeplink())
}
