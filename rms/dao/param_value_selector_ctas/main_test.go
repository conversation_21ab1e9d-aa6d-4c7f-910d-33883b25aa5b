package param_value_selector_ctas_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/rms/test"
)

var rts *test.RmsDaoTestSuite

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	suite, _, _, teardown := test.NewRmsDaoTestSuite(m)
	rts = suite
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
