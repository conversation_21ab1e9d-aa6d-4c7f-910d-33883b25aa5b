// Code generated by MockGen. DO NOT EDIT.
// Source: layout.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	layout "github.com/epifi/gamma/card/provisioning/layout"
	gomock "github.com/golang/mock/gomock"
)

// MockILayoutPriorityProvider is a mock of ILayoutPriorityProvider interface.
type MockILayoutPriorityProvider struct {
	ctrl     *gomock.Controller
	recorder *MockILayoutPriorityProviderMockRecorder
}

// MockILayoutPriorityProviderMockRecorder is the mock recorder for MockILayoutPriorityProvider.
type MockILayoutPriorityProviderMockRecorder struct {
	mock *MockILayoutPriorityProvider
}

// NewMockILayoutPriorityProvider creates a new mock instance.
func NewMockILayoutPriorityProvider(ctrl *gomock.Controller) *MockILayoutPriorityProvider {
	mock := &MockILayoutPriorityProvider{ctrl: ctrl}
	mock.recorder = &MockILayoutPriorityProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILayoutPriorityProvider) EXPECT() *MockILayoutPriorityProviderMockRecorder {
	return m.recorder
}

// GetEligibleLayoutsPriority mocks base method.
func (m *MockILayoutPriorityProvider) GetEligibleLayoutsPriority(ctx context.Context, userData *layout.CardUserData) (map[string]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEligibleLayoutsPriority", ctx, userData)
	ret0, _ := ret[0].(map[string]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEligibleLayoutsPriority indicates an expected call of GetEligibleLayoutsPriority.
func (mr *MockILayoutPriorityProviderMockRecorder) GetEligibleLayoutsPriority(ctx, userData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEligibleLayoutsPriority", reflect.TypeOf((*MockILayoutPriorityProvider)(nil).GetEligibleLayoutsPriority), ctx, userData)
}
