// nolint:protogetter
package provisioning

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	authPb "github.com/epifi/gamma/api/auth"
	authClientMock "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/bankcust"
	mocks2 "github.com/epifi/gamma/api/bankcust/mocks"
	cardPb "github.com/epifi/gamma/api/card"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	vgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	vgCardMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/card/mocks"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	"github.com/epifi/gamma/card/control"
	cardDaoMock "github.com/epifi/gamma/card/dao/mocks"
	daoMock "github.com/epifi/gamma/card/test/mocks"
	eventsMock "github.com/epifi/be-common/pkg/events/mocks"
)

func TestService_CardCvvEnquiry(t *testing.T) {

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockCardDao := cardDaoMock.NewMockCardDao(ctr)
	mockAuthClient := authClientMock.NewMockAuthClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockBcClient := mocks2.NewMockBankCustomerServiceClient(ctr)
	mockVgCardClient := vgCardMocks.NewMockCardProvisioningClient(ctr)
	mockEventsBroker := eventsMock.NewMockBroker(ctr)
	mockCardActionAttemptDao := daoMock.NewMockCardActionAttemptDao(ctr)

	mockEventsBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockCardActionAttemptDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	svc := &Service{
		cardDao:              mockCardDao,
		ccReqDao:             nil,
		caReqDao:             nil,
		pinDao:               nil,
		cardClient:           mockVgCardClient,
		creationPublisher:    nil,
		pinSetEventPublisher: nil,
		actorClient:          mockActorClient,
		authClient:           mockAuthClient,
		bcClient:             mockBcClient,
		eventBroker:          mockEventsBroker,
		cardActionAttemptDao: mockCardActionAttemptDao,
		vgPciCardClient:      mockVgCardClient,
		dynamicConf:          dynamicConf,
	}

	internalErrResp := func(debugMsg string) *rpc.Status {
		rpcStatus := rpc.StatusInternal()
		rpcStatus.DebugMessage = debugMsg
		return rpcStatus
	}

	type mockGetCardById struct {
		enable    bool
		id        string
		cardModel *cardPb.Card
		err       error
	}

	type mockGetDeviceAuth struct {
		enable bool
		req    *authPb.GetDeviceAuthRequest
		res    *authPb.GetDeviceAuthResponse
		err    error
	}

	type mockGetEntityDetailsByActorId struct {
		enable bool
		req    *actorPb.GetEntityDetailsByActorIdRequest
		res    *actorPb.GetEntityDetailsByActorIdResponse
		err    error
	}

	type mockGetBankCustomerDetails struct {
		enable bool
		req    *bankcust.GetBankCustomerRequest
		res    *bankcust.GetBankCustomerResponse
		err    error
	}

	type mockVgCardCvvEnquiry struct {
		enable bool
		req    *vgPb.CardCVVEnquiryRequest
		res    *vgPb.CardCVVEnquiryResponse
		err    error
	}

	tests := []struct {
		name                          string
		req                           *cpPb.GetCardDetailsWithCvvRequest
		want                          *cpPb.GetCardDetailsWithCvvResponse
		mockGetCardById               mockGetCardById
		mockGetDeviceAuth             mockGetDeviceAuth
		mockGetEntityDetailsByActorId mockGetEntityDetailsByActorId
		mockGetBankCustomerDetails    mockGetBankCustomerDetails
		mockVgCardCvvEnquiry          mockVgCardCvvEnquiry
		wantErr                       bool
	}{
		{
			name: "successfully fetched card details from vendor",
			req: &cpPb.GetCardDetailsWithCvvRequest{
				CardId: "card-id",
			},
			want: &cpPb.GetCardDetailsWithCvvResponse{
				Status: rpc.StatusOk(),
				CardInfo: &cardPb.BasicCardInfo{
					CardNumber:       "************",
					Expiry:           "1230",
					Cvv:              "123",
					MaskedCardNumber: "1234XXXX9012",
				},
			},
			mockGetCardById: mockGetCardById{
				enable: true,
				id:     "card-id",
				cardModel: &cardPb.Card{
					Id:             "card-id",
					ActorId:        "actor-id",
					State:          cardPb.CardState_CREATED,
					Type:           cardPb.CardType_DEBIT,
					Form:           cardPb.CardForm_DIGITAL,
					NetworkType:    cardPb.CardNetworkType_VISA,
					IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					IssueType:      cardPb.CardIssueType_FRESH,
					BankIdentifier: "VISA************FED",
					CardCategory:   "",
					BasicInfo: &cardPb.BasicCardInfo{
						MaskedCardNumber: "1234XXXX9012",
					},
					Controls:         nil,
					GroupId:          "",
					SavingsAccountId: "savings-account-id",
				},
				err: nil,
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-id",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status:        rpc.StatusOk(),
					ActorId:       "actor-id",
					UserProfileId: "user-profile",
					DeviceToken:   "device-token",
					Device: &commontypes.Device{
						DeviceId: "device-id",
					},
					VendorDeviceId: "device-id",
				},
				err: nil,
			},
			mockGetEntityDetailsByActorId: mockGetEntityDetailsByActorId{
				enable: true,
				req: &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "user-id",
					MobileNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: *********,
					},
				},
				err: nil,
			},
			mockGetBankCustomerDetails: mockGetBankCustomerDetails{
				enable: true,
				req: &bankcust.GetBankCustomerRequest{
					Identifier: &bankcust.GetBankCustomerRequest_UserId{
						UserId: "user-id",
					},
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
				},
				res: &bankcust.GetBankCustomerResponse{
					BankCustomer: &bankcust.BankCustomer{
						VendorCustomerId: "customer-id",
						Status:           bankcust.Status_STATUS_ACTIVE,
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockVgCardCvvEnquiry: mockVgCardCvvEnquiry{
				enable: true,
				req: &vgPb.CardCVVEnquiryRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &header.Auth{
						DeviceId:      "device-id",
						DeviceToken:   "device-token",
						EncryptedPin:  "",
						UserProfileId: "user-profile",
						CustomerId:    "customer-id",
					},
					VendorCardId:           "VISA************FED",
					DeviceBiometricEnabled: false,
					RequestId:              "request-id",
					CustomerId:             "customer-id",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: *********,
					},
				},
				res: &vgPb.CardCVVEnquiryResponse{
					Status: rpc.StatusOk(),
					CardInfo: &vgPb.BasicCardInfo{
						CardNumber: "************",
						Expiry:     "1230",
						Cvv:        "123",
					},
				},
				err: nil,
			},
			wantErr: false,
		},
		{
			name: "failure on card-id not found",
			req: &cpPb.GetCardDetailsWithCvvRequest{
				CardId: "card-id",
			},
			want: &cpPb.GetCardDetailsWithCvvResponse{
				Status:             internalErrResp("Invalid card id: card-id"),
				InternalStatusCode: control.InternalCardResponseStatusCode,
			},
			wantErr: false,
			mockGetCardById: mockGetCardById{
				enable: true,
				id:     "card-id",
				cardModel: &cardPb.Card{
					Id:               "card-id",
					ActorId:          "actor-id",
					State:            cardPb.CardState_CREATED,
					Type:             cardPb.CardType_DEBIT,
					Form:             cardPb.CardForm_DIGITAL,
					NetworkType:      cardPb.CardNetworkType_VISA,
					IssuerBank:       commonvgpb.Vendor_FEDERAL_BANK,
					IssueType:        cardPb.CardIssueType_FRESH,
					BankIdentifier:   "VISA************FED",
					CardCategory:     "",
					BasicInfo:        nil,
					Controls:         nil,
					GroupId:          "",
					SavingsAccountId: "savings-account-id",
				},
				err: gorm.ErrRecordNotFound,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetCardById.enable {
				mockCardDao.EXPECT().GetByID(context.Background(), tt.mockGetCardById.id).
					Return(tt.mockGetCardById.cardModel, tt.mockGetCardById.err)
			}

			if tt.mockGetDeviceAuth.enable {
				mockAuthClient.EXPECT().GetDeviceAuth(context.Background(), tt.mockGetDeviceAuth.req).
					Return(tt.mockGetDeviceAuth.res, tt.mockGetDeviceAuth.err)
			}

			if tt.mockGetEntityDetailsByActorId.enable {
				mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), tt.mockGetEntityDetailsByActorId.req).
					Return(tt.mockGetEntityDetailsByActorId.res, tt.mockGetEntityDetailsByActorId.err)
			}

			if tt.mockGetBankCustomerDetails.enable {
				mockBcClient.EXPECT().GetBankCustomer(context.Background(), tt.mockGetBankCustomerDetails.req).
					Return(tt.mockGetBankCustomerDetails.res, tt.mockGetBankCustomerDetails.err)
			}

			if tt.mockVgCardCvvEnquiry.enable {
				mockVgCardClient.EXPECT().CardCVVEnquiry(context.Background(), newCardCvvEnquiryVgMatch(tt.mockVgCardCvvEnquiry.req)).
					Return(tt.mockVgCardCvvEnquiry.res, tt.mockVgCardCvvEnquiry.err)
			}

			got, err := svc.GetCardDetailsWithCvv(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CardCvvEnquiry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("vg.CardCvvEnquiry() got = %v, want %v", got, tt.want)
			}
		})
	}
}

type CardCvvEnquiryVgMatch struct {
	want *vgPb.CardCVVEnquiryRequest
}

func newCardCvvEnquiryVgMatch(want *vgPb.CardCVVEnquiryRequest) *CardCvvEnquiryVgMatch {
	return &CardCvvEnquiryVgMatch{
		want: want,
	}
}

func (ce *CardCvvEnquiryVgMatch) Matches(x interface{}) bool {
	got, ok := x.(*vgPb.CardCVVEnquiryRequest)
	if !ok {
		return false
	}

	ce.want.RequestId = got.RequestId
	return reflect.DeepEqual(ce.want, got)
}

func (ce *CardCvvEnquiryVgMatch) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}
