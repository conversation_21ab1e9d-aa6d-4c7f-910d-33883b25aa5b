// nolint: dupl
package comms

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/card/helper"
)

type TxnTypeNotSupportedRule struct {
	helper *helper.CommsHelper
}

func NewTxnTypeNotSupportedRule(helper *helper.CommsHelper) *TxnTypeNotSupportedRule {
	return &TxnTypeNotSupportedRule{
		helper: helper,
	}
}

func (s *TxnTypeNotSupportedRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {

	if data.GetSwitchNotificationResponse() == cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_TYPE_NOT_SUPPORTED {
		// SMS Comms
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_DebitCardTransactionTypeNotSupportedSmsOption{
						DebitCardTransactionTypeNotSupportedSmsOption: &commsPb.DebitCardTransactionTypeNotSupportedSmsOption{
							SmsType: commsPb.SmsType_DEBIT_CARD_TRANSACTION_TYPE_NOT_SUPPORTED,
							Option: &commsPb.DebitCardTransactionTypeNotSupportedSmsOption_DebitCardTransactionTypeNotSupportedSmsOptionV1{
								DebitCardTransactionTypeNotSupportedSmsOptionV1: &commsPb.DebitCardTransactionTypeNotSupportedSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									DownloadLink:    "https://fi.onelink.me/FiMony/yspc3mge",
								},
							},
						},
					},
				},
			},
		})

		pnTitle, pnBody, err := s.helper.GetPnContentForSwitch(data.GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, &dlPb.Deeplink{
				Screen: dlPb.Screen_CARD_USAGE_SCREEN,
				ScreenOptions: &dlPb.Deeplink_CardUsageScreenOptions{
					CardUsageScreenOptions: &dlPb.CardUsageScreenOptions{
						CardId: data.GetCardId(),
					},
				},
			}), helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody))
		}
	}
	return
}
