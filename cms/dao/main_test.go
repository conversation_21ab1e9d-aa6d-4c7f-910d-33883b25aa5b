package dao

import (
	"os"
	"testing"

	"github.com/epifi/gamma/cms/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	cfg, db, teardown := test.InitTestServerV2(true)
	pdts = newProductDaoTestSuite(cfg, db)
	sdts = newSkuDaoTestSuite(cfg, db)
	cdts = newCouponDaoTestSuite(cfg, db)
	rdts = newRedemptionDaoTestSuite(cfg, db)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
