//go:build wireinject
// +build wireinject

package wire

import (
	"net/http"

	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/cfg"
	dsig "github.com/epifi/be-common/pkg/goxmldsig"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	merchantresolutionpb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	vgnamecategoriser "github.com/epifi/gamma/api/vendorgateway/namecheck/merchantnamecategoriser"
	"github.com/epifi/gamma/pkg/vendorapi/inhouse/merchantnamecategoriser"
	"github.com/epifi/gamma/pkg/vendorapi/inhouse/merchantresolution"
	"github.com/epifi/gamma/pkg/vendorapi/inhouse/providers"
)

type ConfWithApplication interface {
	Application() *cfg.Application
}

func nilSigningContextProvider() *dsig.SigningContext {
	return nil
}

var MerchantResolutionPkgWireSet = wire.NewSet(
	InitializeMerchantResolutionService,
	wire.Bind(new(merchantresolutionpb.MerchantResolutionServer), new(*merchantresolution.Service)),
)

var MerchantNameCategoriserPkgWireSet = wire.NewSet(
	InitializeMerchantNameCategoriserService,
	wire.Bind(new(vgnamecategoriser.MerchantNameCategoriserServer), new(*merchantnamecategoriser.Service)),
)

func InitializeMerchantResolutionService(gconf *genconf.Config, env string) *merchantresolution.Service {
	wire.Build(
		SecureHttpClientNilSignCtxWireSet,
		merchantresolution.NewService,
		vendorapi.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapi.HttpDoer), new(*http.Client)),
	)
	return &merchantresolution.Service{}
}

func InitializeMerchantNameCategoriserService(gconf *genconf.Config, env string) *merchantnamecategoriser.Service {
	wire.Build(
		SecureHttpClientNilSignCtxWireSet,
		merchantnamecategoriser.NewService,
		vendorapi.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapi.HttpDoer), new(*http.Client)),
	)
	return &merchantnamecategoriser.Service{}
}

func EnvFromDynConfProvider(gconf ConfWithApplication) string {
	return gconf.Application().Environment
}

var SecureHttpClientNilSignCtxWireSet = wire.NewSet(providers.SecureHttpClientProvider, nilSigningContextProvider)
