package request

import (
	"google.golang.org/protobuf/proto"

	collapserpb "github.com/epifi/be-common/api/pkg/epifigrpc/collapser"
)

type collapsibleReq interface {
	GetCollapsibleReqParams() *collapserpb.CollapsibleReqParams
}

type collapsibleResp interface {
	GetCollapsibleRespParams() *collapserpb.CollapsibleRespParams
}

// GetCollapsibleReqParams returns the collapsible request parameters from
// the request message.
// If the proto definition for the request proto has the field collapsible_req_params of type *collapserpb.CollapsibleRespParams,
// then this function will return the value of that field. If not found, it will return empty params with zeroth values as default behavior.
func GetCollapsibleReqParams(req proto.Message) *collapserpb.CollapsibleReqParams {

	if req, ok := req.(collapsibleReq); ok {
		return req.GetCollapsibleReqParams()
	}
	// return empty params with zeroth values as default behavior.
	return &collapserpb.CollapsibleReqParams{}
}

// GetCollapsibleRespParams returns the collapsible response parameters from
// the response message.
// If the proto definition for the response proto has the field collapsible_resp_params of type *collapserpb.CollapsibleRespParams,
// then this function will return the value of that field. If not found, it will return empty params with zeroth values as default behavior.
func GetCollapsibleRespParams(resp proto.Message) *collapserpb.CollapsibleRespParams {
	if resp, ok := resp.(collapsibleResp); ok {
		return resp.GetCollapsibleRespParams()
	}
	return &collapserpb.CollapsibleRespParams{}
}
