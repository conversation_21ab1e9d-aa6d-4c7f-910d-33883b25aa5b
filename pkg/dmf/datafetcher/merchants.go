//go:generate mockgen -source=merchants.go -destination=./mocks/mock_merchants.go package=mocks
package datafetcher

import (
	"context"
	"fmt"

	"github.com/google/wire"

	merchantPb "github.com/epifi/gamma/api/merchant"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

var IMerchantsWireSet = wire.NewSet(NewMerchantsImpl, wire.Bind(new(IMerchants), new(*MerchantsImpl)))

type IMerchants interface {
	GetMerchants(ctx context.Context, merchantIds []string) (map[string]*merchantPb.Merchant, error)
}

type MerchantsImpl struct {
	merchantClient merchantPb.MerchantServiceClient
}

func NewMerchantsImpl(merchantClient merchantPb.MerchantServiceClient) *MerchantsImpl {
	return &MerchantsImpl{merchantClient: merchantClient}
}

func (m *MerchantsImpl) GetMerchants(ctx context.Context, merchantIds []string) (map[string]*merchantPb.Merchant, error) {
	res, err := m.merchantClient.GetMerchants(ctx, &merchantPb.GetMerchantsRequest{
		Identifier: &merchantPb.GetMerchantsRequest_MerchantIdentifier_{
			MerchantIdentifier: &merchantPb.GetMerchantsRequest_MerchantIdentifier{MerchantIds: merchantIds},
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get merchant details: %w", rpcErr)
	}

	merchantMap := make(map[string]*merchantPb.Merchant)
	for _, merchant := range res.GetMerchants() {
		if merchant.GetName() == "" {
			return nil, fmt.Errorf("empty merchant name resolved by merchant service for id %s", merchant.GetId())
		}
		merchantMap[merchant.GetId()] = merchant
	}

	return merchantMap, nil
}
