// Code generated by MockGen. DO NOT EDIT.
// Source: gst.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	internationalfundtransfer "github.com/epifi/gamma/pkg/internationalfundtransfer"
	gomock "github.com/golang/mock/gomock"
	money "google.golang.org/genproto/googleapis/type/money"
)

// MockGSTCalculator is a mock of GSTCalculator interface.
type MockGSTCalculator struct {
	ctrl     *gomock.Controller
	recorder *MockGSTCalculatorMockRecorder
}

// MockGSTCalculatorMockRecorder is the mock recorder for MockGSTCalculator.
type MockGSTCalculatorMockRecorder struct {
	mock *MockGSTCalculator
}

// NewMockGSTCalculator creates a new mock instance.
func NewMockGSTCalculator(ctrl *gomock.Controller) *MockGSTCalculator {
	mock := &MockGSTCalculator{ctrl: ctrl}
	mock.recorder = &MockGSTCalculatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGSTCalculator) EXPECT() *MockGSTCalculatorMockRecorder {
	return m.recorder
}

// CalculateGST mocks base method.
func (m *MockGSTCalculator) CalculateGST(amount *money.Money) (*internationalfundtransfer.GST, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateGST", amount)
	ret0, _ := ret[0].(*internationalfundtransfer.GST)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateGST indicates an expected call of CalculateGST.
func (mr *MockGSTCalculatorMockRecorder) CalculateGST(amount interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateGST", reflect.TypeOf((*MockGSTCalculator)(nil).CalculateGST), amount)
}
