package employment

import (
	"testing"

	"github.com/epifi/gamma/api/frontend/account/screening"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
)

func TestGetIncomeStringFromRange(t *testing.T) {
	type args struct {
		salaryRange *userPb.SalaryRange
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "25l - 1 cr",
			args: args{salaryRange: &userPb.SalaryRange{
				MinValue: 2500000,
				MaxValue: ********,
			}},
			want: "₹25 Lakhs -1 Crore",
		},
		{
			name: "0-1l",
			args: args{salaryRange: &userPb.SalaryRange{
				MinValue: 0,
				MaxValue: 100000,
			}},
			want: "₹0-1 Lakhs",
		},
		{
			name: "more than 1 cr",
			args: args{salaryRange: &userPb.SalaryRange{
				MinValue: ********,
				MaxValue: ********,
			}},
			want: "Above ₹1 Crore",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetIncomeStringFromRange(tt.args.salaryRange); got != tt.want {
				t.Errorf("GetIncomeStringFromRange() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetIncomeStringFromAnnualSalary(t *testing.T) {
	tests := []struct {
		name   string
		salary *screening.AnnualSalary
		want   string
	}{
		{
			name: "successfully formed without alternate currency",
			salary: &screening.AnnualSalary{
				Range: &screening.AnnualSalaryRange{
					MinValue:     100000,
					MaxValue:     500000,
					CurrencyCode: types.CurrencyCode_INR,
				},
			},
			want: "₹1-5 Lakhs",
		},
		{
			name: "successfully formed with alternate currency",
			salary: &screening.AnnualSalary{
				Range: &screening.AnnualSalaryRange{
					MinValue:     500000,
					MaxValue:     1000000,
					CurrencyCode: types.CurrencyCode_INR,
					AlternateDisplayed: &screening.AnnualSalaryRange_AlternateDisplayed{
						MinVal:       22000,
						MaxVal:       44000,
						CurrencyCode: types.CurrencyCode_AED,
					},
				},
			},
			want: "22,000 - 44,000 AED (₹5L-₹10L)",
		},
		{
			name: "alernate currency below x moneys",
			salary: &screening.AnnualSalary{
				Range: &screening.AnnualSalaryRange{
					MinValue:     0,
					MaxValue:     100000,
					CurrencyCode: types.CurrencyCode_INR,
					AlternateDisplayed: &screening.AnnualSalaryRange_AlternateDisplayed{
						MinVal:       0,
						MaxVal:       4000,
						CurrencyCode: types.CurrencyCode_AED,
					},
				},
			},
			want: "Less than 4,000 AED (Below ₹1L)",
		},
		{
			name: "alernate currency above y moneys",
			salary: &screening.AnnualSalary{
				Range: &screening.AnnualSalaryRange{
					MinValue:     ********,
					MaxValue:     ********,
					CurrencyCode: types.CurrencyCode_INR,
					AlternateDisplayed: &screening.AnnualSalaryRange_AlternateDisplayed{
						MinVal:       440000,
						MaxVal:       1000000,
						CurrencyCode: types.CurrencyCode_AED,
					},
				},
			},
			want: "440,000 + AED (Above ₹1Cr)",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetIncomeStringFromAnnualSalary(tt.salary); got != tt.want {
				t.Errorf("GetIncomeStringFromAnnualSalary() = %v, want %v", got, tt.want)
			}
		})
	}
}
