// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/goxmldsig"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/storage"
	"github.com/epifi/be-common/pkg/vendorapi"
	genconf2 "github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	http2 "github.com/epifi/gamma/api/vendors/http"
	"github.com/epifi/gamma/pkg/llm"
	"github.com/epifi/gamma/pkg/llm/config/genconf"
	"github.com/epifi/gamma/pkg/llm/wire/types"
	"github.com/google/wire"
	"net/http"
)

// Injectors from wire.go:

func InitialiseLLMHandler(gconf *genconf.Config) *llm.LLMHandlerImpl {
	httpRequestHandler := HttpRequestHandlerProvider(gconf)
	llmCacheStorage := LLMCacheProvider(gconf)
	uuidGenerator := idgen.NewUuidGenerator()
	llmHandlerImpl := llm.NewLLMHandlerImpl(httpRequestHandler, gconf, llmCacheStorage, uuidGenerator)
	return llmHandlerImpl
}

func HttpRequestHandlerProvider(gconf *genconf.Config) *vendorapi.HTTPRequestHandler {
	config := VendorApiConfProvider(gconf)
	string2 := EnvProvider(gconf)
	client := SecureHttpClientProvider(config, string2)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, config, string2)
	return httpRequestHandler
}

// wire.go:

var SecureHttpClientNilSignCtxWireSet = wire.NewSet(SecureHttpClientProvider, nilSigningContextProvider)

func EnvProvider(gconf *genconf.Config) string {
	return gconf.Environment()
}

func VendorApiConfProvider(gconf *genconf.Config) *genconf2.Config {
	return gconf.VendorApiConf()
}

func LLMCacheProvider(gconf *genconf.Config) types.LLMCacheStorage {
	llmRedisClient := storage.NewRedisClientFromConfig(gconf.RedisOptions(), false)
	redisStore := cache.NewRedisCacheStorage(llmRedisClient)
	return cache.NewRedisCacheStorageWithHystrix(redisStore, gconf.RedisOptions().HystrixCommand)
}

func nilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func getHttpClient(gconf *genconf2.Config, env string, insecure ...bool) *http.Client {
	return http2.NewHttpClient(gconf.HttpClientConfig())
}

func SecureHttpClientProvider(gconf *genconf2.Config, env string) *http.Client {
	return getHttpClient(gconf, env, false)
}
