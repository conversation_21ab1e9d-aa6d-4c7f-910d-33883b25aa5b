package calculator

import (
	"context"
	"strconv"

	"go.uber.org/zap"

	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/pkg/deposit/interest_rates"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (d *DepositCalculator) GetInterestRate(ctx context.Context, account *depositPb.DepositAccount) (float64, error) {

	if !d.HasDepositRenewedAtLeastOnce(ctx, account) {
		return strconv.ParseFloat(account.GetInterestRate(), 64)
	}

	lastRenewedDate, err := d.GetMostRecentRenewedDate(ctx, account)
	if err != nil {
		logger.Info(ctx, "error in GetLastRenewedDate", zap.String(logger.DEPOSIT_ACCOUNT_ID, account.GetId()), zap.Error(err))
		return strconv.ParseFloat(account.GetInterestRate(), 64)
	}

	res, intErr := d.depositClient.GetInterestRatesForActor(ctx, &depositPb.GetInterestRatesForActorRequest{
		Vendor:      account.GetPartnerBank(),
		ActorId:     account.GetActorId(),
		DepositType: account.GetType(),
		// When a deposit auto-renews, the interest rate on the date of renewal is used.
		Date: lastRenewedDate,
	})
	if rpcErr := epifigrpc.RPCError(res, intErr); rpcErr != nil {
		logger.Info(ctx, "error in GetLastRenewedDate", zap.String(logger.DEPOSIT_ACCOUNT_ID, account.GetId()), zap.Error(err))
		return strconv.ParseFloat(account.GetInterestRate(), 64)
	}
	interestRate, calErr := interest_rates.GetInterestRateForTerm(ctx, account.GetTerm(), res.GetInterestRates())
	if calErr != nil {
		logger.Info(ctx, "error in GetInterestRateForTerm", zap.String(logger.DEPOSIT_ACCOUNT_ID, account.GetId()), zap.Error(calErr))
		return strconv.ParseFloat(account.GetInterestRate(), 64)
	}
	return strconv.ParseFloat(interestRate, 64)
}
