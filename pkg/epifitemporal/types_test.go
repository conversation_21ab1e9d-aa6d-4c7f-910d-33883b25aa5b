package epifitemporal_test

import (
	"testing"

	"github.com/epifi/be-common/pkg/epifitemporal"
)

func TestWorker_GetDirectory(t *testing.T) {
	t.<PERSON>llel()
	tests := []struct {
		name string
		w    epifitemporal.Worker
		want string
	}{
		{
			name: "replace - with empty string",
			w:    epifitemporal.Worker("test-worker"),
			want: "testworker",
		},
		{
			name: "convert to lower",
			w:    epifitemporal.Worker("TEST-worker"),
			want: "testworker",
		},
		{
			name: "return as it is if in appropriate format",
			w:    epifitemporal.Worker("worker"),
			want: "worker",
		},
	}
	for _, tt := range tests {
		// tt is mutated on each iteration, so keep a copy of it
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.<PERSON>()
			if got := tt.w.GetDirectory(); got != tt.want {
				t.Errorf("GetDirectory() = %v, want %v", got, tt.want)
			}
		})
	}
}
