package parser

import "github.com/epifi/be-common/pkg/epifitemporal"

const (
	TransactionBackfill  epifitemporal.Workflow = "TransactionBackfill"
	RemitterInfoBackfill epifitemporal.Workflow = "RemitterInfoBackfill"
)

const (
	RemitterBackfillTaskQueue epifitemporal.TaskQueue = "remitter-backfill"
)

const (
	ParseRawTransactionDetails              epifitemporal.Activity = "ParseRawTransactionDetails"
	EnrichTxnsWithParsedData                epifitemporal.Activity = "EnrichTxnsWithParsedData"
	UpdateTxnsAndOrdersInDb                 epifitemporal.Activity = "UpdateTxnsAndOrdersInDb"
	PublishAndWriteToS3EnrichTxns           epifitemporal.Activity = "PublishAndWriteToS3EnrichTxns"
	ValidateRecursiveRetryForWorkflow       epifitemporal.Activity = "ValidateRecursiveRetryForWorkflow"
	WriteOldEnrichedDataToS3                epifitemporal.Activity = "WriteOldEnrichedDataToS3"
	ValidateUpiAddressAndCreateEnrichedData epifitemporal.Activity = "ValidateUpiAddressAndCreateEnrichedData"
	EnrichTxnsWithRemitterDetails           epifitemporal.Activity = "EnrichTxnsWithRemitterDetails"
	FetchRemitterInfoAndCreateEnrichedData  epifitemporal.Activity = "FetchRemitterInfoAndCreateEnrichedData"
)
