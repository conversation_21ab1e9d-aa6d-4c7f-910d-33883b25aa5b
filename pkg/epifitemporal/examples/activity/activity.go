package activity

import (
	"context"

	"go.temporal.io/sdk/activity"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
)

type Processor struct {
}

func (p *Processor) DomainActivityA(ctx context.Context, req *celestialActivityPb.Request) (*celestialActivityPb.Response, error) {
	activity.GetLogger(ctx).Info("inside  DomainActivityA")
	return &celestialActivityPb.Response{}, nil
}

func (p *Processor) DomainActivityB(ctx context.Context, req *celestialActivityPb.Request) (*celestialActivityPb.Response, error) {
	activity.GetLogger(ctx).Info("inside  DomainActivityB")
	return &celestialActivityPb.Response{}, nil
}

func (p *Processor) DomainActivityC(ctx context.Context, req *celestialActivityPb.Request) (*celestialActivityPb.Response, error) {
	activity.GetLogger(ctx).Info("inside  DomainActivityC")
	return &celestialActivityPb.Response{}, nil
}
