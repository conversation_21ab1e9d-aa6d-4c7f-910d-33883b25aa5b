package dao

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

var (
	// deleted task A
	task1 = &DoOnceTask{
		TaskName:      "actor 1 waiting for customer creation since 30 minutes",
		DeletedAtUnix: 11,
		CreatedAt:     time.Time{},
	}

	// deleted task A
	task2 = &DoOnceTask{
		TaskName:      "actor 1 waiting for customer creation since 30 minutes",
		DeletedAtUnix: 22,
		CreatedAt:     time.Time{},
	}

	// deleted task B
	task3 = &DoOnceTask{
		TaskName:      "task B",
		DeletedAtUnix: 22,
		CreatedAt:     time.Time{},
	}

	// active task B
	task4 = &DoOnceTask{
		TaskName:  "task B",
		CreatedAt: time.Time{},
	}
)

func TestDoOnceCRDB_Create(t *testing.T) {
	dao := &DoOnceCRDB{
		DB: db,
	}
	pkgTest.PrepareScopedDatabase(t, dbName, db, []string{"do_once_tasks"})

	// add fixtures
	dao.DB.Create(task1)
	dao.DB.Create(task2)

	type args struct {
		taskName string
	}
	tests := []struct {
		name     string
		args     args
		wantErr  string
		wantTask *DoOnceTask
	}{
		{
			name: "insert task successfully",
			args: args{
				taskName: task1.TaskName,
			},
			wantTask: &DoOnceTask{
				TaskName:      task1.TaskName,
				DeletedAtUnix: 0,
			},
		},
		{
			name: "duplicate error",
			args: args{
				taskName: task1.TaskName,
			},
			wantErr: epifierrors.ErrDuplicateEntry.Error(),
		},
		{
			name: "insert fresh task successfully",
			args: args{
				taskName: "fresh task 123",
			},
			wantTask: &DoOnceTask{
				TaskName:      "fresh task 123",
				DeletedAtUnix: 0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := dao.Create(context.Background(), tt.args.taskName)
			if (err != nil) != (tt.wantErr != "") {
				t.Errorf(" got error: %v\nwant error: %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr {
					t.Errorf(" got error: %v\nwant error: %v", err, tt.wantErr)
				}
				return
			}

			if tt.wantTask == nil {
				return
			}
			gotTask, err := dao.Get(context.Background(), tt.args.taskName)
			if err != nil {
				t.Errorf("did not want error, got: %v\n", err)
				return
			}
			tt.wantTask.CreatedAt = gotTask.CreatedAt
			if !reflect.DeepEqual(gotTask, tt.wantTask) {
				t.Errorf("\n got: %v\nwant: %v", gotTask, tt.wantTask)
			}
		})
	}
}

func TestDoOnceCRDB_Get(t *testing.T) {
	dao := &DoOnceCRDB{
		DB: db,
	}
	pkgTest.PrepareScopedDatabase(t, dbName, db, []string{"do_once_tasks"})
	// add fixtures
	dao.DB.Create(task1)
	dao.DB.Create(task2)
	dao.DB.Create(task3)
	dao.DB.Create(task4)

	type args struct {
		taskName string
	}
	tests := []struct {
		name     string
		args     args
		wantErr  string
		wantTask *DoOnceTask
	}{
		{
			name: "get successfully",
			args: args{
				taskName: task3.TaskName,
			},
			wantTask: &DoOnceTask{
				TaskName:      task3.TaskName,
				DeletedAtUnix: 0,
			},
		},
		{
			name: "task does not exist",
			args: args{
				taskName: "task name not found",
			},
			wantErr: epifierrors.ErrRecordNotFound.Error(),
		},
		{
			name: "all tasks soft deleted",
			args: args{
				taskName: task1.TaskName,
			},
			wantErr: epifierrors.ErrRecordNotFound.Error(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotTask, err := dao.Get(context.Background(), tt.args.taskName)
			if (err != nil) != (tt.wantErr != "") {
				t.Errorf(" got error: %v\nwant error: %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr {
					t.Errorf(" got error: %v\nwant error: %v", err, tt.wantErr)
				}
				return
			}
			tt.wantTask.CreatedAt = gotTask.CreatedAt
			if !reflect.DeepEqual(gotTask, tt.wantTask) {
				t.Errorf("\n got: %v\nwant: %v", gotTask, tt.wantTask)
			}
		})
	}
}

// nolint
func TestDoOnceCRDB_SoftDelete(t *testing.T) {
	dao := &DoOnceCRDB{
		DB: db,
	}
	pkgTest.PrepareScopedDatabase(t, dbName, db, []string{"do_once_tasks"})
	// add fixtures
	dao.DB.Create(task3)
	dao.DB.Create(task4)

	recNotFound := epifierrors.ErrRecordNotFound.Error()

	type args struct {
		ctx      context.Context
		taskName string
	}

	tests := []struct {
		name    string
		args    args
		wantErr string
	}{
		{
			name: "soft delete successfully",
			args: args{
				ctx:      context.TODO(),
				taskName: task4.TaskName,
			},
		},
		{
			name: "record not found",
			args: args{
				ctx:      context.TODO(),
				taskName: task1.TaskName,
			},
			wantErr: recNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := dao.SoftDelete(tt.args.ctx, tt.args.taskName)
			if err != nil {
				if err.Error() != tt.wantErr {
					t.Errorf(" got error: %v\nwant error: %v", err, tt.wantErr)
				}
				return
			}
			// asserting that soft deleted row is not present
			_, getErr := dao.Get(tt.args.ctx, tt.args.taskName)
			if !errors.Is(getErr, epifierrors.ErrRecordNotFound) {
				t.Errorf(" got error: %v\nwant error: %v", err, tt.wantErr)
			}
			// asserting if we can create the deleted row again
			createErr := dao.Create(tt.args.ctx, tt.args.taskName)
			if createErr != nil {
				t.Errorf("error in creating softDeleted row got createError = %v", createErr)
			}
		})
	}
}
