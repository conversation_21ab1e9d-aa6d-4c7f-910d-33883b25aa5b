package callback

import (
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"

	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/data_structs/roarray"
	"github.com/epifi/be-common/pkg/storage/metrics"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	storageClause "github.com/epifi/be-common/pkg/storage/v2/clause"
)

// ReplicateWrites returns a callback method which is invoked by gorm whenever write is performed using the DB connection
// This includes Create, Update and Delete queries from the application.
// ----------------------- IMPORTANT NOTE -----------------------
// atomicity is only guaranteed in case gorm implicit transactions are enabled for all the queries, since transaction
// is not initiated as part of this callback.
func ReplicateWrites(conf *gencfg.DataReplicationParams) func(db *gorm.DB) {
	if conf.TargetDBName() == "" {
		panic(fmt.Errorf("DB name must be specified for ReplicateWrites to be enabled"))
	}

	return func(db *gorm.DB) {
		// do not replicate the writes in second database in case writes
		// first database itself fails
		if db.Error != nil {
			return
		}

		// don't do anything in case first database write didn't update any row or if no gorm.Statement was generated
		if db.RowsAffected == 0 || db.Statement == nil {
			return
		}

		// checks if data replication is enabled for the tables or not
		tableName, ok := isDataReplicationEnabledForTheTable(db, conf.SourceTables())
		if !ok {
			return
		}

		// don't do anything in case first database write didn't generate any query
		if len(db.Statement.Clauses) == 0 && db.Statement.SQL.Len() == 0 {
			return
		}

		prevRowsAffected := db.RowsAffected
		prevSQL := db.Statement.SQL.String()
		tx := db

		metrics.RecordDataReplicatorSourceWriteSuccess(tableName)

		isRawQuery := len(tx.Statement.Clauses) == 0 && tx.Statement.SQL.Len() != 0
		if isRawQuery {
			metrics.RecordDataReplicatorRawQuery(tableName)
		}

		// Adding storage/v2.UseDB clause to modify the statement using gorm.StatementModifier interface
		tx.Statement.AddClause(storageClause.UseDB{DBName: conf.TargetDBName()})

		// in case the query was generated using gorm query builder, the SQL expression is re-generated from the clause
		// building and concatenate in the end of existing SQL expression, hence it is necessary to reset the previous
		// SQL expression before re-building the query
		if !isRawQuery {
			tx.Statement.SQL.Reset()
			tx.Statement.Vars = nil
		}

		// re-build the SQL query
		tx.Statement.Build(tx.Statement.BuildClauses...)

		// in case post statement modification there is no SQL generated or if prev and new query matches then return error
		// rollback is subjective to conf.IsSoftFailureEnabled.
		if tx.Statement.SQL.Len() == 0 || strings.EqualFold(tx.Statement.SQL.String(), prevSQL) {
			metrics.RecordDataReplicatorFailure(metrics.DataReplicatorFailureTypeEmptyQuery, tableName)

			if !conf.DisableWriteReplicationAtomicity() {
				dbAddErrorErr := tx.AddError(fmt.Errorf("unable to generate dual write query for %s", prevSQL))
				if dbAddErrorErr != nil {
					metrics.RecordDataReplicatorRollbackFailure(tableName)
					tx.Logger.Error(tx.Statement.Context, fmt.Sprintf("failed to add %s as txn error due to: %s", fmt.Sprintf("unable to generate dual write query for %s", prevSQL), dbAddErrorErr))
					return
				}

				metrics.RecordDataReplicatorRollback(metrics.DataReplicatorFailureTypeEmptyQuery, tableName)
				return
			}

			tx.Logger.Error(tx.Statement.Context, fmt.Sprintf("failed to generate SQL for dual write %s", prevSQL))
			return
		}

		// returns the query that is supposed to be printed in the gorm logs and traces
		sql := tx.Dialector.Explain(tx.Statement.SQL.String(), tx.Statement.Vars...)
		tx.Logger.Info(tx.Statement.Context, sql)

		// run the dml query using the same DB connection pool so that second query also runs atomically.
		execStartTm := time.Now()
		res, err := tx.Statement.ConnPool.ExecContext(tx.Statement.Context, tx.Statement.SQL.String(), tx.Statement.Vars...)
		executionDuration := time.Since(execStartTm)
		if err != nil {
			metrics.RecordDataReplicatorQueryDuration(metrics.DateReplicatorQueryResultFailed, tableName, executionDuration)
			metrics.RecordDataReplicatorFailure(metrics.DataReplicatorFailureTypeQueryExecution, tableName)

			if !conf.DisableWriteReplicationAtomicity() {
				dbAddErrorErr := tx.AddError(err)
				if dbAddErrorErr != nil {
					metrics.RecordDataReplicatorRollbackFailure(tableName)
					tx.Logger.Error(tx.Statement.Context, fmt.Sprintf("failed to add %s as txn error due to: %s", err, dbAddErrorErr))
					return
				}

				metrics.RecordDataReplicatorRollback(metrics.DataReplicatorFailureTypeQueryExecution, tableName)
				return
			}

			tx.Logger.Error(tx.Statement.Context, fmt.Sprintf("replication of write to %s failed: %s", conf.TargetDBName(), db.Error))
			return
		} else {
			metrics.RecordDataReplicatorQueryDuration(metrics.DateReplicatorQueryResultSuccess, tableName, executionDuration)
		}

		// returns the row effected
		rowsAffected, err := res.RowsAffected()
		if err != nil {
			metrics.RecordDataReplicatorFailure(metrics.DataReplicatorFailureTypeRowsAffectedFetch, tableName)

			if !conf.DisableWriteReplicationAtomicity() {
				dbAddErrorErr := tx.AddError(err)
				if dbAddErrorErr != nil {
					metrics.RecordDataReplicatorRollbackFailure(tableName)
					tx.Logger.Error(tx.Statement.Context, fmt.Sprintf("failed to add %s as txn error due to: %s", err, dbAddErrorErr))
					return
				}

				metrics.RecordDataReplicatorRollback(metrics.DataReplicatorFailureTypeRowsAffectedFetch, tableName)
				return
			}

			tx.Logger.Error(tx.Statement.Context, fmt.Sprintf("replication of write to %s failed: error while fetching rows effected %s", conf.TargetDBName(), err))
			return
		}

		// perform rollback in case no rows were updated in the second DB
		// or if prev rows affected and current DB rows effected doesn't match
		if rowsAffected == 0 || rowsAffected != prevRowsAffected {
			metrics.RecordDataReplicatorFailure(metrics.DataReplicatorFailureTypeRowsAffectedMismatch, tableName)

			if !conf.DisableWriteReplicationAtomicity() {
				dbAddErrorErr := tx.AddError(fmt.Errorf("couldn't updated entry in %s", conf.TargetDBName()))
				if dbAddErrorErr != nil {
					metrics.RecordDataReplicatorRollbackFailure(tableName)
					tx.Logger.Error(tx.Statement.Context, fmt.Sprintf("failed to add %s as txn error due to: %s", fmt.Sprintf("couldn't updated entry in %s", conf.TargetDBName()), dbAddErrorErr))
					return
				}

				metrics.RecordDataReplicatorRollback(metrics.DataReplicatorFailureTypeRowsAffectedMismatch, tableName)
				return
			}

			tx.Logger.Error(tx.Statement.Context, fmt.Sprintf("mismatch in rows affected in the first and second DB, prev rows affected: %d current rows affected: %d", prevRowsAffected, rowsAffected))
		}

		return
	}
}

// isDataReplicationEnabledForTheTable matches the source tables with the table passed in DB query
func isDataReplicationEnabledForTheTable(db *gorm.DB, sourceTables roarray.ROArray[string]) (string, bool) {
	if sourceTables.Len() == 0 {
		return "", false
	}

	stmt := db.Statement
	// Case 1: Query generated using gorm query builder.
	if stmt.Table != "" {
		return lo.Find(sourceTables.Slice(), func(s string) bool {
			return strings.EqualFold(s, stmt.Table)
		})
	}

	// Case 2: Query generated using raw query builder.
	if stmt.SQL.Len() != 0 && len(stmt.Clauses) == 0 {
		tableName, ok := storage.GetTableNameFromRawQuery(db.Statement)
		if !ok {
			db.Logger.Error(stmt.Context, fmt.Sprintf("failed to extract table name from the raw query: %s", stmt.SQL.String()))
			return "", false
		}

		return lo.Find(sourceTables.Slice(), func(s string) bool {
			return strings.EqualFold(s, tableName)
		})
	}

	return "", false
}
