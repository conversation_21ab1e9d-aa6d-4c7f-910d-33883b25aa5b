package storagev2

import (
	"database/sql"
	"errors"
	"fmt"
	"net"
	"net/url"
	"regexp"
	"strconv"
	"strings"

	"github.com/jackc/pgconn"
	"github.com/mohae/deepcopy"
	errorsPkg "github.com/pkg/errors"
	"github.com/prestodb/presto-go-client/presto"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/plugin/dbresolver"

	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"

	"github.com/epifi/be-common/pkg/file"
	"github.com/epifi/be-common/pkg/storage"
	pkgTracing "github.com/epifi/be-common/pkg/tracing/opentelemetry"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	DMLQueryTableNameRegex = regexp.MustCompile(`^\s*(?i)(UPDATE|DELETE(?:\s*\/\*.*\*\/\s*?)*\s+FROM|INSERT(?:\s*\/\*.*\*\/\s*?)*\s+INTO)(?:\s*\/\*.*\*\/\s*?)*\s+([^\s\/*]+)`)
)

// nolint: funlen
// NewCRDBWithConfig returns a gorm.DB database handle. It is the responsibility of the caller
// to close it after use.
// For secure cluster: The standard terminology used here denotes:
// sslrootcert: digital certificate of the CA containing CA's public key
// sslcert: a digital certificate for the client. It contains the client's public key.
// sslkey: private key for the client
// Deprecated: use NewGormDB with DBType set in config
func NewCRDBWithConfig(crdbConfig *cfg.DB, initTracer bool) (*gorm.DB, error) {
	connString, err := GetCrdbDsnStringFromDBConf(crdbConfig)
	if err != nil {
		return nil, err
	}

	rootCertFilePath := storage.GetCRDBSslRootCertPath(crdbConfig.SSLCertPath)
	clientKeyPath := storage.GetCRDBSslClientKeyPath(crdbConfig.SSLCertPath, crdbConfig.Username)
	clientCertPath := storage.GetCRDBSslClientCertPath(crdbConfig.SSLCertPath, crdbConfig.Username)
	if crdbConfig.SSLMode == storage.DBSSLModeVerifyFull {
		err = storage.CreateSslFiles(crdbConfig, rootCertFilePath, clientKeyPath, clientCertPath)
		if err != nil {
			return nil, err
		}
	}

	gormConfig := &gorm.Config{NowFunc: storage.PgNow}

	if logger.IsDevLogger {
		gormConfig.Logger = gormlogger.Default.LogMode(cfg.GetGORMLogLevel(crdbConfig.GormV2.LogLevelGormV2))
	} else {
		log := logger.DefaultSecureLogger
		if crdbConfig.GormV2.UseInsecureLog {
			log = logger.Log
		}
		gormConfig.Logger = NewGormLogger(log, cfg.GetGORMLogLevel(crdbConfig.GormV2.LogLevelGormV2),
			crdbConfig.GormV2.SlowQueryLogThreshold)
	}
	// https://gorm.io/docs/connecting_to_the_database.html#PostgreSQL
	db, err := gorm.Open(postgres.New(postgres.Config{
		DSN:                  connString,
		PreferSimpleProtocol: crdbConfig.GormV2.DisableImplicitPreparedStmt,
	}), gormConfig)
	if err != nil {
		return nil, err
	}
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	if (initTracer || crdbConfig.InitTracer) &&
		!cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() {
		// skip tracing when clients are deployed in local environment but uses non prod dependencies.
		pkgTracing.InitDbTracer(crdbConfig.GetName(), db)
	}
	if crdbConfig.MaxOpenConn != 0 {
		sqlDB.SetMaxOpenConns(crdbConfig.MaxOpenConn)
	}
	if crdbConfig.MaxIdleConn != 0 {
		sqlDB.SetMaxIdleConns(crdbConfig.MaxIdleConn)
	}
	if crdbConfig.MaxConnTtl != 0 {
		sqlDB.SetConnMaxLifetime(crdbConfig.MaxConnTtl)
	}

	err = storage.SetupStatsCollectorForSQLDB(crdbConfig, sqlDB)
	if err != nil {
		return nil, err
	}

	if crdbConfig.GormV2.EnableMultiDBSupport && !cfg.IsTestTenantEnabled() {
		err = registerDBResolver(db, crdbConfig)
		if err != nil {
			return nil, err
		}
	}

	return db, nil
}

func NewGormDB(conf *cfg.DB) (*gorm.DB, error) {
	switch conf.DbType {
	case cfg.PGDB:
		return NewPostgresDBWithConfig(conf, conf.InitTracer)
	case cfg.CRDB:
		return NewCRDBWithConfig(conf, conf.InitTracer)
	default:
		return nil, fmt.Errorf("invalid db type: %s of db name: %s", conf.DbType, conf.Name)
	}
}

// Deprecated: use NewGormDB with DBType set in config
func NewPostgresDBWithConfig(rdsConfig *cfg.DB, initTracer bool) (*gorm.DB, error) {
	connString, err := GetPgDbDsnStringFromDBConf(rdsConfig)
	if err != nil {
		return nil, err
	}

	// ignore creating SSL files in test env
	// done to avoid panic from race condition in parallel DB setup in acceptance tests
	if rdsConfig.SSLMode != storage.DBSSLModeDisable && !strings.Contains(rdsConfig.Name, "_test") {
		rootCertFilePath := storage.GetPGDBSslRootCertPath(rdsConfig.SSLCertPath)
		err = CreateSslFilesForPGDBConn(cfg.GetPgDsnConfFromDBConf(rdsConfig), rootCertFilePath)
		if err != nil {
			return nil, err
		}
	}

	gormConfig := &gorm.Config{NowFunc: storage.PgNow}

	if logger.IsDevLogger {
		gormConfig.Logger = gormlogger.Default.LogMode(cfg.GetGORMLogLevel(rdsConfig.GormV2.LogLevelGormV2))
	} else {
		log := logger.DefaultSecureLogger
		if rdsConfig.GormV2.UseInsecureLog {
			log = logger.Log
		}
		gormConfig.Logger = NewGormLogger(log, cfg.GetGORMLogLevel(rdsConfig.GormV2.LogLevelGormV2), rdsConfig.GormV2.SlowQueryLogThreshold)
	}
	// https://gorm.io/docs/connecting_to_the_database.html#PostgreSQL
	db, err := gorm.Open(postgres.New(postgres.Config{
		DSN:                  connString,
		PreferSimpleProtocol: rdsConfig.GormV2.DisableImplicitPreparedStmt,
	}), gormConfig)
	if err != nil {
		var dbErr *pgconn.PgError
		var isCreated bool
		// check if DB related error
		if errors.As(err, &dbErr) {
			// Check the SQLSTATE code 3D000 which is DB not found or DB doesn't exist
			if dbErr.Code == "3D000" {
				fmt.Println("Database does not exist or not created.")
				// only for development env, hence create DB for development env
				// creating this inside error only in cases when DB is not created and in development env.
				// this is used mainly by MakeFile for users to create before upgrading DB
				createErr := CreateLocalDatabaseIfNotExistsPgdb(rdsConfig)
				if createErr == nil {
					// if no error, retry to initiate DB session
					db, err = gorm.Open(postgres.New(postgres.Config{
						DSN:                  connString,
						PreferSimpleProtocol: rdsConfig.GormV2.DisableImplicitPreparedStmt,
					}), gormConfig)
					if err == nil {
						isCreated = true
					}
				}
			}

		}
		// if in case DB was not created earlier, and created now, no need to return
		if !isCreated {
			return nil, err
		}
	}
	if (initTracer || rdsConfig.InitTracer) &&
		!cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() {
		// skip tracing when clients are deployed in local environment but uses non prod dependencies.
		pkgTracing.InitDbTracer(rdsConfig.GetName(), db)
	}
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	if rdsConfig.MaxOpenConn != 0 {
		sqlDB.SetMaxOpenConns(rdsConfig.MaxOpenConn)
	}
	if rdsConfig.MaxIdleConn != 0 {
		sqlDB.SetMaxIdleConns(rdsConfig.MaxIdleConn)
	}
	if rdsConfig.MaxConnTtl != 0 {
		sqlDB.SetConnMaxLifetime(rdsConfig.MaxConnTtl)
	}

	err = storage.SetupStatsCollectorForSQLDB(rdsConfig, sqlDB)
	if err != nil {
		return nil, err
	}
	return db, nil
}

func NewDefaultPostgresConn(rdsConfig *cfg.DB) (*gorm.DB, error) {
	connString := storage.FormDefaultPostgresConnString(rdsConfig)
	db, err := gorm.Open(postgres.Open(connString), &gorm.Config{
		NowFunc: storage.PgNow,
		Logger:  gormlogger.Default.LogMode(cfg.GetGORMLogLevel(rdsConfig.GormV2.LogLevelGormV2))})
	if err != nil {
		return nil, err
	}
	return db, nil
}

func GetTableNameFromRawQuery(stmt *gorm.Statement) (string, bool) {
	if stmt.SQL.Len() == 0 {
		return "", false
	}

	regexCaptureGrp := DMLQueryTableNameRegex.FindStringSubmatch(stmt.SQL.String())

	if len(regexCaptureGrp) != 3 {
		return "", false
	}

	// To understand how sub match indexing works in case of capture group in golang
	// Refer- https://golangbyexample.com/regex-replace-string-golang/
	// 0 - points to entire match
	// 1 - first sub match or regex capture group
	// 2 - second sub match or regex capture group
	return regexCaptureGrp[2], true
}

func NewPrestoDBFromConfig(prestoConfig *cfg.Presto, env string) (*sql.DB, error) {
	connString := GetPrestoConnString(prestoConfig, env)
	// Create the Presto client configuration
	config := &presto.Config{
		PrestoURI:   connString,
		Source:      prestoConfig.Source,
		Catalog:     prestoConfig.Catalog,
		Schema:      prestoConfig.Schema,
		SSLCertPath: prestoConfig.SSLCertPath,
	}
	prestoConnString, err := config.FormatDSN()
	if err != nil {
		return nil, err
	}
	prestoDb, err := sql.Open("presto", prestoConnString)
	if err != nil {
		return nil, err
	}
	return prestoDb, nil
}

func GetPrestoConnString(config *cfg.Presto, env string) string {
	if env == cfg.ProductionEnv {
		userInfo := url.UserPassword(config.Username, config.Password)
		encodedUserInfo := userInfo.String()
		return fmt.Sprintf("http://%s@%s:%d", encodedUserInfo, config.Host, config.Port)
	} else {
		return fmt.Sprintf("http://%s@%s:%d", config.Username, config.Host, config.Port)
	}
}

func GetCrdbDsnStringFromDBConf(crdbConfig *cfg.DB) (string, error) {
	pgDsnConf := cfg.GetPgDsnConfFromDBConf(crdbConfig)

	connStr, err := GetCrdbDsnString(pgDsnConf)
	if err != nil {
		return "", err
	}

	logger.DebugNoCtx("connection string dsn", zap.String("dsn", connStr))
	return connStr, nil
}

func GetCrdbDsnString(dsn *cfg.PgDsn) (string, error) {
	uri := GetCrdbDsnUrl(dsn)

	connStr, err := url.QueryUnescape(uri.String())
	if err != nil {
		return "", err
	}

	logger.DebugNoCtx("connection string dsn", zap.String("dsn", connStr))

	return connStr, nil
}

func GetPgDbDsnStringFromDBConf(pgdbConf *cfg.DB) (string, error) {
	pgDsnConf := cfg.GetPgDsnConfFromDBConf(pgdbConf)

	connStr, err := GetPgDbDsnString(pgDsnConf)
	if err != nil {
		return "", err
	}

	return connStr, nil
}

func GetPgDbDsnString(dsn *cfg.PgDsn) (string, error) {
	uri := GetPgDsnUrl(dsn)

	connStr, err := url.QueryUnescape(uri.String())
	if err != nil {
		return "", err
	}

	return connStr, nil
}

func GetCrdbDsnUrl(pgDsnConf *cfg.PgDsn) *url.URL {
	uri := GetPgDsnUrl(pgDsnConf)
	uri.User = url.User(pgDsnConf.Username)
	queryVals := uri.Query()

	if pgDsnConf.SSLMode == storage.DBSSLModeVerifyFull {
		queryVals.Set(storage.DBConnSSLRootCert, storage.GetCRDBSslRootCertPath(pgDsnConf.SSLCertPath))
		queryVals.Set(storage.DBConnSSLKey, storage.GetCRDBSslClientKeyPath(pgDsnConf.SSLCertPath, pgDsnConf.Username))
		queryVals.Set(storage.DBConnSSLCert, storage.GetCRDBSslClientCertPath(pgDsnConf.SSLCertPath, pgDsnConf.Username))
	}

	uri.RawQuery = queryVals.Encode()
	return uri
}

func GetPgDsnUrl(pgDsnConf *cfg.PgDsn) *url.URL {
	queryVals := url.Values{}
	queryVals.Set(storage.DBConnSSLMode, pgDsnConf.SSLMode)

	if pgDsnConf.SSLMode == storage.DBSSLModeVerifyFull {
		queryVals.Set(storage.DBConnSSLRootCert, storage.GetPGDBSslRootCertPath(pgDsnConf.SSLCertPath))
	}

	stmtTimeout := pgDsnConf.StatementTimeout

	if stmtTimeout == 0 {
		stmtTimeout = storage.DefaultStatementTimeout
	}
	if stmtTimeout > 0 {
		queryVals.Set(storage.StatementTimeout, fmt.Sprint(stmtTimeout.Milliseconds()))
	}
	idleTxnTimeout := pgDsnConf.IdleInTxnSessionTimeout

	if idleTxnTimeout == 0 {
		idleTxnTimeout = storage.DefaultIdleInTxnSessionTimeout
	}
	if idleTxnTimeout > 0 {
		queryVals.Set(storage.IdleInTxnSessionTimeout, fmt.Sprint(idleTxnTimeout.Milliseconds()))
	}
	if pgDsnConf.AppName != "" {
		queryVals.Set(storage.DBConnAppName, pgDsnConf.AppName)
	}

	uri := &url.URL{
		Scheme:   storage.PostgresSQLSchema,
		User:     url.UserPassword(pgDsnConf.Username, pgDsnConf.Password),
		Host:     net.JoinHostPort(pgDsnConf.Host, strconv.Itoa(pgDsnConf.Port)),
		Path:     pgDsnConf.GetName(),
		RawQuery: queryVals.Encode(),
	}

	return uri
}

func CreateSslFilesForPGDBConn(pgDsnConf *cfg.PgDsn, rootCertFilePath string) error {
	err := file.CreateFileWithGivenPermissionAndContent(rootCertFilePath, pgDsnConf.SSLRootCert, 0600)
	if err != nil {
		return err
	}
	return nil
}

func registerDBResolver(db *gorm.DB, conf *cfg.DB) error {
	var (
		dr  *dbresolver.DBResolver
		err error
	)

	err = validateDbResolverConf(conf.GormV2.DBResolverList)
	if err != nil {
		return err
	}

	for _, drConf := range conf.GormV2.DBResolverList {
		dr, err = getDbResolverFromConfig(dr, drConf)
		if err != nil {
			return errorsPkg.Wrap(err, fmt.Sprintf("failed to register db resolver for : %s", drConf.Alias))
		}
	}

	// set connection configurations to the db-resolver
	// https://github.com/go-gorm/dbresolver#connection-pool
	if conf.MaxOpenConn != 0 {
		dr.SetMaxOpenConns(conf.MaxOpenConn)
	}
	if conf.MaxIdleConn != 0 {
		dr.SetMaxIdleConns(conf.MaxIdleConn)
	}
	if conf.MaxConnTtl != 0 {
		dr.SetConnMaxLifetime(conf.MaxConnTtl)
	}

	err = db.Use(dr)
	if err != nil {
		return errorsPkg.Wrap(err, "failed to register db resolver")
	}

	return nil
}

func validateDbResolverConf(dBResolverConfList []*cfg.DBResolver) error {
	aliasVisMap := make(map[string]bool)
	for _, drConf := range dBResolverConfList {
		if drConf.Alias == "" {
			return fmt.Errorf("db resolver alias can't be empty")
		}

		if _, ok := aliasVisMap[drConf.Alias]; ok {
			return fmt.Errorf("found conflicting alias definition in DB resolver for alias %s", drConf.Alias)
		}

		aliasVisMap[drConf.Alias] = true

		for _, tableName := range drConf.TableName {
			if _, ok := aliasVisMap[tableName]; ok {
				return fmt.Errorf("found conflicting table name definition in DB resolver for %s", tableName)
			}

			aliasVisMap[tableName] = true
		}
	}

	return nil
}

func getDbResolverFromConfig(dr *dbresolver.DBResolver, drConf *cfg.DBResolver) (*dbresolver.DBResolver, error) {
	var dsnString string
	var err error

	dsnString, err = GetPgDbDsnString(drConf.DbDsn)
	if err != nil {
		return nil, err
	}

	rootCertFilePath := storage.GetPGDBSslRootCertPath(drConf.DbDsn.SSLCertPath)
	if drConf.DbDsn.SSLMode == storage.DBSSLModeVerifyFull {
		err = CreateSslFilesForPGDBConn(drConf.DbDsn, rootCertFilePath)
		if err != nil {
			return nil, err
		}
	}

	var alias []interface{}
	alias = append(alias, drConf.Alias)

	if len(drConf.TableName) != 0 {
		for _, tableName := range drConf.TableName {
			alias = append(alias, tableName)
		}
	}

	var registerFunc func(config dbresolver.Config, datas ...interface{}) *dbresolver.DBResolver

	if dr == nil {
		registerFunc = dbresolver.Register
	} else {
		registerFunc = dr.Register
	}

	return registerFunc(dbresolver.Config{
		Sources:           []gorm.Dialector{postgres.Open(dsnString)},
		Replicas:          []gorm.Dialector{postgres.Open(dsnString)},
		TraceResolverMode: true,
	}, alias...), nil

}

// CreateLocalDatabaseIfNotExistsPgdb creates database for in pgdb if it doesn't exist already in developer env
func CreateLocalDatabaseIfNotExistsPgdb(rdsConfig *cfg.DB) error {
	defaultConfig := deepcopy.Copy(rdsConfig).(*cfg.DB)
	defaultConfig.Name = "postgres"

	// Check if the error is a database-related error
	env, envErr := cfg.GetEnvironment()
	if envErr != nil {
		logger.ErrorNoCtx("error while trying to get environment for gorm error", zap.Error(envErr))
		return errorsPkg.Wrap(envErr, "error while trying to get environment for gorm error")
	}
	if !cfg.IsLocalEnv(env) {
		logger.ErrorNoCtx("not local env")
		return errorsPkg.New("not local env")
	}

	// make connection with default database
	// Init default postgres db connection
	db, err := NewDefaultPostgresConn(defaultConfig)
	if err != nil {
		logger.ErrorNoCtx("failed to connect to default postgres db", zap.Error(envErr))
		return errorsPkg.Wrap(err, "failed to connect to default postgres db")
	}

	dbName := rdsConfig.GetName()
	if CheckPgDbExists(db, dbName) {
		return nil
	}

	if createErr := CreateRdsDatabase(db, dbName); createErr != nil {
		logger.ErrorNoCtx(fmt.Sprintf("error while creating DB: %v", createErr))
		return errorsPkg.Wrap(createErr, "error while creating DB")
	}
	return nil
}

func CheckPgDbExists(db *gorm.DB, dbName string) bool {
	res := db.Exec(fmt.Sprintf(`SELECT FROM pg_database WHERE datname = '%s'`, dbName))
	if res.Error != nil {
		logger.ErrorNoCtx(fmt.Sprintf("error while checking if db exists: %v", res.Error))
	}
	if res.RowsAffected == 1 {
		return true
	}
	return false
}

func CreateRdsDatabase(db *gorm.DB, dbName string) error {
	if err := db.Exec(fmt.Sprintf("CREATE DATABASE %s", dbName)).Error; err != nil {
		return err
	}
	return nil
}
