package kms

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/kms"
)

// instantiate an kms client from an aws config
func InitKMSClient(awsConfig aws.Config) *kms.Client {

	//Create a kms service client.
	return kms.NewFromConfig(awsConfig, func(options *kms.Options) {
		options.EndpointResolverV2 = GetKmsResolver()
	})
}

// GetKeysByDescription returns a list of key ids given a list of descriptions
func GetKeysByDescription(ctx context.Context, kmsClient *kms.Client, descList []string) ([]string, error) {
	if len(descList) == 0 {
		return nil, nil
	}

	// list keys
	req := &kms.ListKeysInput{}
	res, err := kmsClient.ListKeys(ctx, req)
	if err != nil {
		return nil, err
	}

	// create desc to key id reverse mapping
	descToKeyIDMap := make(map[string]string)
	for _, key := range res.Keys {
		dkr := &kms.DescribeKeyInput{
			GrantTokens: nil,
			KeyId:       key.KeyId,
		}
		dkRes, lErr := kmsClient.DescribeKey(ctx, dkr)
		if lErr != nil {
			return nil, err
		}

		descToKeyIDMap[*dkRes.KeyMetadata.Description] = *dkRes.KeyMetadata.KeyId
	}

	var resKeyIds []string
	for _, desc := range descList {
		resKeyIds = append(resKeyIds, descToKeyIDMap[desc])
	}

	return resKeyIds, nil
}
