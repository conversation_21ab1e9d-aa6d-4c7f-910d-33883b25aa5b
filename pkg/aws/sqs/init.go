package sqs

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials/stscreds"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
)

// instantiate an sqs client from an aws session
func InitSQSClient(sess *session.Session) *sqs.SQS {
	//Create a SQS service client.
	return sqs.New(sess)
}

// instantiate an sqs client from an aws session using an assumed role
func InitSQSClientAssumingRole(sess *session.Session, assumeRoleArn string) *sqs.SQS {
	// Create a SQS service client configured with credentials from assumed role.
	return sqs.New(sess, &aws.Config{Credentials: stscreds.NewCredentials(sess, assumeRoleArn)})
}
