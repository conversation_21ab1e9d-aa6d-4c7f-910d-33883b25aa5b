package vendorstore

import (
	"context"
	"testing"

	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/nulltypes"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/user/config"
)

// Test Suite for transaction dao
type VendorResponseDaoTestSuite struct {
	db       *gorm.DB
	vRespDao VendorResponseDAO
	conf     *config.Config
}

const (
	limit  = 100
	offset = 0
)

var (
	vrts               VendorResponseDaoTestSuite
	affectedTestTables = []string{"vendor_responses"}
)

var (
	vendorResp3 = []*VendorResponse{
		vendorResp2,
		vendorResp1,
	}
	vendorResp4 = []*VendorResponse{
		vendorResp1,
	}
	vendorResp5 = []*VendorResponse{
		vendorResp2,
	}
)

func TestVendorResponseCRDB_Create(t *testing.T) {
	r := &VendorResponseCRDB{
		DB: vrts.db,
	}
	pkgTest.PrepareScopedDatabase(t, vrts.conf.EpifiDb.GetName(), vrts.db, affectedTestTables)

	type args struct {
		ctx        context.Context
		vendorResp *VendorResponse
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successful insertion",
			args: args{
				ctx:        context.Background(),
				vendorResp: vendorResp1,
			},
			wantErr: false,
		}, {
			name: "empty actorId",
			args: args{
				ctx: context.Background(),
				vendorResp: &VendorResponse{
					ResponseCode: nulltypes.NewNullString("responseCode"),
					RequestId:    nulltypes.NewNullString("requestId"),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &VendorResponseCRDB{
				DB: r.DB,
			}
			if err := s.Create(tt.args.ctx, tt.args.vendorResp); (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestVendorResponseCRDB_GetByActorId(t *testing.T) {
	r := &VendorResponseCRDB{
		DB: vrts.db,
	}
	pkgTest.PrepareScopedDatabase(t, vrts.conf.EpifiDb.GetName(), vrts.db, affectedTestTables)
	vrts.db.Create(vendorResp1)
	vrts.db.Create(vendorResp2)
	type args struct {
		ctx        context.Context
		limit      int
		offset     int
		vendorResp *VendorResponse
	}
	tests := []struct {
		name    string
		args    args
		want    []*VendorResponse
		wantErr bool
	}{
		{
			name: "fetch successful customer creation",
			args: args{
				ctx:        context.Background(),
				limit:      limit,
				offset:     offset,
				vendorResp: vendorResp1,
			},
			want:    vendorResp3,
			wantErr: false,
		},
		{
			name: "empty actorId",
			args: args{
				ctx:    context.Background(),
				limit:  limit,
				offset: offset,
				vendorResp: &VendorResponse{
					RequestId: nulltypes.NewNullString("reqId1"),
					TraceId:   nulltypes.NewNullString("traceId1"),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "fetch successful with api device registration offset 1",
			args: args{
				ctx:        context.Background(),
				limit:      limit,
				offset:     offset + 1,
				vendorResp: vendorResp1,
			},
			want:    vendorResp4,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &VendorResponseCRDB{
				DB: r.DB,
			}
			got, err := s.GetByActorId(tt.args.ctx, tt.args.limit, tt.args.offset, tt.args.vendorResp)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !isProtosEqual(got, tt.want) {
				t.Errorf("GetByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVendorResponseCRDB_GetByActorIdAndApi(t *testing.T) {
	r := &VendorResponseCRDB{
		DB: vrts.db,
	}
	pkgTest.PrepareScopedDatabase(t, vrts.conf.EpifiDb.GetName(), vrts.db, affectedTestTables)
	vrts.db.Create(vendorResp1)
	vrts.db.Create(vendorResp2)
	type args struct {
		ctx        context.Context
		limit      int
		offset     int
		vendorResp *VendorResponse
	}
	tests := []struct {
		name    string
		args    args
		want    []*VendorResponse
		wantErr bool
	}{
		{
			name: "fetch successful with api customer creation",
			args: args{
				ctx:        context.Background(),
				limit:      limit,
				offset:     offset,
				vendorResp: vendorResp1,
			},
			want:    vendorResp4,
			wantErr: false,
		},
		{
			name: "fetch successful with api device registration",
			args: args{
				ctx:        context.Background(),
				limit:      limit,
				offset:     offset,
				vendorResp: vendorResp2,
			},
			want:    vendorResp5,
			wantErr: false,
		},
		{
			name: "empty actorId",
			args: args{
				ctx:    context.Background(),
				limit:  limit,
				offset: offset,
				vendorResp: &VendorResponse{
					Api: API_CUSTOMER_CREATION_INIT,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "empty api",
			args: args{
				ctx:    context.Background(),
				limit:  limit,
				offset: offset,
				vendorResp: &VendorResponse{
					ActorId: "actor-user-1",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &VendorResponseCRDB{
				DB: r.DB,
			}
			got, err := s.GetByActorIdAndApi(tt.args.ctx, tt.args.limit, tt.args.offset, tt.args.vendorResp)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdAndApi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !isProtosEqual(got, tt.want) {
				t.Errorf("GetByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVendorResponseCRDB_GetByRequestId(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, vrts.conf.EpifiDb.GetName(), vrts.db, affectedTestTables)
	r := &VendorResponseCRDB{
		DB: vrts.db,
	}
	vrts.db.Create(vendorResp1)
	vrts.db.Create(vendorResp2)
	type fields struct {
		DB *gorm.DB
	}
	type args struct {
		ctx        context.Context
		limit      int
		offset     int
		vendorResp *VendorResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*VendorResponse
		wantErr bool
	}{
		{
			name: "fetch successful customer creation",
			args: args{
				ctx:        context.Background(),
				limit:      1,
				offset:     offset,
				vendorResp: vendorResp1,
			},
			want:    vendorResp4,
			wantErr: false,
		},
		{
			name: "empty requestId",
			args: args{
				ctx:    context.Background(),
				limit:  1,
				offset: offset,
				vendorResp: &VendorResponse{
					TraceId: nulltypes.NewNullString("traceId1"),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "fetch successful with api device registration offset 1",
			args: args{
				ctx:        context.Background(),
				limit:      1,
				offset:     offset,
				vendorResp: vendorResp1,
			},
			want:    vendorResp4,
			wantErr: false,
		},
		{
			name: "record not found",
			args: args{
				ctx:    context.Background(),
				limit:  1,
				offset: offset,
				vendorResp: &VendorResponse{
					RequestId: nulltypes.NewNullString("reqIdd"),
				},
			},
			want:    vendorResp4,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &VendorResponseCRDB{
				DB: r.DB,
			}
			got, err := s.GetByRequestId(tt.args.ctx, tt.args.limit, tt.args.offset, tt.args.vendorResp)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByRequestId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isProtosEqual(got, tt.want) {
				t.Errorf("GetByRequestId() got = %v, want = %v", got, tt.want)
			}
		})
	}
}
