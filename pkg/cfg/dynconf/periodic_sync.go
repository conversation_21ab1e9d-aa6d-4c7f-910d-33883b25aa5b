package dynconf

import (
	"context"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/hashicorp/consul/api"
	"github.com/knadh/koanf"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
)

// SyncConfigPeriodically periodically polls KV store through the passed consule client for the config and updates the config.
// Configs are loaded synchronously once and then periodically updated in the background.
func SyncConfigPeriodically(ctx context.Context, consulClient *api.Client, staticConf Conf, setters map[string]SetFunc, consulKeyPath string, period time.Duration) {
	// skipping logs as periodic sync will lead to a lot of logs
	safeConfig := NewSafeConfigV2(staticConf, setters, WithDisableInfoLogs())
	p := ProviderV2WithClient(consulClient, consulKeyPath)
	refreshConfig(p, safeConfig)
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		for {
			select {
			case <-ctx.Done():
				logger.Info(ctx, "ending periodic config sync", zap.String("key", consulKeyPath))
				return
			case <-time.After(period):
				logger.Debug(ctx, "refreshing config", zap.String("key", consulKeyPath))
				refreshConfig(p, safeConfig)
			}
		}
	})
}

func refreshConfig(p *ConsulKVProviderV2, safeConfig *SafeConfigV2) {
	var consulK = koanf.New(".")
	// load config from hashicorp consul vault
	if err := consulK.Load(p, nil); err != nil {
		logger.ErrorNoCtx("error loading new config from consul vault", zap.Error(err))
		return
	}
	err := safeConfig.Refresh(consulK)
	if err != nil {
		logger.ErrorNoCtx("error while refreshing the config", zap.Error(err))
		return
	}
}
