package legality

import (
	"encoding/json"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type SignFlowScreenOptions struct {
	SignUrl  string
	ExitUrl  string
	FlowType pkg.SignFlowType
}

// SignFlowScreenBlob contains flow type
type SignFlowScreenBlob struct {
	FlowType pkg.SignFlowType
}

// BuildLegalitySignFlowDeeplink is used to generate deeplink for legality sign flow screen
func BuildLegalitySignFlowDeeplink(options *SignFlowScreenOptions) (*deeplink.Deeplink, error) {
	blob := &SignFlowScreenBlob{
		FlowType: options.FlowType,
	}
	jsonBytes, err := json.Marshal(blob)
	if err != nil {
		return nil, err
	}
	signOptions := deeplinkv3.GetScreenOptionV2WithoutError(&pkg.LegalitySignFlowScreenOptions{
		StartUrl: options.SignUrl,
		ExitUrl:  options.ExitUrl,
		Blob:     jsonBytes,
	})
	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_LEGALITY_SIGN_FLOW,
		ScreenOptionsV2: signOptions,
	}, nil
}

// UnmarshalSignFlowScreenBlob unmarshal blob received from get blob passed in BuildLegalitySignFlowDeeplink
func UnmarshalSignFlowScreenBlob(bytes []byte) (*SignFlowScreenBlob, error) {
	if len(bytes) == 0 {
		return &SignFlowScreenBlob{}, nil
	}
	var blob SignFlowScreenBlob
	err := json.Unmarshal(bytes, &blob)
	if err != nil {
		return nil, err
	}
	return &blob, nil
}
