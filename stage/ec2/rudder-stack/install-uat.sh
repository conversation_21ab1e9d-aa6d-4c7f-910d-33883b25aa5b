#!/bin/bash

WORKSPACE_ID="$1"
ACCOUNT_ID="$2"

aws configure set default.region ap-south-1
aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin "$ACCOUNT_ID".dkr.ecr.ap-south-1.amazonaws.com
export GOPRIVATE=github.com/epifi
# shellcheck disable=SC2046
docker stop $(docker ps -aq)
docker system prune -f -a

aws secretsmanager get-secret-value --secret-id "$WORKSPACE_ID" --query SecretString --output text >> /data/rudder/workspaceConfig.txt
aws s3 cp s3://epifi-dev-dp-resources/data-platform/data-configs/ec2/rudder-stack/convert_to_json.py /data/rudder/convert_to_json.py
python3 /data/rudder/convert_to_json.py
rm /data/rudder/workspaceConfig.txt
rm /data/rudder/convert_to_json.py

rm /data/rudder/rudder-setup-docker.yml
aws secretsmanager get-secret-value --secret-id rudder-setup-docker-uat --query SecretString --output text >> /data/rudder/rudder-setup-docker.yml
aws s3 cp s3://epifi-dev-dp-resources/data-platform/data-configs/ec2/rudder-stack/mapping.yml /data/rudder/mapping.yml

docker-compose -f /data/rudder/rudder-setup-docker.yml up -d
