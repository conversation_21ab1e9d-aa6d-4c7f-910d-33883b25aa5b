import boto3
import sys

ebs_size = sys.argv[1]
machine_type = sys.argv[2]
env = 'data-dev'
machine_name = 'data-nginx'

ec2 = boto3.resource('ec2', 'ap-south-1')

user_data = '''#!/bin/bash
# Install dependencies as sudo user
sudo apt -y install python3-pip awscli;

# Install nginx as sudo user
sudo apt -y install nginx;
sudo aws s3 cp s3://epifi-dev-dp-resources/data-platform/data-configs/ec2/ngnix/default /etc/nginx/sites-available/;
sudo mkdir /var/www/sourceConfig;
sudo aws s3 cp s3://epifi-dev-dp-resources/data-platform/data-configs/ec2/ngnix/ruddersrc.json  /var/www/sourceConfig/sourceConfig.json;
sudo aws s3 cp s3://epifi-dev-dp-resources/data-platform/data-configs/ec2/ngnix/ruddersrc.json  /usr/share/nginx/html/;
sudo aws s3 cp s3://epifi-dev-dp-resources/data-platform/data-configs/ec2/ngnix/ruddersrc.json  /var/www/ruddersrc.json;
sudo chown www-data -R /var/www/;
sudo systemctl stop nginx;
sudo systemctl start nginx;
'''

instance = ec2.create_instances(
    BlockDeviceMappings=[
        {
            'DeviceName': '/dev/sda1',
            'Ebs': {
                'DeleteOnTermination': True,
                'VolumeSize': int(ebs_size),
                'VolumeType': 'gp2',
                'Encrypted': False
            }
        }
    ],
    ImageId='ami-0cbbb04a851967f21',
    MinCount=1,
    MaxCount=1,
    InstanceType=str(machine_type),
    UserData=user_data,
    SubnetId='subnet-029928f60d1496cc2',
    SecurityGroupIds=['sg-066a36ec2d1358131'],
    IamInstanceProfile={'Name': 'DPEC2ServiceRole'}
)

print(instance[0].id)

ec2_client = boto3.client('ec2', 'ap-south-1')
tag_creation = ec2_client.create_tags(Resources=[instance[0].id], Tags=[{'Key': 'Name', 'Value': machine_name},{'Key': 'service_name', 'Value': machine_name}, {'Key': 'env', 'Value': env}])

print(tag_creation)
