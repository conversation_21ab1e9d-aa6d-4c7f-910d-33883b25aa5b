#!/bin/sh

ENV=$1
MODE=$2
echo "Environment for Jenkins:" "$ENV"
echo "Mode for <PERSON>:" "$MODE"

# download the tar from hortonworks website
# TODO later lets move this to S3, in case Hortonworks pulls of these artifacts.
DIR="/data/schema-registry"
HORTONWORKS_REGISTRY_NAME=hortonworks-registry
HORTONWORKS_VERSION=0.9.0
FILENAME=${HORTONWORKS_REGISTRY_NAME}-${HORTONWORKS_VERSION}
DOWNLOAD_URL=https://github.com/hortonworks/registry/releases/download/0.9.0/hortonworks-registry-0.9.0.tar.gz

update_registry_yaml () {
  aws configure set default.region ap-south-1
  SECRET=$(aws secretsmanager get-secret-value --secret-id schema_regsitry_stage --query SecretString --output text)
  # shellcheck disable=SC2006
  SR_USERNAME=`echo "${SECRET}" | jq -r '.username'`
  # shellcheck disable=SC2006
  SR_PASSWORD=`echo "${SECRET}" | jq -r '.password'`
  yaml_file="$DIR/$FILENAME/conf/registry.yaml"
  aws s3 cp s3://epifi-dev-dp-resources/data-platform/data-configs/ec2/schema-registry/registry.yaml $yaml_file
  user_key="dataSource.user"
  username="\"${SR_USERNAME}\""
  sed -r "s/^(\s*${user_key}\s*:\s*).*/\1${username}/" -i "$yaml_file"
  password_key="dataSource.password"
  password="\"${SR_PASSWORD}\""
  sed -r "s/^(\s*${password_key}\s*:\s*).*/\1${password}/" -i "$yaml_file"
}

echo "Checking for the availability of folders, if not then create one"
if [ -d "$DIR/$FILENAME/" ]
then
	echo "Directory exists, so only stopping the server"
	sudo $DIR/$FILENAME/bin/registry stop
	update_registry_yaml
else
	echo "Directory doesnt exist, so create one and grant necessary permission"
	sudo mkdir -p $DIR
	sudo chown -R ubuntu:ubuntu $DIR
	# shellcheck disable=SC2164
	cd $DIR;
	wget ${DOWNLOAD_URL}
	tar -xzvf ${FILENAME}.tar.gz
	sudo chmod a+x $DIR/$FILENAME/bootstrap/*.sh
	update_registry_yaml
	# TODO If migration to a new version of schema registry, change from create to migrate - Future :)
	sh $DIR/$FILENAME/bootstrap/bootstrap-storage.sh "$MODE"
fi

sudo $DIR/$FILENAME/bin/registry start
echo "Server started... "
