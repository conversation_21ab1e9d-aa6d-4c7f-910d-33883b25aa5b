module "consul" {
  source                 = "../../modules/consul/v1"
  env                    = var.env
  region                 = var.region
  owner                  = var.owner
  instance_count         = 3
  aws_vpc                = var.aws_vpc
  private_subnets        = var.private_subnets
  consul_ami_id          = "ami-0ab70df30f6e9b249"
  consul_instance_type   = "t3a.small"
  vpc_cidr               = var.vpc_cidr
  secret_manager_url     = "arn:aws:secretsmanager:ap-south-1:571894593668:secret:epifi/pki/letsencrypt-jHQhwK"
  pointz_certificate_arn = var.pointz_certificate_arn
}
