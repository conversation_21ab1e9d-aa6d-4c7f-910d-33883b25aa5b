#######################################################################
#. Resource to create backups using AWS Backup Manager.
# - Creates the Backup Vault
# - Creates the IAM Role & attaches relevant IAM Policy
# - Adds the resources to create backups
#######################################################################

resource "aws_backup_vault" "monitoring" {
  name = "monitoring_backup_vault"
}

resource "aws_backup_plan" "monitoring" {
  name = "tf_monitoring_backup_plan"
  rule {
    rule_name         = "tf_monitoring_backup_rule"
    target_vault_name = aws_backup_vault.monitoring.name

    #. Run the job at "0 th" minute of "0 th" hour of each day. In other
    #  words, it creates the daily backup at 00:00 UTC.
    schedule = "cron(0 0 ? * * *)"
    lifecycle {
      cold_storage_after = null
      delete_after       = var.backup_retention_period
    }
  }
  tags = {
    env  = var.env
    name = "monitoring"
  }
}

resource "aws_iam_role" "backup-monitoring" {
  name               = "backup-monitoring"
  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": ["sts:AssumeRole"],
      "Effect": "allow",
      "Principal": {
        "Service": ["backup.amazonaws.com"]
      }
    }
  ]
}
POLICY
}

resource "aws_iam_role_policy_attachment" "backup-monitoring" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup"
  role       = aws_iam_role.backup-monitoring.name
}

resource "aws_backup_selection" "resources-monitoring" {
  count        = var.backup ? 1 : 0
  iam_role_arn = aws_iam_role.backup-monitoring.arn
  name         = "tf_monitoring_backup_selection"
  plan_id      = aws_backup_plan.monitoring.id

  #. TODO (gaurav): The following implementation of count is semantically wrong, fix it.
  resources = [
    aws_instance.prometheus[count.index].arn,
    aws_instance.alertmanager[count.index].arn,
    aws_instance.grafana[count.index].arn,
    aws_instance.victoriametrics[count.index].arn,
    aws_db_instance.grafana-rds.arn,
  ]
}

#######################################################################
