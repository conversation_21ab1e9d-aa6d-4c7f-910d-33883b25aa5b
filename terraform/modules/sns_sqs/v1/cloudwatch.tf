######### SNS cloudwatch alarm ##########
resource "aws_cloudwatch_metric_alarm" "NumberOfNotificationsFailed_topic" {
  count               = var.resource_is_sns_topic && var.env == "prod" && var.enable_cloudwatch_alarm_sns ? 1 : 0
  alarm_name          = "${var.env}-${var.topic_name}-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 5
  metric_name         = "NumberOfNotificationsFailed_topic"
  namespace           = "AWS/SNS"
  period              = "300"
  statistic           = "Average"
  threshold           = var.missed_packages_threshold
  alarm_description   = "The number of notifications failed on SNS has breeched its threshold. Urgency:${var.cloudwatch_alarm_priority}"

  dimensions = {
    QueueName = aws_sns_topic.service-sns[count.index].name
  }

  actions_enabled = true
  alarm_actions   = var.sns_arn[var.team]
  ok_actions      = var.sns_arn[var.team]
  depends_on      = [aws_sns_topic.service-sns]
}


######### SQS cloudwatch alarm ##########
resource "aws_cloudwatch_metric_alarm" "ApproximateNumberOfMessagesVisible" {
  count               = var.resource_is_queue && var.env == "prod" && var.enable_cloudwatch_alarm_sqs ? 1 : 0
  alarm_name          = "${var.env}-${var.queue_name}-ApproximateNumberOfMessagesVisible"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 5
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  period              = "300"
  statistic           = "Average"
  threshold           = var.avg_number_of_messages_visible
  alarm_description   = "The approximate number of messages visible in ${var.env}-${var.queue_name} has breeched its threshold of ${var.avg_number_of_messages_visible}. Urgency:${var.cloudwatch_alarm_visible_message_priority}"

  dimensions = {
    QueueName = aws_sqs_queue.queue[count.index].name
  }

  actions_enabled = true
  alarm_actions   = var.sns_arn[var.team]
  ok_actions      = var.sns_arn[var.team]
  depends_on      = [aws_sqs_queue.queue]
}

resource "aws_cloudwatch_metric_alarm" "ApproximateNumberOfMessagesNotVisible" {
  count               = var.resource_is_queue && var.env == "prod" && var.enable_cloudwatch_alarm_sqs ? 1 : 0
  alarm_name          = "${var.env}-${var.queue_name}-ApproximateNumberOfMessagesNotVisible"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 5
  metric_name         = "ApproximateNumberOfMessagesNotVisible"
  namespace           = "AWS/SQS"
  period              = "300"
  statistic           = "Average"
  threshold           = var.avg_number_of_messages_not_visible
  alarm_description   = "The approximate number of messages not visible in ${var.env}-${var.queue_name} has breeched its threshold of ${var.avg_number_of_messages_not_visible} Urgency:${var.cloudwatch_alarm_not_visible_message_priority}"

  dimensions = {
    QueueName = aws_sqs_queue.queue[count.index].name
  }

  actions_enabled = true
  alarm_actions   = var.sns_arn[var.team]
  ok_actions      = var.sns_arn[var.team]
  depends_on      = [aws_sqs_queue.queue]
}

resource "aws_cloudwatch_metric_alarm" "ApproximateNumberOfMessagesVisible-dlq" {
  count               = var.enable_dlq && var.resource_is_queue && var.env == "prod" && var.enable_cloudwatch_alarm_sqs_dlq ? 1 : 0
  alarm_name          = "${var.env}-dlq-${var.queue_name}-ApproximateNumberOfMessagesVisible-dlq"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 5
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  period              = "300"
  statistic           = "Maximum"
  threshold           = var.approximate_number_messages_visible_dlq
  alarm_description   = "The Number of Message visible in ${var.env}-dlq-${var.queue_name} has breeched its threshold of ${var.approximate_number_messages_visible_dlq}. Urgency:${var.cloudwatch_alarm_visible_message_priority}"

  dimensions = {
    QueueName = aws_sqs_queue.dlq-queue[count.index].name
  }
  actions_enabled = true
  alarm_actions   = var.sns_arn[var.team]
  ok_actions      = var.sns_arn[var.team]
  depends_on      = [aws_sqs_queue.dlq-queue]
}

resource "aws_cloudwatch_metric_alarm" "ApproximateNumberOfMessagesNotVisible-dlq" {
  count               = var.enable_dlq && var.resource_is_queue && var.env == "prod" && var.enable_cloudwatch_alarm_sqs_dlq ? 1 : 0
  alarm_name          = "${var.env}-dlq-${var.queue_name}-ApproximateNumberOfMessagesNotVisible-dlq"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 5
  metric_name         = "ApproximateNumberOfMessagesNotVisible"
  namespace           = "AWS/SQS"
  period              = "300"
  statistic           = "Maximum"
  threshold           = var.avg_number_of_messages_not_visible_dlq
  alarm_description   = "The Number of Message not visible in ${var.env}-dlq-${var.queue_name} has breeched its threshold of ${var.avg_number_of_messages_not_visible_dlq}. Urgency:${var.cloudwatch_alarm_not_visible_message_priority}"

  dimensions = {
    QueueName = aws_sqs_queue.dlq-queue[count.index].name
  }
  actions_enabled = true
  alarm_actions   = var.sns_arn[var.team]
  ok_actions      = var.sns_arn[var.team]
  depends_on      = [aws_sqs_queue.dlq-queue]
}
