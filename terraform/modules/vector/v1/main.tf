# Creation of Launch Template
data "aws_ebs_default_kms_key" "current" {}

data "aws_kms_key" "current" {
  key_id = data.aws_ebs_default_kms_key.current.key_arn
}

resource "aws_launch_template" "service-template" {
  name = "${var.env}-vector-launch-template"
  block_device_mappings {
    device_name = "/dev/sda1"
    ebs {
      volume_type = "gp3"
      iops        = 3000
      throughput  = 125
      volume_size = 12
      encrypted   = true
      kms_key_id  = data.aws_kms_key.current.arn
    }
  }
  ebs_optimized = true
  iam_instance_profile {
    name = aws_iam_instance_profile.vector-instance-profile.name
  }
  user_data                            = base64encode(var.user_data)
  image_id                             = var.image_id
  instance_initiated_shutdown_behavior = "terminate"
  instance_type                        = var.ec2_instance_type

  network_interfaces {
    delete_on_termination       = true
    security_groups             = var.ec2_security_groups
    associate_public_ip_address = false
  }
  placement {
    tenancy = "default"
  }

  tag_specifications {
    resource_type = "instance"
    tags = merge(
      {
        resource_type = "instance"
        resource      = "ec2"
        service       = "vector"
        "epifi:resource"  = "ec2"
      }, var.tags
    )
  }
}

resource "aws_autoscaling_group" "vector" {
  name                      = "${var.env}-${var.name}"
  max_size                  = var.max_size
  min_size                  = var.min_size
  health_check_grace_period = 300
  health_check_type         = "ELB"
  force_delete              = true
  target_group_arns         = [
    aws_lb_target_group.vector.arn,
    aws_lb_target_group.vector_secure.arn
  ]
  vpc_zone_identifier       = var.desired_subnets_for_lb
  launch_template {
    id      = aws_launch_template.service-template.id
    version = aws_launch_template.service-template.latest_version
  }
  tag {
    key                 = "name"
    propagate_at_launch = true
    value               = "${var.env}-${var.name}"
  }
  tag {
    key                 = "environment"
    value               = var.env
    propagate_at_launch = true
  }
  instance_refresh {
    strategy = "Rolling"
    preferences {
      min_healthy_percentage = 90
      instance_warmup = 60
    }
  }
}
