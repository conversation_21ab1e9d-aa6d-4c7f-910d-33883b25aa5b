############################################################################
#. Resource to create the private DNS in Route53.
############################################################################

resource "aws_route53_record" "service_dns_record" {

  zone_id = var.private_hosted_zone_id
  name    = "${var.env}-crdb.epifi.vpc"
  type    = "A"
  alias {
    name                   = aws_lb.cockroach-nlb.dns_name
    zone_id                = aws_lb.cockroach-nlb.zone_id
    evaluate_target_health = false
  }

}

############################################################################
