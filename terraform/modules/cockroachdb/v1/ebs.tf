resource "aws_volume_attachment" "ebs_att" {
  count       = var.instance_count
  device_name = "/dev/sdh"
  volume_id   = aws_ebs_volume.ebs_volume[count.index].id
  instance_id = aws_instance.cockroachdb[count.index].id
}

resource "aws_ebs_volume" "ebs_volume" {
  count             = var.instance_count
  availability_zone = var.availability_zone[count.index % length(var.availability_zone)]
  size              = var.volume_size
  encrypted         = true
  tags = {
    name           = "cockroachdb-ebs-vol"
    env            = var.env
    owner          = var.owner
    resource_owner = var.resource_owner
    type           = "cockroachdb"
    service_name   = "cockroachdb"
  }
}
