package dao

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	gormv2 "gorm.io/gorm"

	typesPb "github.com/epifi/gamma/api/typesv2"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

// WithOwnership returns storage.FilterOption that sets ownership filter in the DB.
func WithOwnership(ownerShip commontypes.Ownership) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("ownership = ?", ownerShip)
	})
}

// WithActorType returns storage.FilterOption that sets actor type filter in the DB.
func WithActorType(actorType typesPb.Actor_Type) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("type = ?", actorType)
	})
}
