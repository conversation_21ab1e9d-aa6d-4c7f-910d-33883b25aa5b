package p2p

import (
	"context"

	"go.uber.org/zap"

	investmentPb "github.com/epifi/gamma/api/investment"
	aggregatorPb "github.com/epifi/gamma/api/investment/aggregator"
	"github.com/epifi/gamma/api/p2pinvestment"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

type P2P struct {
	p2pInvestmentClient p2pinvestment.P2PInvestmentClient
}

func NewP2PAggregatorPlugin(p2pInvestmentClient p2pinvestment.P2PInvestmentClient) *P2P {
	return &P2P{p2pInvestmentClient: p2pInvestmentClient}
}

func (p *P2P) GetAggregatedInvestment(ctx context.Context, actorID string) map[investmentPb.InvestmentInstrumentType]*aggregatorPb.InvestmentSummary {
	res, err := p.p2pInvestmentClient.GetInvestmentDashboard(ctx, &p2pinvestment.GetInvestmentDashboardRequest{ActorId: actorID})
	if res.GetStatus().IsRecordNotFound() ||
		res.GetStatus().GetCode() == uint32(p2pinvestment.GetInvestmentDashboardResponse_INVESTOR_NOT_APPLICABLE_FOR_DASHBOARD) {
		return map[investmentPb.InvestmentInstrumentType]*aggregatorPb.InvestmentSummary{investmentPb.InvestmentInstrumentType_P2P_JUMP: &aggregatorPb.InvestmentSummary{
			InvestedAmount:           moneyPb.ZeroINR().GetPb(),
			CurrentValue:             moneyPb.ZeroINR().GetPb(),
			InProgressBuyOrderAmount: moneyPb.ZeroINR().GetPb(),
		}}
	}
	if grpcErr := epifigrpc.RPCError(res, err); grpcErr != nil {
		logger.Error(ctx, "error in GetInvestmentDashboard", zap.String(logger.ACTOR_ID_V2, actorID), zap.Error(grpcErr))
		return map[investmentPb.InvestmentInstrumentType]*aggregatorPb.InvestmentSummary{investmentPb.InvestmentInstrumentType_P2P_JUMP: {HasError: true}}
	}

	if (res.GetInvestmentData().GetTotalInvestedAmount() != nil && moneyPb.IsPositive(res.GetInvestmentData().GetTotalInvestedAmount())) ||
		res.GetInvestmentData().GetInProgressInvestmentCount() > 0 ||
		moneyPb.IsPositive(res.GetInvestmentData().GetInProgressInvestmentAmount()) {
		growthValue, subErr := moneyPb.Subtract(res.GetInvestmentData().GetCurrentValue(), res.GetInvestmentData().GetTotalPrincipalAmount())
		if subErr != nil {
			logger.Error(ctx, "error in money subtract in GetAggregatedInvestment", zap.String(logger.ACTOR_ID_V2, actorID), zap.Error(subErr))
			return map[investmentPb.InvestmentInstrumentType]*aggregatorPb.InvestmentSummary{investmentPb.InvestmentInstrumentType_P2P_JUMP: {HasError: true}}
		}

		if moneyPb.IsNegative(growthValue) {
			logger.Error(ctx, "growth value is negative", zap.String(logger.ACTOR_ID_V2, actorID), zap.Any("growth_value", growthValue))
			return map[investmentPb.InvestmentInstrumentType]*aggregatorPb.InvestmentSummary{investmentPb.InvestmentInstrumentType_P2P_JUMP: {HasError: true}}
		}

		var growthPercentageFloat float64
		if !moneyPb.IsZero(res.GetInvestmentData().GetTotalPrincipalAmount()) {
			growthPercentage, divErr := moneyPb.CalculateDiffPercentage(res.GetInvestmentData().GetCurrentValue(), res.GetInvestmentData().GetTotalPrincipalAmount())
			if divErr != nil {
				logger.Error(ctx, "error in money division in GetAggregatedInvestment", zap.String(logger.ACTOR_ID_V2, actorID), zap.Error(divErr))
				return map[investmentPb.InvestmentInstrumentType]*aggregatorPb.InvestmentSummary{investmentPb.InvestmentInstrumentType_P2P_JUMP: {HasError: true}}
			}
			growthPercentageFloat, _ = growthPercentage.Float64()
		}

		return map[investmentPb.InvestmentInstrumentType]*aggregatorPb.InvestmentSummary{investmentPb.InvestmentInstrumentType_P2P_JUMP: &aggregatorPb.InvestmentSummary{
			InvestedAmount:           res.GetInvestmentData().GetTotalPrincipalAmount(),
			CurrentValue:             res.GetInvestmentData().GetCurrentValue(),
			GrowthPercentage:         growthPercentageFloat,
			GrowthValue:              growthValue,
			PendingOrdersPresent:     res.GetInvestmentData().GetInProgressInvestmentCount() > 0,
			InProgressBuyOrderAmount: res.GetInvestmentData().GetInProgressInvestmentAmount(),
			PendingOrdersCount:       int64(res.GetInvestmentData().GetInProgressInvestmentCount()),
			HasInvested:              true,
			HasError:                 false,
		}}
	}
	return map[investmentPb.InvestmentInstrumentType]*aggregatorPb.InvestmentSummary{investmentPb.InvestmentInstrumentType_P2P_JUMP: {HasError: false, HasInvested: false}}

}

func (p *P2P) GetInvestmentInstrumentType() investmentPb.InvestmentInstrumentType {
	return investmentPb.InvestmentInstrumentType_P2P_JUMP
}
