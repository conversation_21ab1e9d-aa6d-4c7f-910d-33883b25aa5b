package dao_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/testing/protocmp"

	pb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	"github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/gamma/investment/dynamic_ui_element/dao"
	"github.com/epifi/gamma/investment/dynamic_ui_element/dao/mocks"
	cacheMocks "github.com/epifi/be-common/pkg/cache/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
)

type DynamicUIElementCacheTestSuite struct {
	genConfig *genconf.Config
}

func NewDynamicUIElementCacheTestSuite(dynConf *genconf.Config) *DynamicUIElementCacheTestSuite {
	return &DynamicUIElementCacheTestSuite{genConfig: dynConf}
}

type mockDynamicUIElementCacheStruct struct {
	cacheConfig *genconf.DynamicUIElementCacheConfig
	mockCache   *cacheMocks.MockCacheStorage
	mockDBDao   *mocks.MockDynamicUIElementDBDao
}

func TestDynamicUIElementDaoCache_GetByVariantName(t *testing.T) {
	ctx := context.Background()
	type args struct {
		ctx         context.Context
		variantName string
	}
	var tests = []struct {
		name      string
		args      args
		wantMocks func(args args, mock *mockDynamicUIElementCacheStruct)
		want      *pb.DynamicUIElement
		wantErr   error
	}{
		{
			name: "record found in cache",
			args: args{
				ctx:         ctx,
				variantName: sampleDynamicUIElementObj.GetVariantName(),
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				dynamicUIElementBytes, _ := protojson.Marshal(sampleDynamicUIElementObj)
				cacheKey := dao.DynamicUIElementPrefix + args.variantName
				mock.mockCache.EXPECT().Get(ctx, cacheKey).Return(string(dynamicUIElementBytes), nil)
			},
			want:    sampleDynamicUIElementObj,
			wantErr: nil,
		},
		{
			name: "cache record not found, DB success, update in cache success",
			args: args{
				ctx:         ctx,
				variantName: sampleDynamicUIElementObj.GetVariantName(),
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				cacheKey := dao.DynamicUIElementPrefix + args.variantName
				mock.mockCache.EXPECT().Get(ctx, cacheKey).Return("", epifierrors.ErrRecordNotFound)
				mock.mockDBDao.EXPECT().GetByVariantName(ctx, args.variantName).Return(sampleDynamicUIElementObj, nil)
				configBytes, _ := protojson.Marshal(sampleDynamicUIElementObj)
				mock.mockCache.EXPECT().Set(ctx, cacheKey, string(configBytes), mock.cacheConfig.CacheTTL()).Return(nil)
			},
			want:    sampleDynamicUIElementObj,
			wantErr: nil,
		},
		{
			name: "cache record not found, DB failure",
			args: args{
				ctx:         ctx,
				variantName: sampleDynamicUIElementObj.GetVariantName(),
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				cacheKey := dao.DynamicUIElementPrefix + args.variantName
				mock.mockCache.EXPECT().Get(ctx, cacheKey).Return("", epifierrors.ErrRecordNotFound)
				mock.mockDBDao.EXPECT().GetByVariantName(ctx, args.variantName).Return(nil, errors.New("DB failure"))
			},
			want:    nil,
			wantErr: errors.New("DB failure"),
		},
		{
			name: "cache record not found, DB success, update in cache failed",
			args: args{
				ctx:         ctx,
				variantName: sampleDynamicUIElementObj.GetVariantName(),
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				cacheKey := dao.DynamicUIElementPrefix + args.variantName
				mock.mockCache.EXPECT().Get(ctx, cacheKey).Return("", epifierrors.ErrRecordNotFound)
				mock.mockDBDao.EXPECT().GetByVariantName(ctx, args.variantName).Return(sampleDynamicUIElementObj, nil)
				configBytes, _ := protojson.Marshal(sampleDynamicUIElementObj)
				mock.mockCache.EXPECT().Set(ctx, cacheKey, string(configBytes), mock.cacheConfig.CacheTTL()).Return(errors.New("cache failure"))
			},
			want:    sampleDynamicUIElementObj,
			wantErr: errors.New("cache failure"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockDBDao := mocks.NewMockDynamicUIElementDBDao(ctrl)
			mockCache := cacheMocks.NewMockCacheStorage(ctrl)
			// setting TTL explicitly for test suite to avoid NO_TTL cases
			_ = dynamicUIElementCacheTestSuite.genConfig.DynamicUIElementCacheConfig().SetCacheTTL("1h", false, nil)

			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockDynamicUIElementCacheStruct{
					mockCache:   mockCache,
					mockDBDao:   mockDBDao,
					cacheConfig: dynamicUIElementCacheTestSuite.genConfig.DynamicUIElementCacheConfig(),
				})
			}
			b := dao.NewDynamicUIElementDaoCache(dynamicUIElementCacheTestSuite.genConfig.DynamicUIElementCacheConfig(), mockCache, mockDBDao)
			got, err := b.GetByVariantName(tt.args.ctx, tt.args.variantName)
			if tt.wantErr != nil {
				assert.EqualError(t, err, tt.wantErr.Error(), fmt.Sprintf("GetByVariantName() error = %v, wantErr %v", err, tt.wantErr))
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.DynamicUIElement{}, "created_at", "updated_at", "id"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByVariantName() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestDynamicUIElementDaoCache_Create(t *testing.T) {
	ctx := context.Background()
	type args struct {
		ctx              context.Context
		dynamicUIElement *pb.DynamicUIElement
	}
	tests := []struct {
		name      string
		args      args
		wantMocks func(args args, mock *mockDynamicUIElementCacheStruct)
		want      *pb.DynamicUIElement
		wantErr   error
	}{
		{
			name: "create record DB failure",
			args: args{
				ctx:              ctx,
				dynamicUIElement: sampleDynamicUIElementObj,
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				mock.mockDBDao.EXPECT().Create(ctx, args.dynamicUIElement).Return(nil, errors.New("DB timeout"))
			},
			want:    nil,
			wantErr: errors.New("DB timeout"),
		},
		{
			name: "create success, cache set failed",
			args: args{
				ctx:              ctx,
				dynamicUIElement: sampleDynamicUIElementObj,
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				mock.mockDBDao.EXPECT().Create(ctx, args.dynamicUIElement).Return(sampleDynamicUIElementObj, nil)
				cacheKey := dao.DynamicUIElementPrefix + args.dynamicUIElement.GetVariantName()
				configBytes, _ := protojson.Marshal(sampleDynamicUIElementObj)
				mock.mockCache.EXPECT().Set(ctx, cacheKey, string(configBytes), mock.cacheConfig.CacheTTL()).Return(errors.New("cache error"))
			},
			want:    sampleDynamicUIElementObj,
			wantErr: nil,
		},
		{
			name: "success",
			args: args{
				ctx:              ctx,
				dynamicUIElement: sampleDynamicUIElementObj,
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				mock.mockDBDao.EXPECT().Create(ctx, args.dynamicUIElement).Return(sampleDynamicUIElementObj, nil)
				cacheKey := dao.DynamicUIElementPrefix + args.dynamicUIElement.GetVariantName()
				configBytes, _ := protojson.Marshal(sampleDynamicUIElementObj)
				mock.mockCache.EXPECT().Set(ctx, cacheKey, string(configBytes), mock.cacheConfig.CacheTTL()).Return(nil)
			},
			want:    sampleDynamicUIElementObj,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockDBDao := mocks.NewMockDynamicUIElementDBDao(ctrl)
			mockCache := cacheMocks.NewMockCacheStorage(ctrl)
			// setting TTL explicitly for test suite to avoid NO_TTL cases
			_ = dynamicUIElementCacheTestSuite.genConfig.DynamicUIElementCacheConfig().SetCacheTTL("1h", false, nil)
			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockDynamicUIElementCacheStruct{
					mockCache:   mockCache,
					mockDBDao:   mockDBDao,
					cacheConfig: dynamicUIElementCacheTestSuite.genConfig.DynamicUIElementCacheConfig(),
				})
			}
			b := dao.NewDynamicUIElementDaoCache(dynamicUIElementCacheTestSuite.genConfig.DynamicUIElementCacheConfig(), mockCache, mockDBDao)
			got, err := b.Create(tt.args.ctx, tt.args.dynamicUIElement)
			if tt.wantErr != nil {
				assert.EqualError(t, err, tt.wantErr.Error(), fmt.Sprintf("Create() error = %v, wantErr %v", err, tt.wantErr))
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.DynamicUIElement{}, "created_at", "updated_at", "id"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestDynamicUIElementDaoCache_Update(t *testing.T) {
	ctx := context.Background()
	type args struct {
		ctx              context.Context
		fieldMask        []pb.DynamicUIElementFieldMask
		dynamicUIElement *pb.DynamicUIElement
	}
	tests := []struct {
		name      string
		args      args
		wantMocks func(args args, mock *mockDynamicUIElementCacheStruct)
		want      *pb.DynamicUIElement
		wantErr   error
	}{
		{
			name: "failed to update in cache",
			args: args{
				ctx:              ctx,
				fieldMask:        []pb.DynamicUIElementFieldMask{pb.DynamicUIElementFieldMask_DYNAMIC_UI_ELEMENT_FIELD_MASK_CONTENT_JSON},
				dynamicUIElement: sampleDynamicUIElementObj,
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				cacheKey := dao.DynamicUIElementPrefix + args.dynamicUIElement.GetVariantName()
				configBytes, _ := protojson.Marshal(sampleDynamicUIElementObj)
				mock.mockCache.EXPECT().Set(ctx, cacheKey, string(configBytes), mock.cacheConfig.CacheTTL()).Return(errors.New("cache error"))
			},
			want:    nil,
			wantErr: errors.New("cache error"),
		},
		{
			name: "cache updated, DB update failure",
			args: args{
				ctx:              ctx,
				fieldMask:        []pb.DynamicUIElementFieldMask{pb.DynamicUIElementFieldMask_DYNAMIC_UI_ELEMENT_FIELD_MASK_CONTENT_JSON},
				dynamicUIElement: sampleDynamicUIElementObj,
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				cacheKey := dao.DynamicUIElementPrefix + args.dynamicUIElement.GetVariantName()
				configBytes, _ := protojson.Marshal(sampleDynamicUIElementObj)
				mock.mockCache.EXPECT().Set(ctx, cacheKey, string(configBytes), mock.cacheConfig.CacheTTL()).Return(nil)
				mock.mockDBDao.EXPECT().Update(ctx, []pb.DynamicUIElementFieldMask{
					pb.DynamicUIElementFieldMask_DYNAMIC_UI_ELEMENT_FIELD_MASK_CONTENT_JSON},
					sampleDynamicUIElementObj).Return(nil, errors.New("DB error"))
				mock.mockCache.EXPECT().Delete(ctx, cacheKey).Return(errors.New("cache error"))
			},
			want:    nil,
			wantErr: errors.New("DB error"),
		},
		{
			name: "success",
			args: args{
				ctx:              ctx,
				fieldMask:        []pb.DynamicUIElementFieldMask{pb.DynamicUIElementFieldMask_DYNAMIC_UI_ELEMENT_FIELD_MASK_CONTENT_JSON},
				dynamicUIElement: sampleDynamicUIElementObj,
			},
			wantMocks: func(args args, mock *mockDynamicUIElementCacheStruct) {
				cacheKey := dao.DynamicUIElementPrefix + args.dynamicUIElement.GetVariantName()
				configBytes, _ := protojson.Marshal(sampleDynamicUIElementObj)
				mock.mockCache.EXPECT().Set(ctx, cacheKey, string(configBytes), mock.cacheConfig.CacheTTL()).Return(nil)
				mock.mockDBDao.EXPECT().Update(ctx, []pb.DynamicUIElementFieldMask{
					pb.DynamicUIElementFieldMask_DYNAMIC_UI_ELEMENT_FIELD_MASK_CONTENT_JSON},
					sampleDynamicUIElementObj).Return(sampleDynamicUIElementObj, nil)
			},
			want:    sampleDynamicUIElementObj,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockDBDao := mocks.NewMockDynamicUIElementDBDao(ctrl)
			mockCache := cacheMocks.NewMockCacheStorage(ctrl)
			// setting TTL explicitly for test suite to avoid NO_TTL cases
			_ = dynamicUIElementCacheTestSuite.genConfig.DynamicUIElementCacheConfig().SetCacheTTL("1h", false, nil)
			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockDynamicUIElementCacheStruct{
					mockCache:   mockCache,
					mockDBDao:   mockDBDao,
					cacheConfig: dynamicUIElementCacheTestSuite.genConfig.DynamicUIElementCacheConfig(),
				})
			}
			b := dao.NewDynamicUIElementDaoCache(dynamicUIElementCacheTestSuite.genConfig.DynamicUIElementCacheConfig(), mockCache, mockDBDao)
			got, err := b.Update(tt.args.ctx, tt.args.fieldMask, tt.args.dynamicUIElement)
			if tt.wantErr != nil {
				assert.EqualError(t, err, tt.wantErr.Error(), fmt.Sprintf("Update() error = %v, wantErr %v", err, tt.wantErr))
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.DynamicUIElement{}, "created_at", "updated_at", "id"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Update() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}
