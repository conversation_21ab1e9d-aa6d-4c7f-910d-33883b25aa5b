// nolint:dupl
package usstocks

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	dynamicExperimentPb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	typespb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/be-common/pkg/logger"
)

type USSLandingPageABExperimentEvaluator struct {
	abExperimentEvaluator *release.ABEvaluator[dynamicExperimentPb.USSLandingPageExperiment]
}

func NewUSSLandingPageComponentABExperimentEvaluator(abExperimentEvaluator *release.ABEvaluator[dynamicExperimentPb.USSLandingPageExperiment]) *USSLandingPageABExperimentEvaluator {
	return &USSLandingPageABExperimentEvaluator{abExperimentEvaluator: abExperimentEvaluator}
}

func (s *USSLandingPageABExperimentEvaluator) Evaluate(ctx context.Context, actorId string, expectedVariant string) bool {
	isEnabled, variant, err := s.abExperimentEvaluator.Evaluate(ctx, &release.CommonConstraintData{
		ActorId: actorId,
		Feature: typespb.Feature_USS_LANDING_PAGE_AB_SUPPORT,
	})
	logger.Debug(ctx, "AB evaluator result", zap.Any("is_enable", isEnabled), zap.Any("variant", variant))
	if err != nil || !isEnabled {
		logger.Error(ctx, fmt.Sprintf("error in invoking or feature not enabled for Evaluate. isEnabled: %v . Falling back to default", isEnabled), zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		// falling back to default case
		return strings.Compare("DEFAULT", expectedVariant) == 0
	}
	if variant == dynamicExperimentPb.USSLandingPageExperiment_USS_LANDING_PAGE_EXP_DEFAULT || variant == dynamicExperimentPb.USSLandingPageExperiment_USS_LANDING_PAGE_EXP_UNSPECIFIED {
		// marking for unspecified and default as default
		return strings.Compare("DEFAULT", expectedVariant) == 0
	}
	return strings.Compare(variant.String(), expectedVariant) == 0
}
