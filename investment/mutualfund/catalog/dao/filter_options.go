package dao

import (
	"strings"

	gormv2 "gorm.io/gorm"

	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

// WithId returns storagev2.FilterOption that checks if requested Id is present in DB
func WithId(id string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("id = ?", id)
	})
}

// WithScreen returns storagev2.FilterOption that checks if screen is present in the DB record's allowed_screens
func WithScreen(screen catalog.CollectionScreen) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("'" + screen.String() + "' = ANY(allowed_screens)")
	})
}

// WithUserGroups returns storagev2.FilterOption that checks if any of the provided user groups overlaps with those in the DB record
func WithUserGroups(userGroups []string) storagev2.FilterOption {
	if len(userGroups) == 0 {
		return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
			return db.Where("allowed_user_groups IS NULL")
		})
	}
	userGroupsConcatenated := "'" + strings.Join(userGroups, "', '") + "'"
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("allowed_user_groups IS NULL OR allowed_user_groups &&  Array[" + userGroupsConcatenated + "]")
	})
}

// WithCollectionId returns storagev2.FilterOption that checks if requested collection ID is present
func WithCollectionId(collectionId string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("collection_id = ?", collectionId)
	})
}

// WithMutualFundIds returns storagev2.FilterOption that checks if requested MF IDs are present
func WithMutualFundIds(mfIds []string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("mutual_fund_id IN (?)", mfIds)
	})
}

// WithFilterGroupLevel returns storagev2.FilterOption that checks if requested MF filter groups are of the particular level
func WithFilterGroupLevel(level catalog.FundFilterGroup_Level) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("mf_filter_groups.level = ?", level)
	})
}

// WithFilterGroupIds returns storagev2.FilterOption that checks if requested MF filter group ids are present
func WithFilterGroupIds(groupIds []string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("mf_filter_groups.name IN (?)", groupIds)
	})
}

// WithFilterIds returns storagev2.FilterOption that checks if requested MF filter ids are present
func WithFilterIds(filterIds []string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("mf_filters.name IN (?)", filterIds)
	})
}

func WithNames(filterNames []string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("name IN (?)", filterNames)
	})
}

// WithCollectionIds returns storagev2.FilterOption that filters the collections according to CollectionIDs
func WithCollectionIds(collectionIds []string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gormv2.DB) *gormv2.DB {
		return db.Where("collection_id IN (?)", collectionIds)
	})
}
