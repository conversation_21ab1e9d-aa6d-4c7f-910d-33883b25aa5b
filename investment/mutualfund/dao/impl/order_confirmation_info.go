package impl

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/wire"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/gamma/investment/mutualfund/dao/model"
)

var (
	orderConfirmationMetadataFieldMaskToColumn = map[orderPb.OrderConfirmationInfoFieldMASK]string{
		orderPb.OrderConfirmationInfoFieldMASK_ORDER_CONFIRMATION_METADATA:   "order_confirmation_metadata",
		orderPb.OrderConfirmationInfoFieldMASK_PAYMENT_CONFIRMATION_METADATA: "payment_confirmation_metadata",
	}
	orderConfirmationMetadataUnspecifiedFieldFilter = func(field orderPb.OrderConfirmationInfoFieldMASK) bool {
		return orderPb.OrderConfirmationInfoFieldMASK_ORDER_CONFIRMATION_INFO_MASK_UNSPECIFIED != field
	}
)

type OrderConfirmationInfoCrdb struct {
	db *gormv2.DB
}

func NewMFOrderConfirmationInfoCrdb(db *gormv2.DB) *OrderConfirmationInfoCrdb {
	return &OrderConfirmationInfoCrdb{db: db}
}

var OrderConfirmationInfoWireSet = wire.NewSet(NewMFOrderConfirmationInfoCrdb, wire.Bind(new(dao.OrderConfirmationInfoDao), new(*OrderConfirmationInfoCrdb)))

func (d *OrderConfirmationInfoCrdb) Create(ctx context.Context, orderConfirmationMetadata *orderPb.OrderConfirmationInfo) (*orderPb.OrderConfirmationInfo, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderConfirmationInfoCrdb", "Create", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it for creating the mutual fund entry
	// if not, use the gorm.DB connection object in MutualFundCrdb
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	infoModel := model.NewOrderConfirmationMetadata(orderConfirmationMetadata)
	// ToDO(Junaid): Remove this log
	logger.Info(ctx, fmt.Sprintf("creating OrderConfirmation Info: %v for orderID: %s", infoModel, orderConfirmationMetadata.OrderId))
	if err := db.Create(infoModel).Error; err != nil {
		if storagev2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, err
	}
	// ToDO(Junaid): Remove this log
	logger.Info(ctx, fmt.Sprintf("created OrderConfirmation Info: %v for orderID: %s", infoModel, orderConfirmationMetadata.OrderId))
	return infoModel.ToProto(), nil
}

//nolint:dupl
func (d *OrderConfirmationInfoCrdb) GetByOrderId(ctx context.Context, orderId string) (*orderPb.OrderConfirmationInfo, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderConfirmationInfoCrdb", "GetByOrderId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	infoModel := &model.OrderConfirmationInfo{}
	if res := db.Where("order_id = ?", orderId).First(infoModel); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch OrderConfirmationInfo: %s: %w", orderId, res.Error)
	}
	return infoModel.ToProto(), nil
}

//nolint:dupl
func (d *OrderConfirmationInfoCrdb) GetByTransactionNumberAndRta(ctx context.Context, transactionNumber string, rta commonvgpb.Vendor) (*orderPb.OrderConfirmationInfo, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderConfirmationInfoCrdb", "GetByTransactionNumberAndRta", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	infoModel := &model.OrderConfirmationInfo{}
	if res := db.Where("rta_transaction_number = ? and rta = ?", transactionNumber, rta).First(infoModel); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch OrderConfirmationInfo: %s: %w", transactionNumber, res.Error)
	}
	return infoModel.ToProto(), nil
}

//nolint:dupl
func (d *OrderConfirmationInfoCrdb) UpdateByOrderId(ctx context.Context, info *orderPb.OrderConfirmationInfo,
	updateMasks []orderPb.OrderConfirmationInfoFieldMASK) error {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderConfirmationInfoCrdb", "UpdateByOrderId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	updateMasks = filterOrderConfirmationInfoFieldMaskSlice(updateMasks, orderConfirmationMetadataUnspecifiedFieldFilter)
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}
	updatedColumns := getSelectColumnsForOrderConfirmationInfoUpdate(updateMasks)

	infoModel := model.NewOrderConfirmationMetadata(info)
	resp := db.Model(infoModel).Where("order_id = ?", info.OrderId).Select(updatedColumns).Updates(infoModel)
	if resp.Error != nil {
		return fmt.Errorf("unable to update OrderConfirmationInfo  id %s, %w", info.OrderId, resp.Error)
	}
	if resp.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

// filterOrderConfirmationInfoFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterOrderConfirmationInfoFieldMaskSlice(fieldMasks []orderPb.OrderConfirmationInfoFieldMASK,
	check func(field orderPb.OrderConfirmationInfoFieldMASK) bool) []orderPb.OrderConfirmationInfoFieldMASK {
	var ret []orderPb.OrderConfirmationInfoFieldMASK
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

// getSelectColumnsForOrderConfirmationMetadataUpdate converts update mask to string slice with column name
// corresponding to field name enums
func getSelectColumnsForOrderConfirmationInfoUpdate(updateMask []orderPb.OrderConfirmationInfoFieldMASK) []string {
	var selectColumns []string

	for _, field := range updateMask {
		selectColumns = append(selectColumns, orderConfirmationMetadataFieldMaskToColumn[field])
	}
	return selectColumns
}
