//nolint:dupl
package processor

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	devPb "github.com/epifi/gamma/api/investment/mutualfund/developer"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/dao"
	"github.com/epifi/be-common/pkg/logger"
)

const FILE_ID = "File Id"

type MFFileGenerationAttemptsProcessor struct {
	fileGenerationAttemptDao dao.FileGenerationAttemptDao
}

func NewMFFileGenerationAttemptsProcessor(fileGenerationAttemptDao dao.FileGenerationAttemptDao) *MFFileGenerationAttemptsProcessor {
	return &MFFileGenerationAttemptsProcessor{
		fileGenerationAttemptDao: fileGenerationAttemptDao,
	}
}

//nolint:dupl
func (d *MFFileGenerationAttemptsProcessor) FetchParamList(ctx context.Context, entity devPb.MutualFundEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            FILE_ID,
			Label:           "File ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

//nolint:dupl
func (d *MFFileGenerationAttemptsProcessor) FetchData(ctx context.Context, entity devPb.MutualFundEntity, filters []*db_state.Filter) (string, error) {
	var (
		err    error
		fileId string
	)
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil to fetch file generation attempts")
	}
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case FILE_ID:
			fileId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	fileDetails, err := d.fileGenerationAttemptDao.GetFileGenerationAttemptById(ctx, fileId)
	if err != nil {
		return err.Error(), nil
	}

	marshalOptions := protojson.MarshalOptions{}
	marshalOptions.UseEnumNumbers = false

	e, marshalErr := marshalOptions.Marshal(fileDetails)
	if marshalErr != nil {
		logger.Error(ctx,
			fmt.Sprintf("cannot marshal fileDetails details to json for fileID: %s",
				fileId), zap.Error(err))
		return fmt.Sprintf("{\"error\": \"cannot marshal fileDetails details to json \", \"error\":\"%v\"}", err.Error()), nil
	}

	return string(e), nil
}
