package validator

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"

	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/be-common/pkg/logger"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"

	"github.com/epifi/be-common/pkg/money"
)

var (
	PurchaseConstraintsValidationError         = errors.New("PurchaseConstraintsValidationError")
	PurchaseScheduleConstraintsValidationError = errors.New("PurchaseScheduleConstraintsValidationError")
	SipGranularityToAipFrequency               = map[mfPb.SIPGranularity]mfPb.AipFrequency{
		mfPb.SIPGranularity_SIP_GRANULARITY_DAILY:   mfPb.AipFrequency_DAILY,
		mfPb.SIPGranularity_SIP_GRANULARITY_WEEKLY:  mfPb.AipFrequency_WEEKLY,
		mfPb.SIPGranularity_SIP_GRANULARITY_MONTHLY: mfPb.AipFrequency_MONTHLY,
	}
)

type PurchaseConstraintsValidator struct {
}

func NewPurchaseConstraintsValidator() *PurchaseConstraintsValidator {
	return &PurchaseConstraintsValidator{}
}

func (t *PurchaseConstraintsValidator) Validate(ctx context.Context, request *ValidationRequest) (error, string) {
	fundInfo := request.FundInfo
	order := request.Order
	transactionConstraints := fundInfo.TxnConstraints
	isFirstOrder := len(order.FolioId) == 0

	// Validate SIP Purchase constraints
	if order.OrderSubType == orderPb.OrderSubType_BUY_SIP {
		return t.validateSipPurchaseConstraints(transactionConstraints, request)
	}
	if isFirstOrder {
		if transactionConstraints.NewpMnval != nil && money.Compare(order.Amount, transactionConstraints.NewpMnval) == -1 {
			logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED,
				fmt.Sprintf("order amount: %s is less than the minimum amount for first purchase: %s", money.ToDisplayString(order.GetAmount()), money.ToDisplayString(transactionConstraints.NewpMnval)),
				zap.String(logger.ACTOR_ID_V2, order.GetActorId()),
				zap.String(logger.MF_ID, fundInfo.GetId()), zap.String(logger.ORDER_TYPE, order.GetOrderType().String()),
				zap.String(logger.ORDER_SUBTYPE, order.GetOrderSubType().String()))
			return PurchaseConstraintsValidationError, "order amount is less than the minimum amount for first purchase"
		}
		if transactionConstraints.NewpMxval != nil && money.Compare(order.Amount, transactionConstraints.NewpMxval) == 1 {
			return PurchaseConstraintsValidationError, "order amount is greater than the maximum amount for first purchase"
		}

		err := t.validateIncrementalAmount(order.Amount, transactionConstraints.NewpMnval, transactionConstraints.PMnIncr)
		if err != nil {
			return PurchaseConstraintsValidationError, err.Error()
		}

	} else {
		if transactionConstraints.AdpMnAmt != nil && money.Compare(order.Amount, transactionConstraints.AdpMnAmt) == -1 {
			return PurchaseConstraintsValidationError, "order amount is less than the minimum amount for additional purchase"
		}
		if transactionConstraints.AdpMxAmt != nil && money.Compare(order.Amount, transactionConstraints.AdpMxAmt) == 1 {
			return PurchaseConstraintsValidationError, "order amount is greater than the maximum amount for additional purchase"
		}
		err := t.validateIncrementalAmount(order.Amount, transactionConstraints.AdpMnAmt, transactionConstraints.PMnIncr)
		if err != nil {
			return PurchaseConstraintsValidationError, err.Error()
		}
	}
	return nil, ""
}

func (t *PurchaseConstraintsValidator) validateIncrementalAmount(orderAmount *moneyPb.Money, minimumAmount *moneyPb.Money,
	incrementalAmount *moneyPb.Money) error {
	if orderAmount == nil || minimumAmount == nil || incrementalAmount == nil {
		return nil
	}
	orderPaise, err := money.ToPaise(orderAmount)
	if err != nil {
		return err
	}

	// Ignoring nanos in Incremental amount
	if incrementalAmount.Nanos > 0 {
		newIncrAmount := money.AmountINR(incrementalAmount.Units)
		incrementalAmount = newIncrAmount.GetPb()
	}

	incrementalPaise, err := money.ToPaise(incrementalAmount)
	if err != nil {
		return err
	}

	if incrementalPaise == 0 {
		return nil
	}

	minimumPaise, err := money.ToPaise(minimumAmount)
	if err != nil {
		return err
	}

	if (orderPaise-minimumPaise)%incrementalPaise == 0 {
		return nil
	}

	return fmt.Errorf("incremental amount is invalid with amount: %d, minimumAmount: %d and incrementalAmount: %d",
		orderPaise, minimumPaise, incrementalPaise)
}

func (t *PurchaseConstraintsValidator) validateSipPurchaseConstraints(transactionConstraints *mfPb.TransactionConstraints, request *ValidationRequest) (error, string) {
	if transactionConstraints.GetSipMetadata().GetSiDetails() != nil {
		txnConstr := transactionConstraints.GetSipMetadata().GetSiDetails()
		sipConstr, ok := txnConstr[SipGranularityToAipFrequency[request.SipLedger.GetSipGranularity()].String()]
		if !ok {
			// No Sip constraint present for given fund and frequency
			return nil, ""
		}
		if sipConstr.MinAmount > 1 && money.Compare(request.Order.Amount, money.AmountINR(int64(sipConstr.MinAmount)).GetPb()) == -1 {
			return PurchaseConstraintsValidationError, "order amount is less than the minimum amount for SIP"
		}
		err := t.validateIncrementalAmount(request.Order.Amount,
			money.AmountINR(int64(sipConstr.MinAmount)).GetPb(),
			money.AmountINR(int64(sipConstr.SubsequentAmount)).GetPb())
		if err != nil {
			return PurchaseConstraintsValidationError, err.Error()
		}
		// validate Schedule constraints
		switch request.SipLedger.GetSipGranularity() {
		case mfPb.SIPGranularity_SIP_GRANULARITY_MONTHLY:
			isScheduleValid := true
			if len(sipConstr.AllowedDates) > 0 {
				isScheduleValid = false
				for _, date := range sipConstr.AllowedDates {
					if int(date.Day) == request.SipLedger.GetStartDate().AsTime().Day() {
						isScheduleValid = true
						break
					}
				}
			}
			if !isScheduleValid {
				return PurchaseConstraintsValidationError, fmt.Sprintf("schedule is invalid for monthly SIP. requested sip start date: %v",
					request.SipLedger.GetStartDate().AsTime())
			}
		case mfPb.SIPGranularity_SIP_GRANULARITY_WEEKLY:
			isScheduleValid := true
			if len(sipConstr.AllowedDays) > 0 {
				isScheduleValid = false
				for _, day := range sipConstr.AllowedDays {

					if strings.EqualFold(day.String(), request.SipLedger.GetStartDate().AsTime().Weekday().String()) {
						isScheduleValid = true
						break
					}
				}
			}
			if !isScheduleValid {
				return PurchaseConstraintsValidationError, fmt.Sprintf("schedule is invalid for weekly SIP. requested sip start date: %v",
					request.SipLedger.GetStartDate().AsTime())
			}
		default:

		}
	}
	return nil, ""
}
