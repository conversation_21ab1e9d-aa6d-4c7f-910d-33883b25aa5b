package filegenerator

import (
	"flag"
	"os"
	"testing"

	gormv2 "gorm.io/gorm"

	genConf "github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/gamma/investment/test"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

var (
	conf *genConf.Config
	db   *gormv2.DB
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer

// nolint
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	conf, db, teardown = test.InitTestServer()

	svcTS = newSvcTestSuite(conf)
	txnExecutor = storagev2.NewCRDBIdempotentTxnExecutor(db)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
