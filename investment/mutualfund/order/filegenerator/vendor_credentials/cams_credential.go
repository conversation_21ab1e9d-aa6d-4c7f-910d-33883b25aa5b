package vendor_credentials

import (
	"encoding/json"
	"fmt"

	"github.com/epifi/gamma/investment/config"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	camsKey = "camskey"
)

type CamsCredential struct {
	UserName string `json:"UserName"`
}

func ExtractCamsCredentialsFromConfig(conf *config.Config) *CamsCredential {
	var credential CamsCredential
	err := json.Unmarshal([]byte(conf.Secrets.Ids[camsKey]), &credential)
	if err != nil {
		logger.Fatal(fmt.Sprintf("Marshalling credentials for %s has failed", camsKey))
		return &CamsCredential{}
	}
	return &credential
}
