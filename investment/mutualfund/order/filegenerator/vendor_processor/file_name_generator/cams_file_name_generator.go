package file_name_generator

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/dao"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/cams/mapper"
	sng "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/file_name_generator/sequence_number_generator"
)

const DateFormat string = "2006-01-02"
const DateFormatWithoutSeparator = "20060102"

type CamsFileNameGenerator struct {
	IntraDaySequenceNumberGenerator sng.SequenceNumberGenerator
}

var istTimeZoneLocation = "Asia/Kolkata"

func NewCamsFileNameGenerator(intraDaySNG *sng.IntraDaySequenceNumberGenerator) *CamsFileNameGenerator {
	return &CamsFileNameGenerator{
		IntraDaySequenceNumberGenerator: intraDaySNG,
	}
}

//nolint:dupl
func (c *CamsFileNameGenerator) GenerateFileName(ctx context.Context, input *FileNameGeneratorInput) (string, error) {
	timeZoneLocation, err := time.LoadLocation(istTimeZoneLocation)
	if err != nil {
		return "", fmt.Errorf("error while trying to load IST time zone: %w", err)
	}

	inputTimeInIST := input.Time.In(timeZoneLocation)

	sequenceNumber, err := c.IntraDaySequenceNumberGenerator.GenerateFileSequenceNumber(ctx,
		&dao.SequenceNumberGeneratorInput{
			Vendor:   input.Vendor,
			FileType: input.FileType,
			Date:     inputTimeInIST.Format(DateFormat),
		},
	)
	if err != nil {
		return "", err
	}

	var amc string
	switch {
	case input.FileType == filegenerator.FileType_FILE_TYPE_CREDIT_MIS || input.FileType == filegenerator.FileType_FILE_TYPE_CREDIT_MIS_EXCEL:
		if val, ok := mapper.AmcMapper[input.AMCCode]; ok {
			amc = val + "000"
			// few sample amc prefixes generated here for reference
			amc = amc[0:4]
		} else {
			return "", fmt.Errorf("invalid AMC code: %s for CREDIT MIS File Type", input.AMCCode)
		}
	default:
		// this is the default for order feed file
		amc = "P000"
	}

	// cams expect a file generation time stamp in the file name. This will be added to the file name just before
	// vendor communication.
	if input.FileType == filegenerator.FileType_FILE_TYPE_CREDIT_MIS_EXCEL {
		fileName := amc + fmt.Sprintf("%04d", input.TotalRecords) + fmt.Sprintf("%04d", sequenceNumber) + inputTimeInIST.Format(DateFormatWithoutSeparator) + ".xlsx"
		return fileName, nil
	}
	fileName := amc + fmt.Sprintf("%04d", input.TotalRecords) + fmt.Sprintf("%04d", sequenceNumber) + inputTimeInIST.Format(DateFormatWithoutSeparator) + ".txt"
	return fileName, nil
}
