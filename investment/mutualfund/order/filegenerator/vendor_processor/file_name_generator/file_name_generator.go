package file_name_generator

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"time"

	pb "github.com/epifi/gamma/api/investment/mutualfund"
	"github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
)

type FileNameGeneratorInput struct {
	Time         time.Time
	FileType     filegenerator.FileType
	Vendor       commonvgpb.Vendor
	TotalRecords int
	AMCCode      pb.Amc
}

type FileNameGenerator interface {
	GenerateFileName(ctx context.Context, input *FileNameGeneratorInput) (string, error)
}
