package pkg

import (
	devOmeglePb "github.com/epifi/gamma/api/omegle/developer"
	"github.com/epifi/gamma/api/vkyccall"
	devVkycCallPb "github.com/epifi/gamma/api/vkyccall/developer"
	troubleshootPb "github.com/epifi/gamma/api/vkyccall/troubleshoot"
)

// VkycCall clients
type VkycCallClientToOnboardingServer vkyccall.VkycCallClient
type VkycCallClientToSGApiGatewayServer vkyccall.VkycCallClient

// VkycCall dev clients
type DevVkycCallClientToOnboardingServer devVkycCallPb.DevVkycCallClient
type DevVkycCallClientToSGApiGatewayServer devVkycCallPb.DevVkycCallClient

// VkycCall troubleshoot clients
type VkycCallTroubleshootClientToOnboardingServer troubleshootPb.TroubleshootClient
type VkycCallTroubleshootClientToSGApiGatewayServer troubleshootPb.TroubleshootClient

// nolint: goimports
// Omegle dev clients
type DevOmegleClientToOnboardingServer devOmeglePb.DevOmegleClient
type DevOmegleClientToSGApiGatewayServer devOmeglePb.DevOmegleClient
