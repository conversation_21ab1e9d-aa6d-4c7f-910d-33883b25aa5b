package autoupgrade

import (
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
)

type TierAutoUpgradeParams struct {
	ActorId    string
	Provenance tieringEnumPb.Provenance
	FromTier   tieringEnumPb.Tier
	ToTier     tieringEnumPb.Tier
	MovementTs *timestampPb.Timestamp
	Reason     *tiering.TierMovementReason
}

func (t *TierAutoUpgradeParams) GetActorId() string {
	if t != nil {
		return t.ActorId
	}

	return ""
}

func (t *TierAutoUpgradeParams) GetProvenance() tieringEnumPb.Provenance {
	if t != nil {
		return t.Provenance
	}

	return tieringEnumPb.Provenance_PROVENANCE_UNSPECIFIED
}

func (t *TierAutoUpgradeParams) GetFromTier() tieringEnumPb.Tier {
	if t != nil {
		return t.FromTier
	}

	return tieringEnumPb.Tier_TIER_UNSPECIFIED
}

func (t *TierAutoUpgradeParams) GetToTier() tieringEnumPb.Tier {
	if t != nil {
		return t.ToTier
	}

	return tieringEnumPb.Tier_TIER_UNSPECIFIED
}

func (t *TierAutoUpgradeParams) GetMovementTs() *timestampPb.Timestamp {
	if t != nil {
		return t.MovementTs
	}

	return nil
}

func (t *TierAutoUpgradeParams) GetUpgradeReason() tieringEnumPb.UpgradeReason {
	if t != nil {
		return t.Reason.GetUpgradeReason()
	}

	return tieringEnumPb.UpgradeReason_UPGRADE_REASON_UNSPECIFIED
}

func (t *TierAutoUpgradeParams) GetReason() *tiering.TierMovementReason {
	if t != nil {
		return t.Reason
	}

	return nil
}
