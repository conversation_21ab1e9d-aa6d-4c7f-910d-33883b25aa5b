package processors

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/errgroup"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/dao"
	"github.com/epifi/gamma/tiering/data"
	tieringErrors "github.com/epifi/gamma/tiering/errors"
	tieringHelper "github.com/epifi/gamma/tiering/helper"
	"github.com/epifi/gamma/tiering/tier_options"
	"github.com/epifi/gamma/tiering/tiermappings"
	"github.com/epifi/gamma/tiering/timeline"
)

// GraceProcessor processor to handle tier grace comms
type GraceProcessor struct {
	gconf              *genconf.Config
	timelineManager    timeline.TierTimeline
	atiDao             dao.ActorTierInfoDao
	dataprocessor      data.TieringDataProcessor
	tierOptionsManager tier_options.Manager
}

func NewGraceProcessor(
	gconf *genconf.Config,
	timelineManager timeline.TierTimeline,
	atiDao dao.ActorTierInfoDao,
	dataProcessor data.TieringDataProcessor,
	tierOptionsManager tier_options.Manager,
) *GraceProcessor {
	return &GraceProcessor{
		gconf:              gconf,
		timelineManager:    timelineManager,
		atiDao:             atiDao,
		dataprocessor:      dataProcessor,
		tierOptionsManager: tierOptionsManager,
	}
}

var _ Processor = &GraceProcessor{}

// nolint:dupl
func (p *GraceProcessor) GetInAppTemplate(ctx context.Context, actorId string) (*fcmPb.InAppTemplate, error) {
	notifConf, getNotifConfErr := p.gconf.GetNotificationConfigFromType(tieringEnumPb.NotificationType_NOTIFICATION_TYPE_GRACE)
	if getNotifConfErr != nil {
		return nil, errors.Wrap(getNotifConfErr, "error fetching notification config")
	}
	if !notifConf.IsEnabled() {
		return nil, nil
	}
	currTier, tierOptionsMap, movementTimestamp, gatherDataErr := p.gatherData(ctx, actorId)
	if gatherDataErr != nil {
		return nil, errors.Wrap(gatherDataErr, "error gathering data for grace processor")
	}

	if !p.shouldShowGraceNotif(currTier) {
		return nil, nil
	}
	title := GetGraceTitle(currTier)
	body := GetGraceBody(currTier)
	iconUrl := GetGraceIconUrl(currTier)
	if title == "" || body == "" || iconUrl == "" {
		return nil, fmt.Errorf("title or body or iconUrl cannot be empty for toTier: %s", currTier.String())
	}
	minBalance, getMinBalanceErr := tieringHelper.GetMinBalanceFromTierOptions(tierOptionsMap[currTier])
	if getMinBalanceErr != nil && !errors.Is(getMinBalanceErr, tieringErrors.ErrTierHasNoMinBalanceCriteria) {
		return nil, fmt.Errorf("error fetching min balance for %s", currTier.String())
	}
	// this indicated tier has min balance criteria
	if getMinBalanceErr == nil {
		body = fmt.Sprintf(body, moneyPkg.ToDisplayStringInIndianFormat(minBalance, 0, true))
	}
	return &fcmPb.InAppTemplate{
		CommonTemplateFields: &fcmPb.CommonTemplateFields{
			Title:   title,
			Body:    body,
			TitleV2: commontypes.GetTextFromStringFontColourFontStyle(title, notifTitleFontColor, commontypes.FontStyle_SUBTITLE_2),
			BodyV2:  commontypes.GetTextFromStringFontColourFontStyle(body, notifBodyFontColor, commontypes.FontStyle_SUBTITLE_2),
			IconAttributes: &fcmPb.IconAttributes{
				IconUrl: iconUrl,
			},
			Deeplink:                       GetGraceDeeplinkScreen(currTier),
			ExpireAt:                       movementTimestamp,
			NotificationReferenceId:        GraceRefPrefId + uuid.New().String(),
			BgColor:                        GraceBgColor,
			NotificationDismissIconBgColor: ui.GetBlockColor(notifDismissIconBgColor),
			NotificationDismissIconColor:   ui.GetBlockColor(notifDismissIconColor),
		},
		ShowOnHome:           commontypes.BooleanEnum_TRUE,
		NotificationPriority: fcmPb.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL,
	}, nil
}

func (p *GraceProcessor) gatherData(ctx context.Context, actorId string) (tieringEnumPb.Tier,
	map[tieringEnumPb.Tier][]*criteriaPb.Option, *timestampPb.Timestamp, error) {
	isUserInGrace, graceDetails, checkGraceErr := p.timelineManager.IsUserInGracePeriod(ctx, actorId)
	if checkGraceErr != nil {
		return 0, nil, nil, errors.Wrap(checkGraceErr, "error checking grace for user")
	}
	if !isUserInGrace {
		return 0, nil, nil, errors.New("user not in grace period")
	}
	graceErrGrp, gCtx := errgroup.WithContext(ctx)
	// get current tier for actor
	var currTier tieringEnumPb.Tier
	graceErrGrp.Go(func() error {
		var getCurrTierErr error
		currTier, getCurrTierErr = p.dataprocessor.GetCurrentTierDefaultToBaseTier(gCtx, actorId)
		if getCurrTierErr != nil {
			return errors.Wrap(getCurrTierErr, "error fetching current tier for actor")
		}

		return nil
	})
	// get tier options map
	var tierOptionsMap map[tieringEnumPb.Tier][]*criteriaPb.Option
	graceErrGrp.Go(func() error {
		var getTierOptionsMapErr error
		tierOptionsMap, getTierOptionsMapErr = p.tierOptionsManager.GetTierOptionsMap(gCtx, actorId)
		if getTierOptionsMapErr != nil {
			return errors.Wrap(getTierOptionsMapErr, "error fetching tier options map")
		}
		return nil
	})
	graceErr := graceErrGrp.Wait()
	if graceErr != nil {
		return 0, nil, nil, graceErr
	}
	return currTier, tierOptionsMap, graceDetails.GetMovementTimestamp(), nil
}

// Do not show upgrade for lowest tier and salary lite tier. salary lite comms are done via moengage
func (p *GraceProcessor) shouldShowGraceNotif(tier tieringEnumPb.Tier) bool {
	extTier, _ := tiermappings.GetExternalTierFromInternalTier(tier)
	if extTier.IsBaseTier() || extTier.IsSalaryOrSalaryLiteTier() {
		return false
	}

	return true
}
