package ratelimiter

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	vgEkycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	lendingFederalPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	qwikcilverVgPb "github.com/epifi/gamma/api/vendorgateway/offers/qwikcilver"
	accountStatusPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts/status"
	paymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	"github.com/epifi/gamma/vendorgateway/interceptor/ratelimiter/namespace"
)

func TestVgKeyGenerator_GenerateKey(t *testing.T) {

	initiateB2CPayNamespaceGenerator := namespace.NewInitiateB2CPayNamespaceGenerator()
	iFactory := namespace.NewDefaultFactory(initiateB2CPayNamespaceGenerator)
	keyGenerator := NewVgKeyGenerator(iFactory)

	type args struct {
		fullGrpcMethodName string
		request            interface{}
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			args: args{
				fullGrpcMethodName: "vendorgateway.openbanking.payment.b2c.Payment/InitiateB2CPay",
				request: &b2c.InitiateB2CPayRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
				},
			},
			want:    "vendorgateway_openbanking_payment_b2c_payment_initiateb2cpay_federal_bank",
			wantErr: false,
		},
		{
			args: args{
				fullGrpcMethodName: "/vendorgateway.offers.qwikcilver.Qwikcilver/GetProductList",
				request: &qwikcilverVgPb.GetProductListRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_QWIKCILVER},
				},
			},
			want:    "vendorgateway_offers_qwikcilver_qwikcilver_getproductlist_qwikcilver",
			wantErr: false,
		},
		{
			name: "should return error as request object does not implements RequestWithHeader interface",
			args: args{
				fullGrpcMethodName: "/A1.A2.A3/A4/",
				request:            nil,
			},
			want:    "",
			wantErr: true,
		},
		{
			args: args{
				fullGrpcMethodName: "vendorgateway.gplace.Gplace/FindPlace",
				request: &b2c.InitiateB2CPayRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_GPLACE},
				},
			},
			want:    "vendorgateway_gplace_gplace_findplace_gplace",
			wantErr: false,
		},
		{
			args: args{
				fullGrpcMethodName: "/vendorgateway.openbanking.payment.b2c.Payment/InitiateB2CPay",
				request: &b2c.InitiateB2CPayRequest{
					Header:   &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Remitter: &paymentPb.Account{AccountNumber: "**************"},
				},
			},
			want:    "vendorgateway_openbanking_payment_b2c_payment_initiateb2cpay_federal_bank_1640",
			wantErr: false,
		},
		{
			args: args{
				fullGrpcMethodName: "vendorgateway_openbanking_accounts_accounts_accountstatusenquiry",
				request: &accountStatusPb.AccountStatusEnquiryRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					RequestId:     "",
					AccountNumber: "**************",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
			},
			want:    "vendorgateway_openbanking_accounts_accounts_accountstatusenquiry_federal_bank",
			wantErr: false,
		},
		{
			args: args{
				fullGrpcMethodName: "vendorgateway.lending.preapprovedloan.PreApprovedLoan/GetInstantLoanClosureEnquiry",
				request: &lendingFederalPb.GetInstantLoanClosureEnquiryRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: "**************",
				},
			},
			want:    "vendorgateway_lending_preapprovedloan_preapprovedloan_getinstantloanclosureenquiry_federal_bank",
			wantErr: false,
		},
		{
			args: args{
				fullGrpcMethodName: "vendorgateway.lending.preapprovedloan.PreApprovedLoan/FetchLoanDetails",
				request: &lendingFederalPb.GetInstantLoanClosureEnquiryRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: "**************",
				},
			},
			want:    "vendorgateway_lending_preapprovedloan_preapprovedloan_fetchloandetails_federal_bank",
			wantErr: false,
		},
		{
			args: args{
				fullGrpcMethodName: "vendorgateway.lending.securedloans.fiftyfin.FiftyFin/GetLoanSoaStatement",
				request: &lendingFederalPb.GetInstantLoanClosureEnquiryRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FIFTYFIN},
					AccountNumber: "**************",
				},
			},
			want:    "vendorgateway_lending_securedloans_fiftyfin_fiftyfin_getloansoastatement_fiftyfin",
			wantErr: false,
		},
		{
			args: args{
				fullGrpcMethodName: "vendorgateway.lending.securedloans.fiftyfin.FiftyFin/GetLoanForeclosureStatement",
				request: &lendingFederalPb.GetInstantLoanClosureEnquiryRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FIFTYFIN},
					AccountNumber: "**************",
				},
			},
			want:    "vendorgateway_lending_securedloans_fiftyfin_fiftyfin_getloanforeclosurestatement_fiftyfin",
			wantErr: false,
		},
		{
			args: args{
				fullGrpcMethodName: "vendorgateway.ekyc.EKYC/ValidateAadharMobile",
				request: &vgEkycPb.ValidateAadharMobileRequest{
					Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					RequestId: "request-id-1",
					EkycRrn:   "ekyc-rrn-1",
					MobileNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
			},
			want:    "vendorgateway_ekyc_ekyc_validateaadharmobile_federal_bank",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run("", func(t *testing.T) {
			got, err := keyGenerator.GenerateKey(context.Background(), tt.args.request, tt.args.fullGrpcMethodName)
			if (err != nil) != tt.wantErr {
				t.Errorf("keyGenerator() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("keyGenerator() got = %v, want %v", got, tt.want)
			}
		})
	}
}
