package liquiloans

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/epifi/be-common/api/rpc"
	p2pPb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	liquiloansPb "github.com/epifi/gamma/api/vendors/liquiloans"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

type GetMaturityLinkOTPReq struct {
	Method string
	Req    *p2pPb.GetMaturityLinkOTPRequest
	Conf   *config.Liquiloans
}

func (r *GetMaturityLinkOTPReq) Marshal() ([]byte, error) {
	req := &liquiloansPb.GetMaturityLinkOTPRequest{
		Mid:        r.Conf.Mid,
		Timestamp:  time.Now().In(datetime.IST).Format(dateTimeLayout),
		InvestorId: r.Req.GetInvestorId(),
		LinkToken:  r.Req.GetLinkToken(),
	}
	checksumDataString := fmt.Sprintf("%v||%v||%v", req.GetInvestorId(), req.GetLinkToken(), req.GetTimestamp())
	req.Checksum = calculateChecksum(checksumDataString, r.Conf.Key)
	return protojson.Marshal(req)
}

func (r *GetMaturityLinkOTPReq) URL() string {
	return fmt.Sprintf("%v/GetMaturityLinkOTP", r.Conf.Host)
}

func (r *GetMaturityLinkOTPReq) HTTPMethod() string {
	return r.Method
}

func (r *GetMaturityLinkOTPReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (r *GetMaturityLinkOTPReq) GetResponse() vendorapi.Response {
	return &GetMaturityLinkOTPRes{}
}

func (r *GetMaturityLinkOTPReq) CanLogUnredactedEncryptedPayload() bool {
	return true
}

type GetMaturityLinkOTPErrRes struct {
	Status  bool   `json:"status"`
	Message string `json:"message"`
	Code    int64  `json:"code"`
}

type GetMaturityLinkOTPRes struct {
}

func (n *GetMaturityLinkOTPRes) Unmarshal(b []byte) (proto.Message, error) {
	res := &liquiloansPb.GetMaturityLinkOTPResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res)
	if err != nil {
		return nil, err
	}

	return &p2pPb.GetMaturityLinkOTPResponse{
		Status:        rpc.StatusOk(),
		RawStatusCode: strconv.Itoa(int(res.GetCode())),
		RawMessage:    res.GetMessage(),
	}, nil
}

func (n GetMaturityLinkOTPRes) CanLogUnredactedEncryptedPayload() bool {
	return true
}

func (n *GetMaturityLinkOTPRes) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	var res GetMaturityLinkOTPErrRes
	err := json.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v, err = %v", httpStatus, string(b), err))
	}
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
