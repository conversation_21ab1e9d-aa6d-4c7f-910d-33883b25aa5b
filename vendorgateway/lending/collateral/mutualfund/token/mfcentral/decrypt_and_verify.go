package mfcentral

import (
	"context"
	"fmt"
	"net/http"

	"go.uber.org/zap"
	json "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/gamma/api/vendors/mfcentral/collateral"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"
)

type VerifyAndDecrypt struct {
	Url     string
	Handler *vendorapi.HTTPRequestHandler
}

func NewVerifyAndDecrypt(handler *vendorapi.HTTPRequestHandler, url string) *VerifyAndDecrypt {
	return &VerifyAndDecrypt{
		Handler: handler,
		Url:     url,
	}
}

func (e *VerifyAndDecrypt) VerifyAndDecrypt(ctx context.Context, payLoad string, signature string) (string, error) {
	aux := NewVerifyAndDecryptRequest(e.Url, payLoad, signature)
	resp, err := e.Handler.Handle(ctx, aux)
	if err != nil {
		logger.Error(ctx, "error in verify and decrypt", zap.Error(err))
		return "", err
	}

	if response, ok := resp.(*collateral.VerifyAndDecryptResponse); ok {
		return response.GetDecryptedPayload(), nil
	}

	return "", fmt.Errorf("error in type-casting VerifyAndDecryptResponse")

}

type VerifyAndDecryptRequest struct {
	Url       string
	PayLoad   string
	Signature string
}

func NewVerifyAndDecryptRequest(url string, payLoad string, signature string) *VerifyAndDecryptRequest {
	return &VerifyAndDecryptRequest{
		Url:       url,
		PayLoad:   payLoad,
		Signature: signature,
	}
}

type VerifyAndDecryptResponse struct {
}

func (m *VerifyAndDecryptRequest) Marshal() ([]byte, error) {
	req := &collateral.VerifyAndDecryptRequest{Payload: m.PayLoad, Signature: m.Signature}
	return json.Marshal(req)
}

func (m *VerifyAndDecryptRequest) ContentTypeString() string {
	return httpcontentredactor.ContentTypeJSON
}

func (m *VerifyAndDecryptRequest) GetResponseContentTypeString() string {
	return httpcontentredactor.ContentTypeJSON
}

func (m *VerifyAndDecryptRequest) URL() string {
	return m.Url
}

func (m *VerifyAndDecryptRequest) HTTPMethod() string {
	return http.MethodPost
}

func (m *VerifyAndDecryptRequest) GetResponse() vendorapi.Response {
	return &VerifyAndDecryptResponse{}
}

func (m *VerifyAndDecryptResponse) Unmarshal(b []byte) (proto.Message, error) {
	response := &collateral.VerifyAndDecryptResponse{}
	err := json.Unmarshal(b, response)
	if err != nil {
		return nil, err
	}
	return response, nil
}
