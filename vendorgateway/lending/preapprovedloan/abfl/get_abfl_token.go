package abfl

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/vendorapi"

	vgAbflPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	vendorAfblPb "github.com/epifi/gamma/api/vendors/abfl/lending"
	abflPkg "github.com/epifi/gamma/pkg/loans/abfl"
	"github.com/epifi/gamma/vendorgateway/config"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

type GetTokenRequest struct {
	Method string
	Req    *vgAbflPb.CreateTokenRequest
	Conf   *config.Abfl
}

func (c *GetTokenRequest) HTTPMethod() string {
	return c.Method
}

func (c *GetTokenRequest) URL() string {
	return c.Conf.Url + "/createToken"
}

func (c *GetTokenRequest) GetResponse() vendorapi.Response {
	return &GetTokenResponse{}
}

func (c *GetTokenRequest) Marshal() ([]byte, error) {
	createTokenReq := &vendorAfblPb.CreateTokenRequest{
		Username: c.Conf.Username,
		Password: c.Conf.Password,
	}
	return protojson.Marshal(createTokenReq)
}

type GetTokenResponse struct{}

func (c *GetTokenRequest) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (c *GetTokenResponse) Unmarshal(b []byte) (proto.Message, error) {
	res := vendorAfblPb.CreateTokenResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to abfl proto message")
	}

	vgResponse := &vgAbflPb.CreateTokenResponse{
		Status: rpc.StatusOk(),
		CreateTokenResp: &vgAbflPb.CreateTokenResponse_CreateToken{
			Token: res.GetData().GetToken(),
		},
	}
	return vgResponse, nil
}

func (c *GetTokenResponse) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	res := vendorAfblPb.CreateTokenResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to abfl proto message")
	}

	if httpStatus == 401 || httpStatus == 500 {
		if !abflPkg.ResponseStatus[res.GetResponseStatus()] {
			return &vgAbflPb.CreateTokenResponse{
				Status: abflPkg.VendorErrorResponseCodesToRpcResponse(res.GetError().GetCode(), res.GetError().GetDescription()),
			}, nil
		}
	}
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
