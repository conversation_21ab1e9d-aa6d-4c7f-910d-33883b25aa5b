package moneyview

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	moneyviewVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	vendorMoneyviewPb "github.com/epifi/gamma/api/vendors/moneyview"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
)

var (
	leadCreationFailureVendorStatusCodeToRpcStatusMap = map[string]*rpc.Status{
		// denotes that user age passed was invalid, can happen if the passed age does not qualifies the min age criteria for applying for a loan at MV's end.
		"6121": rpc.NewStatusWithoutDebug(uint32(moneyviewVgPb.CreateLeadResponse_FAILED_PRECONDITION_INVALID_USER_AGE), "invalid user age"),
		// denotes that a lead with passed details already exists in MV system and therefore a new/fresh lead can't be created now.
		"6133": rpc.NewStatusWithoutDebug(uint32(moneyviewVgPb.CreateLeadResponse_FAILED_PRECONDITION_DUPLICATE_LEAD), "Duplicate lead found in MV system"),
		// denotes that the salary details passed to MV does not meet the minimum qualifying criteria for generating a loan offer at MV's end e.g when the
		// salary amount passed is less than min required salary amount for MV offer generation then we can get this error.
		"6203": rpc.NewStatusWithoutDebug(uint32(moneyviewVgPb.CreateLeadResponse_FAILED_PRECONDITION_INVALID_USER_INCOME_DETAILS), "Invalid salary details passed to MV"),
		// denotes that the lead has been REJECTED at MV's end.
		"7501": rpc.NewStatusWithoutDebug(uint32(moneyviewVgPb.CreateLeadResponse_FAILED_PRECONDITION_LEAD_REJECTED), "lead has been rejected at MV's end"),
		// denotes the PA offer does not exist at MV's end for the passed PA offer-id, this can happen if the offers are wrongly configured.
		"15003": rpc.StatusFailedPreconditionWithDebugMsg("pre-approved offer does not exists at MV's end"),
	}
)

type CreateLeadRequest struct {
	AccessToken string
	PartnerCode int
	BaseUrl     string
	Req         *moneyviewVgPb.CreateLeadRequest
}

type CreateLeadResponse struct {
}

func (r *CreateLeadRequest) HTTPMethod() string {
	return http.MethodPost
}

func (r *CreateLeadRequest) URL() string {
	return r.BaseUrl + "/lead"
}

func (c *CreateLeadRequest) SetAuth(req *http.Request) *http.Request {
	req.Header.Add(tokenHeaderKey, c.AccessToken)
	return req
}

func (r *CreateLeadRequest) GetResponse() vendorapi.Response {
	return &CreateLeadResponse{}
}

// nolint: funlen
func (r *CreateLeadRequest) Marshal() ([]byte, error) {
	declaredIncome, _ := money.ToDecimal(r.Req.GetDeclaredIncome()).Float64()

	genderString := "others"
	if value, ok := genderEnumToStringMap[r.Req.GetGender()]; ok {
		genderString = value
	}

	var addressList []*vendorMoneyviewPb.Address
	for _, addressWithType := range r.Req.GetAddresses() {
		addressList = append(addressList, &vendorMoneyviewPb.Address{
			AddressType: addressTypeEnumToStringMap[addressWithType.GetType()],
			// money view only needs the pin code of the user.
			Pincode: addressWithType.GetAddress().GetPostalCode(),
		})
	}

	var annualFamilyIncomeString string
	if r.Req.GetAnnualFamilyIncome().GetUnits() != 0 {
		// check if annual family income is less than 3 Lac and accordingly create appropriate annual income string to be sent to vendor.
		compareRes, err := money.CompareV2(r.Req.GetAnnualFamilyIncome(), money.AmountINR(300000).GetPb())
		switch {
		case err != nil:
			// intentionally muting the error here as annualFamilyIncome is an optional field in vendor api, so not failing the flow due to this failure.
			logger.Warn("error comparing annualFamilyIncome with 3Lacs value", zap.Error(err))
		case compareRes < 1:
			annualFamilyIncomeString = "Less than ₹3 lakhs"
		default:
			annualFamilyIncomeString = "More than ₹3 lakhs"
		}
	}

	vendorReq := &vendorMoneyviewPb.CreateLeadRequest{
		PartnerCode:      int32(r.PartnerCode),
		PartnerRef:       r.Req.GetPartnerRefId(),
		Phone:            r.Req.GetPhoneNumber().ToStringNationalNumber(),
		Pan:              r.Req.GetPan(),
		Name:             r.Req.GetName().ToString(),
		Gender:           genderString,
		DateOfBirth:      datetime.DateToTime(r.Req.GetDob(), datetime.IST).Format(datetime.DATE_LAYOUT_YYYYMMDD),
		BureauPermission: true,
		EmploymentType:   empTypeEnumToStringMap[r.Req.GetEmploymentType()],
		// we always pass online here as we don't collect this data from user in our flow.
		IncomeMode:         "online",
		DeclaredIncome:     float32(declaredIncome),
		AnnualFamilyIncome: annualFamilyIncomeString,
		AddressList:        addressList,
		EmailList: []*vendorMoneyviewPb.Email{
			{
				// currently we would only be passing primary device id of the user.
				Type:  "primary_device",
				Email: r.Req.GetEmailId(),
			},
		},
	}

	return protojson.Marshal(vendorReq)
}

func (c *CreateLeadResponse) Unmarshal(b []byte) (proto.Message, error) {
	vendorRes := &vendorMoneyviewPb.CreateLeadResponse{}
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err := unmarshaller.Unmarshal(b, vendorRes); err != nil {
		return nil, fmt.Errorf("error unmarshalling get offers api response, err : %w", err)
	}
	if !strings.EqualFold(vendorRes.GetStatus(), apiStatusSuccess) || vendorRes.GetLeadId() == "" {
		logger.ErrorNoCtx("create lead api failed", zap.String("vendorStatusCode", vendorRes.GetCode()), zap.String(logger.VENDOR_STATUS, vendorRes.GetStatus()), zap.String("vendorStatusMsg", vendorRes.GetMessage()), zap.String(logger.LEAD_ID, vendorRes.GetLeadId()))
		rpcStatus, ok := leadCreationFailureVendorStatusCodeToRpcStatusMap[vendorRes.GetCode()]
		if !ok {
			rpcStatus = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("create lead api failed, status : %s, code : %s, message : %s", vendorRes.GetStatus(), vendorRes.GetCode(), vendorRes.GetMessage()))
		}

		return &moneyviewVgPb.CreateLeadResponse{
			Status: rpcStatus,
			// intentionally adding the leadId in failure cases as well as we need the leadId for checking failures with the vendor.
			LeadId: vendorRes.GetLeadId(),
		}, nil
	}

	return &moneyviewVgPb.CreateLeadResponse{
		Status: rpc.StatusOk(),
		LeadId: vendorRes.GetLeadId(),
	}, nil
}

//nolint:dupl
func (c *CreateLeadResponse) HandleHttpError(ctx context.Context, httpStatusCode int, responseBody []byte) (proto.Message, error) {
	logger.Error(ctx, "error in create lead api", zap.Int("httpStatusCode", httpStatusCode), zap.String("rawResponse", string(responseBody)))

	return &moneyviewVgPb.CreateLeadResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("create lead api failed")),
	}, nil
}
