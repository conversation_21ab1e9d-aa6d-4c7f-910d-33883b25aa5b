package liquiloans

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	// nolint:depguard
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	vgLlPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	vendorLlPb "github.com/epifi/gamma/api/vendors/liquiloans/lending"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
)

type ValidateOtpForOkycResponse struct {
}

type ValidateOtpForOkycRequest struct {
	Method string
	Req    *vgLlPb.ValidateOtpForOkycRequest
	Conf   *config.LiquiloansPreapprovedLoan
}

func (c *ValidateOtpForOkycRequest) HTTPMethod() string {
	return c.Method
}

func (c *ValidateOtpForOkycRequest) URL() string {
	return c.Conf.Url + "/api/credit-line/v1/aadhaar/validate-okyc-xml"
}

func (c *ValidateOtpForOkycRequest) GetResponse() vendorapi.Response {
	return &ValidateOtpForOkycResponse{}
}

func (c *ValidateOtpForOkycRequest) Marshal() ([]byte, error) {
	sid, key, _ := getSidKeyAndSchemeCode(c.Req.GetLoanProgram(), c.Conf, c.Req.GetSchemeVersion())

	requestPayload := &vendorLlPb.ValidateOtpForOkycRequest{
		Sid:           sid,
		ApplicationId: c.Req.GetApplicantId(),
		Otp:           c.Req.GetOtp(),
		Hash:          c.Req.GetHash(),
		RequestToken:  c.Req.GetRequestToken(),
		Checksum:      c.getChecksum(sid, key),
	}

	return protojson.Marshal(requestPayload)
}

func (c *ValidateOtpForOkycResponse) Unmarshal(b []byte) (proto.Message, error) {
	res := vendorLlPb.ValidateOtpForOkycResponse{}

	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to liquiloans proto message")
	}

	vgResponse := &vgLlPb.ValidateOtpForOkycResponse{
		Status: rpc.StatusOk(),
	}

	return vgResponse, nil
}

func (c *ValidateOtpForOkycRequest) getChecksum(sid string, key string) string {
	checksumDataString := fmt.Sprintf("%v||%v||%v||%v||%v", c.Req.GetApplicantId(), c.Req.GetHash(), c.Req.GetOtp(), c.Req.GetRequestToken(), sid)
	return calculateChecksum(checksumDataString, key)
}

//nolint:dupl
func (c *ValidateOtpForOkycResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in ValidateOtpForOkyc API", zap.String("error", string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	if httpStatus == 400 {
		res := &vendorLlPb.ValidateOtpForOkycResponse{}
		err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res)
		if err != nil {
			return nil, errors.New(fmt.Sprintf("error while unmarshalling response : %v", string(b)))
		}

		// TODO(Anupam): update status when vendor give error message
		status, ok := vendorErrMsgToRpcStatus[strings.ToUpper(res.GetMessage())]
		if ok {
			return &vgLlPb.AddDetailsResponse{
				Status: status,
			}, nil
		}
	}
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
