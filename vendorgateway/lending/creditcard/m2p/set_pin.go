//nolint:dupl
package m2p

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	m2pLending "github.com/epifi/gamma/api/vendors/m2p/lending/creditcard"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/m2p"
	m2pCryptor "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/m2p"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

const mmyyLayout = "0106"

type SetPinRequest struct {
	*m2pCryptor.DefaultM2PSecuredExchange
	*m2p.DefaultHeaderAdder
	Method string
	Req    *creditcard.SetPinRequest
	Conf   *config.CreditCard
}

type SetPinResp struct{}

func (s *SetPinRequest) CanLogUnredactedEncryptedPayload() bool {
	return true
}

func (s *SetPinResp) CanLogUnredactedEncryptedPayload() bool {
	return true
}

func (s *SetPinResp) Unmarshal(b []byte) (proto.Message, error) {
	res := &m2pLending.SetPinResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res)
	if err != nil {
		return nil, errors.Wrap(err, "Error unmarshalling set pin Response")
	}
	if res.GetException() != nil {
		status, ok := m2pCodesVsRpcCodes[res.GetException().GetErrorCode()]
		if !ok {
			status = rpc.StatusInternal()
		}
		return &creditcard.SetPinResponse{Status: status}, nil
	}

	return &creditcard.SetPinResponse{Status: rpc.StatusOk()}, nil
}

func (s *SetPinRequest) HTTPMethod() string {
	return s.Method
}

func (s *SetPinRequest) URL() string {
	return s.Conf.M2P.M2PHost + "Yappay/business-entity-manager/setPin"
}

func (s *SetPinRequest) GetResponse() vendorapi.Response {
	return &SetPinResp{}
}

func (s *SetPinRequest) Marshal() ([]byte, error) {
	if err := s.validateSetPinRequest(); err != nil {
		return nil, errors.Wrap(err, "request field error")
	}
	if s.Req.GetExpiryDate().GetDay() == 0 {
		s.Req.ExpiryDate.Day = 1
	}

	expiryDate := datetime.DateToString(s.Req.GetExpiryDate(), mmyyLayout, datetime.IST)
	dob := datetime.DateToString(s.Req.GetDob(), ddmmyyyyLayout, datetime.IST)
	return protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(
		&m2pLending.SetPinRequest{
			CustomerId: s.Req.GetCustomerId(),
			Pin:        s.Req.GetPin(),
			KitNumber:  s.Req.GetKitNumber(),
			ExpiryDate: expiryDate,
			Dob:        dob,
		})
}

func (s *SetPinRequest) validateSetPinRequest() error {
	if s.Req.GetCustomerId() == "" {
		return errors.New("customer id missing in set pin request")
	}
	if s.Req.GetPin() == "" {
		return errors.New("pin missing in set pin request")
	}
	if s.Req.GetKitNumber() == "" {
		return errors.New("kit number missing in set pin request")
	}
	if s.Req.GetExpiryDate() == nil {
		return errors.New("expiry date missing in set pin request")
	}
	if s.Req.GetDob() == nil {
		return errors.New("dob missing in set pin request")
	}
	return nil
}

func (s *SetPinResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	if len(b) == 0 {
		logger.Error(ctx, "no response received from set pin response")
		return &creditcard.SetPinResponse{Status: rpc.StatusInternal()}, nil
	}
	res := m2pLending.SetPinResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response from ")
	}
	return &creditcard.SetPinResponse{Status: rpc.StatusInternalWithDebugMsg(res.GetException().GetDetailMessage())}, nil
}

func (s *SetPinRequest) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}
