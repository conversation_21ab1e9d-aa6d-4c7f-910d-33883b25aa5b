//nolint:dupl
package m2p

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	m2pLending "github.com/epifi/gamma/api/vendors/m2p/lending/creditcard"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/m2p"
	m2pCryptor "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/m2p"
)

var (
	cardReplacementResponseCodeToStatusCode = map[string]rpc.StatusFactory{
		"Y3264": rpc.StatusAlreadyExists,
		"Y3290": rpc.StatusPermissionDenied, // "Invalid action! Valid Actions are 'BLOCK'"
	}
)

type CardReplacementRequest struct {
	*m2pCryptor.DefaultM2PSecuredExchange
	*m2p.DefaultHeaderAdder
	Method string
	Req    *creditcard.ReplaceCardRequest
	Conf   *config.CreditCard
}

type CardReplacementResp struct{}

func (c *CardReplacementRequest) CanLogUnredactedEncryptedPayload() bool {
	return true
}

func (c *CardReplacementResp) CanLogUnredactedEncryptedPayload() bool {
	return true
}
func (c *CardReplacementResp) Unmarshal(b []byte) (proto.Message, error) {
	res := &m2pLending.ReplaceCardResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res)
	if err != nil {
		return nil, errors.Wrap(err, "Error unmarshalling replace card response")
	}
	if res.GetException() != nil {
		status, ok := cardReplacementResponseCodeToStatusCode[res.GetException().GetErrorCode()]
		if !ok {
			status = rpc.StatusInternal
		}
		return &creditcard.ReplaceCardResponse{Status: status()}, nil
	}
	return &creditcard.ReplaceCardResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (c *CardReplacementRequest) HTTPMethod() string {
	return c.Method
}

func (c *CardReplacementRequest) URL() string {
	return c.Conf.M2P.M2PHost + "Yappay/business-entity-manager/replaceCard"
}

func (c *CardReplacementRequest) GetResponse() vendorapi.Response {
	return &CardReplacementResp{}
}

func (c *CardReplacementRequest) Marshal() ([]byte, error) {
	if c.Req.GetCustomerId() == "" {
		return nil, errors.New("customer id missing from request")
	}
	if c.Req.GetOldKitNumber() == "" {
		return nil, errors.New("old kit number missing from request")
	}

	return protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&m2pLending.ReplaceCardRequest{
		EntityId:     c.Req.GetCustomerId(),
		OldKitNumber: c.Req.GetOldKitNumber(),
		NewKitNumber: c.Req.GetNewKitNumber(),
	})
}

func (c *CardReplacementResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	if len(b) == 0 {
		logger.Error(ctx, "no response received from ReplaceCard response")
		return &creditcard.ReplaceCardResponse{Status: rpc.StatusInternal()}, nil
	}
	res := m2pLending.ReplaceCardResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response from CardReplacement API")
	}
	status, ok := cardReplacementResponseCodeToStatusCode[res.GetException().GetErrorCode()]
	if !ok {
		logger.Error(ctx, "status mapping not found for replace card", zap.String(logger.RESPONSE_CODE,
			res.GetException().GetErrorCode()))
		return &creditcard.ReplaceCardResponse{Status: rpc.StatusInternalWithDebugMsg(res.GetException().GetDetailMessage())}, nil
	}
	return &creditcard.ReplaceCardResponse{Status: status()}, nil
}

func (c *CardReplacementRequest) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}
