//nolint:dupl
package m2p

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"golang.org/x/text/currency"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	m2pLending "github.com/epifi/gamma/api/vendors/m2p/lending/creditcard"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/m2p"
	m2pCryptor "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/m2p"
)

type FetchUnbilledTransactionsRequest struct {
	*m2pCryptor.DefaultM2PSecuredExchange
	*m2p.DefaultHeaderAdder
	Method string
	Req    *creditcard.FetchUnbilledTransactionsRequest
	Conf   *config.CreditCard
}

type FetchUnbilledTransactionsResp struct{}

func (f *FetchUnbilledTransactionsRequest) CanLogUnredactedEncryptedPayload() bool {
	return true
}

func (f *FetchUnbilledTransactionsResp) CanLogUnredactedEncryptedPayload() bool {
	return true
}
func (f *FetchUnbilledTransactionsResp) Unmarshal(b []byte) (proto.Message, error) {
	vendorResponse := &m2pLending.FetchUnbilledTransactionsResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, vendorResponse)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response from fetchUnbilledTransactions API")
	}
	if vendorResponse.GetException() != nil {
		status, ok := m2pCodesVsRpcCodes[vendorResponse.GetException().GetErrorCode()]
		if !ok {
			status = rpc.StatusInternal()
		}
		return &creditcard.FetchUnbilledTransactionsResponse{Status: status}, nil
	}
	transactions, err := f.getTransactions(vendorResponse.GetTransactions())
	if err != nil {
		return nil, errors.Wrap(err, "error fetching transaction from fetchUnbilledTransactions API")
	}
	var paginationInfo *creditcard.Pagination = nil

	if vendorResponse.GetPaginationInfo() != nil {
		paginationInfo = &creditcard.Pagination{
			PageSize:      vendorResponse.GetPaginationInfo().GetPageSize(),
			PageNumber:    vendorResponse.GetPaginationInfo().GetPageNumber(),
			TotalPages:    vendorResponse.GetPaginationInfo().GetTotalPages(),
			TotalElements: vendorResponse.GetPaginationInfo().GetTotalElements(),
		}
	}

	return &creditcard.FetchUnbilledTransactionsResponse{
		Status:         rpc.StatusOk(),
		Transactions:   transactions,
		PaginationInfo: paginationInfo,
	}, nil
}

func (f *FetchUnbilledTransactionsResp) getTransactions(transactions []*m2pLending.TransactionWithTimestamp) ([]*creditcard.Transaction, error) {
	vgTransactions := make([]*creditcard.Transaction, 0)
	for _, transaction := range transactions {
		vgTransaction, err := f.getTransaction(transaction)
		if err != nil {
			return nil, errors.Wrap(err, "error fetching transaction list for fetchUnbilledTransaction API")
		}
		vgTransactions = append(vgTransactions, vgTransaction)
	}
	return vgTransactions, nil
}

func (f *FetchUnbilledTransactionsResp) getTransaction(vendorTransaction *m2pLending.TransactionWithTimestamp) (*creditcard.Transaction, error) {
	transactionTransferType := getTransactionTransferTypeFromCrDr(vendorTransaction.GetCreditDebit())
	amount := money.ParseFloat(vendorTransaction.GetAmount(), currency.INR.String())
	transactionType := creditcard.TransactionType_value[vendorTransaction.GetTransactionCategory()]
	if transactionType == 0 {
		transactionType = int32(transactionTypeMap[vendorTransaction.GetTransactionCategory()])
	}
	transactionOrigin := ccEnumsPb.TransactionOrigin(ccEnumsPb.TransactionOrigin_value[vendorTransaction.GetTransactionOrigin()])
	if transactionOrigin == 0 {
		transactionOrigin = transactionOriginMap[vendorTransaction.GetTransactionOrigin()]
	}
	transactionTime := timestamppb.New(time.UnixMilli(vendorTransaction.GetTransactionDate()))
	return &creditcard.Transaction{
		TransactionType:         ccEnumsPb.TransactionType(transactionType),
		TransactionTransferType: transactionTransferType,
		TransactionTime:         transactionTime,
		TransactionOrigin:       transactionOrigin,
		AuthorizationCode:       vendorTransaction.GetAuthorizationCode(),
		AcquirerId:              vendorTransaction.GetAcquirerId(),
		MerchantCategoryCode:    vendorTransaction.GetMerchantCategoryCode(),
		MonetaryDetails: &creditcard.TransactionMonetaryDetails{
			Amount: amount,
		},
		MerchantInformation: &creditcard.MerchantInformation{
			MerchantId:         vendorTransaction.GetMerchantId(),
			MerchantName:       vendorTransaction.GetMerchantName(),
			MerchantLocation:   vendorTransaction.GetMerchantLocation(),
			MerchantTerminalId: vendorTransaction.GetMerchantTerminalId(),
		},
		StanNumber:                       vendorTransaction.GetStanNumber(),
		CustomerRetrievalReferenceNumber: vendorTransaction.GetRrn(),
		InternalTransactionId:            vendorTransaction.GetInternalTransactionId(),
		ExternalTransactionId:            vendorTransaction.GetExtTransactionId(),
	}, nil
}

func (f *FetchUnbilledTransactionsRequest) HTTPMethod() string {
	return f.Method
}

func (f *FetchUnbilledTransactionsRequest) URL() string {
	baseUrl := f.Conf.M2P.M2PHost + "statement/customer/fetchUnbilledTxns"
	req, _ := http.NewRequest(http.MethodPost, baseUrl, nil)
	q := req.URL.Query()
	if f.Req.GetStartDate() != nil {
		startDateString := datetime.DateToString(f.Req.GetStartDate(), yyyyMmDdLayout, datetime.IST)
		q.Add("fromDate", startDateString)
	}
	if f.Req.GetEndDate() != nil {
		endDateString := datetime.DateToString(f.Req.GetEndDate(), yyyyMmDdLayout, datetime.IST)
		q.Add("toDate", endDateString)
	}
	if f.Req.GetPaginationInfo().GetPageNumber() != 0 {
		pageNumberString := strconv.FormatInt(int64(f.Req.GetPaginationInfo().GetPageNumber()), 10)
		q.Add("pageNumber", pageNumberString)
	}
	if f.Req.GetPaginationInfo().GetPageSize() != 0 {
		pageSizeString := strconv.FormatInt(int64(f.Req.GetPaginationInfo().GetPageSize()), 10)
		q.Add("pageSize", pageSizeString)
	}
	req.URL.RawQuery = q.Encode()
	return req.URL.String()
}

func (f *FetchUnbilledTransactionsRequest) GetResponse() vendorapi.Response {
	return &FetchUnbilledTransactionsResp{}
}

func (f *FetchUnbilledTransactionsRequest) Marshal() ([]byte, error) {
	if f.Req.GetCustomerId() == "" {
		return nil, errors.New("customer id missing in fetch unbilled transaction request")
	}
	return protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&m2pLending.FetchUnbilledTransactionsRequest{CustomerId: f.Req.GetCustomerId()})
}

func (f *FetchUnbilledTransactionsResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	if len(b) == 0 {
		logger.Error(ctx, "no response received from fetchUnbilledTransactions API")
		return &creditcard.FetchUnbilledTransactionsResponse{Status: rpc.StatusInternal()}, nil
	}
	res := m2pLending.FetchUnbilledTransactionsResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response from fetchUnbilledTransactions API")
	}
	return &creditcard.FetchUnbilledTransactionsResponse{Status: rpc.StatusInternalWithDebugMsg(res.GetException().GetDetailMessage())}, nil
}

func (f *FetchUnbilledTransactionsRequest) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}
