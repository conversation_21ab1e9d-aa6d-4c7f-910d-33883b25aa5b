package auth

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"net/url"
	"strconv"
	"strings"
	"time"

	loyltyPb "github.com/epifi/gamma/api/vendorgateway/offers/loylty"

	"github.com/epifi/gamma/vendorgateway/config"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
)

const (
	PipeDelimiter  = "|"
	ColonDelimiter = ":"
)

// interface implemented by all loylty requests using AuthParams
type ILoyaltyRequestWithAuth interface {
	vendorapi.SyncRequest
	GetAuthToken() string
	GetRequestTimestamp() string
	GetIV() string
}

// These are params required by all requests for performing authentication related stuff.
type AuthParams struct {
	IV               string
	RequestTimestamp string
	AuthToken        string
}

func (a AuthParams) GetIV() string {
	return a.IV
}

func (a AuthParams) GetRequestTimestamp() string {
	return a.RequestTimestamp
}

func (a AuthParams) GetAuthToken() string {
	return a.AuthToken
}

func NewAuthParam(authToken string) AuthParams {
	return AuthParams{
		IV:               idgen.RandAlphaNumericString(loyltyAesGcmIvSizeInBytes),
		RequestTimestamp: strconv.FormatInt(time.Now().UnixNano()/1000000, 10),
		AuthToken:        authToken,
	}
}

func getHmacString(req ILoyaltyRequestWithAuth, cfg *config.Config) (string, error) {
	payload, err := req.Marshal()
	if err != nil {
		return "", errors.Wrap(err, "error generating hmac string")
	}
	sha256OfPayload := ""
	if len(payload) > 0 {
		sha256Bytes := sha256.Sum256(payload)
		sha256OfPayload = base64.StdEncoding.EncodeToString(sha256Bytes[:])
	}
	encodedRequestUrl := strings.ToLower(url.QueryEscape(req.URL()))

	hmacString := cfg.Secrets.Ids[config.LoyltyClientId] + PipeDelimiter +
		req.HTTPMethod() + PipeDelimiter +
		encodedRequestUrl + PipeDelimiter +
		req.GetIV() + PipeDelimiter +
		req.GetRequestTimestamp() + PipeDelimiter + sha256OfPayload

	return hmacString, nil
}

func GenerateSignAuth(req ILoyaltyRequestWithAuth, cfg *config.Config) (string, error) {
	hmacString, err := getHmacString(req, cfg)
	if err != nil {
		logger.ErrorNoCtx("error generating hmac string", zap.Error(err))
		return "", err
	}
	hmacSign, err := createHmacSignature([]byte(hmacString), cfg)
	if err != nil {
		logger.ErrorNoCtx("error generating hmac signature", zap.Error(err))
		return "", err
	}
	return cfg.Secrets.Ids[config.LoyltyClientKey] + ColonDelimiter +
		hmacSign + ColonDelimiter +
		req.GetIV() + ColonDelimiter +
		req.GetRequestTimestamp(), nil
}

func createHmacSignature(data []byte, cfg *config.Config) (string, error) {
	loyltyHmacCryptor := loyltyPb.NewLoyltyHmacCryptor(cfg.Secrets.Ids[config.LoyltyClientSecret])
	signBytes, err := loyltyHmacCryptor.Sign(context.Background(), data, "")
	if err != nil {
		return "", errors.Wrap(err, "error creating hmac signature")
	}
	return base64.StdEncoding.EncodeToString(signBytes), nil
}
