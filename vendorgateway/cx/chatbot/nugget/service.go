package nugget

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	nuggetPb "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
)

// Service provides an implementation of the Nugget gRPC service
type Service struct {
	nuggetPb.UnimplementedNuggetChatbotServiceServer
	Handler *vendorapi.HTTPRequestHandler
	genConf *genconf.Config
}

func NewService(handler *vendorapi.HTTPRequestHandler, genConf *genconf.Config) *Service {
	return &Service{
		Handler: handler,
		genConf: genConf,
	}
}

func (s *Service) FetchAccessToken(ctx context.Context, req *nuggetPb.FetchAccessTokenRequest) (*nuggetPb.FetchAccessTokenResponse, error) {
	vendorReq, err := vendorapi.NewVendorRequest(req, s.getRequestFactoryMap())
	if err != nil {
		logger.Error(ctx, "Error creating vendor request for FetchAccessToken", zap.Error(err))
		return &nuggetPb.FetchAccessTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "Error handling vendor request for FetchAccessToken", zap.Error(err))
		return &nuggetPb.FetchAccessTokenResponse{
			Status: vendorapi.GetStatusFromError(err),
		}, nil
	}

	return res.(*nuggetPb.FetchAccessTokenResponse), nil
}
