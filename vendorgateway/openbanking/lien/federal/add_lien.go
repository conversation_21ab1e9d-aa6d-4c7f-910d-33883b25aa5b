package federal

// nolint:depguard
import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	lienPb "github.com/epifi/gamma/api/vendorgateway/openbanking/lien"
	federalPb "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/federal"
)

type AddLienReq struct {
	*federal.DefaultHeaderAdder
	Method string
	Req    *lienPb.AddLienRequest
	Url    string
	Conf   *config.Config
}

// Marshal provides the json of request data for API call
func (r *AddLienReq) Marshal() ([]byte, error) {
	if err := r.Req.Validate(); err != nil {
		return nil, fmt.Errorf("add lien request validation failed %w", err)
	}
	req, err := r.Req.ToVendorAddLienRequest(r.Conf)
	if err != nil {
		return nil, fmt.Errorf("error in converting add lien request to federal add lien request %w", err)
	}
	mReq, err := protojson.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("error in marshaling add lien request to federal add lien request %w", err)
	}
	return mReq, nil
}

// URL provides the URL to send the request to
func (r *AddLienReq) URL() string {
	return r.Url
}

// HTTPMethod returns the http method to use for the API call.
func (r *AddLienReq) HTTPMethod() string {
	return r.Method
}

type AddLienResp struct {
	Conf *config.Config
}

func (r *AddLienReq) GetResponse() vendorapi.Response {
	return &AddLienResp{
		Conf: r.Conf,
	}
}

func (r *AddLienResp) Unmarshal(b []byte) (proto.Message, error) {
	federalLienRes := &federalPb.LienResponse{}
	unmarshaler := protojson.UnmarshalOptions{
		DiscardUnknown: true,
		AllowPartial:   true,
	}
	err := unmarshaler.Unmarshal(b, federalLienRes)
	if err != nil {
		logger.ErrorNoCtx("failed to unmarshal response", zap.Error(err))
		return &lienPb.AddLienResponse{Status: rpc.StatusInternal()}, nil
	}
	addLienResponse, err := lienPb.ParseAddLienResponse(r.Conf, federalLienRes)
	if err != nil {
		logger.ErrorNoCtx("failed to parse federal lien response to lien details", zap.Error(err))
		return &lienPb.AddLienResponse{Status: rpc.StatusInternal()}, nil
	}
	return addLienResponse, nil
}

func (r *AddLienResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "error in add lien call", zap.Int(logger.HTTP_STATUS, httpStatus))
	return &lienPb.AddLienResponse{
		Status: vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatus),
	}, nil
}
