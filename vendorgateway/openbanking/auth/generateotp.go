package auth

import (
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	authPb "github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	federalAuth "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/federal"
	federalCryptor "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/federal"
)

type GenerateOTPReq struct {
	*federal.DefaultHeaderAdder
	*federalCryptor.DefaultPGPSecuredExchange

	Method string
	Req    *authPb.GenerateOTPRequest
	Url    string
}

func (g *GenerateOTPReq) Marshal() ([]byte, error) {
	commonPayload := federal.GetCommonRequestParams()

	requestPayLoad := &federalAuth.GenerateOTPRequest{
		SenderCode:        (*commonPayload)[federal.SenderCodeKey],
		ServiceAccessId:   (*commonPayload)[federal.ServiceAccessIdKey],
		ServiceAccessCode: (*commonPayload)[federal.ServiceAccessCodeKey],
		RequestId:         g.Req.GetRequestId(),
		DeviceId:          g.Req.GetAuth().GetDeviceId(),
		UserProfileId:     g.Req.GetAuth().GetUserProfileId(),
		DeviceToken:       g.Req.GetAuth().GetDeviceToken(),
		AccountNumber:     g.Req.GetAccountNumber(),
		Email:             g.Req.GetEmail(),
		MobileNumber:      g.Req.GetPhone().ToString(),
	}
	return protojson.Marshal(requestPayLoad)
}

func (g *GenerateOTPReq) HTTPMethod() string {
	return g.Method
}

func (g *GenerateOTPReq) URL() string {
	return g.Url
}

func (g *GenerateOTPReq) GetResponse() vendorapi.Response {
	return &GenerateOTPRes{}
}

type GenerateOTPRes struct {
}

var generateOTPStatusCodes = map[string]rpc.StatusFactory{
	"000":     rpc.StatusOk,
	"OBE0001": rpc.StatusInvalidArgument,
	"OBE0002": rpc.StatusInvalidArgument,
	"OBE0003": rpc.StatusInvalidArgument,
	"OBE0004": rpc.StatusInvalidArgument,
	"OBE0007": rpc.StatusUnknown,
	"OBE0008": rpc.StatusRecordNotFound,
	"OBE0009": rpc.StatusInvalidArgument,
	"OBE0010": rpc.StatusInvalidArgument,
	"OBE0022": rpc.StatusInternal,
	"OBE0048": rpc.StatusInvalidArgument,
	"OBE0049": rpc.StatusInvalidArgument,
	"OBE0050": rpc.StatusInvalidArgument,
	"OBE0047": rpc.StatusInvalidArgument,
	"OBE0027": rpc.StatusInvalidArgument,
	"OBE0059": rpc.StatusInvalidArgument,
	"OBE0053": rpc.StatusInvalidArgument,
}

func (g *GenerateOTPRes) Unmarshal(b []byte) (proto.Message, error) {
	j := &federalAuth.GenerateOTPResponse{}
	err := protojson.Unmarshal(b, j)
	if err != nil {
		logger.ErrorNoCtx("Could not parse json response")
		return nil, err
	}
	if statusFactory, ok := generateOTPStatusCodes[j.Response]; ok {
		status := statusFactory()
		status.SetDebugMessage(j.Reason)
		return &authPb.GenerateOTPResponse{
			Status:               status,
			VendorResponseCode:   j.Response,
			VendorResponseReason: j.Reason,
		}, nil
	}
	logger.InfoNoCtx("Got unknown response",
		zap.String("ResponseCode", j.Response),
		zap.String("Reason", j.Reason),
	)
	return &authPb.GenerateOTPResponse{
		Status:               rpc.StatusUnknown(),
		VendorResponseCode:   j.Response,
		VendorResponseReason: j.Reason,
	}, nil
}
