package videosdk

import (
	"context"
	"errors"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/vendorgateway/vkyccall"
	"github.com/epifi/gamma/api/vendors/vkyccall/videosdk"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
)

type StopRecordingRequest struct {
	method string
	req    *vkyccall.StopRecordingRequest
	conf   *config.VideoSdk
}

func NewStopRecordingRequest(method string, req *vkyccall.StopRecordingRequest, conf *config.VideoSdk) *StopRecordingRequest {
	return &StopRecordingRequest{
		method: method,
		req:    req,
		conf:   conf,
	}
}

func (s *StopRecordingRequest) HTTPMethod() string {
	return s.method
}
func (s *StopRecordingRequest) URL() string {
	return s.conf.ApiHost + "/" + s.conf.ApiVersion + "/recordings/end"
}
func (s *StopRecordingRequest) GetResponse() vendorapi.Response {
	return &StopRecordingResponse{}
}

func (s *StopRecordingRequest) Marshal() ([]byte, error) {
	vendorReq := &videosdk.StopRecordingRequest{RoomId: s.req.GetRoomId()}
	return protojson.Marshal(vendorReq)
}

func (s *StopRecordingRequest) SetAuth(req *http.Request) *http.Request {
	token, err := generateServerJwt(s.conf.Secrets.ApiKey, s.conf.Secrets.SecretKey)
	if err != nil {
		token = ""
		logger.ErrorNoCtx("unable to create JWT for backend request", zap.Error(err))
	}
	req.Header.Add("Authorization", token)
	return req
}

type StopRecordingResponse struct{}

func (s *StopRecordingResponse) Unmarshal(b []byte) (proto.Message, error) {
	vendorRes := string(b)
	if vendorRes == "" {
		return &vkyccall.StopRecordingResponse{Status: rpcPb.StatusInternal()}, errors.New("received no response from vendor")
	}
	return &vkyccall.StopRecordingResponse{Status: rpcPb.StatusOk(), Message: vendorRes}, nil
}

// The content-type header in response is set to application/json but
// the actual response is a plain text, which causes redaction errors.
// Since the response doesn't contain any sensitive data, it is fine to log the response as it is.
func (s *StopRecordingResponse) RedactResponseBody(ctx context.Context, responseBody []byte, contentType string) ([]byte, error) {
	return responseBody, nil
}
