package inhouse

import (
	"encoding/json"
	"fmt"
	"net/http"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	typesPb "github.com/epifi/gamma/api/typesv2"
	parserPb "github.com/epifi/gamma/api/vendorgateway/parser"
	"github.com/epifi/be-common/pkg/vendorapi"
)

type AAParserBulkReq struct {
	Method string
	Req    *parserPb.ParseAATxnBulkRequest
	Url    string
}

func (r *AAParserBulkReq) HTTPMethod() string {
	return r.Method
}

func (r *AAParserBulkReq) URL() string {
	return r.Url
}

func (r *AAParserBulkReq) Add(req *http.Request) *http.Request {
	req.Header.Add("accept", "application/json")
	req.Header.Add("Content-Type", "application/json")

	return req
}

func (r *AAParserBulkReq) GetResponse() vendorapi.Response {
	return &AAParserBulkResp{}
}

func (r *AAParserBulkReq) Marshal() ([]byte, error) {
	var reqPayloadList []*vendorAAParserReq

	for _, value := range r.Req.GetParseAaTxnInputList() {
		txnItem := &vendorAAParserReq{
			TransactionId:   value.GetTxnId(),
			Narration:       value.GetNarration(),
			Bank:            value.GetBank().String(),
			TransactionType: value.GetTxnType().String(),
		}
		reqPayloadList = append(reqPayloadList, txnItem)
	}

	reqPayload := &VendorAAParserReqList{
		TransactionDetailsList: reqPayloadList,
	}

	return json.Marshal(reqPayload)
}

type AAParserBulkResp struct{}

func (r *AAParserBulkResp) Unmarshal(b []byte) (proto.Message, error) {
	vendorRes := &VendorAAParserRespList{}

	if err := json.Unmarshal(b, &vendorRes); err != nil {
		return nil, fmt.Errorf("failed to unmarshal inhouse aa Bulk perser response: %w", err)
	}
	responseVG := &parserPb.ParseAATxnBulkResponse{}
	responseVG.Status = rpc.StatusOk()
	var txnList []*parserPb.ParseAATxnOutput

	for _, value := range vendorRes.GetList() {

		txnItem := &parserPb.ParseAATxnOutput{
			TxnId:             value.TxnId,
			Parsed:            value.Parsed,
			TemplateId:        value.TemplateId,
			Utr:               value.Utr,
			PaymentProtocol:   paymentPb.PaymentProtocol(paymentPb.PaymentProtocol_value[value.Protocol]),
			PiType:            piPb.PaymentInstrumentType(piPb.PaymentInstrumentType_value[value.PaymentInstrumentType]),
			PaymentAddress:    value.PaymentAddress,
			Bank:              typesPb.Bank(typesPb.Bank_value[value.BankName]),
			Ifsc:              value.Ifsc,
			SelfActorName:     value.SelfActorName,
			OtherActorName:    value.ActorName,
			ActorType:         typesPb.ActorType(typesPb.ActorType_value[value.ActorType]),
			Provenance:        orderPb.OrderProvenance(orderPb.OrderProvenance_value[value.Provenance]),
			Keyword:           value.Keyword,
			AtmName:           value.AtmName,
			TransactionRemark: value.TransactionRemark,
			City:              value.City,
			Mcc:               value.Mcc,
			Version:           value.Version,
		}

		txnList = append(txnList, txnItem)
	}

	responseVG.ParseAaTxnOutputList = txnList

	return responseVG, nil
}
