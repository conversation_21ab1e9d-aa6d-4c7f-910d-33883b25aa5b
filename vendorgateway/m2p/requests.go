package m2p

import (
	json2 "encoding/json"
	"net/http"

	"go.uber.org/zap"

	ccVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/be-common/pkg/logger"
	vgPciConf "github.com/epifi/gamma/vendorgateway-pci/config"
	"github.com/epifi/gamma/vendorgateway/config"
)

var (
	cfg, _      = config.Load()
	vgPciCfg, _ = vgPciConf.Load()
)

const (
	authorizationString = "Authorization"
	tenantString        = "TENANT"
)

type DefaultHeaderAdder struct {
	VendorRequestHeader *ccVgPb.CcVendorRequestHeader
}

func (d *DefaultHeaderAdder) Add(req *http.Request) *http.Request {
	authHeaders := config.M2PSecrets{}
	var (
		secretJson string
	)
	switch {
	case vgPciCfg != nil:
		switch {
		case d == nil:
			secretJson = vgPciCfg.Secrets.Ids[config.M2pSecrets]
		case ffPkg.IsSecuredCardProgram(d.VendorRequestHeader):
			secretJson = vgPciCfg.Secrets.Ids[config.M2PSecuredCardSecrets]
		case ffPkg.IsMassUnsecuredProgram(d.VendorRequestHeader):
			secretJson = vgPciCfg.Secrets.Ids[config.M2PMassUnsecuredCardSecrets]
		default:
			secretJson = vgPciCfg.Secrets.Ids[config.M2pSecrets]
		}
	default:
		switch {
		case d == nil:
			secretJson = cfg.Secrets.Ids[config.M2pSecrets]
		case ffPkg.IsSecuredCardProgram(d.VendorRequestHeader):
			secretJson = cfg.Secrets.Ids[config.M2PSecuredCardSecrets]
		case ffPkg.IsMassUnsecuredProgram(d.VendorRequestHeader):
			secretJson = cfg.Secrets.Ids[config.M2PMassUnsecuredCardSecrets]
		default:
			secretJson = cfg.Secrets.Ids[config.M2pSecrets]
		}
	}
	err := json2.Unmarshal([]byte(secretJson), &authHeaders)
	if err != nil {
		logger.ErrorNoCtx("Error unmarshalling secrets : " + err.Error())
		return req
	}
	req.Header.Set("partnerId", authHeaders.PartnerId)
	req.Header.Set("partnerToken", authHeaders.PartnerToken)
	req.Header.Set(authorizationString, authHeaders.Authorization)
	req.Header.Set(tenantString, authHeaders.Tenant)
	req.Header.Set("channel-id", authHeaders.Tenant)
	logger.InfoNoCtx("tenant passed in req:", zap.String("Tenant", authHeaders.Tenant))
	return req
}
