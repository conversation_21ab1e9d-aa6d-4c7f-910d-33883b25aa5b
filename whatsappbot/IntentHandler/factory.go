package IntentHandler

import (
	"github.com/pkg/errors"

	whatsappBotPb "github.com/epifi/gamma/api/whatsapp_bot"
	"github.com/epifi/gamma/whatsappbot/IntentHandler/handlers"
)

// this factory will be used to initialise specific intent handler instance based on user intent
type IHandlerFactory interface {
	// will return error if no handler is found for given intent
	GetIntentHandler(intent whatsappBotPb.UserIntent) (IntentHandler, error)
}

type HandlerFactory struct {
	checkStatusHandler *handlers.CheckStatusHandler
	genericHandler     *handlers.GenericHandler
	getHelpHandler     *handlers.GetHelpHandler
	startHandler       *handlers.StartHandler
	stopHandler        *handlers.StopHandler
	helloHandler       *handlers.HelloHandler
	benefitsHandler    *handlers.BenefitsHandler
	featuresHandler    *handlers.FeaturesHandler
}

func NewHandlerFactory(checkStatusHandler *handlers.CheckStatusHandler, genericHandler *handlers.<PERSON><PERSON><PERSON><PERSON><PERSON>,
	getHelpHandler *handlers.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, startHandler *handlers.<PERSON><PERSON>and<PERSON>,
	stopHandler *handlers.<PERSON><PERSON><PERSON><PERSON>, helloHandler *handlers.HelloHand<PERSON>, benefitsHandler *handlers.BenefitsHandler,
	featuresHandler *handlers.FeaturesHandler) *HandlerFactory {
	return &HandlerFactory{
		checkStatusHandler: checkStatusHandler,
		genericHandler:     genericHandler,
		getHelpHandler:     getHelpHandler,
		stopHandler:        stopHandler,
		startHandler:       startHandler,
		helloHandler:       helloHandler,
		benefitsHandler:    benefitsHandler,
		featuresHandler:    featuresHandler,
	}
}

// will return error if no handler is found for given intent
func (h *HandlerFactory) GetIntentHandler(intent whatsappBotPb.UserIntent) (IntentHandler, error) {
	switch intent {
	case whatsappBotPb.UserIntent_CHECK_STATUS:
		return h.checkStatusHandler, nil
	case whatsappBotPb.UserIntent_GENERIC:
		return h.genericHandler, nil
	case whatsappBotPb.UserIntent_GET_HELP:
		return h.getHelpHandler, nil
	case whatsappBotPb.UserIntent_START:
		return h.startHandler, nil
	case whatsappBotPb.UserIntent_STOP:
		return h.stopHandler, nil
	case whatsappBotPb.UserIntent_HELLO:
		return h.helloHandler, nil
	case whatsappBotPb.UserIntent_BENEFITS:
		return h.benefitsHandler, nil
	case whatsappBotPb.UserIntent_FEATURES:
		return h.featuresHandler, nil
	default:
		return nil, errors.New("no handler found for intent")
	}
}
