package developer

import (
	"context"

	"github.com/epifi/gamma/api/categorizer/developer"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
)

type IParameterFetcher interface {
	FetchParamList(ctx context.Context, entity developer.CategorizerEntity) ([]*cxDsPb.ParameterMeta, error)
}

type IDataFetcher interface {
	FetchData(ctx context.Context, entity developer.CategorizerEntity, filters []*cxDsPb.Filter) (string, error)
}
