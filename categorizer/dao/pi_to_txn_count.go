package dao

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	wireTypes "github.com/epifi/gamma/categorizer/wire/types"
)

var (
	PiIdEmptyErr = fmt.Errorf("pi id is empty")
)

const (
	GplacePiCountRedisKeyPrefix = "gplace-pi-count:"
	TwoWeeksInSeconds           = 1209600 * 1e9
	NumberOfTxnValue            = "number_of_txn"
)

type PiToTxnCountDaoRedis struct {
	RedisClient *redis.Client
}

func NewPiToTxnCountDaoRedis(redisClient wireTypes.CategorizerVMRedisStore) *PiToTxnCountDaoRedis {
	return &PiToTxnCountDaoRedis{
		RedisClient: redisClient,
	}
}

func (s *PiToTxnCountDaoRedis) InsertPiAndTxnCount(ctx context.Context, pi string, txnCount int64) error {
	defer metric_util.TrackDuration("categorizer/dao", "PiToTxnCountDaoRedis", "InsertPiAndTxnCount", time.Now())
	if pi == "" {
		return PiIdEmptyErr
	}
	redisKey := getGplacePiCountRedisKey(pi)

	res := s.RedisClient.HSet(ctx, redisKey, NumberOfTxnValue, txnCount)
	if res.Err() != nil {
		return res.Err()
	}
	_, err := s.RedisClient.Expire(ctx, redisKey, TwoWeeksInSeconds).Result()
	return err
}

func (s *PiToTxnCountDaoRedis) GetTxnCountByPi(ctx context.Context, pi string) (int64, error) {
	defer metric_util.TrackDuration("categorizer/dao", "PiToTxnCountDaoRedis", "GetTxnCountByPi", time.Now())
	if pi == "" {
		return 0, PiIdEmptyErr
	}
	redisKey := getGplacePiCountRedisKey(pi)

	val, err := s.RedisClient.HGet(ctx, redisKey, NumberOfTxnValue).Result()
	if errors.Is(err, redis.Nil) {
		return 0, epifierrors.ErrRecordNotFound
	}
	count, err := strconv.ParseInt(val, 10, 64)
	if err != nil {
		count = 0
	}
	return count, err
}

func (s *PiToTxnCountDaoRedis) IncrementPiTxnCount(ctx context.Context, pi string, incrementBy int64) (int64, error) {
	defer metric_util.TrackDuration("categorizer/dao", "PiToTxnCountDaoRedis", "IncrementPiTxnCount", time.Now())
	if pi == "" {
		return 0, PiIdEmptyErr
	}
	redisKey := getGplacePiCountRedisKey(pi)

	val, err := s.RedisClient.HIncrBy(ctx, redisKey, NumberOfTxnValue, incrementBy).Result()
	if err != nil {
		return 0, err
	}
	_, err2 := s.RedisClient.Expire(ctx, redisKey, TwoWeeksInSeconds).Result()
	return val, err2
}

func (s *PiToTxnCountDaoRedis) DeletePiEntry(ctx context.Context, pi string) error {
	defer metric_util.TrackDuration("categorizer/dao", "PiToTxnCountDaoRedis", "DeletePiEntry", time.Now())
	if pi == "" {
		return PiIdEmptyErr
	}
	redisKey := getGplacePiCountRedisKey(pi)
	return s.RedisClient.HDel(ctx, redisKey, NumberOfTxnValue).Err()
}

func getGplacePiCountRedisKey(piId string) string {
	return GplacePiCountRedisKeyPrefix + piId
}
