package converters

import (
	"testing"

	"github.com/stretchr/testify/assert"

	typesPb "github.com/epifi/be-common/api/typesv2/common"
	vFinfluxTypesPb "github.com/epifi/gringott/api/stockguardian/vendors/finflux/types"
)

func TestConverters_ConvertPostalAddressToVendor(t *testing.T) {
	tests := []struct {
		name        string
		vgAddress   *typesPb.PostalAddress
		addressType string
		want        *vFinfluxTypesPb.Address
	}{
		{
			name: "single address line under 200 chars",
			vgAddress: &typesPb.PostalAddress{
				AddressLines: []string{"123 Main St, Apt 4B"},
				PostalCode:   "123456",
			},
			addressType: "PERMANENT",
			want: &vFinfluxTypesPb.Address{
				AddressType:    []string{"PERMANENT"},
				AddressLineOne: "123 Main St, Apt 4B",
				AddressLineTwo: "",
				PostalCode:     "123456",
			},
		},
		{
			name: "multiple address lines under 200 chars total",
			vgAddress: &typesPb.PostalAddress{
				AddressLines: []string{
					"123 Main St",
					"Apt 4B",
					"Downtown Area",
				},
				PostalCode: "123456",
			},
			addressType: "PERMANENT",
			want: &vFinfluxTypesPb.Address{
				AddressType:    []string{"PERMANENT"},
				AddressLineOne: "123 Main St, Apt 4B, Downtown Area",
				AddressLineTwo: "",
				PostalCode:     "123456",
			},
		},
		{
			name: "address over 200 chars but under 400",
			vgAddress: &typesPb.PostalAddress{
				AddressLines: []string{
					"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890",
					"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890",
					"Additional Text",
				},
				PostalCode: "123456",
			},
			addressType: "PERMANENT",
			want: &vFinfluxTypesPb.Address{
				AddressType:    []string{"PERMANENT"},
				AddressLineOne: "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890, 12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678",
				AddressLineTwo: "90, Additional Text",
				PostalCode:     "123456",
			},
		},
		{
			name: "address over 400 chars",
			vgAddress: &typesPb.PostalAddress{
				AddressLines: []string{
					"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890",
					"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890",
					"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890",
					"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890",
				},
				PostalCode: "123456",
			},
			addressType: "PERMANENT",
			want: &vFinfluxTypesPb.Address{
				AddressType:    []string{"PERMANENT"},
				AddressLineOne: "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890, 12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678",
				AddressLineTwo: "90, 1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890, 1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234",
				PostalCode:     "123456",
			},
		},
		{
			name: "address with trailing comma in first 200 chars",
			vgAddress: &typesPb.PostalAddress{
				AddressLines: []string{
					"123 Main St",
					"Apt 4B,",
				},
				PostalCode: "123456",
			},
			addressType: "PERMANENT",
			want: &vFinfluxTypesPb.Address{
				AddressType:    []string{"PERMANENT"},
				AddressLineOne: "123 Main St, Apt 4B",
				AddressLineTwo: "",
				PostalCode:     "123456",
			},
		},
		{
			name:        "nil address",
			vgAddress:   nil,
			addressType: "PERMANENT",
			want: &vFinfluxTypesPb.Address{
				AddressType:    []string{"PERMANENT"},
				AddressLineOne: "",
				AddressLineTwo: "",
				PostalCode:     "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ConvertPostalAddressToVendor(tt.vgAddress, tt.addressType)
			assert.Equal(t, tt.want, got)
		})
	}
}
