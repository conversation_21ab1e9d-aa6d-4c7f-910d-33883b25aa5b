// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/datasharing"
	"github.com/epifi/gamma/datasharing/dao"
	"github.com/epifi/gamma/datasharing/dataprep"
	"github.com/epifi/gamma/datasharing/dataprep/prepper"
	"github.com/epifi/gamma/datasharing/wire/types"
)

// Injectors from wire.go:

func InitialiseDataSharingService(salaryEstimationClient salaryestimation.SalaryEstimationClient, connectedAccountClient connected_account.ConnectedAccountClient, sharedDataStorageS3Client types.SharedDataStorageS3Client, eventBroker events.Broker) *datasharing.Service {
	aaAccountsRawPrepper := prepper.NewAaAccountsRawPrepper(connectedAccountClient)
	factory := dataprep.NewFactory(aaAccountsRawPrepper)
	sharedAADataDaoS3 := dao.NewSharedAADataDaoS3(sharedDataStorageS3Client, factory)
	service := datasharing.NewService(connectedAccountClient, salaryEstimationClient, sharedAADataDaoS3, eventBroker)
	return service
}
