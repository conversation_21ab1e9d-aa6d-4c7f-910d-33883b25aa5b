package opening_balance_map_test

import (
	"context"
	"reflect"
	"testing"

	"github.com/epifi/be-common/pkg/aws/v2/s3/mocks"

	"github.com/epifi/gamma/accounts/statement/opening_balance_map"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
)

func TestGetOpeningBalanceForAccount(t *testing.T) {

	encryptedTestData := `-----BEGIN PGP MESSAGE-----
Version: GnuPG v2

hQIMA8+AdX2+1YmsARAAqNRzg5tO+vd33OAhYyZ0DKIZX1A2rpKpMfa/1n26LsG+
td7jQNeBhIFGy+GyI/9+xHVxA6d9RZbHzbWHTvu7kX/zhvBPvgwALgSYj6kq9U8u
F51HOvf8BMCo8pAr5nsaz+WIWNfdXquWgdWmI48fnGaHZ/IWagZPWptIBU6RlrY0
Bw6CH58M4kBceKc/H4T511HTyJBpdwVUvi4oFQGQB8a+I7olwSUPycL0mahounCl
GJuUVynZQSpCgOii1KXa7Cj/AyqJ/gSr8vs6DkyVB0mLwm/IwuuVyv0Qg9CkMW9r
6L3j6WQel6p7bXmJoPHwcAwg7PAGLNqsbDdRexkjwkWe6rb+QCb8LSHI6ISIuUry
5fsaYiTGUk4EM3xgWnQF776TV90cj3tn5Nposy4ZJ98VgSslVMWKv8gT0A0ryCBL
Lbeh1J+ZUs0kr0++PkYwiI+Otm13TYN31taxTUiidDHXavFpAXCq0QYa5TFeh0CO
Q+wilpyBQYcl8DuUZJkbNPgmQJXgeHH803+njgWMWhVSNooB0tE2xNHAmQg0JSBP
No5ElWxxG2WOAsMe2O0URtWUaq323AY3Rr/F+3dXE5GKsSpndmIUVJ/fNcGPnlw9
E8RyN7qVcA+VAkR3SxXUs8bfjR+D81TfFgRuvqjcNksyrV4qrvt0piXEdvHxecfS
rwF9uO2tysxADMU70ZS4ciomiBWl05tl3UfXk+jAfUMeSuiENf8JHKqTcGkfvGv3
GjZY25sfh8xKw3kYei9IC3vnGM78Z9tA0RTX1wys0sd8c56aEL1gMwh6jTlX3Rfy
V346nc5sfwBLkGrP/4IovjGNzfJFFPBi3U4/oqSrLhtYJCa6NhnYyZZxfMiT2HW+
NBqfo+Myh8I5XqvP5gQZv9zlPVA20qM05FlDi5uQ+ck=
=oeRU
-----END PGP MESSAGE-----`

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	s3ClientMock := mocks.NewMockS3Client(ctr)
	s3ClientMock.EXPECT().Read(gomock.Any(), gomock.Any()).Return([]byte(encryptedTestData), nil)

	opening_balance_map.InitOpeningBalanceMap(context.Background(), s3ClientMock, "any", cryptorStore)

	type args struct {
		accountNumber string
	}
	tests := []struct {
		name    string
		args    args
		want    *moneyPb.Money
		wantErr bool
	}{
		{
			name: "get data for valid account",
			args: args{
				accountNumber: "**************",
			},
			want: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        0,
				Nanos:        0,
			},
			wantErr: false,
		},
		{
			name: "get data for valid account",
			args: args{
				accountNumber: "**************",
			},
			want: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        4427,
				Nanos:        *********,
			},
			wantErr: false,
		},
		{
			name: "get data for in-valid account",
			args: args{
				accountNumber: "***************",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := opening_balance_map.GetOpeningBalanceForAccount(context.Background(), tt.args.accountNumber)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOpeningBalanceForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOpeningBalanceForAccount() got = %v, want %v", got, tt.want)
			}
		})
	}
}
