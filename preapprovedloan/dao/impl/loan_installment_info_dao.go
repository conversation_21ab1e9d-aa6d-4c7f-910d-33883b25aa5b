//nolint:dupl
package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/dao/model"

	"github.com/google/wire"
	"gorm.io/gorm"
)

type CrdbLoanInstallmentInfoDao struct {
	dbResourceProvider *storage.DBResourceProvider[*gorm.DB]
	idGen              idgen.IdGenerator
}

var _ dao.LoanInstallmentInfoDao = &CrdbLoanInstallmentInfoDao{}

var LoanInstallmentInfoDaoWireSet = wire.NewSet(NewCrdbLoanInstallmentInfoDao, wire.Bind(new(dao.LoanInstallmentInfoDao), new(*CrdbLoanInstallmentInfoDao)))

var installmentInfoColumnNames = map[preapprovedloanPb.LoanInstallmentInfoFieldMask]string{
	preapprovedloanPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_ACCOUNT_ID:              "account_id",
	preapprovedloanPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_AMOUNT:            "total_amount",
	preapprovedloanPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_START_DATE:              "start_date",
	preapprovedloanPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_END_DATE:                "end_date",
	preapprovedloanPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_INSTALLMENT_COUNT: "total_installment_count",
	preapprovedloanPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_NEXT_INSTALLMENT_DATE:   "next_installment_date",
	preapprovedloanPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_DETAILS:                 "details",
	preapprovedloanPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_STATUS:                  "status",
	preapprovedloanPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_DEACTIVATED_AT:          "deactivated_at",
}

func NewCrdbLoanInstallmentInfoDao(dbResourceProvider *storage.DBResourceProvider[*gorm.DB], idGen idgen.IdGenerator) *CrdbLoanInstallmentInfoDao {
	return &CrdbLoanInstallmentInfoDao{
		dbResourceProvider: dbResourceProvider,
		idGen:              idGen,
	}
}

func (c *CrdbLoanInstallmentInfoDao) Create(ctx context.Context, installmentInfo *preapprovedloanPb.LoanInstallmentInfo) (*preapprovedloanPb.LoanInstallmentInfo, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanInstallmentInfoDao", "Create", time.Now())
	loanInstallmentInfoModel := model.NewLoanInstallmentInfo(installmentInfo)
	id, err := c.idGen.Get(idgen.PreApprovedLoanInstallmentInfo)
	if err != nil {
		return nil, fmt.Errorf("id generation failed: %w", err)
	}
	loanInstallmentInfoModel.Id = id
	handle, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	res := handle.Create(loanInstallmentInfoModel)
	if res.Error != nil {
		// if trying to create more than one entry for an actor_id and vendor return duplicate entry error
		if storage.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}
	return loanInstallmentInfoModel.GetProto(), nil
}

func (c *CrdbLoanInstallmentInfoDao) Update(ctx context.Context, installmentInfo *preapprovedloanPb.LoanInstallmentInfo, updateMasks []preapprovedloanPb.LoanInstallmentInfoFieldMask) error {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanInstallmentInfoDao", "Update", time.Now())
	if installmentInfo.GetId() == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}
	installmentInfoModel := model.NewLoanInstallmentInfo(installmentInfo)
	updateColumns := c.selectedColumnsForUpdate(updateMasks)
	handle, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	whereClause := &model.LoanInstallmentInfo{
		Id: installmentInfo.GetId(),
	}
	res := handle.Model(installmentInfoModel).Where(whereClause).Select(updateColumns).Updates(installmentInfoModel)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

func (c *CrdbLoanInstallmentInfoDao) GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanInstallmentInfo, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanInstallmentInfoDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id can't be blank")
	}
	var loanInstallmentInfoModel model.LoanInstallmentInfo
	d, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	if err := d.Where("id = ?", id).First(&loanInstallmentInfoModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanInstallmentInfoModel.GetProto(), nil
}

func (c *CrdbLoanInstallmentInfoDao) GetByAccountIdAndStatuses(ctx context.Context, accountId string, statuses []preapprovedloanPb.LoanInstallmentInfoStatus) ([]*preapprovedloanPb.LoanInstallmentInfo, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanInstallmentInfoDao", "GetByAccountIdAndStatuses", time.Now())
	if accountId == "" {
		return nil, errors.New("AccountId can't be blank")
	}
	for index := range statuses {
		if statuses[index] == preapprovedloanPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_UNSPECIFIED {
			return nil, fmt.Errorf("status can't be unspecified at index: %d", index)
		}
	}
	d, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	loanInstallmentInfoModels := make([]*model.LoanInstallmentInfo, 0)
	query := d.Where("account_id = ?", accountId)
	if len(statuses) > 0 {
		query = query.Where("status in (?)", statuses)
	}
	if err := query.Find(&loanInstallmentInfoModels).Error; err != nil {
		return nil, fmt.Errorf("failed to get LoanInstallmentInfo by Account id: %s and statuses: %v, %w", accountId, statuses, err)
	}
	if len(loanInstallmentInfoModels) == 0 {
		return nil, fmt.Errorf("no Loan Installment Info exist for Account id: %v and statues %v, %w", accountId, statuses, epifierrors.ErrRecordNotFound)
	}

	var loanInstallmentInfos []*preapprovedloanPb.LoanInstallmentInfo
	for _, itModel := range loanInstallmentInfoModels {
		loanInstallmentInfos = append(loanInstallmentInfos, itModel.GetProto())
	}
	return loanInstallmentInfos, nil
}

func (c *CrdbLoanInstallmentInfoDao) GetByActiveAccountId(ctx context.Context, accountId string) (*preapprovedloanPb.LoanInstallmentInfo, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanInstallmentInfoDao", "GetByActiveAccountId", time.Now())
	if accountId == "" {
		return nil, errors.New("AccountId can't be blank")
	}
	var loanInstallmentInfoModel model.LoanInstallmentInfo
	d, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	if err := d.Where("account_id = ?", accountId).Where("status = ?", preapprovedloanPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE).First(&loanInstallmentInfoModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanInstallmentInfoModel.GetProto(), nil
}

func (c *CrdbLoanInstallmentInfoDao) selectedColumnsForUpdate(updateMasks []preapprovedloanPb.LoanInstallmentInfoFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, installmentInfoColumnNames[field])
	}
	return selectColumns
}

func (c *CrdbLoanInstallmentInfoDao) GetByNextInstallmentDateAndStatus(ctx context.Context, nextInstallmentDate *time.Time, status preapprovedloanPb.LoanInstallmentInfoStatus) ([]*preapprovedloanPb.LoanInstallmentInfo, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanInstallmentInfoDao", "GetByNextInstallmentDateAndStatus", time.Now())
	if nextInstallmentDate == nil {
		return nil, errors.New("nextInstallmentDate can't be blank")
	}
	var loanInstallmentInfoModels []*model.LoanInstallmentInfo
	d, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	if err := d.Where("next_installment_date = ? and status = ?", nextInstallmentDate, status).Find(&loanInstallmentInfoModels).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	if len(loanInstallmentInfoModels) == 0 {
		return nil, fmt.Errorf("no Loan Installment Info exist for next Installment Date %v, %w", nextInstallmentDate, epifierrors.ErrRecordNotFound)
	}
	var loanInstallmentInfos []*preapprovedloanPb.LoanInstallmentInfo

	for _, itModel := range loanInstallmentInfoModels {
		loanInstallmentInfos = append(loanInstallmentInfos, itModel.GetProto())
	}
	return loanInstallmentInfos, nil
}
