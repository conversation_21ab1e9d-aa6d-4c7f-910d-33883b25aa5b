//nolint:dupl,goimports
package epifitech

import (
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	"go.temporal.io/sdk/workflow"

	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

type OfferCreation struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewOfferCreation() stages.IStage {
	return &OfferCreation{}
}

var _ stages.IStage = &OfferCreation{}

func (l *OfferCreation) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	// execute the activity to update LOEC data status
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}

	actErr := activityPkg.Execute(ctx, palNs.OfferCreationActivity, actRes, actReq)
	if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, false) {
		return res, actErr
	}
	res.SkipNextActionUpdate = true
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS

	return res, nil
}

func (l *OfferCreation) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION
}

func (l *OfferCreation) GetCelestialStage() epifitemporal.Stage {
	return palNs.OfferCreation
}
