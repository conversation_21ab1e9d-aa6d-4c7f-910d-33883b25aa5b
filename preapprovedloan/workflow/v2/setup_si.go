// nolint:dupl
package v2

import (
	"fmt"

	"github.com/pkg/errors"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/fiftyfin/lamf/si"
)

//nolint:dupl
func SetupSi(ctx workflow.Context, _ *workflowPb.Request) error {
	lg := workflow.GetLogger(ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID

	var payload palWorkflowPb.SetupSiPayload
	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, &payload)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return errors.Wrap(err, "failed to fetch workflow processing params")
	}

	vp, vpErr := getSetupSiVendorProvider(payload.GetLoanHeader())
	if vpErr != nil {
		lg.Error("failed to get vendor provider by payload", zap.Error(err))
		return errors.Wrap(vpErr, "failed to get vendor provider by payload")
	}

	execErr := executeGroupStages(ctx, &executeGroupStagesAndStagesRequest{
		wfReqId:            wfReqID,
		wfProcessingParams: wfProcessingParams,
		loanHeader:         payload.GetLoanHeader(),
		vendorProvider:     vp,
		flowName:           palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_SETUP_SI,
	})
	if execErr != nil {
		lg.Error("workflow execution failed for setup si", zap.Error(execErr))
		return errors.Wrap(execErr, "workflow execution failed for setup si")
	}

	return nil
}

func getSetupSiVendorProvider(lh *palPb.LoanHeader) (providers.IVendorProvider, error) {
	switch lh.GetVendor() {
	case palPb.Vendor_FIFTYFIN:
		return si.NewSiProvider(), nil
	default:
		return nil, fmt.Errorf("no vendor provider found")
	}
}
