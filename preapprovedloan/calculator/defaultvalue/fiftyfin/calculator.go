package fiftyfin

import (
	"google.golang.org/genproto/googleapis/type/money"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	defaultValueCalculator "github.com/epifi/gamma/preapprovedloan/calculator/defaultvalue"
	"github.com/epifi/gamma/preapprovedloan/calculator/types"
)

type FiftyfinCalculator struct {
	*palPb.LoanOffer
	*defaultValueCalculator.Calculator
}

var _ types.DefaultValueCalculator = &FiftyfinCalculator{}

func NewfiftyfinCalculator(loanOffer *palPb.LoanOffer, baseCalculator *defaultValueCalculator.Calculator) *FiftyfinCalculator {
	return &FiftyfinCalculator{
		loanOffer,
		baseCalculator,
	}
}

func (c *FiftyfinCalculator) GetMaxAmountBasisOnMaxTenureAndMaxEmi() *money.Money {
	return c.GetOfferConstraints().GetMaxLoanAmount()
}
