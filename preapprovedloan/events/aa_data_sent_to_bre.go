//nolint:dupl
package events

import (
	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type AADataReceivedForBRE struct {
	EventName   string
	Vendor      string
	LoanProgram string
	EventType   string
	EventId     string
	ActorId     string
	ProspectId  string
}

func NewAADataReceivedForBRE(actorId, vendor, loanProgram string) *AADataReceivedForBRE {
	return &AADataReceivedForBRE{
		EventName:   FiAADataReceivedForBREEventName,
		Vendor:      vendor,
		LoanProgram: loanProgram,
		EventType:   events.EventTrack,
		EventId:     uuid.New().String(),
		ActorId:     actorId,
		ProspectId:  "",
	}
}

func (c *AADataReceivedForBRE) GetEventType() string {
	return c.EventType
}

func (c *AADataReceivedForBRE) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *AADataReceivedForBRE) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *AADataReceivedForBRE) GetEventId() string {
	return c.EventId
}

func (c *AADataReceivedForBRE) GetUserId() string {
	return c.ActorId
}

func (c *AADataReceivedForBRE) GetProspectId() string {
	return c.ProspectId
}

func (c *AADataReceivedForBRE) GetEventName() string {
	return c.EventName
}
