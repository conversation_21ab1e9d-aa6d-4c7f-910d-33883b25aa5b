package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func (p *Processor) CheckForRiskV3(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	res, err := ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		loanRequest, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			lg.Error("failed to fetch loan request by orch Id", zap.Error(err))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("failed to fetch record from Db in CheckForRiskV3, %v", err))
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to get the loan request in CheckForRiskV3, err: %v", err))
		}

		isRiskyUser, riskErr := p.rpcHelper.IsRiskyUser(ctx, loanRequest.GetActorId())
		if riskErr != nil {
			lg.Error("failed to check risk profile for PL", zap.Error(riskErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to get the is risky user in CheckForRiskV3, err: %v", riskErr))
		}
		if isRiskyUser {
			return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Errorf("application failed, risky user in CheckForRiskV3").Error())
		}
		res.LoanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS

		return res, nil
	})
	return res, err
}
