// nolint:ineffassign
package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

// isIncomeDetailsUpdateNeeded checks if the income details need to be updated in loan applicant or not
// currently income details are taken only in case of realtime distribution
var isIncomeDetailsUpdateNeededMap = map[palPb.LoanProgram]bool{
	palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB: true,
}

func (p *Processor) UpdateApplicantDetails(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		addressLse, err := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS)
		if err != nil {
			lg.Error("failed to fetching address lse", zap.Error(err), zap.String("lrId", lse.GetRefId()))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetching address lse: %v", err.Error()))
		}

		employmentLse, err := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT)
		if err != nil {
			lg.Error("failed to fetching employment lse", zap.Error(err), zap.String("lrId", lse.GetRefId()))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetching employment lse: %v", err.Error()))
		}

		incomeLse := &palPb.LoanStepExecution{}
		if isIncomeDetailsUpdateNeededMap[req.GetLoanProgram()] {
			incomeLse, err = p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INCOME_VERIFICATION)
			if err != nil {
				lg.Error("failed to fetching income lse", zap.Error(err), zap.String("lrId", lse.GetRefId()))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetching income lse: %v", err.Error()))
			}
		}
		lr, err := p.loanRequestDao.GetById(ctx, req.GetLoanStep().GetRefId())
		if err != nil {
			lg.Error("unable to fetch loan request.", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		vendor := lr.GetVendor()
		// We are currently using this activity in eligibility journey owned by epifi tech. But the loan_applicant entity is owned by lender.
		// Since we are evaluating eligibility only for Liquiloans lender, hardcoding the ownership to LL to update loan_applicant
		// If eligibility journey is owned by any other lender entity, then we can use the same ownership to update loan_applicant as well
		if lr.GetVendor() == palPb.Vendor_EPIFI_TECH && req.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY {
			vendor = palPb.Vendor_LIQUILOANS
		} else if req.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY {
			// making stock guardian value hard-coded in terms of getting eligibility
			// loan program, and will be moving to a better logic to support every program in eligibility flow here
			vendor = palPb.Vendor_STOCK_GUARDIAN_LSP
		}

		ctx = RunWithOwnershipAndReset(ctx, helper.GetPalOwnership(vendor), func(ctx context.Context) {
			var lapp *palPb.LoanApplicant
			lapp, err = p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, req.GetLoanStep().GetActorId(), vendor, getApplicantLpFromLseLp(req.GetLoanProgram()), lr.GetDetails().GetProgramVersion())
			if err != nil {
				return
			}
			if lapp.GetEmploymentDetails() == nil {
				lapp.EmploymentDetails = &palPb.EmploymentDetails{}
			}
			lapp.GetEmploymentDetails().IncomeEstimateData = incomeLse.GetDetails().GetIncomeEstimateData()
			lapp.GetEmploymentDetails().Occupation = employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetOccupation()
			lapp.GetEmploymentDetails().StatedIncome = employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetMonthlyIncome()
			lapp.GetEmploymentDetails().OrganizationName = employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetOrganizationName()
			lapp.GetEmploymentDetails().WorkEmail = employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetWorkEmail()
			lapp.GetEmploymentDetails().OfficeAddress = employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetOfficeAddress()
			if lapp.GetPersonalDetails() == nil {
				lapp.PersonalDetails = &palPb.PersonalDetails{}
			}
			lapp.GetPersonalDetails().Address = addressLse.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails()

			err = p.loanApplicantDao.Update(ctx, lapp, []palPb.LoanApplicantFieldMask{
				palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS,
				palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_EMPLOYMENT_DETAILS,
			})
			if err != nil {
				return
			}
		})

		if err != nil {
			lg.Error("error in updating loan applicant", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating loan applicant: %v", err.Error()))
		}

		return res, nil
	})
	return actRes, actErr
}

func getApplicantLpFromLseLp(lseLp palPb.LoanProgram) palPb.LoanProgram {
	switch lseLp {
	case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
		return palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
	case palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		return palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
	default:
		return lseLp
	}
}
