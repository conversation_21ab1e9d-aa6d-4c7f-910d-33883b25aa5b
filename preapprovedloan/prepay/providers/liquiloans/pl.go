// nolint:dupl
package liquiloans

import (
	"context"

	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/prepay/providers"
)

type LiquiloansPL struct {
	prePayConf *common.Prepay
	dlProvider provider.IDeeplinkProvider
	*Liquiloans
}

var _ providers.ILenderPrepay = &LiquiloansPL{}

func NewLiquiloansPL(prePayConf *common.Prepay, dlProvider provider.IDeeplinkProvider, liquiloans *Liquiloans) *LiquiloansPL {
	return &LiquiloansPL{prePayConf: prePayConf, dlProvider: dlProvider, Liquiloans: liquiloans}
}

func (l *LiquiloansPL) GetOrderRequestForPrePayment(ctx context.Context, req *providers.GetOrderRequestForPrePaymentRequest) (*providers.GetOrderRequestForPrePaymentResponse, error) {
	return l.Liquiloans.GetOrderRequestForPrePayment(ctx, req.OrchId, req.ActorId, l.prePayConf.PoolAccounts.LLActorId,
		l.prePayConf.PoolAccounts.LLPersonalLoanPoolAccountPI, l.dlProvider.GetLoanDashboardScreenDeepLink(l.dlProvider.GetLoanHeader()),
		req.Amount, nil, payment.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED)
}

func (l *LiquiloansPL) ReconPrePaymentAtLender(ctx context.Context, req *providers.ReconPrepaymentAtLenderRequest) (*providers.ReconPrepaymentAtLenderResponse, error) {
	return l.Liquiloans.ReconPrePaymentAtLender(ctx, req)
}

func (l *LiquiloansPL) GetPayeeInfo() (*providers.PayeeInfo, error) {
	return &providers.PayeeInfo{
		PiId:    l.prePayConf.PoolAccounts.LLPersonalLoanPoolAccountPI,
		ActorId: l.prePayConf.PoolAccounts.LLActorId,
	}, nil
}
