// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/order/actoractivity/service.proto

package actoractivity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	enums "github.com/epifi/gamma/api/order/actoractivity/enums"

	payment "github.com/epifi/gamma/api/order/payment"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = enums.ActivitySource(0)

	_ = payment.PaymentProtocol(0)
)

// Validate checks the field values on GetActivitiesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActivitiesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActivitiesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActivitiesRequestMultiError, or nil if none found.
func (m *GetActivitiesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivitiesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentActorId

	for idx, item := range m.GetAccountFilter() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetActivitiesRequestValidationError{
						field:  fmt.Sprintf("AccountFilter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetActivitiesRequestValidationError{
						field:  fmt.Sprintf("AccountFilter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetActivitiesRequestValidationError{
					field:  fmt.Sprintf("AccountFilter[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetActivitiesStartTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesRequestValidationError{
					field:  "ActivitiesStartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesRequestValidationError{
					field:  "ActivitiesStartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivitiesStartTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesRequestValidationError{
				field:  "ActivitiesStartTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 10 || val > 40 {
		err := GetActivitiesRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [10, 40]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ActivityOffset

	// no validation rules for Descending

	// no validation rules for OrderOffset

	// no validation rules for AaTxnOffset

	if all {
		switch v := interface{}(m.GetActivitiesEndTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesRequestValidationError{
					field:  "ActivitiesEndTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesRequestValidationError{
					field:  "ActivitiesEndTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivitiesEndTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesRequestValidationError{
				field:  "ActivitiesEndTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaymentFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesRequestValidationError{
					field:  "PaymentFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesRequestValidationError{
					field:  "PaymentFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesRequestValidationError{
				field:  "PaymentFilter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageLandingTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesRequestValidationError{
					field:  "PageLandingTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesRequestValidationError{
					field:  "PageLandingTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageLandingTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesRequestValidationError{
				field:  "PageLandingTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntryPointType

	// no validation rules for FetchSoftDeletedUsers

	if len(errors) > 0 {
		return GetActivitiesRequestMultiError(errors)
	}

	return nil
}

// GetActivitiesRequestMultiError is an error wrapping multiple validation
// errors returned by GetActivitiesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetActivitiesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivitiesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivitiesRequestMultiError) AllErrors() []error { return m }

// GetActivitiesRequestValidationError is the validation error returned by
// GetActivitiesRequest.Validate if the designated constraints aren't met.
type GetActivitiesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivitiesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActivitiesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActivitiesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActivitiesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActivitiesRequestValidationError) ErrorName() string {
	return "GetActivitiesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivitiesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivitiesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivitiesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivitiesRequestValidationError{}

// Validate checks the field values on GetActivitiesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActivitiesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActivitiesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActivitiesResponseMultiError, or nil if none found.
func (m *GetActivitiesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivitiesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActivities() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetActivitiesResponseValidationError{
						field:  fmt.Sprintf("Activities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetActivitiesResponseValidationError{
						field:  fmt.Sprintf("Activities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetActivitiesResponseValidationError{
					field:  fmt.Sprintf("Activities[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetActivitiesResponseMultiError(errors)
	}

	return nil
}

// GetActivitiesResponseMultiError is an error wrapping multiple validation
// errors returned by GetActivitiesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetActivitiesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivitiesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivitiesResponseMultiError) AllErrors() []error { return m }

// GetActivitiesResponseValidationError is the validation error returned by
// GetActivitiesResponse.Validate if the designated constraints aren't met.
type GetActivitiesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivitiesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActivitiesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActivitiesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActivitiesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActivitiesResponseValidationError) ErrorName() string {
	return "GetActivitiesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivitiesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivitiesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivitiesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivitiesResponseValidationError{}

// Validate checks the field values on GetFinancialActivitiesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFinancialActivitiesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFinancialActivitiesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFinancialActivitiesRequestMultiError, or nil if none found.
func (m *GetFinancialActivitiesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFinancialActivitiesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentActorId

	if all {
		switch v := interface{}(m.GetActivitiesStartTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequestValidationError{
					field:  "ActivitiesStartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequestValidationError{
					field:  "ActivitiesStartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivitiesStartTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFinancialActivitiesRequestValidationError{
				field:  "ActivitiesStartTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActivitiesEndTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequestValidationError{
					field:  "ActivitiesEndTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequestValidationError{
					field:  "ActivitiesEndTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivitiesEndTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFinancialActivitiesRequestValidationError{
				field:  "ActivitiesEndTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 10 || val > 40 {
		err := GetFinancialActivitiesRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [10, 40]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Descending

	// no validation rules for ActivitySource

	if all {
		switch v := interface{}(m.GetActivityOffset()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequestValidationError{
					field:  "ActivityOffset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequestValidationError{
					field:  "ActivityOffset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivityOffset()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFinancialActivitiesRequestValidationError{
				field:  "ActivityOffset",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaymentFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequestValidationError{
					field:  "PaymentFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequestValidationError{
					field:  "PaymentFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFinancialActivitiesRequestValidationError{
				field:  "PaymentFilter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFinancialActivitiesRequestMultiError(errors)
	}

	return nil
}

// GetFinancialActivitiesRequestMultiError is an error wrapping multiple
// validation errors returned by GetFinancialActivitiesRequest.ValidateAll()
// if the designated constraints aren't met.
type GetFinancialActivitiesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFinancialActivitiesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFinancialActivitiesRequestMultiError) AllErrors() []error { return m }

// GetFinancialActivitiesRequestValidationError is the validation error
// returned by GetFinancialActivitiesRequest.Validate if the designated
// constraints aren't met.
type GetFinancialActivitiesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFinancialActivitiesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFinancialActivitiesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFinancialActivitiesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFinancialActivitiesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFinancialActivitiesRequestValidationError) ErrorName() string {
	return "GetFinancialActivitiesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFinancialActivitiesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFinancialActivitiesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFinancialActivitiesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFinancialActivitiesRequestValidationError{}

// Validate checks the field values on GetFinancialActivitiesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFinancialActivitiesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFinancialActivitiesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFinancialActivitiesResponseMultiError, or nil if none found.
func (m *GetFinancialActivitiesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFinancialActivitiesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFinancialActivitiesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFinancialActivitiesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFinancialActivitiesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActivities() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFinancialActivitiesResponseValidationError{
						field:  fmt.Sprintf("Activities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFinancialActivitiesResponseValidationError{
						field:  fmt.Sprintf("Activities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFinancialActivitiesResponseValidationError{
					field:  fmt.Sprintf("Activities[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFinancialActivitiesResponseMultiError(errors)
	}

	return nil
}

// GetFinancialActivitiesResponseMultiError is an error wrapping multiple
// validation errors returned by GetFinancialActivitiesResponse.ValidateAll()
// if the designated constraints aren't met.
type GetFinancialActivitiesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFinancialActivitiesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFinancialActivitiesResponseMultiError) AllErrors() []error { return m }

// GetFinancialActivitiesResponseValidationError is the validation error
// returned by GetFinancialActivitiesResponse.Validate if the designated
// constraints aren't met.
type GetFinancialActivitiesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFinancialActivitiesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFinancialActivitiesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFinancialActivitiesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFinancialActivitiesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFinancialActivitiesResponseValidationError) ErrorName() string {
	return "GetFinancialActivitiesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFinancialActivitiesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFinancialActivitiesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFinancialActivitiesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFinancialActivitiesResponseValidationError{}

// Validate checks the field values on GetActivitiesRequest_AccountFilter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetActivitiesRequest_AccountFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActivitiesRequest_AccountFilter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetActivitiesRequest_AccountFilterMultiError, or nil if none found.
func (m *GetActivitiesRequest_AccountFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivitiesRequest_AccountFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for AccountType

	if len(errors) > 0 {
		return GetActivitiesRequest_AccountFilterMultiError(errors)
	}

	return nil
}

// GetActivitiesRequest_AccountFilterMultiError is an error wrapping multiple
// validation errors returned by
// GetActivitiesRequest_AccountFilter.ValidateAll() if the designated
// constraints aren't met.
type GetActivitiesRequest_AccountFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivitiesRequest_AccountFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivitiesRequest_AccountFilterMultiError) AllErrors() []error { return m }

// GetActivitiesRequest_AccountFilterValidationError is the validation error
// returned by GetActivitiesRequest_AccountFilter.Validate if the designated
// constraints aren't met.
type GetActivitiesRequest_AccountFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivitiesRequest_AccountFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActivitiesRequest_AccountFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActivitiesRequest_AccountFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActivitiesRequest_AccountFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActivitiesRequest_AccountFilterValidationError) ErrorName() string {
	return "GetActivitiesRequest_AccountFilterValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivitiesRequest_AccountFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivitiesRequest_AccountFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivitiesRequest_AccountFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivitiesRequest_AccountFilterValidationError{}

// Validate checks the field values on GetActivitiesRequest_PaymentFilter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetActivitiesRequest_PaymentFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActivitiesRequest_PaymentFilter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetActivitiesRequest_PaymentFilterMultiError, or nil if none found.
func (m *GetActivitiesRequest_PaymentFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivitiesRequest_PaymentFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFromAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesRequest_PaymentFilterValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesRequest_PaymentFilterValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesRequest_PaymentFilterValidationError{
				field:  "FromAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesRequest_PaymentFilterValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesRequest_PaymentFilterValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesRequest_PaymentFilterValidationError{
				field:  "ToAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionType

	if len(errors) > 0 {
		return GetActivitiesRequest_PaymentFilterMultiError(errors)
	}

	return nil
}

// GetActivitiesRequest_PaymentFilterMultiError is an error wrapping multiple
// validation errors returned by
// GetActivitiesRequest_PaymentFilter.ValidateAll() if the designated
// constraints aren't met.
type GetActivitiesRequest_PaymentFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivitiesRequest_PaymentFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivitiesRequest_PaymentFilterMultiError) AllErrors() []error { return m }

// GetActivitiesRequest_PaymentFilterValidationError is the validation error
// returned by GetActivitiesRequest_PaymentFilter.Validate if the designated
// constraints aren't met.
type GetActivitiesRequest_PaymentFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivitiesRequest_PaymentFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActivitiesRequest_PaymentFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActivitiesRequest_PaymentFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActivitiesRequest_PaymentFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActivitiesRequest_PaymentFilterValidationError) ErrorName() string {
	return "GetActivitiesRequest_PaymentFilterValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivitiesRequest_PaymentFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivitiesRequest_PaymentFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivitiesRequest_PaymentFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivitiesRequest_PaymentFilterValidationError{}

// Validate checks the field values on GetActivitiesResponse_Activity with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActivitiesResponse_Activity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActivitiesResponse_Activity with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetActivitiesResponse_ActivityMultiError, or nil if none found.
func (m *GetActivitiesResponse_Activity) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivitiesResponse_Activity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconUrl

	// no validation rules for Title

	// no validation rules for ShortDesc

	// no validation rules for ShortDescIconUrl

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesResponse_ActivityValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesResponse_ActivityValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesResponse_ActivityValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AmountBadge

	if all {
		switch v := interface{}(m.GetActivityTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesResponse_ActivityValidationError{
					field:  "ActivityTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesResponse_ActivityValidationError{
					field:  "ActivityTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivityTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesResponse_ActivityValidationError{
				field:  "ActivityTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActivityId

	// no validation rules for SecondActorId

	// no validation rules for ActivityType

	// no validation rules for ActivityEntryPoint

	if all {
		switch v := interface{}(m.GetPartnerTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivitiesResponse_ActivityValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivitiesResponse_ActivityValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPartnerTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivitiesResponse_ActivityValidationError{
				field:  "PartnerTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.DeeplinkIdentifier.(type) {
	case *GetActivitiesResponse_Activity_TimelineId:
		if v == nil {
			err := GetActivitiesResponse_ActivityValidationError{
				field:  "DeeplinkIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for TimelineId
	case *GetActivitiesResponse_Activity_DepositAccountIdentifier_:
		if v == nil {
			err := GetActivitiesResponse_ActivityValidationError{
				field:  "DeeplinkIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDepositAccountIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetActivitiesResponse_ActivityValidationError{
						field:  "DepositAccountIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetActivitiesResponse_ActivityValidationError{
						field:  "DepositAccountIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDepositAccountIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetActivitiesResponse_ActivityValidationError{
					field:  "DepositAccountIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetActivitiesResponse_ActivityMultiError(errors)
	}

	return nil
}

// GetActivitiesResponse_ActivityMultiError is an error wrapping multiple
// validation errors returned by GetActivitiesResponse_Activity.ValidateAll()
// if the designated constraints aren't met.
type GetActivitiesResponse_ActivityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivitiesResponse_ActivityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivitiesResponse_ActivityMultiError) AllErrors() []error { return m }

// GetActivitiesResponse_ActivityValidationError is the validation error
// returned by GetActivitiesResponse_Activity.Validate if the designated
// constraints aren't met.
type GetActivitiesResponse_ActivityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivitiesResponse_ActivityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActivitiesResponse_ActivityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActivitiesResponse_ActivityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActivitiesResponse_ActivityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActivitiesResponse_ActivityValidationError) ErrorName() string {
	return "GetActivitiesResponse_ActivityValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivitiesResponse_ActivityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivitiesResponse_Activity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivitiesResponse_ActivityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivitiesResponse_ActivityValidationError{}

// Validate checks the field values on
// GetActivitiesResponse_Activity_DepositAccountIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActivitiesResponse_Activity_DepositAccountIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetActivitiesResponse_Activity_DepositAccountIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActivitiesResponse_Activity_DepositAccountIdentifierMultiError, or nil
// if none found.
func (m *GetActivitiesResponse_Activity_DepositAccountIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivitiesResponse_Activity_DepositAccountIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for AccountType

	if len(errors) > 0 {
		return GetActivitiesResponse_Activity_DepositAccountIdentifierMultiError(errors)
	}

	return nil
}

// GetActivitiesResponse_Activity_DepositAccountIdentifierMultiError is an
// error wrapping multiple validation errors returned by
// GetActivitiesResponse_Activity_DepositAccountIdentifier.ValidateAll() if
// the designated constraints aren't met.
type GetActivitiesResponse_Activity_DepositAccountIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivitiesResponse_Activity_DepositAccountIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivitiesResponse_Activity_DepositAccountIdentifierMultiError) AllErrors() []error {
	return m
}

// GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError is
// the validation error returned by
// GetActivitiesResponse_Activity_DepositAccountIdentifier.Validate if the
// designated constraints aren't met.
type GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError) ErrorName() string {
	return "GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivitiesResponse_Activity_DepositAccountIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivitiesResponse_Activity_DepositAccountIdentifierValidationError{}

// Validate checks the field values on
// GetFinancialActivitiesRequest_ActivityOffSet with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFinancialActivitiesRequest_ActivityOffSet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFinancialActivitiesRequest_ActivityOffSet with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetFinancialActivitiesRequest_ActivityOffSetMultiError, or nil if none found.
func (m *GetFinancialActivitiesRequest_ActivityOffSet) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFinancialActivitiesRequest_ActivityOffSet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FiTxnOffset

	// no validation rules for ConnectedAccountTxnOffset

	if len(errors) > 0 {
		return GetFinancialActivitiesRequest_ActivityOffSetMultiError(errors)
	}

	return nil
}

// GetFinancialActivitiesRequest_ActivityOffSetMultiError is an error wrapping
// multiple validation errors returned by
// GetFinancialActivitiesRequest_ActivityOffSet.ValidateAll() if the
// designated constraints aren't met.
type GetFinancialActivitiesRequest_ActivityOffSetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFinancialActivitiesRequest_ActivityOffSetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFinancialActivitiesRequest_ActivityOffSetMultiError) AllErrors() []error { return m }

// GetFinancialActivitiesRequest_ActivityOffSetValidationError is the
// validation error returned by
// GetFinancialActivitiesRequest_ActivityOffSet.Validate if the designated
// constraints aren't met.
type GetFinancialActivitiesRequest_ActivityOffSetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFinancialActivitiesRequest_ActivityOffSetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFinancialActivitiesRequest_ActivityOffSetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFinancialActivitiesRequest_ActivityOffSetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFinancialActivitiesRequest_ActivityOffSetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFinancialActivitiesRequest_ActivityOffSetValidationError) ErrorName() string {
	return "GetFinancialActivitiesRequest_ActivityOffSetValidationError"
}

// Error satisfies the builtin error interface
func (e GetFinancialActivitiesRequest_ActivityOffSetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFinancialActivitiesRequest_ActivityOffSet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFinancialActivitiesRequest_ActivityOffSetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFinancialActivitiesRequest_ActivityOffSetValidationError{}

// Validate checks the field values on
// GetFinancialActivitiesRequest_PaymentFilter with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFinancialActivitiesRequest_PaymentFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFinancialActivitiesRequest_PaymentFilter with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetFinancialActivitiesRequest_PaymentFilterMultiError, or nil if none found.
func (m *GetFinancialActivitiesRequest_PaymentFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFinancialActivitiesRequest_PaymentFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFromAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequest_PaymentFilterValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequest_PaymentFilterValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFinancialActivitiesRequest_PaymentFilterValidationError{
				field:  "FromAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequest_PaymentFilterValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequest_PaymentFilterValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFinancialActivitiesRequest_PaymentFilterValidationError{
				field:  "ToAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionType

	if all {
		switch v := interface{}(m.GetPiFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequest_PaymentFilterValidationError{
					field:  "PiFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFinancialActivitiesRequest_PaymentFilterValidationError{
					field:  "PiFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPiFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFinancialActivitiesRequest_PaymentFilterValidationError{
				field:  "PiFilter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFinancialActivitiesRequest_PaymentFilterMultiError(errors)
	}

	return nil
}

// GetFinancialActivitiesRequest_PaymentFilterMultiError is an error wrapping
// multiple validation errors returned by
// GetFinancialActivitiesRequest_PaymentFilter.ValidateAll() if the designated
// constraints aren't met.
type GetFinancialActivitiesRequest_PaymentFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFinancialActivitiesRequest_PaymentFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFinancialActivitiesRequest_PaymentFilterMultiError) AllErrors() []error { return m }

// GetFinancialActivitiesRequest_PaymentFilterValidationError is the validation
// error returned by GetFinancialActivitiesRequest_PaymentFilter.Validate if
// the designated constraints aren't met.
type GetFinancialActivitiesRequest_PaymentFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFinancialActivitiesRequest_PaymentFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFinancialActivitiesRequest_PaymentFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFinancialActivitiesRequest_PaymentFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFinancialActivitiesRequest_PaymentFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFinancialActivitiesRequest_PaymentFilterValidationError) ErrorName() string {
	return "GetFinancialActivitiesRequest_PaymentFilterValidationError"
}

// Error satisfies the builtin error interface
func (e GetFinancialActivitiesRequest_PaymentFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFinancialActivitiesRequest_PaymentFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFinancialActivitiesRequest_PaymentFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFinancialActivitiesRequest_PaymentFilterValidationError{}

// Validate checks the field values on
// GetFinancialActivitiesRequest_PaymentFilter_PiFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFinancialActivitiesRequest_PaymentFilter_PiFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetFinancialActivitiesRequest_PaymentFilter_PiFilterMultiError, or nil if
// none found.
func (m *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetFinancialActivitiesRequest_PaymentFilter_PiFilterMultiError(errors)
	}

	return nil
}

// GetFinancialActivitiesRequest_PaymentFilter_PiFilterMultiError is an error
// wrapping multiple validation errors returned by
// GetFinancialActivitiesRequest_PaymentFilter_PiFilter.ValidateAll() if the
// designated constraints aren't met.
type GetFinancialActivitiesRequest_PaymentFilter_PiFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFinancialActivitiesRequest_PaymentFilter_PiFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFinancialActivitiesRequest_PaymentFilter_PiFilterMultiError) AllErrors() []error { return m }

// GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError is the
// validation error returned by
// GetFinancialActivitiesRequest_PaymentFilter_PiFilter.Validate if the
// designated constraints aren't met.
type GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError) ErrorName() string {
	return "GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError"
}

// Error satisfies the builtin error interface
func (e GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFinancialActivitiesRequest_PaymentFilter_PiFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFinancialActivitiesRequest_PaymentFilter_PiFilterValidationError{}
