// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/order/cx/service.proto

package cx

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	order "github.com/epifi/gamma/api/order"

	payment "github.com/epifi/gamma/api/order/payment"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = order.OrderStatus(0)

	_ = payment.PaymentProtocol(0)
)

// Validate checks the field values on PageToken with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PageToken) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageToken with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PageTokenMultiError, or nil
// if none found.
func (m *PageToken) ValidateAll() error {
	return m.validate(true)
}

func (m *PageToken) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLastOrderTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageTokenValidationError{
					field:  "LastOrderTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageTokenValidationError{
					field:  "LastOrderTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastOrderTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageTokenValidationError{
				field:  "LastOrderTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderOffset

	if len(errors) > 0 {
		return PageTokenMultiError(errors)
	}

	return nil
}

// PageTokenMultiError is an error wrapping multiple validation errors returned
// by PageToken.ValidateAll() if the designated constraints aren't met.
type PageTokenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageTokenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageTokenMultiError) AllErrors() []error { return m }

// PageTokenValidationError is the validation error returned by
// PageToken.Validate if the designated constraints aren't met.
type PageTokenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageTokenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageTokenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageTokenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageTokenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageTokenValidationError) ErrorName() string { return "PageTokenValidationError" }

// Error satisfies the builtin error interface
func (e PageTokenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageToken.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageTokenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageTokenValidationError{}

// Validate checks the field values on GetOrdersForActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrdersForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrdersForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrdersForActorRequestMultiError, or nil if none found.
func (m *GetOrdersForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrdersForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransactionId

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetOrdersForActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFromDate() == nil {
		err := GetOrdersForActorRequestValidationError{
			field:  "FromDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetToDate() == nil {
		err := GetOrdersForActorRequestValidationError{
			field:  "ToDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorRequestValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFromAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorRequestValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorRequestValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorRequestValidationError{
				field:  "FromAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorRequestValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorRequestValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorRequestValidationError{
				field:  "ToAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if val := m.GetPageSize(); val < 0 || val >= 30 {
		err := GetOrdersForActorRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [0, 30)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Token.(type) {
	case *GetOrdersForActorRequest_BeforePageToken:
		if v == nil {
			err := GetOrdersForActorRequestValidationError{
				field:  "Token",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBeforePageToken()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrdersForActorRequestValidationError{
						field:  "BeforePageToken",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrdersForActorRequestValidationError{
						field:  "BeforePageToken",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBeforePageToken()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrdersForActorRequestValidationError{
					field:  "BeforePageToken",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetOrdersForActorRequest_AfterPageToken:
		if v == nil {
			err := GetOrdersForActorRequestValidationError{
				field:  "Token",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAfterPageToken()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrdersForActorRequestValidationError{
						field:  "AfterPageToken",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrdersForActorRequestValidationError{
						field:  "AfterPageToken",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAfterPageToken()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrdersForActorRequestValidationError{
					field:  "AfterPageToken",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.UtrFilter.(type) {
	case *GetOrdersForActorRequest_FullUtr:
		if v == nil {
			err := GetOrdersForActorRequestValidationError{
				field:  "UtrFilter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for FullUtr
	case *GetOrdersForActorRequest_PartialUtr:
		if v == nil {
			err := GetOrdersForActorRequestValidationError{
				field:  "UtrFilter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PartialUtr
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetOrdersForActorRequestMultiError(errors)
	}

	return nil
}

// GetOrdersForActorRequestMultiError is an error wrapping multiple validation
// errors returned by GetOrdersForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrdersForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrdersForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrdersForActorRequestMultiError) AllErrors() []error { return m }

// GetOrdersForActorRequestValidationError is the validation error returned by
// GetOrdersForActorRequest.Validate if the designated constraints aren't met.
type GetOrdersForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrdersForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrdersForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrdersForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrdersForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrdersForActorRequestValidationError) ErrorName() string {
	return "GetOrdersForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrdersForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrdersForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrdersForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrdersForActorRequestValidationError{}

// Validate checks the field values on GetOrdersForActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrdersForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrdersForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrdersForActorResponseMultiError, or nil if none found.
func (m *GetOrdersForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrdersForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetResults() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrdersForActorResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrdersForActorResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrdersForActorResponseValidationError{
					field:  fmt.Sprintf("Results[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBeforePageToken()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorResponseValidationError{
					field:  "BeforePageToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorResponseValidationError{
					field:  "BeforePageToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBeforePageToken()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorResponseValidationError{
				field:  "BeforePageToken",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAfterPageToken()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorResponseValidationError{
					field:  "AfterPageToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorResponseValidationError{
					field:  "AfterPageToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAfterPageToken()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorResponseValidationError{
				field:  "AfterPageToken",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrdersForActorResponseMultiError(errors)
	}

	return nil
}

// GetOrdersForActorResponseMultiError is an error wrapping multiple validation
// errors returned by GetOrdersForActorResponse.ValidateAll() if the
// designated constraints aren't met.
type GetOrdersForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrdersForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrdersForActorResponseMultiError) AllErrors() []error { return m }

// GetOrdersForActorResponseValidationError is the validation error returned by
// GetOrdersForActorResponse.Validate if the designated constraints aren't met.
type GetOrdersForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrdersForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrdersForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrdersForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrdersForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrdersForActorResponseValidationError) ErrorName() string {
	return "GetOrdersForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrdersForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrdersForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrdersForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrdersForActorResponseValidationError{}

// Validate checks the field values on GetOrderWithTransactionByUtrRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOrderWithTransactionByUtrRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderWithTransactionByUtrRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOrderWithTransactionByUtrRequestMultiError, or nil if none found.
func (m *GetOrderWithTransactionByUtrRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderWithTransactionByUtrRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetUtr()) < 1 {
		err := GetOrderWithTransactionByUtrRequestValidationError{
			field:  "Utr",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetOrderWithTransactionByUtrRequestMultiError(errors)
	}

	return nil
}

// GetOrderWithTransactionByUtrRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetOrderWithTransactionByUtrRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrderWithTransactionByUtrRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderWithTransactionByUtrRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderWithTransactionByUtrRequestMultiError) AllErrors() []error { return m }

// GetOrderWithTransactionByUtrRequestValidationError is the validation error
// returned by GetOrderWithTransactionByUtrRequest.Validate if the designated
// constraints aren't met.
type GetOrderWithTransactionByUtrRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderWithTransactionByUtrRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderWithTransactionByUtrRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderWithTransactionByUtrRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderWithTransactionByUtrRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderWithTransactionByUtrRequestValidationError) ErrorName() string {
	return "GetOrderWithTransactionByUtrRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderWithTransactionByUtrRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderWithTransactionByUtrRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderWithTransactionByUtrRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderWithTransactionByUtrRequestValidationError{}

// Validate checks the field values on GetOrderWithTransactionByUtrResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetOrderWithTransactionByUtrResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderWithTransactionByUtrResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOrderWithTransactionByUtrResponseMultiError, or nil if none found.
func (m *GetOrderWithTransactionByUtrResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderWithTransactionByUtrResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderWithTransactionByUtrResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderWithTransactionByUtrResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderWithTransactionByUtrResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderWithTxn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderWithTransactionByUtrResponseValidationError{
					field:  "OrderWithTxn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderWithTransactionByUtrResponseValidationError{
					field:  "OrderWithTxn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderWithTxn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderWithTransactionByUtrResponseValidationError{
				field:  "OrderWithTxn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOrdersWithTxnList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderWithTransactionByUtrResponseValidationError{
						field:  fmt.Sprintf("OrdersWithTxnList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderWithTransactionByUtrResponseValidationError{
						field:  fmt.Sprintf("OrdersWithTxnList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderWithTransactionByUtrResponseValidationError{
					field:  fmt.Sprintf("OrdersWithTxnList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetOrderWithTransactionByUtrResponseMultiError(errors)
	}

	return nil
}

// GetOrderWithTransactionByUtrResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetOrderWithTransactionByUtrResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOrderWithTransactionByUtrResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderWithTransactionByUtrResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderWithTransactionByUtrResponseMultiError) AllErrors() []error { return m }

// GetOrderWithTransactionByUtrResponseValidationError is the validation error
// returned by GetOrderWithTransactionByUtrResponse.Validate if the designated
// constraints aren't met.
type GetOrderWithTransactionByUtrResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderWithTransactionByUtrResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderWithTransactionByUtrResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderWithTransactionByUtrResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderWithTransactionByUtrResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderWithTransactionByUtrResponseValidationError) ErrorName() string {
	return "GetOrderWithTransactionByUtrResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderWithTransactionByUtrResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderWithTransactionByUtrResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderWithTransactionByUtrResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderWithTransactionByUtrResponseValidationError{}

// Validate checks the field values on GetOrdersForActorResponse_Result with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOrdersForActorResponse_Result) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrdersForActorResponse_Result with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetOrdersForActorResponse_ResultMultiError, or nil if none found.
func (m *GetOrdersForActorResponse_Result) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrdersForActorResponse_Result) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorResponse_ResultValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorResponse_ResultValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorResponse_ResultValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FromActorId

	// no validation rules for ToActorId

	// no validation rules for PiFrom

	// no validation rules for PiTo

	// no validation rules for OrderId

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorResponse_ResultValidationError{
					field:  "DetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorResponse_ResultValidationError{
					field:  "DetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorResponse_ResultValidationError{
				field:  "DetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersForActorResponse_ResultValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersForActorResponse_ResultValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersForActorResponse_ResultValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provenance

	// no validation rules for Workflow

	// no validation rules for PaymentProtocol

	// no validation rules for ExternalOrderId

	// no validation rules for RecurringPaymentId

	if len(errors) > 0 {
		return GetOrdersForActorResponse_ResultMultiError(errors)
	}

	return nil
}

// GetOrdersForActorResponse_ResultMultiError is an error wrapping multiple
// validation errors returned by
// GetOrdersForActorResponse_Result.ValidateAll() if the designated
// constraints aren't met.
type GetOrdersForActorResponse_ResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrdersForActorResponse_ResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrdersForActorResponse_ResultMultiError) AllErrors() []error { return m }

// GetOrdersForActorResponse_ResultValidationError is the validation error
// returned by GetOrdersForActorResponse_Result.Validate if the designated
// constraints aren't met.
type GetOrdersForActorResponse_ResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrdersForActorResponse_ResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrdersForActorResponse_ResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrdersForActorResponse_ResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrdersForActorResponse_ResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrdersForActorResponse_ResultValidationError) ErrorName() string {
	return "GetOrdersForActorResponse_ResultValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrdersForActorResponse_ResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrdersForActorResponse_Result.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrdersForActorResponse_ResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrdersForActorResponse_ResultValidationError{}

// Validate checks the field values on
// GetOrdersForActorResponse_Result_DetailedStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetOrdersForActorResponse_Result_DetailedStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetOrdersForActorResponse_Result_DetailedStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetOrdersForActorResponse_Result_DetailedStatusMultiError, or nil if none found.
func (m *GetOrdersForActorResponse_Result_DetailedStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrdersForActorResponse_Result_DetailedStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Description

	if len(errors) > 0 {
		return GetOrdersForActorResponse_Result_DetailedStatusMultiError(errors)
	}

	return nil
}

// GetOrdersForActorResponse_Result_DetailedStatusMultiError is an error
// wrapping multiple validation errors returned by
// GetOrdersForActorResponse_Result_DetailedStatus.ValidateAll() if the
// designated constraints aren't met.
type GetOrdersForActorResponse_Result_DetailedStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrdersForActorResponse_Result_DetailedStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrdersForActorResponse_Result_DetailedStatusMultiError) AllErrors() []error { return m }

// GetOrdersForActorResponse_Result_DetailedStatusValidationError is the
// validation error returned by
// GetOrdersForActorResponse_Result_DetailedStatus.Validate if the designated
// constraints aren't met.
type GetOrdersForActorResponse_Result_DetailedStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrdersForActorResponse_Result_DetailedStatusValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetOrdersForActorResponse_Result_DetailedStatusValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetOrdersForActorResponse_Result_DetailedStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrdersForActorResponse_Result_DetailedStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrdersForActorResponse_Result_DetailedStatusValidationError) ErrorName() string {
	return "GetOrdersForActorResponse_Result_DetailedStatusValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrdersForActorResponse_Result_DetailedStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrdersForActorResponse_Result_DetailedStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrdersForActorResponse_Result_DetailedStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrdersForActorResponse_Result_DetailedStatusValidationError{}
