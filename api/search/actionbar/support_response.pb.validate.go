// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/search/action_bar/support_response.proto

package actionbar

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SupportResult with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SupportResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SupportResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SupportResultMultiError, or
// nil if none found.
func (m *SupportResult) ValidateAll() error {
	return m.validate(true)
}

func (m *SupportResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for ContentUrl

	// no validation rules for ImgUrl

	// no validation rules for ContentBlock

	// no validation rules for ArticleId

	// no validation rules for FolderName

	// no validation rules for CategoryId

	if all {
		switch v := interface{}(m.GetDebugInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SupportResultValidationError{
					field:  "DebugInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SupportResultValidationError{
					field:  "DebugInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDebugInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SupportResultValidationError{
				field:  "DebugInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SupportResultMultiError(errors)
	}

	return nil
}

// SupportResultMultiError is an error wrapping multiple validation errors
// returned by SupportResult.ValidateAll() if the designated constraints
// aren't met.
type SupportResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SupportResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SupportResultMultiError) AllErrors() []error { return m }

// SupportResultValidationError is the validation error returned by
// SupportResult.Validate if the designated constraints aren't met.
type SupportResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SupportResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SupportResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SupportResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SupportResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SupportResultValidationError) ErrorName() string { return "SupportResultValidationError" }

// Error satisfies the builtin error interface
func (e SupportResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSupportResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SupportResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SupportResultValidationError{}

// Validate checks the field values on SupportResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SupportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SupportResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SupportResponseMultiError, or nil if none found.
func (m *SupportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SupportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetResult() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SupportResponseValidationError{
						field:  fmt.Sprintf("Result[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SupportResponseValidationError{
						field:  fmt.Sprintf("Result[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SupportResponseValidationError{
					field:  fmt.Sprintf("Result[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SupportResponseMultiError(errors)
	}

	return nil
}

// SupportResponseMultiError is an error wrapping multiple validation errors
// returned by SupportResponse.ValidateAll() if the designated constraints
// aren't met.
type SupportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SupportResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SupportResponseMultiError) AllErrors() []error { return m }

// SupportResponseValidationError is the validation error returned by
// SupportResponse.Validate if the designated constraints aren't met.
type SupportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SupportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SupportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SupportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SupportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SupportResponseValidationError) ErrorName() string { return "SupportResponseValidationError" }

// Error satisfies the builtin error interface
func (e SupportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSupportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SupportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SupportResponseValidationError{}
