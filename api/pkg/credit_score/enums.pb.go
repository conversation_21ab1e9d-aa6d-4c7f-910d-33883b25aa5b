// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/pkg/credit_score/enums.proto

package credit_score

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreditScoreParameter int32

const (
	CreditScoreParameter_CREDIT_SCORE_PARAMETER_UNSPECIFIED        CreditScoreParameter = 0
	CreditScoreParameter_CREDIT_SCORE_PARAMETER_ON_TIME_PAYMENTS   CreditScoreParameter = 1
	CreditScoreParameter_CREDIT_SCORE_PARAMETER_AVAILABLE_CREDITS  CreditScoreParameter = 2
	CreditScoreParameter_CREDIT_SCORE_PARAMETER_CREDIT_AGE         CreditScoreParameter = 3
	CreditScoreParameter_CREDIT_SCORE_PARAMETER_ACTIVE_ACCOUNTS    CreditScoreParameter = 4
	CreditScoreParameter_CREDIT_SCORE_PARAMETER_DETAIL_CONSISTENCY CreditScoreParameter = 5
	CreditScoreParameter_CREDIT_SCORE_PARAMETER_RECENT_ENQUIRIES   CreditScoreParameter = 6
)

// Enum value maps for CreditScoreParameter.
var (
	CreditScoreParameter_name = map[int32]string{
		0: "CREDIT_SCORE_PARAMETER_UNSPECIFIED",
		1: "CREDIT_SCORE_PARAMETER_ON_TIME_PAYMENTS",
		2: "CREDIT_SCORE_PARAMETER_AVAILABLE_CREDITS",
		3: "CREDIT_SCORE_PARAMETER_CREDIT_AGE",
		4: "CREDIT_SCORE_PARAMETER_ACTIVE_ACCOUNTS",
		5: "CREDIT_SCORE_PARAMETER_DETAIL_CONSISTENCY",
		6: "CREDIT_SCORE_PARAMETER_RECENT_ENQUIRIES",
	}
	CreditScoreParameter_value = map[string]int32{
		"CREDIT_SCORE_PARAMETER_UNSPECIFIED":        0,
		"CREDIT_SCORE_PARAMETER_ON_TIME_PAYMENTS":   1,
		"CREDIT_SCORE_PARAMETER_AVAILABLE_CREDITS":  2,
		"CREDIT_SCORE_PARAMETER_CREDIT_AGE":         3,
		"CREDIT_SCORE_PARAMETER_ACTIVE_ACCOUNTS":    4,
		"CREDIT_SCORE_PARAMETER_DETAIL_CONSISTENCY": 5,
		"CREDIT_SCORE_PARAMETER_RECENT_ENQUIRIES":   6,
	}
)

func (x CreditScoreParameter) Enum() *CreditScoreParameter {
	p := new(CreditScoreParameter)
	*p = x
	return p
}

func (x CreditScoreParameter) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditScoreParameter) Descriptor() protoreflect.EnumDescriptor {
	return file_api_pkg_credit_score_enums_proto_enumTypes[0].Descriptor()
}

func (CreditScoreParameter) Type() protoreflect.EnumType {
	return &file_api_pkg_credit_score_enums_proto_enumTypes[0]
}

func (x CreditScoreParameter) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditScoreParameter.Descriptor instead.
func (CreditScoreParameter) EnumDescriptor() ([]byte, []int) {
	return file_api_pkg_credit_score_enums_proto_rawDescGZIP(), []int{0}
}

var File_api_pkg_credit_score_enums_proto protoreflect.FileDescriptor

var file_api_pkg_credit_score_enums_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x10, 0x70, 0x6b, 0x67, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x2a, 0xc8, 0x02, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x26, 0x0a,
	0x22, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x50, 0x41,
	0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f,
	0x4f, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53,
	0x10, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x41, 0x56, 0x41,
	0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x53, 0x10, 0x02,
	0x12, 0x25, 0x0a, 0x21, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x41, 0x47, 0x45, 0x10, 0x03, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45,
	0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x53, 0x10, 0x04, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43,
	0x4f, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x49, 0x53, 0x54, 0x45, 0x4e, 0x43, 0x59,
	0x10, 0x05, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43,
	0x45, 0x4e, 0x54, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x49, 0x45, 0x53, 0x10, 0x06, 0x42,
	0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6b,
	0x67, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5a, 0x2b,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_pkg_credit_score_enums_proto_rawDescOnce sync.Once
	file_api_pkg_credit_score_enums_proto_rawDescData = file_api_pkg_credit_score_enums_proto_rawDesc
)

func file_api_pkg_credit_score_enums_proto_rawDescGZIP() []byte {
	file_api_pkg_credit_score_enums_proto_rawDescOnce.Do(func() {
		file_api_pkg_credit_score_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_pkg_credit_score_enums_proto_rawDescData)
	})
	return file_api_pkg_credit_score_enums_proto_rawDescData
}

var file_api_pkg_credit_score_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_pkg_credit_score_enums_proto_goTypes = []interface{}{
	(CreditScoreParameter)(0), // 0: pkg.credit_score.CreditScoreParameter
}
var file_api_pkg_credit_score_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_pkg_credit_score_enums_proto_init() }
func file_api_pkg_credit_score_enums_proto_init() {
	if File_api_pkg_credit_score_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_pkg_credit_score_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_pkg_credit_score_enums_proto_goTypes,
		DependencyIndexes: file_api_pkg_credit_score_enums_proto_depIdxs,
		EnumInfos:         file_api_pkg_credit_score_enums_proto_enumTypes,
	}.Build()
	File_api_pkg_credit_score_enums_proto = out.File
	file_api_pkg_credit_score_enums_proto_rawDesc = nil
	file_api_pkg_credit_score_enums_proto_goTypes = nil
	file_api_pkg_credit_score_enums_proto_depIdxs = nil
}
