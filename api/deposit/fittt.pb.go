// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/deposit/fittt.proto

package deposit

import (
	manager "github.com/epifi/gamma/api/rms/manager"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DepositAutoSaveParams contains the auto save details if the user opted for setting up auto save rules while creating the deposit account.
type DepositAutoSaveParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleId string `protobuf:"bytes,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	// rule param values of the auto save suggestion
	RuleParamValues *manager.RuleParamValues `protobuf:"bytes,2,opt,name=rule_param_values,json=ruleParamValues,proto3" json:"rule_param_values,omitempty"`
}

func (x *DepositAutoSaveParams) Reset() {
	*x = DepositAutoSaveParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_deposit_fittt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositAutoSaveParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositAutoSaveParams) ProtoMessage() {}

func (x *DepositAutoSaveParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_deposit_fittt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositAutoSaveParams.ProtoReflect.Descriptor instead.
func (*DepositAutoSaveParams) Descriptor() ([]byte, []int) {
	return file_api_deposit_fittt_proto_rawDescGZIP(), []int{0}
}

func (x *DepositAutoSaveParams) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *DepositAutoSaveParams) GetRuleParamValues() *manager.RuleParamValues {
	if x != nil {
		return x.RuleParamValues
	}
	return nil
}

var File_api_deposit_fittt_proto protoreflect.FileDescriptor

var file_api_deposit_fittt_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2f, 0x66, 0x69,
	0x74, 0x74, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x6d, 0x73, 0x2f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7e,
	0x0a, 0x15, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x61, 0x76,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x4c, 0x0a, 0x11, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x6d, 0x73, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x0f, 0x72,
	0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x48,
	0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_deposit_fittt_proto_rawDescOnce sync.Once
	file_api_deposit_fittt_proto_rawDescData = file_api_deposit_fittt_proto_rawDesc
)

func file_api_deposit_fittt_proto_rawDescGZIP() []byte {
	file_api_deposit_fittt_proto_rawDescOnce.Do(func() {
		file_api_deposit_fittt_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_deposit_fittt_proto_rawDescData)
	})
	return file_api_deposit_fittt_proto_rawDescData
}

var file_api_deposit_fittt_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_deposit_fittt_proto_goTypes = []interface{}{
	(*DepositAutoSaveParams)(nil),   // 0: deposit.DepositAutoSaveParams
	(*manager.RuleParamValues)(nil), // 1: api.rms.manager.RuleParamValues
}
var file_api_deposit_fittt_proto_depIdxs = []int32{
	1, // 0: deposit.DepositAutoSaveParams.rule_param_values:type_name -> api.rms.manager.RuleParamValues
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_deposit_fittt_proto_init() }
func file_api_deposit_fittt_proto_init() {
	if File_api_deposit_fittt_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_deposit_fittt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositAutoSaveParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_deposit_fittt_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_deposit_fittt_proto_goTypes,
		DependencyIndexes: file_api_deposit_fittt_proto_depIdxs,
		MessageInfos:      file_api_deposit_fittt_proto_msgTypes,
	}.Build()
	File_api_deposit_fittt_proto = out.File
	file_api_deposit_fittt_proto_rawDesc = nil
	file_api_deposit_fittt_proto_goTypes = nil
	file_api_deposit_fittt_proto_depIdxs = nil
}
