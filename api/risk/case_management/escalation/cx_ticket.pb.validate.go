// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/escalation/cx_ticket.proto

package escalation

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	ticket "github.com/epifi/gamma/api/cx/ticket"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = ticket.TicketFieldUpdateMask(0)
)

// Validate checks the field values on UpdateCXTicketParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCXTicketParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCXTicketParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCXTicketParamsMultiError, or nil if none found.
func (m *UpdateCXTicketParams) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCXTicketParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTicket() == nil {
		err := UpdateCXTicketParamsValidationError{
			field:  "Ticket",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCXTicketParamsValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCXTicketParamsValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCXTicketParamsValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetUpdateFieldMasks()) < 1 {
		err := UpdateCXTicketParamsValidationError{
			field:  "UpdateFieldMasks",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UpdateSecondaryTickets

	if len(errors) > 0 {
		return UpdateCXTicketParamsMultiError(errors)
	}

	return nil
}

// UpdateCXTicketParamsMultiError is an error wrapping multiple validation
// errors returned by UpdateCXTicketParams.ValidateAll() if the designated
// constraints aren't met.
type UpdateCXTicketParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCXTicketParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCXTicketParamsMultiError) AllErrors() []error { return m }

// UpdateCXTicketParamsValidationError is the validation error returned by
// UpdateCXTicketParams.Validate if the designated constraints aren't met.
type UpdateCXTicketParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCXTicketParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCXTicketParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCXTicketParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCXTicketParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCXTicketParamsValidationError) ErrorName() string {
	return "UpdateCXTicketParamsValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCXTicketParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCXTicketParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCXTicketParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCXTicketParamsValidationError{}
