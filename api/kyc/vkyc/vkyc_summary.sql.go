package vkyc

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing data while reading from DB
func (v *VKYCSummaryStatus) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		err := fmt.Errorf("expected string got %T", src)
		return err
	}
	*v = VKYCSummaryStatus(VKYCSummaryStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the data in JSONB format in DB
func (v VKYCSummaryStatus) Value() (driver.Value, error) {
	return v.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (v *VKYCSummarySubStatus) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		err := fmt.Errorf("expected string got %T", src)
		return err
	}
	*v = VKYCSummarySubStatus(VKYCSummarySubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the data in JSONB format in DB
func (v VKYCSummarySubStatus) Value() (driver.Value, error) {
	return v.String(), nil
}

func (v *SummaryMetadata) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte, got %T", input)
		return err
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, val, v)
}

func (v *SummaryMetadata) Value() (driver.Value, error) {
	if v == nil {
		return nil, nil
	}
	return protojson.Marshal(v)
}
