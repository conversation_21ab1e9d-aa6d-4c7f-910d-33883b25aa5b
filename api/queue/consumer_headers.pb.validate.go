// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/queue/consumer_headers.proto

package queue

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ConsumerResponseHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumerResponseHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumerResponseHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumerResponseHeaderMultiError, or nil if none found.
func (m *ConsumerResponseHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumerResponseHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for NextTimeout

	if all {
		switch v := interface{}(m.GetGrpcStatusCode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumerResponseHeaderValidationError{
					field:  "GrpcStatusCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumerResponseHeaderValidationError{
					field:  "GrpcStatusCode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGrpcStatusCode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumerResponseHeaderValidationError{
				field:  "GrpcStatusCode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumerResponseHeaderMultiError(errors)
	}

	return nil
}

// ConsumerResponseHeaderMultiError is an error wrapping multiple validation
// errors returned by ConsumerResponseHeader.ValidateAll() if the designated
// constraints aren't met.
type ConsumerResponseHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumerResponseHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumerResponseHeaderMultiError) AllErrors() []error { return m }

// ConsumerResponseHeaderValidationError is the validation error returned by
// ConsumerResponseHeader.Validate if the designated constraints aren't met.
type ConsumerResponseHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumerResponseHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumerResponseHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumerResponseHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumerResponseHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumerResponseHeaderValidationError) ErrorName() string {
	return "ConsumerResponseHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumerResponseHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumerResponseHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumerResponseHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumerResponseHeaderValidationError{}

// Validate checks the field values on ConsumerRequestHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumerRequestHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumerRequestHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumerRequestHeaderMultiError, or nil if none found.
func (m *ConsumerRequestHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumerRequestHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsLastAttempt

	if len(errors) > 0 {
		return ConsumerRequestHeaderMultiError(errors)
	}

	return nil
}

// ConsumerRequestHeaderMultiError is an error wrapping multiple validation
// errors returned by ConsumerRequestHeader.ValidateAll() if the designated
// constraints aren't met.
type ConsumerRequestHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumerRequestHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumerRequestHeaderMultiError) AllErrors() []error { return m }

// ConsumerRequestHeaderValidationError is the validation error returned by
// ConsumerRequestHeader.Validate if the designated constraints aren't met.
type ConsumerRequestHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumerRequestHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumerRequestHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumerRequestHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumerRequestHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumerRequestHeaderValidationError) ErrorName() string {
	return "ConsumerRequestHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumerRequestHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumerRequestHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumerRequestHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumerRequestHeaderValidationError{}
