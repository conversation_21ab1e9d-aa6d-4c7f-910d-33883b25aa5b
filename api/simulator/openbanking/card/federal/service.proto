// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package simulator.openbanking.card.federal;

import "api/vendors/federal/card.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/openbanking/card/federal";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.card.federal";

service CardProvisioning {
  rpc CardCreation (vendors.federal.CardCreationRequest) returns (vendors.federal.CardCreationResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/CardCreation"
      body: "*"
    };
  }

  rpc CardEnquiry (vendors.federal.CardEnquiryRequest) returns (vendors.federal.CardEnquiryResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/CardEnqService"
      body: "*"
    };
  }

  rpc CardActivation (vendors.federal.CardActivationRequest) returns (vendors.federal.CardActivationResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/CardActivation"
      body: "*"
    };
  }

  rpc PinManagement (vendors.federal.PinManagementRequest) returns (vendors.federal.PinManagementResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/PinManagement"
      body: "*"
    };
  }

  rpc CardControl (vendors.federal.CardControlRequest) returns (vendors.federal.CardControlResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/CardControl"
      body: "*"
    };
  }

  rpc CardCVVEnquiry (vendors.federal.CardCVVEnquiryRequest) returns (vendors.federal.CardCVVEnquiryResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/CVVEnquiry"
      body: "*"
    };
  }

  rpc CardLimitEnquiry (vendors.federal.CardLimitEnquiryRequest) returns (vendors.federal.CardLimitEnquiryResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/LimitEnquiry"
      body: "*"
    };
  }

  rpc CardLimitUpdate (vendors.federal.CardLimitUpdateRequest) returns (vendors.federal.CardLimitUpdateResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/LimitUpdate"
      body: "*"
    };
  }

  rpc CardDeliveryTracking (vendors.federal.CardDeliveryTrackingRequest) returns (vendors.federal.CardDeliveryTrackingResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/DeliveryTracking"
      body: "*"
    };
  }

  rpc PhysicalCardDispatch (vendors.federal.PhysicalCardDispatchRequest) returns (vendors.federal.PhysicalCardDispatchResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/cardDispatch"
      body: "*"
    };
  }

  // rpc to enable/disable any number of controls in a single go
  // To enable any control we need encrypted pin from the user or we can use pin set token during onboarding
  // Doc with vendor contracts : https://docs.google.com/document/d/1jGgkpzpboUJ5uK1k8z0ar4DpVCq9ILxE/edit
  rpc ConsolidatedCardControl (vendors.federal.ConsolidatedCardControlRequest) returns (vendors.federal.ConsolidatedCardControlResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/neobanking-card/FHMCardONOFF"
      body: "*"
    };
  }

  // RPC to Collect DC issuance fee
  rpc CollectDCIssuanceFee(vendors.federal.CollectDCIssuanceFeeRequest) returns (vendors.federal.CollectDCIssuanceFeeResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcation"
      body: "*"
    };
  }

  // RPC to check the status of the DC issuance fee.
  rpc CheckDCIssuanceFeeStatus(vendors.federal.CheckDCIssuanceFeeStatusRequest) returns (vendors.federal.CheckDCIssuanceFeeStatusResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcationStatus"
      body: "*"
    };
  }

}
