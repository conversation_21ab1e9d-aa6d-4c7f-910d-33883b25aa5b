// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/simulator/profileevaluator/service.proto

package profileevaluator

import (
	rpc "github.com/epifi/be-common/api/rpc"
	federal "github.com/epifi/gamma/api/vendors/federal"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DevActionRequest_ProfileEvaluatorActionType int32

const (
	DevActionRequest_UNSPECIFIED            DevActionRequest_ProfileEvaluatorActionType = 0
	DevActionRequest_CRITICAL_RULE_FAIL     DevActionRequest_ProfileEvaluatorActionType = 1
	DevActionRequest_NON_CRITICAL_RULE_FAIL DevActionRequest_ProfileEvaluatorActionType = 2
	DevActionRequest_PROCEED                DevActionRequest_ProfileEvaluatorActionType = 3
)

// Enum value maps for DevActionRequest_ProfileEvaluatorActionType.
var (
	DevActionRequest_ProfileEvaluatorActionType_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "CRITICAL_RULE_FAIL",
		2: "NON_CRITICAL_RULE_FAIL",
		3: "PROCEED",
	}
	DevActionRequest_ProfileEvaluatorActionType_value = map[string]int32{
		"UNSPECIFIED":            0,
		"CRITICAL_RULE_FAIL":     1,
		"NON_CRITICAL_RULE_FAIL": 2,
		"PROCEED":                3,
	}
)

func (x DevActionRequest_ProfileEvaluatorActionType) Enum() *DevActionRequest_ProfileEvaluatorActionType {
	p := new(DevActionRequest_ProfileEvaluatorActionType)
	*p = x
	return p
}

func (x DevActionRequest_ProfileEvaluatorActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DevActionRequest_ProfileEvaluatorActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_simulator_profileevaluator_service_proto_enumTypes[0].Descriptor()
}

func (DevActionRequest_ProfileEvaluatorActionType) Type() protoreflect.EnumType {
	return &file_api_simulator_profileevaluator_service_proto_enumTypes[0]
}

func (x DevActionRequest_ProfileEvaluatorActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DevActionRequest_ProfileEvaluatorActionType.Descriptor instead.
func (DevActionRequest_ProfileEvaluatorActionType) EnumDescriptor() ([]byte, []int) {
	return file_api_simulator_profileevaluator_service_proto_rawDescGZIP(), []int{0, 0}
}

type DevActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                        string                                      `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ProfileEvaluatorDevAction DevActionRequest_ProfileEvaluatorActionType `protobuf:"varint,2,opt,name=profile_evaluator_dev_action,json=profileEvaluatorDevAction,proto3,enum=simulator.profileevaluator.DevActionRequest_ProfileEvaluatorActionType" json:"profile_evaluator_dev_action,omitempty"`
}

func (x *DevActionRequest) Reset() {
	*x = DevActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_profileevaluator_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevActionRequest) ProtoMessage() {}

func (x *DevActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_profileevaluator_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevActionRequest.ProtoReflect.Descriptor instead.
func (*DevActionRequest) Descriptor() ([]byte, []int) {
	return file_api_simulator_profileevaluator_service_proto_rawDescGZIP(), []int{0}
}

func (x *DevActionRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevActionRequest) GetProfileEvaluatorDevAction() DevActionRequest_ProfileEvaluatorActionType {
	if x != nil {
		return x.ProfileEvaluatorDevAction
	}
	return DevActionRequest_UNSPECIFIED
}

type DevActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DevActionResponse) Reset() {
	*x = DevActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_profileevaluator_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevActionResponse) ProtoMessage() {}

func (x *DevActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_profileevaluator_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevActionResponse.ProtoReflect.Descriptor instead.
func (*DevActionResponse) Descriptor() ([]byte, []int) {
	return file_api_simulator_profileevaluator_service_proto_rawDescGZIP(), []int{1}
}

func (x *DevActionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_simulator_profileevaluator_service_proto protoreflect.FileDescriptor

var file_api_simulator_profileevaluator_service_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f,
	0x68, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x9d, 0x02, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x88, 0x01, 0x0a, 0x1c, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x76, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x19, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x6e, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x16, 0x0a, 0x12, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x52, 0x55, 0x4c, 0x45,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4e, 0x4f, 0x4e, 0x5f, 0x43,
	0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x45, 0x44, 0x10, 0x03,
	0x22, 0x38, 0x0a, 0x11, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xcd, 0x02, 0x0a, 0x10, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0xa6, 0x01, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x2f, 0x76, 0x31, 0x2e, 0x30, 0x2e, 0x30, 0x2f, 0x64, 0x69,
	0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x50, 0x56, 0x12, 0x8f, 0x01, 0x0a, 0x09, 0x44, 0x65, 0x76,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x6f, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a,
	0x2f, 0x44, 0x65, 0x76, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x42, 0x6e, 0x0a, 0x35, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x6f, 0x72, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_simulator_profileevaluator_service_proto_rawDescOnce sync.Once
	file_api_simulator_profileevaluator_service_proto_rawDescData = file_api_simulator_profileevaluator_service_proto_rawDesc
)

func file_api_simulator_profileevaluator_service_proto_rawDescGZIP() []byte {
	file_api_simulator_profileevaluator_service_proto_rawDescOnce.Do(func() {
		file_api_simulator_profileevaluator_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_simulator_profileevaluator_service_proto_rawDescData)
	})
	return file_api_simulator_profileevaluator_service_proto_rawDescData
}

var file_api_simulator_profileevaluator_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_simulator_profileevaluator_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_simulator_profileevaluator_service_proto_goTypes = []interface{}{
	(DevActionRequest_ProfileEvaluatorActionType)(0), // 0: simulator.profileevaluator.DevActionRequest.ProfileEvaluatorActionType
	(*DevActionRequest)(nil),                         // 1: simulator.profileevaluator.DevActionRequest
	(*DevActionResponse)(nil),                        // 2: simulator.profileevaluator.DevActionResponse
	(*rpc.Status)(nil),                               // 3: rpc.Status
	(*federal.CheckProfileValidationRequest)(nil),    // 4: vendors.federal.CheckProfileValidationRequest
	(*federal.CheckProfileValidationResponse)(nil),   // 5: vendors.federal.CheckProfileValidationResponse
}
var file_api_simulator_profileevaluator_service_proto_depIdxs = []int32{
	0, // 0: simulator.profileevaluator.DevActionRequest.profile_evaluator_dev_action:type_name -> simulator.profileevaluator.DevActionRequest.ProfileEvaluatorActionType
	3, // 1: simulator.profileevaluator.DevActionResponse.status:type_name -> rpc.Status
	4, // 2: simulator.profileevaluator.ProfileEvaluator.CheckProfileValidation:input_type -> vendors.federal.CheckProfileValidationRequest
	1, // 3: simulator.profileevaluator.ProfileEvaluator.DevAction:input_type -> simulator.profileevaluator.DevActionRequest
	5, // 4: simulator.profileevaluator.ProfileEvaluator.CheckProfileValidation:output_type -> vendors.federal.CheckProfileValidationResponse
	2, // 5: simulator.profileevaluator.ProfileEvaluator.DevAction:output_type -> simulator.profileevaluator.DevActionResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_simulator_profileevaluator_service_proto_init() }
func file_api_simulator_profileevaluator_service_proto_init() {
	if File_api_simulator_profileevaluator_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_simulator_profileevaluator_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_profileevaluator_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_simulator_profileevaluator_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_simulator_profileevaluator_service_proto_goTypes,
		DependencyIndexes: file_api_simulator_profileevaluator_service_proto_depIdxs,
		EnumInfos:         file_api_simulator_profileevaluator_service_proto_enumTypes,
		MessageInfos:      file_api_simulator_profileevaluator_service_proto_msgTypes,
	}.Build()
	File_api_simulator_profileevaluator_service_proto = out.File
	file_api_simulator_profileevaluator_service_proto_rawDesc = nil
	file_api_simulator_profileevaluator_service_proto_goTypes = nil
	file_api_simulator_profileevaluator_service_proto_depIdxs = nil
}
