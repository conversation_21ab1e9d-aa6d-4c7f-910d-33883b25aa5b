// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/insights/accessinfo/consumer
package consumer

import (
	"github.com/epifi/be-common/pkg/queue"
)

const (
	UpdateOnboardingStatusToOnboardedMethod = "UpdateOnboardingStatusToOnboarded"
)

// RegisterUpdateOnboardingStatusToOnboardedMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterUpdateOnboardingStatusToOnboardedMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, UpdateOnboardingStatusToOnboardedMethod)
}
