// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/epf/model/epf_sms_data.proto

package model

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EpfSmsData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EpfSmsData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EpfSmsData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EpfSmsDataMultiError, or
// nil if none found.
func (m *EpfSmsData) ValidateAll() error {
	return m.validate(true)
}

func (m *EpfSmsData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for MaskedUanNumber

	// no validation rules for PassbookNumber

	if all {
		switch v := interface{}(m.GetCreditAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "CreditAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "CreditAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfSmsDataValidationError{
				field:  "CreditAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPassbookBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "PassbookBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "PassbookBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPassbookBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfSmsDataValidationError{
				field:  "PassbookBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreditMonth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "CreditMonth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "CreditMonth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditMonth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfSmsDataValidationError{
				field:  "CreditMonth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfSmsDataValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfSmsDataValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EpfSmsDataValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EpfSmsDataValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EpfSmsDataMultiError(errors)
	}

	return nil
}

// EpfSmsDataMultiError is an error wrapping multiple validation errors
// returned by EpfSmsData.ValidateAll() if the designated constraints aren't met.
type EpfSmsDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EpfSmsDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EpfSmsDataMultiError) AllErrors() []error { return m }

// EpfSmsDataValidationError is the validation error returned by
// EpfSmsData.Validate if the designated constraints aren't met.
type EpfSmsDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EpfSmsDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EpfSmsDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EpfSmsDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EpfSmsDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EpfSmsDataValidationError) ErrorName() string { return "EpfSmsDataValidationError" }

// Error satisfies the builtin error interface
func (e EpfSmsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEpfSmsData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EpfSmsDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EpfSmsDataValidationError{}
