// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/wealthonboarding/onboarding_summary.proto

package wealthonboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on OnboardingSummary with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OnboardingSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OnboardingSummaryMultiError, or nil if none found.
func (m *OnboardingSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOnboardingDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingSummaryValidationError{
					field:  "OnboardingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingSummaryValidationError{
					field:  "OnboardingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOnboardingDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingSummaryValidationError{
				field:  "OnboardingDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentStepDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingSummaryValidationError{
					field:  "CurrentStepDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingSummaryValidationError{
					field:  "CurrentStepDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentStepDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingSummaryValidationError{
				field:  "CurrentStepDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OnboardingSummaryMultiError(errors)
	}

	return nil
}

// OnboardingSummaryMultiError is an error wrapping multiple validation errors
// returned by OnboardingSummary.ValidateAll() if the designated constraints
// aren't met.
type OnboardingSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingSummaryMultiError) AllErrors() []error { return m }

// OnboardingSummaryValidationError is the validation error returned by
// OnboardingSummary.Validate if the designated constraints aren't met.
type OnboardingSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingSummaryValidationError) ErrorName() string {
	return "OnboardingSummaryValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingSummaryValidationError{}
