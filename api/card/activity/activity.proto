syntax = "proto3";

package card.activity;

import "api/card/enums/enums.proto";
import "api/card/provisioning/card_request.proto";
import "api/celestial/activity/header.proto";
import "api/celestial/workflow/stage.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/money.proto";

option go_package = "github.com/epifi/gamma/api/card/activity";
option java_package = "com.github.epifi.gamma.api.card.activity";

message BlockCardRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;
  // card id of the card which needs to be blocked
  string card_id = 2;
}

message BlockCardResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}


message CardRequestUpdateRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;
  // Next action deeplink can be sent in here for update
  frontend.deeplink.Deeplink next_action = 2;
  card.enums.CardRequestStatus status = 3;
  repeated card.enums.CardRequestFieldMask field_masks = 4;
  // Workflow stage can be sent in when next action needs to be generated
  celestial.workflow.StageEnum workflow_stage_enum = 5;
  //Request Details
  card.provisioning.CardRequestDetails card_request_details = 6;
  // Workflow stage can be sent in when next action needs to be generated
  card.enums.CardRequestStageName card_request_stage = 7;
}

message CardRequestUpdateResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;

  // updated card request
  provisioning.CardRequest card_request = 2;
}

message CreateNewCardRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;
}

message CreateNewCardResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;

  // card id of the newly created card in reissue flow
  string new_card_id = 2;
}

message PollCardCreationStatusRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  // card_id for which card creation was initiated
  string card_id = 2;
}

message PollCardCreationStatusResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}

message InitiatePhysicalCardDispatchRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;
  // card id for which physical dispatch needs to be initiated
  string card_id = 2;
  string actor_id = 3;
  api.typesv2.Money amount = 4;
}

message InitiatePhysicalCardDispatchResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;

  // flag to check, if should start poll for physical dispatch status
  bool Should_skip_dispatch_status_poll = 2;
}

message PollPhysicalDispatchStatusRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;
  // card id to poll dispatch status
  string card_id = 2;
}

message PollPhysicalDispatchStatusResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}
