syntax = "proto3";

package event;

import "api/queue/consumer_headers.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/event";
option java_package = "com.github.epifi.gamma.api.event";

// RudderEvent defines the payload of all rudder events received via kafka stack
message RudderEvent {
  queue.ConsumerRequestHeader request_header = 1;

  // id of the message
  string message_id = 2;

  // id of the user associated with the event
  string user_id = 3;

  // name of the event
  string event = 4;

  // timestamp of the event
  string timestamp = 5;

  // properties of rudder event provided while publishing the event
  google.protobuf.Struct properties = 6;
}
