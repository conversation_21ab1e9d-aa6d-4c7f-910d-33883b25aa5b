// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/creditreportv2/consumer/consumer.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProcessReportPresenceCheckRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessReportPresenceCheckRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReportPresenceCheckRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessReportPresenceCheckRequestMultiError, or nil if none found.
func (m *ProcessReportPresenceCheckRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReportPresenceCheckRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReportPresenceCheckRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReportPresenceCheckRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReportPresenceCheckRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetPresenceCheckStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReportPresenceCheckRequestValidationError{
					field:  "PresenceCheckStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReportPresenceCheckRequestValidationError{
					field:  "PresenceCheckStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPresenceCheckStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReportPresenceCheckRequestValidationError{
				field:  "PresenceCheckStartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessReportPresenceCheckRequestMultiError(errors)
	}

	return nil
}

// ProcessReportPresenceCheckRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessReportPresenceCheckRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessReportPresenceCheckRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReportPresenceCheckRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReportPresenceCheckRequestMultiError) AllErrors() []error { return m }

// ProcessReportPresenceCheckRequestValidationError is the validation error
// returned by ProcessReportPresenceCheckRequest.Validate if the designated
// constraints aren't met.
type ProcessReportPresenceCheckRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReportPresenceCheckRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReportPresenceCheckRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReportPresenceCheckRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReportPresenceCheckRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReportPresenceCheckRequestValidationError) ErrorName() string {
	return "ProcessReportPresenceCheckRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReportPresenceCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReportPresenceCheckRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReportPresenceCheckRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReportPresenceCheckRequestValidationError{}

// Validate checks the field values on ProcessReportPresenceCheckResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessReportPresenceCheckResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReportPresenceCheckResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessReportPresenceCheckResponseMultiError, or nil if none found.
func (m *ProcessReportPresenceCheckResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReportPresenceCheckResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReportPresenceCheckResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReportPresenceCheckResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReportPresenceCheckResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessReportPresenceCheckResponseMultiError(errors)
	}

	return nil
}

// ProcessReportPresenceCheckResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessReportPresenceCheckResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessReportPresenceCheckResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReportPresenceCheckResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReportPresenceCheckResponseMultiError) AllErrors() []error { return m }

// ProcessReportPresenceCheckResponseValidationError is the validation error
// returned by ProcessReportPresenceCheckResponse.Validate if the designated
// constraints aren't met.
type ProcessReportPresenceCheckResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReportPresenceCheckResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReportPresenceCheckResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReportPresenceCheckResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReportPresenceCheckResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReportPresenceCheckResponseValidationError) ErrorName() string {
	return "ProcessReportPresenceCheckResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReportPresenceCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReportPresenceCheckResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReportPresenceCheckResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReportPresenceCheckResponseValidationError{}

// Validate checks the field values on ProcessReportVerificationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessReportVerificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReportVerificationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessReportVerificationRequestMultiError, or nil if none found.
func (m *ProcessReportVerificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReportVerificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReportVerificationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReportVerificationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReportVerificationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetVerificationStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReportVerificationRequestValidationError{
					field:  "VerificationStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReportVerificationRequestValidationError{
					field:  "VerificationStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVerificationStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReportVerificationRequestValidationError{
				field:  "VerificationStartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessReportVerificationRequestMultiError(errors)
	}

	return nil
}

// ProcessReportVerificationRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessReportVerificationRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessReportVerificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReportVerificationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReportVerificationRequestMultiError) AllErrors() []error { return m }

// ProcessReportVerificationRequestValidationError is the validation error
// returned by ProcessReportVerificationRequest.Validate if the designated
// constraints aren't met.
type ProcessReportVerificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReportVerificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReportVerificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReportVerificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReportVerificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReportVerificationRequestValidationError) ErrorName() string {
	return "ProcessReportVerificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReportVerificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReportVerificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReportVerificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReportVerificationRequestValidationError{}

// Validate checks the field values on ProcessReportVerificationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessReportVerificationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReportVerificationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessReportVerificationResponseMultiError, or nil if none found.
func (m *ProcessReportVerificationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReportVerificationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReportVerificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReportVerificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReportVerificationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessReportVerificationResponseMultiError(errors)
	}

	return nil
}

// ProcessReportVerificationResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessReportVerificationResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessReportVerificationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReportVerificationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReportVerificationResponseMultiError) AllErrors() []error { return m }

// ProcessReportVerificationResponseValidationError is the validation error
// returned by ProcessReportVerificationResponse.Validate if the designated
// constraints aren't met.
type ProcessReportVerificationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReportVerificationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReportVerificationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReportVerificationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReportVerificationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReportVerificationResponseValidationError) ErrorName() string {
	return "ProcessReportVerificationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReportVerificationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReportVerificationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReportVerificationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReportVerificationResponseValidationError{}

// Validate checks the field values on ProcessCreditReportFlatteningRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCreditReportFlatteningRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCreditReportFlatteningRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCreditReportFlatteningRequestMultiError, or nil if none found.
func (m *ProcessCreditReportFlatteningRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCreditReportFlatteningRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCreditReportFlatteningRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCreditReportFlatteningRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCreditReportFlatteningRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawReportId

	if len(errors) > 0 {
		return ProcessCreditReportFlatteningRequestMultiError(errors)
	}

	return nil
}

// ProcessCreditReportFlatteningRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCreditReportFlatteningRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCreditReportFlatteningRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCreditReportFlatteningRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCreditReportFlatteningRequestMultiError) AllErrors() []error { return m }

// ProcessCreditReportFlatteningRequestValidationError is the validation error
// returned by ProcessCreditReportFlatteningRequest.Validate if the designated
// constraints aren't met.
type ProcessCreditReportFlatteningRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCreditReportFlatteningRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCreditReportFlatteningRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCreditReportFlatteningRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCreditReportFlatteningRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCreditReportFlatteningRequestValidationError) ErrorName() string {
	return "ProcessCreditReportFlatteningRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCreditReportFlatteningRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCreditReportFlatteningRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCreditReportFlatteningRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCreditReportFlatteningRequestValidationError{}

// Validate checks the field values on ProcessCreditReportFlatteningResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCreditReportFlatteningResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCreditReportFlatteningResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCreditReportFlatteningResponseMultiError, or nil if none found.
func (m *ProcessCreditReportFlatteningResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCreditReportFlatteningResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCreditReportFlatteningResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCreditReportFlatteningResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCreditReportFlatteningResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCreditReportFlatteningResponseMultiError(errors)
	}

	return nil
}

// ProcessCreditReportFlatteningResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCreditReportFlatteningResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCreditReportFlatteningResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCreditReportFlatteningResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCreditReportFlatteningResponseMultiError) AllErrors() []error { return m }

// ProcessCreditReportFlatteningResponseValidationError is the validation error
// returned by ProcessCreditReportFlatteningResponse.Validate if the
// designated constraints aren't met.
type ProcessCreditReportFlatteningResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCreditReportFlatteningResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCreditReportFlatteningResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCreditReportFlatteningResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCreditReportFlatteningResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCreditReportFlatteningResponseValidationError) ErrorName() string {
	return "ProcessCreditReportFlatteningResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCreditReportFlatteningResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCreditReportFlatteningResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCreditReportFlatteningResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCreditReportFlatteningResponseValidationError{}
