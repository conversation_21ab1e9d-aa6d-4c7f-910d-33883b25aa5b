//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/creditreportv2/derivedattributes/service.proto

package derivedattributes

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DerivedAttributesManager_GetDerivedAttributes_FullMethodName = "/creditreportv2.derivedattributes.DerivedAttributesManager/GetDerivedAttributes"
)

// DerivedAttributesManagerClient is the client API for DerivedAttributesManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DerivedAttributesManagerClient interface {
	// This rpc returns the derived attributes for a given actor id and report id (optional).
	// The latest verified report for that actor will be used to fetch the derived attributes.
	GetDerivedAttributes(ctx context.Context, in *GetDerivedAttributesRequest, opts ...grpc.CallOption) (*GetDerivedAttributesResponse, error)
}

type derivedAttributesManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewDerivedAttributesManagerClient(cc grpc.ClientConnInterface) DerivedAttributesManagerClient {
	return &derivedAttributesManagerClient{cc}
}

func (c *derivedAttributesManagerClient) GetDerivedAttributes(ctx context.Context, in *GetDerivedAttributesRequest, opts ...grpc.CallOption) (*GetDerivedAttributesResponse, error) {
	out := new(GetDerivedAttributesResponse)
	err := c.cc.Invoke(ctx, DerivedAttributesManager_GetDerivedAttributes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DerivedAttributesManagerServer is the server API for DerivedAttributesManager service.
// All implementations should embed UnimplementedDerivedAttributesManagerServer
// for forward compatibility
type DerivedAttributesManagerServer interface {
	// This rpc returns the derived attributes for a given actor id and report id (optional).
	// The latest verified report for that actor will be used to fetch the derived attributes.
	GetDerivedAttributes(context.Context, *GetDerivedAttributesRequest) (*GetDerivedAttributesResponse, error)
}

// UnimplementedDerivedAttributesManagerServer should be embedded to have forward compatible implementations.
type UnimplementedDerivedAttributesManagerServer struct {
}

func (UnimplementedDerivedAttributesManagerServer) GetDerivedAttributes(context.Context, *GetDerivedAttributesRequest) (*GetDerivedAttributesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDerivedAttributes not implemented")
}

// UnsafeDerivedAttributesManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DerivedAttributesManagerServer will
// result in compilation errors.
type UnsafeDerivedAttributesManagerServer interface {
	mustEmbedUnimplementedDerivedAttributesManagerServer()
}

func RegisterDerivedAttributesManagerServer(s grpc.ServiceRegistrar, srv DerivedAttributesManagerServer) {
	s.RegisterService(&DerivedAttributesManager_ServiceDesc, srv)
}

func _DerivedAttributesManager_GetDerivedAttributes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDerivedAttributesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DerivedAttributesManagerServer).GetDerivedAttributes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DerivedAttributesManager_GetDerivedAttributes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DerivedAttributesManagerServer).GetDerivedAttributes(ctx, req.(*GetDerivedAttributesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DerivedAttributesManager_ServiceDesc is the grpc.ServiceDesc for DerivedAttributesManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DerivedAttributesManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "creditreportv2.derivedattributes.DerivedAttributesManager",
	HandlerType: (*DerivedAttributesManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDerivedAttributes",
			Handler:    _DerivedAttributesManager_GetDerivedAttributes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/creditreportv2/derivedattributes/service.proto",
}
