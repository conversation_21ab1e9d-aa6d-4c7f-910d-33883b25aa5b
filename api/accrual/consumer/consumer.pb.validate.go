// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/accrual/consumer/consumer.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ConsumerResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ConsumerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumerResponseMultiError, or nil if none found.
func (m *ConsumerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumerResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumerResponseMultiError(errors)
	}

	return nil
}

// ConsumerResponseMultiError is an error wrapping multiple validation errors
// returned by ConsumerResponse.ValidateAll() if the designated constraints
// aren't met.
type ConsumerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumerResponseMultiError) AllErrors() []error { return m }

// ConsumerResponseValidationError is the validation error returned by
// ConsumerResponse.Validate if the designated constraints aren't met.
type ConsumerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumerResponseValidationError) ErrorName() string { return "ConsumerResponseValidationError" }

// Error satisfies the builtin error interface
func (e ConsumerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumerResponseValidationError{}
