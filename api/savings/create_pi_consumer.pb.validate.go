// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/savings/create_pi_consumer.proto

package savings

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	account "github.com/epifi/gamma/api/typesv2/account"

	accounts "github.com/epifi/gamma/api/accounts"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = account.AccountProductOffering(0)

	_ = accounts.Type(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on CreateVPARequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateVPARequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVPARequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVPARequestMultiError, or nil if none found.
func (m *CreateVPARequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVPARequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVPARequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVPARequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVPARequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserId

	if l := utf8.RuneCountInString(m.GetAccountId()); l < 4 || l > 100 {
		err := CreateVPARequestValidationError{
			field:  "AccountId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateVPARequest_PartnerBank_NotInLookup[m.GetPartnerBank()]; ok {
		err := CreateVPARequestValidationError{
			field:  "PartnerBank",
			reason: "value must not be in list [VENDOR_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateVPARequest_AccountType_NotInLookup[m.GetAccountType()]; ok {
		err := CreateVPARequestValidationError{
			field:  "AccountType",
			reason: "value must not be in list [TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateVPARequestMultiError(errors)
	}

	return nil
}

// CreateVPARequestMultiError is an error wrapping multiple validation errors
// returned by CreateVPARequest.ValidateAll() if the designated constraints
// aren't met.
type CreateVPARequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVPARequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVPARequestMultiError) AllErrors() []error { return m }

// CreateVPARequestValidationError is the validation error returned by
// CreateVPARequest.Validate if the designated constraints aren't met.
type CreateVPARequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVPARequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVPARequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVPARequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVPARequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVPARequestValidationError) ErrorName() string { return "CreateVPARequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateVPARequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVPARequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVPARequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVPARequestValidationError{}

var _CreateVPARequest_PartnerBank_NotInLookup = map[vendorgateway.Vendor]struct{}{
	0: {},
}

var _CreateVPARequest_AccountType_NotInLookup = map[accounts.Type]struct{}{
	0: {},
}

// Validate checks the field values on CreateVPAResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateVPAResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVPAResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVPAResponseMultiError, or nil if none found.
func (m *CreateVPAResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVPAResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVPAResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVPAResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVPAResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVPAResponseMultiError(errors)
	}

	return nil
}

// CreateVPAResponseMultiError is an error wrapping multiple validation errors
// returned by CreateVPAResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateVPAResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVPAResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVPAResponseMultiError) AllErrors() []error { return m }

// CreateVPAResponseValidationError is the validation error returned by
// CreateVPAResponse.Validate if the designated constraints aren't met.
type CreateVPAResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVPAResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVPAResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVPAResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVPAResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVPAResponseValidationError) ErrorName() string {
	return "CreateVPAResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVPAResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVPAResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVPAResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVPAResponseValidationError{}

// Validate checks the field values on ProcessSavingsAccountPICreationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessSavingsAccountPICreationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessSavingsAccountPICreationRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ProcessSavingsAccountPICreationRequestMultiError, or nil if none found.
func (m *ProcessSavingsAccountPICreationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessSavingsAccountPICreationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessSavingsAccountPICreationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessSavingsAccountPICreationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessSavingsAccountPICreationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for EntityId

	// no validation rules for AccountNo

	// no validation rules for AccountId

	// no validation rules for IfscCode

	// no validation rules for AccountProductOffering

	if len(errors) > 0 {
		return ProcessSavingsAccountPICreationRequestMultiError(errors)
	}

	return nil
}

// ProcessSavingsAccountPICreationRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessSavingsAccountPICreationRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessSavingsAccountPICreationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessSavingsAccountPICreationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessSavingsAccountPICreationRequestMultiError) AllErrors() []error { return m }

// ProcessSavingsAccountPICreationRequestValidationError is the validation
// error returned by ProcessSavingsAccountPICreationRequest.Validate if the
// designated constraints aren't met.
type ProcessSavingsAccountPICreationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessSavingsAccountPICreationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessSavingsAccountPICreationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessSavingsAccountPICreationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessSavingsAccountPICreationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessSavingsAccountPICreationRequestValidationError) ErrorName() string {
	return "ProcessSavingsAccountPICreationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessSavingsAccountPICreationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessSavingsAccountPICreationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessSavingsAccountPICreationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessSavingsAccountPICreationRequestValidationError{}

// Validate checks the field values on ProcessSavingsAccountPICreationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessSavingsAccountPICreationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessSavingsAccountPICreationResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ProcessSavingsAccountPICreationResponseMultiError, or nil if none found.
func (m *ProcessSavingsAccountPICreationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessSavingsAccountPICreationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessSavingsAccountPICreationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessSavingsAccountPICreationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessSavingsAccountPICreationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessSavingsAccountPICreationResponseMultiError(errors)
	}

	return nil
}

// ProcessSavingsAccountPICreationResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessSavingsAccountPICreationResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessSavingsAccountPICreationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessSavingsAccountPICreationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessSavingsAccountPICreationResponseMultiError) AllErrors() []error { return m }

// ProcessSavingsAccountPICreationResponseValidationError is the validation
// error returned by ProcessSavingsAccountPICreationResponse.Validate if the
// designated constraints aren't met.
type ProcessSavingsAccountPICreationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessSavingsAccountPICreationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessSavingsAccountPICreationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessSavingsAccountPICreationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessSavingsAccountPICreationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessSavingsAccountPICreationResponseValidationError) ErrorName() string {
	return "ProcessSavingsAccountPICreationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessSavingsAccountPICreationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessSavingsAccountPICreationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessSavingsAccountPICreationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessSavingsAccountPICreationResponseValidationError{}
