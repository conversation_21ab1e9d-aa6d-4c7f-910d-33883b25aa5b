// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendornotification/sms/netcore/netcore.proto

package netcore

import (
	context "context"
	netcore "github.com/epifi/gamma/api/vendors/netcore"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NetCoreCallback_NetCoreSmsDLR_FullMethodName = "/vendornotification.sms.netcore.NetCoreCallback/NetCoreSmsDLR"
)

// NetCoreCallbackClient is the client API for NetCoreCallback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NetCoreCallbackClient interface {
	NetCoreSmsDLR(ctx context.Context, in *netcore.NetCoreSmsDLRRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type netCoreCallbackClient struct {
	cc grpc.ClientConnInterface
}

func NewNetCoreCallbackClient(cc grpc.ClientConnInterface) NetCoreCallbackClient {
	return &netCoreCallbackClient{cc}
}

func (c *netCoreCallbackClient) NetCoreSmsDLR(ctx context.Context, in *netcore.NetCoreSmsDLRRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NetCoreCallback_NetCoreSmsDLR_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NetCoreCallbackServer is the server API for NetCoreCallback service.
// All implementations should embed UnimplementedNetCoreCallbackServer
// for forward compatibility
type NetCoreCallbackServer interface {
	NetCoreSmsDLR(context.Context, *netcore.NetCoreSmsDLRRequest) (*emptypb.Empty, error)
}

// UnimplementedNetCoreCallbackServer should be embedded to have forward compatible implementations.
type UnimplementedNetCoreCallbackServer struct {
}

func (UnimplementedNetCoreCallbackServer) NetCoreSmsDLR(context.Context, *netcore.NetCoreSmsDLRRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NetCoreSmsDLR not implemented")
}

// UnsafeNetCoreCallbackServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NetCoreCallbackServer will
// result in compilation errors.
type UnsafeNetCoreCallbackServer interface {
	mustEmbedUnimplementedNetCoreCallbackServer()
}

func RegisterNetCoreCallbackServer(s grpc.ServiceRegistrar, srv NetCoreCallbackServer) {
	s.RegisterService(&NetCoreCallback_ServiceDesc, srv)
}

func _NetCoreCallback_NetCoreSmsDLR_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(netcore.NetCoreSmsDLRRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetCoreCallbackServer).NetCoreSmsDLR(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetCoreCallback_NetCoreSmsDLR_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetCoreCallbackServer).NetCoreSmsDLR(ctx, req.(*netcore.NetCoreSmsDLRRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NetCoreCallback_ServiceDesc is the grpc.ServiceDesc for NetCoreCallback service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NetCoreCallback_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendornotification.sms.netcore.NetCoreCallback",
	HandlerType: (*NetCoreCallbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NetCoreSmsDLR",
			Handler:    _NetCoreCallback_NetCoreSmsDLR_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendornotification/sms/netcore/netcore.proto",
}
