// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendornotification/sms/kaleyra/kaleyra_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	kaleyra "github.com/epifi/gamma/api/vendornotification/sms/kaleyra"
	kaleyra0 "github.com/epifi/gamma/api/vendors/kaleyra"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockKaleyraCallbackClient is a mock of KaleyraCallbackClient interface.
type MockKaleyraCallbackClient struct {
	ctrl     *gomock.Controller
	recorder *MockKaleyraCallbackClientMockRecorder
}

// MockKaleyraCallbackClientMockRecorder is the mock recorder for MockKaleyraCallbackClient.
type MockKaleyraCallbackClientMockRecorder struct {
	mock *MockKaleyraCallbackClient
}

// NewMockKaleyraCallbackClient creates a new mock instance.
func NewMockKaleyraCallbackClient(ctrl *gomock.Controller) *MockKaleyraCallbackClient {
	mock := &MockKaleyraCallbackClient{ctrl: ctrl}
	mock.recorder = &MockKaleyraCallbackClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKaleyraCallbackClient) EXPECT() *MockKaleyraCallbackClientMockRecorder {
	return m.recorder
}

// KaleyraIOSmsDLR mocks base method.
func (m *MockKaleyraCallbackClient) KaleyraIOSmsDLR(ctx context.Context, in *kaleyra0.KaleyraIOSmsDLRRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "KaleyraIOSmsDLR", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KaleyraIOSmsDLR indicates an expected call of KaleyraIOSmsDLR.
func (mr *MockKaleyraCallbackClientMockRecorder) KaleyraIOSmsDLR(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KaleyraIOSmsDLR", reflect.TypeOf((*MockKaleyraCallbackClient)(nil).KaleyraIOSmsDLR), varargs...)
}

// KaleyraSmsDLR mocks base method.
func (m *MockKaleyraCallbackClient) KaleyraSmsDLR(ctx context.Context, in *kaleyra.KaleyraSmsDLRRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "KaleyraSmsDLR", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KaleyraSmsDLR indicates an expected call of KaleyraSmsDLR.
func (mr *MockKaleyraCallbackClientMockRecorder) KaleyraSmsDLR(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KaleyraSmsDLR", reflect.TypeOf((*MockKaleyraCallbackClient)(nil).KaleyraSmsDLR), varargs...)
}

// MockKaleyraCallbackServer is a mock of KaleyraCallbackServer interface.
type MockKaleyraCallbackServer struct {
	ctrl     *gomock.Controller
	recorder *MockKaleyraCallbackServerMockRecorder
}

// MockKaleyraCallbackServerMockRecorder is the mock recorder for MockKaleyraCallbackServer.
type MockKaleyraCallbackServerMockRecorder struct {
	mock *MockKaleyraCallbackServer
}

// NewMockKaleyraCallbackServer creates a new mock instance.
func NewMockKaleyraCallbackServer(ctrl *gomock.Controller) *MockKaleyraCallbackServer {
	mock := &MockKaleyraCallbackServer{ctrl: ctrl}
	mock.recorder = &MockKaleyraCallbackServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKaleyraCallbackServer) EXPECT() *MockKaleyraCallbackServerMockRecorder {
	return m.recorder
}

// KaleyraIOSmsDLR mocks base method.
func (m *MockKaleyraCallbackServer) KaleyraIOSmsDLR(arg0 context.Context, arg1 *kaleyra0.KaleyraIOSmsDLRRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KaleyraIOSmsDLR", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KaleyraIOSmsDLR indicates an expected call of KaleyraIOSmsDLR.
func (mr *MockKaleyraCallbackServerMockRecorder) KaleyraIOSmsDLR(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KaleyraIOSmsDLR", reflect.TypeOf((*MockKaleyraCallbackServer)(nil).KaleyraIOSmsDLR), arg0, arg1)
}

// KaleyraSmsDLR mocks base method.
func (m *MockKaleyraCallbackServer) KaleyraSmsDLR(arg0 context.Context, arg1 *kaleyra.KaleyraSmsDLRRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KaleyraSmsDLR", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KaleyraSmsDLR indicates an expected call of KaleyraSmsDLR.
func (mr *MockKaleyraCallbackServerMockRecorder) KaleyraSmsDLR(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KaleyraSmsDLR", reflect.TypeOf((*MockKaleyraCallbackServer)(nil).KaleyraSmsDLR), arg0, arg1)
}

// MockUnsafeKaleyraCallbackServer is a mock of UnsafeKaleyraCallbackServer interface.
type MockUnsafeKaleyraCallbackServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeKaleyraCallbackServerMockRecorder
}

// MockUnsafeKaleyraCallbackServerMockRecorder is the mock recorder for MockUnsafeKaleyraCallbackServer.
type MockUnsafeKaleyraCallbackServerMockRecorder struct {
	mock *MockUnsafeKaleyraCallbackServer
}

// NewMockUnsafeKaleyraCallbackServer creates a new mock instance.
func NewMockUnsafeKaleyraCallbackServer(ctrl *gomock.Controller) *MockUnsafeKaleyraCallbackServer {
	mock := &MockUnsafeKaleyraCallbackServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeKaleyraCallbackServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeKaleyraCallbackServer) EXPECT() *MockUnsafeKaleyraCallbackServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedKaleyraCallbackServer mocks base method.
func (m *MockUnsafeKaleyraCallbackServer) mustEmbedUnimplementedKaleyraCallbackServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedKaleyraCallbackServer")
}

// mustEmbedUnimplementedKaleyraCallbackServer indicates an expected call of mustEmbedUnimplementedKaleyraCallbackServer.
func (mr *MockUnsafeKaleyraCallbackServerMockRecorder) mustEmbedUnimplementedKaleyraCallbackServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedKaleyraCallbackServer", reflect.TypeOf((*MockUnsafeKaleyraCallbackServer)(nil).mustEmbedUnimplementedKaleyraCallbackServer))
}
