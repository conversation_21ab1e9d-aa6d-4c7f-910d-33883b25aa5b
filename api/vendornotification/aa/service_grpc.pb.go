// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendornotification/aa/service.proto

package aa

import (
	context "context"
	aa "github.com/epifi/gamma/api/vendors/aa"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AANotification_ConsentNotification_FullMethodName     = "/vendornotification.aa.AANotification/ConsentNotification"
	AANotification_FINotification_FullMethodName          = "/vendornotification.aa.AANotification/FINotification"
	AANotification_AccountLinkNotification_FullMethodName = "/vendornotification.aa.AANotification/AccountLinkNotification"
)

// AANotificationClient is the client API for AANotification service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AANotificationClient interface {
	// To receive consent status from AA
	// API handles the notifications corresponding to the events generated during consent flow.
	ConsentNotification(ctx context.Context, in *aa.ConsentNotification, opts ...grpc.CallOption) (*aa.AANotificationResponse, error)
	// To receive FI status from AA
	// API handles the notifications corresponding to the events generated during data-flow.
	FINotification(ctx context.Context, in *aa.FINotification, opts ...grpc.CallOption) (*aa.AANotificationResponse, error)
	// To receive account delink status from AA (one money)
	// API handles the notifications corresponding to the events generated during account de link flow
	AccountLinkNotification(ctx context.Context, in *aa.AccountLinkNotification, opts ...grpc.CallOption) (*aa.AANotificationResponse, error)
}

type aANotificationClient struct {
	cc grpc.ClientConnInterface
}

func NewAANotificationClient(cc grpc.ClientConnInterface) AANotificationClient {
	return &aANotificationClient{cc}
}

func (c *aANotificationClient) ConsentNotification(ctx context.Context, in *aa.ConsentNotification, opts ...grpc.CallOption) (*aa.AANotificationResponse, error) {
	out := new(aa.AANotificationResponse)
	err := c.cc.Invoke(ctx, AANotification_ConsentNotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aANotificationClient) FINotification(ctx context.Context, in *aa.FINotification, opts ...grpc.CallOption) (*aa.AANotificationResponse, error) {
	out := new(aa.AANotificationResponse)
	err := c.cc.Invoke(ctx, AANotification_FINotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aANotificationClient) AccountLinkNotification(ctx context.Context, in *aa.AccountLinkNotification, opts ...grpc.CallOption) (*aa.AANotificationResponse, error) {
	out := new(aa.AANotificationResponse)
	err := c.cc.Invoke(ctx, AANotification_AccountLinkNotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AANotificationServer is the server API for AANotification service.
// All implementations should embed UnimplementedAANotificationServer
// for forward compatibility
type AANotificationServer interface {
	// To receive consent status from AA
	// API handles the notifications corresponding to the events generated during consent flow.
	ConsentNotification(context.Context, *aa.ConsentNotification) (*aa.AANotificationResponse, error)
	// To receive FI status from AA
	// API handles the notifications corresponding to the events generated during data-flow.
	FINotification(context.Context, *aa.FINotification) (*aa.AANotificationResponse, error)
	// To receive account delink status from AA (one money)
	// API handles the notifications corresponding to the events generated during account de link flow
	AccountLinkNotification(context.Context, *aa.AccountLinkNotification) (*aa.AANotificationResponse, error)
}

// UnimplementedAANotificationServer should be embedded to have forward compatible implementations.
type UnimplementedAANotificationServer struct {
}

func (UnimplementedAANotificationServer) ConsentNotification(context.Context, *aa.ConsentNotification) (*aa.AANotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsentNotification not implemented")
}
func (UnimplementedAANotificationServer) FINotification(context.Context, *aa.FINotification) (*aa.AANotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FINotification not implemented")
}
func (UnimplementedAANotificationServer) AccountLinkNotification(context.Context, *aa.AccountLinkNotification) (*aa.AANotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountLinkNotification not implemented")
}

// UnsafeAANotificationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AANotificationServer will
// result in compilation errors.
type UnsafeAANotificationServer interface {
	mustEmbedUnimplementedAANotificationServer()
}

func RegisterAANotificationServer(s grpc.ServiceRegistrar, srv AANotificationServer) {
	s.RegisterService(&AANotification_ServiceDesc, srv)
}

func _AANotification_ConsentNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(aa.ConsentNotification)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AANotificationServer).ConsentNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AANotification_ConsentNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AANotificationServer).ConsentNotification(ctx, req.(*aa.ConsentNotification))
	}
	return interceptor(ctx, in, info, handler)
}

func _AANotification_FINotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(aa.FINotification)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AANotificationServer).FINotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AANotification_FINotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AANotificationServer).FINotification(ctx, req.(*aa.FINotification))
	}
	return interceptor(ctx, in, info, handler)
}

func _AANotification_AccountLinkNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(aa.AccountLinkNotification)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AANotificationServer).AccountLinkNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AANotification_AccountLinkNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AANotificationServer).AccountLinkNotification(ctx, req.(*aa.AccountLinkNotification))
	}
	return interceptor(ctx, in, info, handler)
}

// AANotification_ServiceDesc is the grpc.ServiceDesc for AANotification service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AANotification_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendornotification.aa.AANotification",
	HandlerType: (*AANotificationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ConsentNotification",
			Handler:    _AANotification_ConsentNotification_Handler,
		},
		{
			MethodName: "FINotification",
			Handler:    _AANotification_FINotification_Handler,
		},
		{
			MethodName: "AccountLinkNotification",
			Handler:    _AANotification_AccountLinkNotification_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendornotification/aa/service.proto",
}
