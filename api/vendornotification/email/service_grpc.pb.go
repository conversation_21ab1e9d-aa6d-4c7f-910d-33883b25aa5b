// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendornotification/email/service.proto

package email

import (
	context "context"
	sendgrid "github.com/epifi/gamma/api/vendors/email/sendgrid"
	ses "github.com/epifi/gamma/api/vendors/email/ses"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	EmailCallback_SesCallback_FullMethodName      = "/vendornotification.email.EmailCallback/SesCallback"
	EmailCallback_SendgridCallback_FullMethodName = "/vendornotification.email.EmailCallback/SendgridCallback"
)

// EmailCallbackClient is the client API for EmailCallback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EmailCallbackClient interface {
	SesCallback(ctx context.Context, in *ses.AwsSesCallback, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SendgridCallback(ctx context.Context, in *sendgrid.SendGridCallbackList, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type emailCallbackClient struct {
	cc grpc.ClientConnInterface
}

func NewEmailCallbackClient(cc grpc.ClientConnInterface) EmailCallbackClient {
	return &emailCallbackClient{cc}
}

func (c *emailCallbackClient) SesCallback(ctx context.Context, in *ses.AwsSesCallback, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EmailCallback_SesCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailCallbackClient) SendgridCallback(ctx context.Context, in *sendgrid.SendGridCallbackList, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EmailCallback_SendgridCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EmailCallbackServer is the server API for EmailCallback service.
// All implementations should embed UnimplementedEmailCallbackServer
// for forward compatibility
type EmailCallbackServer interface {
	SesCallback(context.Context, *ses.AwsSesCallback) (*emptypb.Empty, error)
	SendgridCallback(context.Context, *sendgrid.SendGridCallbackList) (*emptypb.Empty, error)
}

// UnimplementedEmailCallbackServer should be embedded to have forward compatible implementations.
type UnimplementedEmailCallbackServer struct {
}

func (UnimplementedEmailCallbackServer) SesCallback(context.Context, *ses.AwsSesCallback) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SesCallback not implemented")
}
func (UnimplementedEmailCallbackServer) SendgridCallback(context.Context, *sendgrid.SendGridCallbackList) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendgridCallback not implemented")
}

// UnsafeEmailCallbackServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EmailCallbackServer will
// result in compilation errors.
type UnsafeEmailCallbackServer interface {
	mustEmbedUnimplementedEmailCallbackServer()
}

func RegisterEmailCallbackServer(s grpc.ServiceRegistrar, srv EmailCallbackServer) {
	s.RegisterService(&EmailCallback_ServiceDesc, srv)
}

func _EmailCallback_SesCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ses.AwsSesCallback)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailCallbackServer).SesCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmailCallback_SesCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailCallbackServer).SesCallback(ctx, req.(*ses.AwsSesCallback))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailCallback_SendgridCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(sendgrid.SendGridCallbackList)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailCallbackServer).SendgridCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmailCallback_SendgridCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailCallbackServer).SendgridCallback(ctx, req.(*sendgrid.SendGridCallbackList))
	}
	return interceptor(ctx, in, info, handler)
}

// EmailCallback_ServiceDesc is the grpc.ServiceDesc for EmailCallback service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EmailCallback_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendornotification.email.EmailCallback",
	HandlerType: (*EmailCallbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SesCallback",
			Handler:    _EmailCallback_SesCallback_Handler,
		},
		{
			MethodName: "SendgridCallback",
			Handler:    _EmailCallback_SendgridCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendornotification/email/service.proto",
}
