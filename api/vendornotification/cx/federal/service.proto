syntax = "proto3";

package cx.escalation.federal;

import "api/vendornotification/cx/federal/message.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/federal";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.federal";

service FederalEscalationHandling {
  rpc ProcessFederalEscalationEvent(ProcessFederalEscalationEventRequest) returns (ProcessFederalEscalationEventResponse) {
    option (google.api.http) = {
      post: "/cx/escalation/federal-io/update"
      body: "*"
    };
  }
}
