// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vkyccall/developer/service.proto

package developer

import (
	dbstate "github.com/epifi/be-common/api/sherlock/dev/dbstate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_vkyccall_developer_service_proto protoreflect.FileDescriptor

var file_api_vkyccall_developer_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x6b, 0x79, 0x63, 0x63, 0x61, 0x6c, 0x6c, 0x2f, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x6b, 0x79, 0x63,
	0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x1a, 0x28,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x2f, 0x64, 0x65, 0x76,
	0x2f, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2f, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xc8, 0x02, 0x0a, 0x0b, 0x44, 0x65, 0x76,
	0x56, 0x6b, 0x79, 0x63, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x6a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x2e, 0x73, 0x68, 0x65, 0x72,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x64, 0x65, 0x76, 0x2e, 0x64, 0x62, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b,
	0x2e, 0x64, 0x65, 0x76, 0x2e, 0x64, 0x62, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c,
	0x6f, 0x63, 0x6b, 0x2e, 0x64, 0x65, 0x76, 0x2e, 0x64, 0x62, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f,
	0x63, 0x6b, 0x2e, 0x64, 0x65, 0x76, 0x2e, 0x64, 0x62, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x07, 0x47, 0x65, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x24, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x64, 0x65, 0x76, 0x2e, 0x64, 0x62, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x68, 0x65,
	0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x64, 0x65, 0x76, 0x2e, 0x64, 0x62, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x42, 0x6a, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x6b, 0x79, 0x63, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x76, 0x6b, 0x79,
	0x63, 0x63, 0x61, 0x6c, 0x6c, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_api_vkyccall_developer_service_proto_goTypes = []interface{}{
	(*dbstate.GetEntityListRequest)(nil),     // 0: sherlock.dev.dbstate.GetEntityListRequest
	(*dbstate.GetParameterListRequest)(nil),  // 1: sherlock.dev.dbstate.GetParameterListRequest
	(*dbstate.GetDataRequest)(nil),           // 2: sherlock.dev.dbstate.GetDataRequest
	(*dbstate.GetEntityListResponse)(nil),    // 3: sherlock.dev.dbstate.GetEntityListResponse
	(*dbstate.GetParameterListResponse)(nil), // 4: sherlock.dev.dbstate.GetParameterListResponse
	(*dbstate.GetDataResponse)(nil),          // 5: sherlock.dev.dbstate.GetDataResponse
}
var file_api_vkyccall_developer_service_proto_depIdxs = []int32{
	0, // 0: api.vkyccall.developer.DevVkycCall.GetEntityList:input_type -> sherlock.dev.dbstate.GetEntityListRequest
	1, // 1: api.vkyccall.developer.DevVkycCall.GetParameterList:input_type -> sherlock.dev.dbstate.GetParameterListRequest
	2, // 2: api.vkyccall.developer.DevVkycCall.GetData:input_type -> sherlock.dev.dbstate.GetDataRequest
	3, // 3: api.vkyccall.developer.DevVkycCall.GetEntityList:output_type -> sherlock.dev.dbstate.GetEntityListResponse
	4, // 4: api.vkyccall.developer.DevVkycCall.GetParameterList:output_type -> sherlock.dev.dbstate.GetParameterListResponse
	5, // 5: api.vkyccall.developer.DevVkycCall.GetData:output_type -> sherlock.dev.dbstate.GetDataResponse
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vkyccall_developer_service_proto_init() }
func file_api_vkyccall_developer_service_proto_init() {
	if File_api_vkyccall_developer_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vkyccall_developer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vkyccall_developer_service_proto_goTypes,
		DependencyIndexes: file_api_vkyccall_developer_service_proto_depIdxs,
	}.Build()
	File_api_vkyccall_developer_service_proto = out.File
	file_api_vkyccall_developer_service_proto_rawDesc = nil
	file_api_vkyccall_developer_service_proto_goTypes = nil
	file_api_vkyccall_developer_service_proto_depIdxs = nil
}
