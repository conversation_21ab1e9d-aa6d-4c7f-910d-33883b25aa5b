// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/user/onboarding/pin_code_details.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetPinCodeDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPinCodeDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPinCodeDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPinCodeDetailsRequestMultiError, or nil if none found.
func (m *GetPinCodeDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPinCodeDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPinCode()) != 6 {
		err := GetPinCodeDetailsRequestValidationError{
			field:  "PinCode",
			reason: "value length must be 6 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if len(errors) > 0 {
		return GetPinCodeDetailsRequestMultiError(errors)
	}

	return nil
}

// GetPinCodeDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetPinCodeDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPinCodeDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPinCodeDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPinCodeDetailsRequestMultiError) AllErrors() []error { return m }

// GetPinCodeDetailsRequestValidationError is the validation error returned by
// GetPinCodeDetailsRequest.Validate if the designated constraints aren't met.
type GetPinCodeDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPinCodeDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPinCodeDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPinCodeDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPinCodeDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPinCodeDetailsRequestValidationError) ErrorName() string {
	return "GetPinCodeDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPinCodeDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPinCodeDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPinCodeDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPinCodeDetailsRequestValidationError{}

// Validate checks the field values on GetPinCodeDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPinCodeDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPinCodeDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPinCodeDetailsResponseMultiError, or nil if none found.
func (m *GetPinCodeDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPinCodeDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinCodeDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinCodeDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinCodeDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPinCodeDetailsResponseValidationError{
						field:  fmt.Sprintf("Details[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPinCodeDetailsResponseValidationError{
						field:  fmt.Sprintf("Details[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPinCodeDetailsResponseValidationError{
					field:  fmt.Sprintf("Details[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetPinCodeDetailsResponseMultiError(errors)
	}

	return nil
}

// GetPinCodeDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetPinCodeDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetPinCodeDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPinCodeDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPinCodeDetailsResponseMultiError) AllErrors() []error { return m }

// GetPinCodeDetailsResponseValidationError is the validation error returned by
// GetPinCodeDetailsResponse.Validate if the designated constraints aren't met.
type GetPinCodeDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPinCodeDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPinCodeDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPinCodeDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPinCodeDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPinCodeDetailsResponseValidationError) ErrorName() string {
	return "GetPinCodeDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPinCodeDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPinCodeDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPinCodeDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPinCodeDetailsResponseValidationError{}

// Validate checks the field values on AddPinCodeEntryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPinCodeEntryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPinCodeEntryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddPinCodeEntryRequestMultiError, or nil if none found.
func (m *AddPinCodeEntryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPinCodeEntryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPinCodeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPinCodeEntryRequestValidationError{
					field:  "PinCodeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPinCodeEntryRequestValidationError{
					field:  "PinCodeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPinCodeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPinCodeEntryRequestValidationError{
				field:  "PinCodeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddPinCodeEntryRequestMultiError(errors)
	}

	return nil
}

// AddPinCodeEntryRequestMultiError is an error wrapping multiple validation
// errors returned by AddPinCodeEntryRequest.ValidateAll() if the designated
// constraints aren't met.
type AddPinCodeEntryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPinCodeEntryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPinCodeEntryRequestMultiError) AllErrors() []error { return m }

// AddPinCodeEntryRequestValidationError is the validation error returned by
// AddPinCodeEntryRequest.Validate if the designated constraints aren't met.
type AddPinCodeEntryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPinCodeEntryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPinCodeEntryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPinCodeEntryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPinCodeEntryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPinCodeEntryRequestValidationError) ErrorName() string {
	return "AddPinCodeEntryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddPinCodeEntryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPinCodeEntryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPinCodeEntryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPinCodeEntryRequestValidationError{}

// Validate checks the field values on AddPinCodeEntryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPinCodeEntryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPinCodeEntryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddPinCodeEntryResponseMultiError, or nil if none found.
func (m *AddPinCodeEntryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPinCodeEntryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPinCodeEntryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPinCodeEntryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPinCodeEntryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddPinCodeEntryResponseMultiError(errors)
	}

	return nil
}

// AddPinCodeEntryResponseMultiError is an error wrapping multiple validation
// errors returned by AddPinCodeEntryResponse.ValidateAll() if the designated
// constraints aren't met.
type AddPinCodeEntryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPinCodeEntryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPinCodeEntryResponseMultiError) AllErrors() []error { return m }

// AddPinCodeEntryResponseValidationError is the validation error returned by
// AddPinCodeEntryResponse.Validate if the designated constraints aren't met.
type AddPinCodeEntryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPinCodeEntryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPinCodeEntryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPinCodeEntryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPinCodeEntryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPinCodeEntryResponseValidationError) ErrorName() string {
	return "AddPinCodeEntryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddPinCodeEntryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPinCodeEntryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPinCodeEntryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPinCodeEntryResponseValidationError{}

// Validate checks the field values on PinCodeDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PinCodeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PinCodeDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PinCodeDetailsMultiError,
// or nil if none found.
func (m *PinCodeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PinCodeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PinCode

	// no validation rules for Division

	// no validation rules for Region

	// no validation rules for Circle

	// no validation rules for Taluk

	// no validation rules for District

	// no validation rules for State

	// no validation rules for Longitude

	// no validation rules for Latitude

	// no validation rules for PostOffice

	if len(errors) > 0 {
		return PinCodeDetailsMultiError(errors)
	}

	return nil
}

// PinCodeDetailsMultiError is an error wrapping multiple validation errors
// returned by PinCodeDetails.ValidateAll() if the designated constraints
// aren't met.
type PinCodeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PinCodeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PinCodeDetailsMultiError) AllErrors() []error { return m }

// PinCodeDetailsValidationError is the validation error returned by
// PinCodeDetails.Validate if the designated constraints aren't met.
type PinCodeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PinCodeDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PinCodeDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PinCodeDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PinCodeDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PinCodeDetailsValidationError) ErrorName() string { return "PinCodeDetailsValidationError" }

// Error satisfies the builtin error interface
func (e PinCodeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPinCodeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PinCodeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PinCodeDetailsValidationError{}
