// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/user/contact/user_contact.proto

package contact

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UserContact with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserContact) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserContact with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserContactMultiError, or
// nil if none found.
func (m *UserContact) ValidateAll() error {
	return m.validate(true)
}

func (m *UserContact) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for PhoneNumberHash

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserContactValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserContactValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserContactValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserContactValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserContactValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserContactValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserContactValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserContactValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserContactValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserContactMultiError(errors)
	}

	return nil
}

// UserContactMultiError is an error wrapping multiple validation errors
// returned by UserContact.ValidateAll() if the designated constraints aren't met.
type UserContactMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserContactMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserContactMultiError) AllErrors() []error { return m }

// UserContactValidationError is the validation error returned by
// UserContact.Validate if the designated constraints aren't met.
type UserContactValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserContactValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserContactValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserContactValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserContactValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserContactValidationError) ErrorName() string { return "UserContactValidationError" }

// Error satisfies the builtin error interface
func (e UserContactValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserContact.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserContactValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserContactValidationError{}

// Validate checks the field values on UserContactDevResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserContactDevResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserContactDevResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserContactDevResponseMultiError, or nil if none found.
func (m *UserContactDevResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UserContactDevResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUserContactList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserContactDevResponseValidationError{
						field:  fmt.Sprintf("UserContactList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserContactDevResponseValidationError{
						field:  fmt.Sprintf("UserContactList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserContactDevResponseValidationError{
					field:  fmt.Sprintf("UserContactList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UserContactDevResponseMultiError(errors)
	}

	return nil
}

// UserContactDevResponseMultiError is an error wrapping multiple validation
// errors returned by UserContactDevResponse.ValidateAll() if the designated
// constraints aren't met.
type UserContactDevResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserContactDevResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserContactDevResponseMultiError) AllErrors() []error { return m }

// UserContactDevResponseValidationError is the validation error returned by
// UserContactDevResponse.Validate if the designated constraints aren't met.
type UserContactDevResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserContactDevResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserContactDevResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserContactDevResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserContactDevResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserContactDevResponseValidationError) ErrorName() string {
	return "UserContactDevResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UserContactDevResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserContactDevResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserContactDevResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserContactDevResponseValidationError{}
