// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/stocks/catalog/bridgewise/service.proto

package bridgewise

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	catalog "github.com/epifi/gamma/api/securities/catalog"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = catalog.Exchange(0)
)

// Validate checks the field values on GetAccessTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccessTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccessTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccessTokenRequestMultiError, or nil if none found.
func (m *GetAccessTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccessTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccessTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccessTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccessTokenRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccessTokenRequestMultiError(errors)
	}

	return nil
}

// GetAccessTokenRequestMultiError is an error wrapping multiple validation
// errors returned by GetAccessTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAccessTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccessTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccessTokenRequestMultiError) AllErrors() []error { return m }

// GetAccessTokenRequestValidationError is the validation error returned by
// GetAccessTokenRequest.Validate if the designated constraints aren't met.
type GetAccessTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccessTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccessTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccessTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccessTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccessTokenRequestValidationError) ErrorName() string {
	return "GetAccessTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccessTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccessTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccessTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccessTokenRequestValidationError{}

// Validate checks the field values on GetAccessTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccessTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccessTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccessTokenResponseMultiError, or nil if none found.
func (m *GetAccessTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccessTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccessTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccessTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccessTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessToken

	if all {
		switch v := interface{}(m.GetExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccessTokenResponseValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccessTokenResponseValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccessTokenResponseValidationError{
				field:  "ExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccessTokenResponseMultiError(errors)
	}

	return nil
}

// GetAccessTokenResponseMultiError is an error wrapping multiple validation
// errors returned by GetAccessTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAccessTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccessTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccessTokenResponseMultiError) AllErrors() []error { return m }

// GetAccessTokenResponseValidationError is the validation error returned by
// GetAccessTokenResponse.Validate if the designated constraints aren't met.
type GetAccessTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccessTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccessTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccessTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccessTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccessTokenResponseValidationError) ErrorName() string {
	return "GetAccessTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccessTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccessTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccessTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccessTokenResponseValidationError{}

// Validate checks the field values on GetAssetIdentifierDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAssetIdentifierDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetIdentifierDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAssetIdentifierDetailsRequestMultiError, or nil if none found.
func (m *GetAssetIdentifierDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetIdentifierDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAssetIdentifierDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAssetIdentifierDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAssetIdentifierDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetIdentifierValue()) < 1 {
		err := GetAssetIdentifierDetailsRequestValidationError{
			field:  "IdentifierValue",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := IdentifierType_name[int32(m.GetIdentifierType())]; !ok {
		err := GetAssetIdentifierDetailsRequestValidationError{
			field:  "IdentifierType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAssetIdentifierDetailsRequestMultiError(errors)
	}

	return nil
}

// GetAssetIdentifierDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAssetIdentifierDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAssetIdentifierDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetIdentifierDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetIdentifierDetailsRequestMultiError) AllErrors() []error { return m }

// GetAssetIdentifierDetailsRequestValidationError is the validation error
// returned by GetAssetIdentifierDetailsRequest.Validate if the designated
// constraints aren't met.
type GetAssetIdentifierDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetIdentifierDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetIdentifierDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetIdentifierDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetIdentifierDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetIdentifierDetailsRequestValidationError) ErrorName() string {
	return "GetAssetIdentifierDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetIdentifierDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetIdentifierDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetIdentifierDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetIdentifierDetailsRequestValidationError{}

// Validate checks the field values on GetAssetIdentifierDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAssetIdentifierDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetIdentifierDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAssetIdentifierDetailsResponseMultiError, or nil if none found.
func (m *GetAssetIdentifierDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetIdentifierDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAssetIdentifierDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAssetIdentifierDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAssetIdentifierDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAssetDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAssetIdentifierDetailsResponseValidationError{
						field:  fmt.Sprintf("AssetDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAssetIdentifierDetailsResponseValidationError{
						field:  fmt.Sprintf("AssetDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAssetIdentifierDetailsResponseValidationError{
					field:  fmt.Sprintf("AssetDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAssetIdentifierDetailsResponseMultiError(errors)
	}

	return nil
}

// GetAssetIdentifierDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAssetIdentifierDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAssetIdentifierDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetIdentifierDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetIdentifierDetailsResponseMultiError) AllErrors() []error { return m }

// GetAssetIdentifierDetailsResponseValidationError is the validation error
// returned by GetAssetIdentifierDetailsResponse.Validate if the designated
// constraints aren't met.
type GetAssetIdentifierDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetIdentifierDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetIdentifierDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetIdentifierDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetIdentifierDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetIdentifierDetailsResponseValidationError) ErrorName() string {
	return "GetAssetIdentifierDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetIdentifierDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetIdentifierDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetIdentifierDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetIdentifierDetailsResponseValidationError{}

// Validate checks the field values on GetCompanyRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyRequestMultiError, or nil if none found.
func (m *GetCompanyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCompanyId()) < 1 {
		err := GetCompanyRequestValidationError{
			field:  "CompanyId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCompanyRequestMultiError(errors)
	}

	return nil
}

// GetCompanyRequestMultiError is an error wrapping multiple validation errors
// returned by GetCompanyRequest.ValidateAll() if the designated constraints
// aren't met.
type GetCompanyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyRequestMultiError) AllErrors() []error { return m }

// GetCompanyRequestValidationError is the validation error returned by
// GetCompanyRequest.Validate if the designated constraints aren't met.
type GetCompanyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyRequestValidationError) ErrorName() string {
	return "GetCompanyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyRequestValidationError{}

// Validate checks the field values on GetCompanyResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyResponseMultiError, or nil if none found.
func (m *GetCompanyResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCompanies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCompanyResponseValidationError{
						field:  fmt.Sprintf("Companies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCompanyResponseValidationError{
						field:  fmt.Sprintf("Companies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCompanyResponseValidationError{
					field:  fmt.Sprintf("Companies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCompanyResponseMultiError(errors)
	}

	return nil
}

// GetCompanyResponseMultiError is an error wrapping multiple validation errors
// returned by GetCompanyResponse.ValidateAll() if the designated constraints
// aren't met.
type GetCompanyResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyResponseMultiError) AllErrors() []error { return m }

// GetCompanyResponseValidationError is the validation error returned by
// GetCompanyResponse.Validate if the designated constraints aren't met.
type GetCompanyResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyResponseValidationError) ErrorName() string {
	return "GetCompanyResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyResponseValidationError{}

// Validate checks the field values on GetCompanyFundamentalParametersRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCompanyFundamentalParametersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCompanyFundamentalParametersRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCompanyFundamentalParametersRequestMultiError, or nil if none found.
func (m *GetCompanyFundamentalParametersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyFundamentalParametersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyFundamentalParametersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyFundamentalParametersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyFundamentalParametersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCompanyId()) < 1 {
		err := GetCompanyFundamentalParametersRequestValidationError{
			field:  "CompanyId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CalendarYear

	// no validation rules for CalendarQuarter

	// no validation rules for PeriodType

	if len(errors) > 0 {
		return GetCompanyFundamentalParametersRequestMultiError(errors)
	}

	return nil
}

// GetCompanyFundamentalParametersRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetCompanyFundamentalParametersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyFundamentalParametersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyFundamentalParametersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyFundamentalParametersRequestMultiError) AllErrors() []error { return m }

// GetCompanyFundamentalParametersRequestValidationError is the validation
// error returned by GetCompanyFundamentalParametersRequest.Validate if the
// designated constraints aren't met.
type GetCompanyFundamentalParametersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyFundamentalParametersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyFundamentalParametersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyFundamentalParametersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyFundamentalParametersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyFundamentalParametersRequestValidationError) ErrorName() string {
	return "GetCompanyFundamentalParametersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyFundamentalParametersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyFundamentalParametersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyFundamentalParametersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyFundamentalParametersRequestValidationError{}

// Validate checks the field values on GetCompanyFundamentalParametersResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCompanyFundamentalParametersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCompanyFundamentalParametersResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCompanyFundamentalParametersResponseMultiError, or nil if none found.
func (m *GetCompanyFundamentalParametersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyFundamentalParametersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyFundamentalParametersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyFundamentalParametersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyFundamentalParametersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetParameters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCompanyFundamentalParametersResponseValidationError{
						field:  fmt.Sprintf("Parameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCompanyFundamentalParametersResponseValidationError{
						field:  fmt.Sprintf("Parameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCompanyFundamentalParametersResponseValidationError{
					field:  fmt.Sprintf("Parameters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCompanyFundamentalParametersResponseMultiError(errors)
	}

	return nil
}

// GetCompanyFundamentalParametersResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetCompanyFundamentalParametersResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyFundamentalParametersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyFundamentalParametersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyFundamentalParametersResponseMultiError) AllErrors() []error { return m }

// GetCompanyFundamentalParametersResponseValidationError is the validation
// error returned by GetCompanyFundamentalParametersResponse.Validate if the
// designated constraints aren't met.
type GetCompanyFundamentalParametersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyFundamentalParametersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyFundamentalParametersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyFundamentalParametersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyFundamentalParametersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyFundamentalParametersResponseValidationError) ErrorName() string {
	return "GetCompanyFundamentalParametersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyFundamentalParametersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyFundamentalParametersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyFundamentalParametersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyFundamentalParametersResponseValidationError{}

// Validate checks the field values on GetCompanyFundamentalParagraphsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCompanyFundamentalParagraphsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCompanyFundamentalParagraphsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCompanyFundamentalParagraphsRequestMultiError, or nil if none found.
func (m *GetCompanyFundamentalParagraphsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyFundamentalParagraphsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyFundamentalParagraphsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyFundamentalParagraphsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyFundamentalParagraphsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCompanyId()) < 1 {
		err := GetCompanyFundamentalParagraphsRequestValidationError{
			field:  "CompanyId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCompanyFundamentalParagraphsRequestMultiError(errors)
	}

	return nil
}

// GetCompanyFundamentalParagraphsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetCompanyFundamentalParagraphsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyFundamentalParagraphsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyFundamentalParagraphsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyFundamentalParagraphsRequestMultiError) AllErrors() []error { return m }

// GetCompanyFundamentalParagraphsRequestValidationError is the validation
// error returned by GetCompanyFundamentalParagraphsRequest.Validate if the
// designated constraints aren't met.
type GetCompanyFundamentalParagraphsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyFundamentalParagraphsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyFundamentalParagraphsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyFundamentalParagraphsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyFundamentalParagraphsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyFundamentalParagraphsRequestValidationError) ErrorName() string {
	return "GetCompanyFundamentalParagraphsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyFundamentalParagraphsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyFundamentalParagraphsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyFundamentalParagraphsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyFundamentalParagraphsRequestValidationError{}

// Validate checks the field values on GetCompanyFundamentalParagraphsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCompanyFundamentalParagraphsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCompanyFundamentalParagraphsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCompanyFundamentalParagraphsResponseMultiError, or nil if none found.
func (m *GetCompanyFundamentalParagraphsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyFundamentalParagraphsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyFundamentalParagraphsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyFundamentalParagraphsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyFundamentalParagraphsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetParagraphs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCompanyFundamentalParagraphsResponseValidationError{
						field:  fmt.Sprintf("Paragraphs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCompanyFundamentalParagraphsResponseValidationError{
						field:  fmt.Sprintf("Paragraphs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCompanyFundamentalParagraphsResponseValidationError{
					field:  fmt.Sprintf("Paragraphs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCompanyFundamentalParagraphsResponseMultiError(errors)
	}

	return nil
}

// GetCompanyFundamentalParagraphsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetCompanyFundamentalParagraphsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyFundamentalParagraphsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyFundamentalParagraphsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyFundamentalParagraphsResponseMultiError) AllErrors() []error { return m }

// GetCompanyFundamentalParagraphsResponseValidationError is the validation
// error returned by GetCompanyFundamentalParagraphsResponse.Validate if the
// designated constraints aren't met.
type GetCompanyFundamentalParagraphsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyFundamentalParagraphsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyFundamentalParagraphsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyFundamentalParagraphsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyFundamentalParagraphsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyFundamentalParagraphsResponseValidationError) ErrorName() string {
	return "GetCompanyFundamentalParagraphsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyFundamentalParagraphsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyFundamentalParagraphsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyFundamentalParagraphsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyFundamentalParagraphsResponseValidationError{}

// Validate checks the field values on GetCompanyMarketDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyMarketDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyMarketDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyMarketDataRequestMultiError, or nil if none found.
func (m *GetCompanyMarketDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyMarketDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyMarketDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyMarketDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyMarketDataRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TradingItemId

	if utf8.RuneCountInString(m.GetCompanyId()) < 1 {
		err := GetCompanyMarketDataRequestValidationError{
			field:  "CompanyId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyMarketDataRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyMarketDataRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyMarketDataRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyMarketDataRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyMarketDataRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyMarketDataRequestValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCompanyMarketDataRequestMultiError(errors)
	}

	return nil
}

// GetCompanyMarketDataRequestMultiError is an error wrapping multiple
// validation errors returned by GetCompanyMarketDataRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCompanyMarketDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyMarketDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyMarketDataRequestMultiError) AllErrors() []error { return m }

// GetCompanyMarketDataRequestValidationError is the validation error returned
// by GetCompanyMarketDataRequest.Validate if the designated constraints
// aren't met.
type GetCompanyMarketDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyMarketDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyMarketDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyMarketDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyMarketDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyMarketDataRequestValidationError) ErrorName() string {
	return "GetCompanyMarketDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyMarketDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyMarketDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyMarketDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyMarketDataRequestValidationError{}

// Validate checks the field values on GetCompanyMarketDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyMarketDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyMarketDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyMarketDataResponseMultiError, or nil if none found.
func (m *GetCompanyMarketDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyMarketDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyMarketDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyMarketDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyMarketDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMarketData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCompanyMarketDataResponseValidationError{
						field:  fmt.Sprintf("MarketData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCompanyMarketDataResponseValidationError{
						field:  fmt.Sprintf("MarketData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCompanyMarketDataResponseValidationError{
					field:  fmt.Sprintf("MarketData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCompanyMarketDataResponseMultiError(errors)
	}

	return nil
}

// GetCompanyMarketDataResponseMultiError is an error wrapping multiple
// validation errors returned by GetCompanyMarketDataResponse.ValidateAll() if
// the designated constraints aren't met.
type GetCompanyMarketDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyMarketDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyMarketDataResponseMultiError) AllErrors() []error { return m }

// GetCompanyMarketDataResponseValidationError is the validation error returned
// by GetCompanyMarketDataResponse.Validate if the designated constraints
// aren't met.
type GetCompanyMarketDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyMarketDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyMarketDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyMarketDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyMarketDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyMarketDataResponseValidationError) ErrorName() string {
	return "GetCompanyMarketDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyMarketDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyMarketDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyMarketDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyMarketDataResponseValidationError{}

// Validate checks the field values on GetCompanyLogosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyLogosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyLogosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyLogosRequestMultiError, or nil if none found.
func (m *GetCompanyLogosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyLogosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyLogosRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyLogosRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyLogosRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetCompanyIds()) < 1 {
		err := GetCompanyLogosRequestValidationError{
			field:  "CompanyIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCompanyLogosRequestMultiError(errors)
	}

	return nil
}

// GetCompanyLogosRequestMultiError is an error wrapping multiple validation
// errors returned by GetCompanyLogosRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyLogosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyLogosRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyLogosRequestMultiError) AllErrors() []error { return m }

// GetCompanyLogosRequestValidationError is the validation error returned by
// GetCompanyLogosRequest.Validate if the designated constraints aren't met.
type GetCompanyLogosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyLogosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyLogosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyLogosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyLogosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyLogosRequestValidationError) ErrorName() string {
	return "GetCompanyLogosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyLogosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyLogosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyLogosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyLogosRequestValidationError{}

// Validate checks the field values on GetCompanyLogosResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyLogosResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyLogosResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyLogosResponseMultiError, or nil if none found.
func (m *GetCompanyLogosResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyLogosResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyLogosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyLogosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyLogosResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLogos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCompanyLogosResponseValidationError{
						field:  fmt.Sprintf("Logos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCompanyLogosResponseValidationError{
						field:  fmt.Sprintf("Logos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCompanyLogosResponseValidationError{
					field:  fmt.Sprintf("Logos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCompanyLogosResponseMultiError(errors)
	}

	return nil
}

// GetCompanyLogosResponseMultiError is an error wrapping multiple validation
// errors returned by GetCompanyLogosResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyLogosResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyLogosResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyLogosResponseMultiError) AllErrors() []error { return m }

// GetCompanyLogosResponseValidationError is the validation error returned by
// GetCompanyLogosResponse.Validate if the designated constraints aren't met.
type GetCompanyLogosResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyLogosResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyLogosResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyLogosResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyLogosResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyLogosResponseValidationError) ErrorName() string {
	return "GetCompanyLogosResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyLogosResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyLogosResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyLogosResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyLogosResponseValidationError{}

// Validate checks the field values on GetCompanyMarketStatisticsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCompanyMarketStatisticsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyMarketStatisticsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCompanyMarketStatisticsRequestMultiError, or nil if none found.
func (m *GetCompanyMarketStatisticsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyMarketStatisticsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyMarketStatisticsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyMarketStatisticsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyMarketStatisticsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCompanyId()) < 1 {
		err := GetCompanyMarketStatisticsRequestValidationError{
			field:  "CompanyId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCompanyMarketStatisticsRequestMultiError(errors)
	}

	return nil
}

// GetCompanyMarketStatisticsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCompanyMarketStatisticsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyMarketStatisticsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyMarketStatisticsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyMarketStatisticsRequestMultiError) AllErrors() []error { return m }

// GetCompanyMarketStatisticsRequestValidationError is the validation error
// returned by GetCompanyMarketStatisticsRequest.Validate if the designated
// constraints aren't met.
type GetCompanyMarketStatisticsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyMarketStatisticsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyMarketStatisticsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyMarketStatisticsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyMarketStatisticsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyMarketStatisticsRequestValidationError) ErrorName() string {
	return "GetCompanyMarketStatisticsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyMarketStatisticsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyMarketStatisticsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyMarketStatisticsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyMarketStatisticsRequestValidationError{}

// Validate checks the field values on GetCompanyMarketStatisticsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCompanyMarketStatisticsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyMarketStatisticsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCompanyMarketStatisticsResponseMultiError, or nil if none found.
func (m *GetCompanyMarketStatisticsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyMarketStatisticsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyMarketStatisticsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyMarketStatisticsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyMarketStatisticsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMarketStatistics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCompanyMarketStatisticsResponseValidationError{
						field:  fmt.Sprintf("MarketStatistics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCompanyMarketStatisticsResponseValidationError{
						field:  fmt.Sprintf("MarketStatistics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCompanyMarketStatisticsResponseValidationError{
					field:  fmt.Sprintf("MarketStatistics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCompanyMarketStatisticsResponseMultiError(errors)
	}

	return nil
}

// GetCompanyMarketStatisticsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCompanyMarketStatisticsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyMarketStatisticsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyMarketStatisticsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyMarketStatisticsResponseMultiError) AllErrors() []error { return m }

// GetCompanyMarketStatisticsResponseValidationError is the validation error
// returned by GetCompanyMarketStatisticsResponse.Validate if the designated
// constraints aren't met.
type GetCompanyMarketStatisticsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyMarketStatisticsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyMarketStatisticsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyMarketStatisticsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyMarketStatisticsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyMarketStatisticsResponseValidationError) ErrorName() string {
	return "GetCompanyMarketStatisticsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyMarketStatisticsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyMarketStatisticsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyMarketStatisticsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyMarketStatisticsResponseValidationError{}

// Validate checks the field values on GetCompaniesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompaniesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompaniesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompaniesRequestMultiError, or nil if none found.
func (m *GetCompaniesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompaniesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompaniesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompaniesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompaniesRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PageSize

	// no validation rules for Page

	if len(errors) > 0 {
		return GetCompaniesRequestMultiError(errors)
	}

	return nil
}

// GetCompaniesRequestMultiError is an error wrapping multiple validation
// errors returned by GetCompaniesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCompaniesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompaniesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompaniesRequestMultiError) AllErrors() []error { return m }

// GetCompaniesRequestValidationError is the validation error returned by
// GetCompaniesRequest.Validate if the designated constraints aren't met.
type GetCompaniesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompaniesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompaniesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompaniesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompaniesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompaniesRequestValidationError) ErrorName() string {
	return "GetCompaniesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompaniesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompaniesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompaniesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompaniesRequestValidationError{}

// Validate checks the field values on GetCompaniesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompaniesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompaniesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompaniesResponseMultiError, or nil if none found.
func (m *GetCompaniesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompaniesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompaniesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompaniesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompaniesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompanies()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompaniesResponseValidationError{
					field:  "Companies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompaniesResponseValidationError{
					field:  "Companies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompanies()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompaniesResponseValidationError{
				field:  "Companies",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCompaniesResponseMultiError(errors)
	}

	return nil
}

// GetCompaniesResponseMultiError is an error wrapping multiple validation
// errors returned by GetCompaniesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCompaniesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompaniesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompaniesResponseMultiError) AllErrors() []error { return m }

// GetCompaniesResponseValidationError is the validation error returned by
// GetCompaniesResponse.Validate if the designated constraints aren't met.
type GetCompaniesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompaniesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompaniesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompaniesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompaniesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompaniesResponseValidationError) ErrorName() string {
	return "GetCompaniesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompaniesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompaniesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompaniesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompaniesResponseValidationError{}

// Validate checks the field values on GetCompanyTradingItemsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyTradingItemsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyTradingItemsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCompanyTradingItemsRequestMultiError, or nil if none found.
func (m *GetCompanyTradingItemsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyTradingItemsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyTradingItemsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyTradingItemsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyTradingItemsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCompanyId()) < 1 {
		err := GetCompanyTradingItemsRequestValidationError{
			field:  "CompanyId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCompanyTradingItemsRequestMultiError(errors)
	}

	return nil
}

// GetCompanyTradingItemsRequestMultiError is an error wrapping multiple
// validation errors returned by GetCompanyTradingItemsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetCompanyTradingItemsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyTradingItemsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyTradingItemsRequestMultiError) AllErrors() []error { return m }

// GetCompanyTradingItemsRequestValidationError is the validation error
// returned by GetCompanyTradingItemsRequest.Validate if the designated
// constraints aren't met.
type GetCompanyTradingItemsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyTradingItemsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyTradingItemsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyTradingItemsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyTradingItemsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyTradingItemsRequestValidationError) ErrorName() string {
	return "GetCompanyTradingItemsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyTradingItemsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyTradingItemsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyTradingItemsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyTradingItemsRequestValidationError{}

// Validate checks the field values on GetCompanyTradingItemsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyTradingItemsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyTradingItemsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCompanyTradingItemsResponseMultiError, or nil if none found.
func (m *GetCompanyTradingItemsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyTradingItemsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyTradingItemsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyTradingItemsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyTradingItemsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAssetDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCompanyTradingItemsResponseValidationError{
						field:  fmt.Sprintf("AssetDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCompanyTradingItemsResponseValidationError{
						field:  fmt.Sprintf("AssetDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCompanyTradingItemsResponseValidationError{
					field:  fmt.Sprintf("AssetDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCompanyTradingItemsResponseMultiError(errors)
	}

	return nil
}

// GetCompanyTradingItemsResponseMultiError is an error wrapping multiple
// validation errors returned by GetCompanyTradingItemsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetCompanyTradingItemsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyTradingItemsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyTradingItemsResponseMultiError) AllErrors() []error { return m }

// GetCompanyTradingItemsResponseValidationError is the validation error
// returned by GetCompanyTradingItemsResponse.Validate if the designated
// constraints aren't met.
type GetCompanyTradingItemsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyTradingItemsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyTradingItemsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyTradingItemsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyTradingItemsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyTradingItemsResponseValidationError) ErrorName() string {
	return "GetCompanyTradingItemsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyTradingItemsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyTradingItemsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyTradingItemsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyTradingItemsResponseValidationError{}

// Validate checks the field values on GetFundRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFundRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetFundRequestMultiError,
// or nil if none found.
func (m *GetFundRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetFundId()) < 1 {
		err := GetFundRequestValidationError{
			field:  "FundId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetFundRequestMultiError(errors)
	}

	return nil
}

// GetFundRequestMultiError is an error wrapping multiple validation errors
// returned by GetFundRequest.ValidateAll() if the designated constraints
// aren't met.
type GetFundRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundRequestMultiError) AllErrors() []error { return m }

// GetFundRequestValidationError is the validation error returned by
// GetFundRequest.Validate if the designated constraints aren't met.
type GetFundRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundRequestValidationError) ErrorName() string { return "GetFundRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetFundRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundRequestValidationError{}

// Validate checks the field values on GetFundResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundResponseMultiError, or nil if none found.
func (m *GetFundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFunds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFundResponseValidationError{
						field:  fmt.Sprintf("Funds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFundResponseValidationError{
						field:  fmt.Sprintf("Funds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFundResponseValidationError{
					field:  fmt.Sprintf("Funds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFundResponseMultiError(errors)
	}

	return nil
}

// GetFundResponseMultiError is an error wrapping multiple validation errors
// returned by GetFundResponse.ValidateAll() if the designated constraints
// aren't met.
type GetFundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundResponseMultiError) AllErrors() []error { return m }

// GetFundResponseValidationError is the validation error returned by
// GetFundResponse.Validate if the designated constraints aren't met.
type GetFundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundResponseValidationError) ErrorName() string { return "GetFundResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetFundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundResponseValidationError{}

// Validate checks the field values on GetFundsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFundsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundsRequestMultiError, or nil if none found.
func (m *GetFundsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PageSize

	// no validation rules for Page

	// no validation rules for FilterByAnalytics

	if len(errors) > 0 {
		return GetFundsRequestMultiError(errors)
	}

	return nil
}

// GetFundsRequestMultiError is an error wrapping multiple validation errors
// returned by GetFundsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetFundsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundsRequestMultiError) AllErrors() []error { return m }

// GetFundsRequestValidationError is the validation error returned by
// GetFundsRequest.Validate if the designated constraints aren't met.
type GetFundsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundsRequestValidationError) ErrorName() string { return "GetFundsRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetFundsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundsRequestValidationError{}

// Validate checks the field values on GetFundsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFundsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundsResponseMultiError, or nil if none found.
func (m *GetFundsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFunds()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundsResponseValidationError{
					field:  "Funds",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundsResponseValidationError{
					field:  "Funds",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFunds()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundsResponseValidationError{
				field:  "Funds",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFundsResponseMultiError(errors)
	}

	return nil
}

// GetFundsResponseMultiError is an error wrapping multiple validation errors
// returned by GetFundsResponse.ValidateAll() if the designated constraints
// aren't met.
type GetFundsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundsResponseMultiError) AllErrors() []error { return m }

// GetFundsResponseValidationError is the validation error returned by
// GetFundsResponse.Validate if the designated constraints aren't met.
type GetFundsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundsResponseValidationError) ErrorName() string { return "GetFundsResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetFundsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundsResponseValidationError{}

// Validate checks the field values on GetFundParametersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundParametersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundParametersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundParametersRequestMultiError, or nil if none found.
func (m *GetFundParametersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundParametersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundParametersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundParametersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundParametersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetFundId()) < 1 {
		err := GetFundParametersRequestValidationError{
			field:  "FundId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Year

	// no validation rules for Quarter

	if len(errors) > 0 {
		return GetFundParametersRequestMultiError(errors)
	}

	return nil
}

// GetFundParametersRequestMultiError is an error wrapping multiple validation
// errors returned by GetFundParametersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFundParametersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundParametersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundParametersRequestMultiError) AllErrors() []error { return m }

// GetFundParametersRequestValidationError is the validation error returned by
// GetFundParametersRequest.Validate if the designated constraints aren't met.
type GetFundParametersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundParametersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundParametersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundParametersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundParametersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundParametersRequestValidationError) ErrorName() string {
	return "GetFundParametersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundParametersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundParametersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundParametersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundParametersRequestValidationError{}

// Validate checks the field values on GetFundParametersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundParametersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundParametersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundParametersResponseMultiError, or nil if none found.
func (m *GetFundParametersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundParametersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundParametersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundParametersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundParametersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetParameters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFundParametersResponseValidationError{
						field:  fmt.Sprintf("Parameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFundParametersResponseValidationError{
						field:  fmt.Sprintf("Parameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFundParametersResponseValidationError{
					field:  fmt.Sprintf("Parameters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFundParametersResponseMultiError(errors)
	}

	return nil
}

// GetFundParametersResponseMultiError is an error wrapping multiple validation
// errors returned by GetFundParametersResponse.ValidateAll() if the
// designated constraints aren't met.
type GetFundParametersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundParametersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundParametersResponseMultiError) AllErrors() []error { return m }

// GetFundParametersResponseValidationError is the validation error returned by
// GetFundParametersResponse.Validate if the designated constraints aren't met.
type GetFundParametersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundParametersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundParametersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundParametersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundParametersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundParametersResponseValidationError) ErrorName() string {
	return "GetFundParametersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundParametersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundParametersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundParametersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundParametersResponseValidationError{}

// Validate checks the field values on GetFundParagraphsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundParagraphsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundParagraphsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundParagraphsRequestMultiError, or nil if none found.
func (m *GetFundParagraphsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundParagraphsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundParagraphsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundParagraphsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundParagraphsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetFundId()) < 1 {
		err := GetFundParagraphsRequestValidationError{
			field:  "FundId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetFundParagraphsRequestMultiError(errors)
	}

	return nil
}

// GetFundParagraphsRequestMultiError is an error wrapping multiple validation
// errors returned by GetFundParagraphsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFundParagraphsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundParagraphsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundParagraphsRequestMultiError) AllErrors() []error { return m }

// GetFundParagraphsRequestValidationError is the validation error returned by
// GetFundParagraphsRequest.Validate if the designated constraints aren't met.
type GetFundParagraphsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundParagraphsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundParagraphsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundParagraphsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundParagraphsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundParagraphsRequestValidationError) ErrorName() string {
	return "GetFundParagraphsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundParagraphsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundParagraphsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundParagraphsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundParagraphsRequestValidationError{}

// Validate checks the field values on GetFundParagraphsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundParagraphsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundParagraphsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundParagraphsResponseMultiError, or nil if none found.
func (m *GetFundParagraphsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundParagraphsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundParagraphsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundParagraphsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundParagraphsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetParagraphs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFundParagraphsResponseValidationError{
						field:  fmt.Sprintf("Paragraphs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFundParagraphsResponseValidationError{
						field:  fmt.Sprintf("Paragraphs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFundParagraphsResponseValidationError{
					field:  fmt.Sprintf("Paragraphs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFundParagraphsResponseMultiError(errors)
	}

	return nil
}

// GetFundParagraphsResponseMultiError is an error wrapping multiple validation
// errors returned by GetFundParagraphsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetFundParagraphsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundParagraphsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundParagraphsResponseMultiError) AllErrors() []error { return m }

// GetFundParagraphsResponseValidationError is the validation error returned by
// GetFundParagraphsResponse.Validate if the designated constraints aren't met.
type GetFundParagraphsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundParagraphsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundParagraphsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundParagraphsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundParagraphsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundParagraphsResponseValidationError) ErrorName() string {
	return "GetFundParagraphsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundParagraphsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundParagraphsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundParagraphsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundParagraphsResponseValidationError{}

// Validate checks the field values on GetSegmentsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSegmentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSegmentsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSegmentsRequestMultiError, or nil if none found.
func (m *GetSegmentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSegmentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSegmentsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSegmentsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSegmentsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetFundId()) < 1 {
		err := GetSegmentsRequestValidationError{
			field:  "FundId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSegmentsRequestMultiError(errors)
	}

	return nil
}

// GetSegmentsRequestMultiError is an error wrapping multiple validation errors
// returned by GetSegmentsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSegmentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSegmentsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSegmentsRequestMultiError) AllErrors() []error { return m }

// GetSegmentsRequestValidationError is the validation error returned by
// GetSegmentsRequest.Validate if the designated constraints aren't met.
type GetSegmentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSegmentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSegmentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSegmentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSegmentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSegmentsRequestValidationError) ErrorName() string {
	return "GetSegmentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSegmentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSegmentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSegmentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSegmentsRequestValidationError{}

// Validate checks the field values on GetSegmentsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSegmentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSegmentsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSegmentsResponseMultiError, or nil if none found.
func (m *GetSegmentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSegmentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSegmentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSegmentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSegmentsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSegmentDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSegmentsResponseValidationError{
						field:  fmt.Sprintf("SegmentDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSegmentsResponseValidationError{
						field:  fmt.Sprintf("SegmentDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSegmentsResponseValidationError{
					field:  fmt.Sprintf("SegmentDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSegmentsResponseMultiError(errors)
	}

	return nil
}

// GetSegmentsResponseMultiError is an error wrapping multiple validation
// errors returned by GetSegmentsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSegmentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSegmentsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSegmentsResponseMultiError) AllErrors() []error { return m }

// GetSegmentsResponseValidationError is the validation error returned by
// GetSegmentsResponse.Validate if the designated constraints aren't met.
type GetSegmentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSegmentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSegmentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSegmentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSegmentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSegmentsResponseValidationError) ErrorName() string {
	return "GetSegmentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSegmentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSegmentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSegmentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSegmentsResponseValidationError{}

// Validate checks the field values on GetHoldingsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetHoldingsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHoldingsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetHoldingsRequestMultiError, or nil if none found.
func (m *GetHoldingsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHoldingsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHoldingsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHoldingsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHoldingsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetFundId()) < 1 {
		err := GetHoldingsRequestValidationError{
			field:  "FundId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetHoldingsRequestMultiError(errors)
	}

	return nil
}

// GetHoldingsRequestMultiError is an error wrapping multiple validation errors
// returned by GetHoldingsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetHoldingsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHoldingsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHoldingsRequestMultiError) AllErrors() []error { return m }

// GetHoldingsRequestValidationError is the validation error returned by
// GetHoldingsRequest.Validate if the designated constraints aren't met.
type GetHoldingsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHoldingsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHoldingsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHoldingsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHoldingsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHoldingsRequestValidationError) ErrorName() string {
	return "GetHoldingsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetHoldingsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHoldingsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHoldingsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHoldingsRequestValidationError{}

// Validate checks the field values on GetHoldingsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetHoldingsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHoldingsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetHoldingsResponseMultiError, or nil if none found.
func (m *GetHoldingsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHoldingsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHoldingsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHoldingsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHoldingsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetHoldingDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetHoldingsResponseValidationError{
						field:  fmt.Sprintf("HoldingDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetHoldingsResponseValidationError{
						field:  fmt.Sprintf("HoldingDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetHoldingsResponseValidationError{
					field:  fmt.Sprintf("HoldingDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetHoldingsResponseMultiError(errors)
	}

	return nil
}

// GetHoldingsResponseMultiError is an error wrapping multiple validation
// errors returned by GetHoldingsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetHoldingsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHoldingsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHoldingsResponseMultiError) AllErrors() []error { return m }

// GetHoldingsResponseValidationError is the validation error returned by
// GetHoldingsResponse.Validate if the designated constraints aren't met.
type GetHoldingsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHoldingsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHoldingsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHoldingsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHoldingsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHoldingsResponseValidationError) ErrorName() string {
	return "GetHoldingsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetHoldingsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHoldingsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHoldingsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHoldingsResponseValidationError{}

// Validate checks the field values on GetFundMarketDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundMarketDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundMarketDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundMarketDataRequestMultiError, or nil if none found.
func (m *GetFundMarketDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundMarketDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundMarketDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundMarketDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundMarketDataRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TradingItemId

	if utf8.RuneCountInString(m.GetFundId()) < 1 {
		err := GetFundMarketDataRequestValidationError{
			field:  "FundId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundMarketDataRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundMarketDataRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundMarketDataRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundMarketDataRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundMarketDataRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundMarketDataRequestValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFundMarketDataRequestMultiError(errors)
	}

	return nil
}

// GetFundMarketDataRequestMultiError is an error wrapping multiple validation
// errors returned by GetFundMarketDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFundMarketDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundMarketDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundMarketDataRequestMultiError) AllErrors() []error { return m }

// GetFundMarketDataRequestValidationError is the validation error returned by
// GetFundMarketDataRequest.Validate if the designated constraints aren't met.
type GetFundMarketDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundMarketDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundMarketDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundMarketDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundMarketDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundMarketDataRequestValidationError) ErrorName() string {
	return "GetFundMarketDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundMarketDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundMarketDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundMarketDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundMarketDataRequestValidationError{}

// Validate checks the field values on GetFundMarketDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundMarketDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundMarketDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundMarketDataResponseMultiError, or nil if none found.
func (m *GetFundMarketDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundMarketDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundMarketDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundMarketDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundMarketDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMarketData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFundMarketDataResponseValidationError{
						field:  fmt.Sprintf("MarketData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFundMarketDataResponseValidationError{
						field:  fmt.Sprintf("MarketData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFundMarketDataResponseValidationError{
					field:  fmt.Sprintf("MarketData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFundMarketDataResponseMultiError(errors)
	}

	return nil
}

// GetFundMarketDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetFundMarketDataResponse.ValidateAll() if the
// designated constraints aren't met.
type GetFundMarketDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundMarketDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundMarketDataResponseMultiError) AllErrors() []error { return m }

// GetFundMarketDataResponseValidationError is the validation error returned by
// GetFundMarketDataResponse.Validate if the designated constraints aren't met.
type GetFundMarketDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundMarketDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundMarketDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundMarketDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundMarketDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundMarketDataResponseValidationError) ErrorName() string {
	return "GetFundMarketDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundMarketDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundMarketDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundMarketDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundMarketDataResponseValidationError{}
