// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/lending/preapprovedloan/lenden/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	lenden "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockLendenClient is a mock of LendenClient interface.
type MockLendenClient struct {
	ctrl     *gomock.Controller
	recorder *MockLendenClientMockRecorder
}

// MockLendenClientMockRecorder is the mock recorder for MockLendenClient.
type MockLendenClientMockRecorder struct {
	mock *MockLendenClient
}

// NewMockLendenClient creates a new mock instance.
func NewMockLendenClient(ctrl *gomock.Controller) *MockLendenClient {
	mock := &MockLendenClient{ctrl: ctrl}
	mock.recorder = &MockLendenClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLendenClient) EXPECT() *MockLendenClientMockRecorder {
	return m.recorder
}

// AddBankDetails mocks base method.
func (m *MockLendenClient) AddBankDetails(ctx context.Context, in *lenden.AddBankDetailsRequest, opts ...grpc.CallOption) (*lenden.AddBankDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddBankDetails", varargs...)
	ret0, _ := ret[0].(*lenden.AddBankDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBankDetails indicates an expected call of AddBankDetails.
func (mr *MockLendenClientMockRecorder) AddBankDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBankDetails", reflect.TypeOf((*MockLendenClient)(nil).AddBankDetails), varargs...)
}

// ApplyForLoan mocks base method.
func (m *MockLendenClient) ApplyForLoan(ctx context.Context, in *lenden.ApplyForLoanRequest, opts ...grpc.CallOption) (*lenden.ApplyForLoanResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyForLoan", varargs...)
	ret0, _ := ret[0].(*lenden.ApplyForLoanResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyForLoan indicates an expected call of ApplyForLoan.
func (mr *MockLendenClientMockRecorder) ApplyForLoan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyForLoan", reflect.TypeOf((*MockLendenClient)(nil).ApplyForLoan), varargs...)
}

// CheckHardEligibility mocks base method.
func (m *MockLendenClient) CheckHardEligibility(ctx context.Context, in *lenden.CheckHardEligibilityRequest, opts ...grpc.CallOption) (*lenden.CheckHardEligibilityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckHardEligibility", varargs...)
	ret0, _ := ret[0].(*lenden.CheckHardEligibilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckHardEligibility indicates an expected call of CheckHardEligibility.
func (mr *MockLendenClientMockRecorder) CheckHardEligibility(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHardEligibility", reflect.TypeOf((*MockLendenClient)(nil).CheckHardEligibility), varargs...)
}

// CheckKycStatus mocks base method.
func (m *MockLendenClient) CheckKycStatus(ctx context.Context, in *lenden.CheckKycStatusRequest, opts ...grpc.CallOption) (*lenden.CheckKycStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckKycStatus", varargs...)
	ret0, _ := ret[0].(*lenden.CheckKycStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckKycStatus indicates an expected call of CheckKycStatus.
func (mr *MockLendenClientMockRecorder) CheckKycStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckKycStatus", reflect.TypeOf((*MockLendenClient)(nil).CheckKycStatus), varargs...)
}

// CheckMandateStatus mocks base method.
func (m *MockLendenClient) CheckMandateStatus(ctx context.Context, in *lenden.CheckMandateStatusRequest, opts ...grpc.CallOption) (*lenden.CheckMandateStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckMandateStatus", varargs...)
	ret0, _ := ret[0].(*lenden.CheckMandateStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMandateStatus indicates an expected call of CheckMandateStatus.
func (mr *MockLendenClientMockRecorder) CheckMandateStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMandateStatus", reflect.TypeOf((*MockLendenClient)(nil).CheckMandateStatus), varargs...)
}

// CreateUser mocks base method.
func (m *MockLendenClient) CreateUser(ctx context.Context, in *lenden.CreateUserRequest, opts ...grpc.CallOption) (*lenden.CreateUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateUser", varargs...)
	ret0, _ := ret[0].(*lenden.CreateUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUser indicates an expected call of CreateUser.
func (mr *MockLendenClientMockRecorder) CreateUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUser", reflect.TypeOf((*MockLendenClient)(nil).CreateUser), varargs...)
}

// GenerateKfsLa mocks base method.
func (m *MockLendenClient) GenerateKfsLa(ctx context.Context, in *lenden.GenerateKfsLaRequest, opts ...grpc.CallOption) (*lenden.GenerateKfsLaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateKfsLa", varargs...)
	ret0, _ := ret[0].(*lenden.GenerateKfsLaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateKfsLa indicates an expected call of GenerateKfsLa.
func (mr *MockLendenClientMockRecorder) GenerateKfsLa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateKfsLa", reflect.TypeOf((*MockLendenClient)(nil).GenerateKfsLa), varargs...)
}

// GeneratePaymentLink mocks base method.
func (m *MockLendenClient) GeneratePaymentLink(ctx context.Context, in *lenden.GeneratePaymentLinkRequest, opts ...grpc.CallOption) (*lenden.GeneratePaymentLinkResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePaymentLink", varargs...)
	ret0, _ := ret[0].(*lenden.GeneratePaymentLinkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePaymentLink indicates an expected call of GeneratePaymentLink.
func (mr *MockLendenClientMockRecorder) GeneratePaymentLink(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePaymentLink", reflect.TypeOf((*MockLendenClient)(nil).GeneratePaymentLink), varargs...)
}

// GetAmortizationSchedule mocks base method.
func (m *MockLendenClient) GetAmortizationSchedule(ctx context.Context, in *lenden.GetAmortizationScheduleRequest, opts ...grpc.CallOption) (*lenden.GetAmortizationScheduleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAmortizationSchedule", varargs...)
	ret0, _ := ret[0].(*lenden.GetAmortizationScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmortizationSchedule indicates an expected call of GetAmortizationSchedule.
func (mr *MockLendenClientMockRecorder) GetAmortizationSchedule(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmortizationSchedule", reflect.TypeOf((*MockLendenClient)(nil).GetAmortizationSchedule), varargs...)
}

// GetForeclosureDetails mocks base method.
func (m *MockLendenClient) GetForeclosureDetails(ctx context.Context, in *lenden.GetForeclosureDetailsRequest, opts ...grpc.CallOption) (*lenden.GetForeclosureDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetForeclosureDetails", varargs...)
	ret0, _ := ret[0].(*lenden.GetForeclosureDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForeclosureDetails indicates an expected call of GetForeclosureDetails.
func (mr *MockLendenClientMockRecorder) GetForeclosureDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForeclosureDetails", reflect.TypeOf((*MockLendenClient)(nil).GetForeclosureDetails), varargs...)
}

// GetLoanDetails mocks base method.
func (m *MockLendenClient) GetLoanDetails(ctx context.Context, in *lenden.GetLoanDetailsRequest, opts ...grpc.CallOption) (*lenden.GetLoanDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanDetails", varargs...)
	ret0, _ := ret[0].(*lenden.GetLoanDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanDetails indicates an expected call of GetLoanDetails.
func (mr *MockLendenClientMockRecorder) GetLoanDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanDetails", reflect.TypeOf((*MockLendenClient)(nil).GetLoanDetails), varargs...)
}

// GetPaymentStatus mocks base method.
func (m *MockLendenClient) GetPaymentStatus(ctx context.Context, in *lenden.GetPaymentStatusRequest, opts ...grpc.CallOption) (*lenden.GetPaymentStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPaymentStatus", varargs...)
	ret0, _ := ret[0].(*lenden.GetPaymentStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentStatus indicates an expected call of GetPaymentStatus.
func (mr *MockLendenClientMockRecorder) GetPaymentStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentStatus", reflect.TypeOf((*MockLendenClient)(nil).GetPaymentStatus), varargs...)
}

// GetPreDisbursementDetails mocks base method.
func (m *MockLendenClient) GetPreDisbursementDetails(ctx context.Context, in *lenden.GetPreDisbursementDetailsRequest, opts ...grpc.CallOption) (*lenden.GetPreDisbursementDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPreDisbursementDetails", varargs...)
	ret0, _ := ret[0].(*lenden.GetPreDisbursementDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreDisbursementDetails indicates an expected call of GetPreDisbursementDetails.
func (mr *MockLendenClientMockRecorder) GetPreDisbursementDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreDisbursementDetails", reflect.TypeOf((*MockLendenClient)(nil).GetPreDisbursementDetails), varargs...)
}

// InitMandate mocks base method.
func (m *MockLendenClient) InitMandate(ctx context.Context, in *lenden.InitMandateRequest, opts ...grpc.CallOption) (*lenden.InitMandateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitMandate", varargs...)
	ret0, _ := ret[0].(*lenden.InitMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitMandate indicates an expected call of InitMandate.
func (mr *MockLendenClientMockRecorder) InitMandate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitMandate", reflect.TypeOf((*MockLendenClient)(nil).InitMandate), varargs...)
}

// KycInit mocks base method.
func (m *MockLendenClient) KycInit(ctx context.Context, in *lenden.KycInitRequest, opts ...grpc.CallOption) (*lenden.KycInitResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "KycInit", varargs...)
	ret0, _ := ret[0].(*lenden.KycInitResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KycInit indicates an expected call of KycInit.
func (mr *MockLendenClientMockRecorder) KycInit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KycInit", reflect.TypeOf((*MockLendenClient)(nil).KycInit), varargs...)
}

// ModifyRateOfInterest mocks base method.
func (m *MockLendenClient) ModifyRateOfInterest(ctx context.Context, in *lenden.ModifyRateOfInterestRequest, opts ...grpc.CallOption) (*lenden.ModifyRateOfInterestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyRateOfInterest", varargs...)
	ret0, _ := ret[0].(*lenden.ModifyRateOfInterestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyRateOfInterest indicates an expected call of ModifyRateOfInterest.
func (mr *MockLendenClientMockRecorder) ModifyRateOfInterest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyRateOfInterest", reflect.TypeOf((*MockLendenClient)(nil).ModifyRateOfInterest), varargs...)
}

// PostExternalData mocks base method.
func (m *MockLendenClient) PostExternalData(ctx context.Context, in *lenden.PostExternalDataRequest, opts ...grpc.CallOption) (*lenden.PostExternalDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostExternalData", varargs...)
	ret0, _ := ret[0].(*lenden.PostExternalDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostExternalData indicates an expected call of PostExternalData.
func (mr *MockLendenClientMockRecorder) PostExternalData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostExternalData", reflect.TypeOf((*MockLendenClient)(nil).PostExternalData), varargs...)
}

// SelectOffer mocks base method.
func (m *MockLendenClient) SelectOffer(ctx context.Context, in *lenden.SelectOfferRequest, opts ...grpc.CallOption) (*lenden.SelectOfferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SelectOffer", varargs...)
	ret0, _ := ret[0].(*lenden.SelectOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectOffer indicates an expected call of SelectOffer.
func (mr *MockLendenClientMockRecorder) SelectOffer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectOffer", reflect.TypeOf((*MockLendenClient)(nil).SelectOffer), varargs...)
}

// SignKfsLa mocks base method.
func (m *MockLendenClient) SignKfsLa(ctx context.Context, in *lenden.SignKfsLaRequest, opts ...grpc.CallOption) (*lenden.SignKfsLaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SignKfsLa", varargs...)
	ret0, _ := ret[0].(*lenden.SignKfsLaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignKfsLa indicates an expected call of SignKfsLa.
func (mr *MockLendenClientMockRecorder) SignKfsLa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignKfsLa", reflect.TypeOf((*MockLendenClient)(nil).SignKfsLa), varargs...)
}

// MockLendenServer is a mock of LendenServer interface.
type MockLendenServer struct {
	ctrl     *gomock.Controller
	recorder *MockLendenServerMockRecorder
}

// MockLendenServerMockRecorder is the mock recorder for MockLendenServer.
type MockLendenServerMockRecorder struct {
	mock *MockLendenServer
}

// NewMockLendenServer creates a new mock instance.
func NewMockLendenServer(ctrl *gomock.Controller) *MockLendenServer {
	mock := &MockLendenServer{ctrl: ctrl}
	mock.recorder = &MockLendenServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLendenServer) EXPECT() *MockLendenServerMockRecorder {
	return m.recorder
}

// AddBankDetails mocks base method.
func (m *MockLendenServer) AddBankDetails(arg0 context.Context, arg1 *lenden.AddBankDetailsRequest) (*lenden.AddBankDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBankDetails", arg0, arg1)
	ret0, _ := ret[0].(*lenden.AddBankDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBankDetails indicates an expected call of AddBankDetails.
func (mr *MockLendenServerMockRecorder) AddBankDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBankDetails", reflect.TypeOf((*MockLendenServer)(nil).AddBankDetails), arg0, arg1)
}

// ApplyForLoan mocks base method.
func (m *MockLendenServer) ApplyForLoan(arg0 context.Context, arg1 *lenden.ApplyForLoanRequest) (*lenden.ApplyForLoanResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyForLoan", arg0, arg1)
	ret0, _ := ret[0].(*lenden.ApplyForLoanResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyForLoan indicates an expected call of ApplyForLoan.
func (mr *MockLendenServerMockRecorder) ApplyForLoan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyForLoan", reflect.TypeOf((*MockLendenServer)(nil).ApplyForLoan), arg0, arg1)
}

// CheckHardEligibility mocks base method.
func (m *MockLendenServer) CheckHardEligibility(arg0 context.Context, arg1 *lenden.CheckHardEligibilityRequest) (*lenden.CheckHardEligibilityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHardEligibility", arg0, arg1)
	ret0, _ := ret[0].(*lenden.CheckHardEligibilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckHardEligibility indicates an expected call of CheckHardEligibility.
func (mr *MockLendenServerMockRecorder) CheckHardEligibility(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHardEligibility", reflect.TypeOf((*MockLendenServer)(nil).CheckHardEligibility), arg0, arg1)
}

// CheckKycStatus mocks base method.
func (m *MockLendenServer) CheckKycStatus(arg0 context.Context, arg1 *lenden.CheckKycStatusRequest) (*lenden.CheckKycStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckKycStatus", arg0, arg1)
	ret0, _ := ret[0].(*lenden.CheckKycStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckKycStatus indicates an expected call of CheckKycStatus.
func (mr *MockLendenServerMockRecorder) CheckKycStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckKycStatus", reflect.TypeOf((*MockLendenServer)(nil).CheckKycStatus), arg0, arg1)
}

// CheckMandateStatus mocks base method.
func (m *MockLendenServer) CheckMandateStatus(arg0 context.Context, arg1 *lenden.CheckMandateStatusRequest) (*lenden.CheckMandateStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckMandateStatus", arg0, arg1)
	ret0, _ := ret[0].(*lenden.CheckMandateStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMandateStatus indicates an expected call of CheckMandateStatus.
func (mr *MockLendenServerMockRecorder) CheckMandateStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMandateStatus", reflect.TypeOf((*MockLendenServer)(nil).CheckMandateStatus), arg0, arg1)
}

// CreateUser mocks base method.
func (m *MockLendenServer) CreateUser(arg0 context.Context, arg1 *lenden.CreateUserRequest) (*lenden.CreateUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUser", arg0, arg1)
	ret0, _ := ret[0].(*lenden.CreateUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUser indicates an expected call of CreateUser.
func (mr *MockLendenServerMockRecorder) CreateUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUser", reflect.TypeOf((*MockLendenServer)(nil).CreateUser), arg0, arg1)
}

// GenerateKfsLa mocks base method.
func (m *MockLendenServer) GenerateKfsLa(arg0 context.Context, arg1 *lenden.GenerateKfsLaRequest) (*lenden.GenerateKfsLaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateKfsLa", arg0, arg1)
	ret0, _ := ret[0].(*lenden.GenerateKfsLaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateKfsLa indicates an expected call of GenerateKfsLa.
func (mr *MockLendenServerMockRecorder) GenerateKfsLa(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateKfsLa", reflect.TypeOf((*MockLendenServer)(nil).GenerateKfsLa), arg0, arg1)
}

// GeneratePaymentLink mocks base method.
func (m *MockLendenServer) GeneratePaymentLink(arg0 context.Context, arg1 *lenden.GeneratePaymentLinkRequest) (*lenden.GeneratePaymentLinkResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeneratePaymentLink", arg0, arg1)
	ret0, _ := ret[0].(*lenden.GeneratePaymentLinkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePaymentLink indicates an expected call of GeneratePaymentLink.
func (mr *MockLendenServerMockRecorder) GeneratePaymentLink(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePaymentLink", reflect.TypeOf((*MockLendenServer)(nil).GeneratePaymentLink), arg0, arg1)
}

// GetAmortizationSchedule mocks base method.
func (m *MockLendenServer) GetAmortizationSchedule(arg0 context.Context, arg1 *lenden.GetAmortizationScheduleRequest) (*lenden.GetAmortizationScheduleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmortizationSchedule", arg0, arg1)
	ret0, _ := ret[0].(*lenden.GetAmortizationScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmortizationSchedule indicates an expected call of GetAmortizationSchedule.
func (mr *MockLendenServerMockRecorder) GetAmortizationSchedule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmortizationSchedule", reflect.TypeOf((*MockLendenServer)(nil).GetAmortizationSchedule), arg0, arg1)
}

// GetForeclosureDetails mocks base method.
func (m *MockLendenServer) GetForeclosureDetails(arg0 context.Context, arg1 *lenden.GetForeclosureDetailsRequest) (*lenden.GetForeclosureDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForeclosureDetails", arg0, arg1)
	ret0, _ := ret[0].(*lenden.GetForeclosureDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForeclosureDetails indicates an expected call of GetForeclosureDetails.
func (mr *MockLendenServerMockRecorder) GetForeclosureDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForeclosureDetails", reflect.TypeOf((*MockLendenServer)(nil).GetForeclosureDetails), arg0, arg1)
}

// GetLoanDetails mocks base method.
func (m *MockLendenServer) GetLoanDetails(arg0 context.Context, arg1 *lenden.GetLoanDetailsRequest) (*lenden.GetLoanDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanDetails", arg0, arg1)
	ret0, _ := ret[0].(*lenden.GetLoanDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanDetails indicates an expected call of GetLoanDetails.
func (mr *MockLendenServerMockRecorder) GetLoanDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanDetails", reflect.TypeOf((*MockLendenServer)(nil).GetLoanDetails), arg0, arg1)
}

// GetPaymentStatus mocks base method.
func (m *MockLendenServer) GetPaymentStatus(arg0 context.Context, arg1 *lenden.GetPaymentStatusRequest) (*lenden.GetPaymentStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentStatus", arg0, arg1)
	ret0, _ := ret[0].(*lenden.GetPaymentStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentStatus indicates an expected call of GetPaymentStatus.
func (mr *MockLendenServerMockRecorder) GetPaymentStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentStatus", reflect.TypeOf((*MockLendenServer)(nil).GetPaymentStatus), arg0, arg1)
}

// GetPreDisbursementDetails mocks base method.
func (m *MockLendenServer) GetPreDisbursementDetails(arg0 context.Context, arg1 *lenden.GetPreDisbursementDetailsRequest) (*lenden.GetPreDisbursementDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreDisbursementDetails", arg0, arg1)
	ret0, _ := ret[0].(*lenden.GetPreDisbursementDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreDisbursementDetails indicates an expected call of GetPreDisbursementDetails.
func (mr *MockLendenServerMockRecorder) GetPreDisbursementDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreDisbursementDetails", reflect.TypeOf((*MockLendenServer)(nil).GetPreDisbursementDetails), arg0, arg1)
}

// InitMandate mocks base method.
func (m *MockLendenServer) InitMandate(arg0 context.Context, arg1 *lenden.InitMandateRequest) (*lenden.InitMandateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitMandate", arg0, arg1)
	ret0, _ := ret[0].(*lenden.InitMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitMandate indicates an expected call of InitMandate.
func (mr *MockLendenServerMockRecorder) InitMandate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitMandate", reflect.TypeOf((*MockLendenServer)(nil).InitMandate), arg0, arg1)
}

// KycInit mocks base method.
func (m *MockLendenServer) KycInit(arg0 context.Context, arg1 *lenden.KycInitRequest) (*lenden.KycInitResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KycInit", arg0, arg1)
	ret0, _ := ret[0].(*lenden.KycInitResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KycInit indicates an expected call of KycInit.
func (mr *MockLendenServerMockRecorder) KycInit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KycInit", reflect.TypeOf((*MockLendenServer)(nil).KycInit), arg0, arg1)
}

// ModifyRateOfInterest mocks base method.
func (m *MockLendenServer) ModifyRateOfInterest(arg0 context.Context, arg1 *lenden.ModifyRateOfInterestRequest) (*lenden.ModifyRateOfInterestResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyRateOfInterest", arg0, arg1)
	ret0, _ := ret[0].(*lenden.ModifyRateOfInterestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyRateOfInterest indicates an expected call of ModifyRateOfInterest.
func (mr *MockLendenServerMockRecorder) ModifyRateOfInterest(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyRateOfInterest", reflect.TypeOf((*MockLendenServer)(nil).ModifyRateOfInterest), arg0, arg1)
}

// PostExternalData mocks base method.
func (m *MockLendenServer) PostExternalData(arg0 context.Context, arg1 *lenden.PostExternalDataRequest) (*lenden.PostExternalDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PostExternalData", arg0, arg1)
	ret0, _ := ret[0].(*lenden.PostExternalDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostExternalData indicates an expected call of PostExternalData.
func (mr *MockLendenServerMockRecorder) PostExternalData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostExternalData", reflect.TypeOf((*MockLendenServer)(nil).PostExternalData), arg0, arg1)
}

// SelectOffer mocks base method.
func (m *MockLendenServer) SelectOffer(arg0 context.Context, arg1 *lenden.SelectOfferRequest) (*lenden.SelectOfferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectOffer", arg0, arg1)
	ret0, _ := ret[0].(*lenden.SelectOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectOffer indicates an expected call of SelectOffer.
func (mr *MockLendenServerMockRecorder) SelectOffer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectOffer", reflect.TypeOf((*MockLendenServer)(nil).SelectOffer), arg0, arg1)
}

// SignKfsLa mocks base method.
func (m *MockLendenServer) SignKfsLa(arg0 context.Context, arg1 *lenden.SignKfsLaRequest) (*lenden.SignKfsLaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignKfsLa", arg0, arg1)
	ret0, _ := ret[0].(*lenden.SignKfsLaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignKfsLa indicates an expected call of SignKfsLa.
func (mr *MockLendenServerMockRecorder) SignKfsLa(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignKfsLa", reflect.TypeOf((*MockLendenServer)(nil).SignKfsLa), arg0, arg1)
}

// MockUnsafeLendenServer is a mock of UnsafeLendenServer interface.
type MockUnsafeLendenServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLendenServerMockRecorder
}

// MockUnsafeLendenServerMockRecorder is the mock recorder for MockUnsafeLendenServer.
type MockUnsafeLendenServerMockRecorder struct {
	mock *MockUnsafeLendenServer
}

// NewMockUnsafeLendenServer creates a new mock instance.
func NewMockUnsafeLendenServer(ctrl *gomock.Controller) *MockUnsafeLendenServer {
	mock := &MockUnsafeLendenServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLendenServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLendenServer) EXPECT() *MockUnsafeLendenServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLendenServer mocks base method.
func (m *MockUnsafeLendenServer) mustEmbedUnimplementedLendenServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLendenServer")
}

// mustEmbedUnimplementedLendenServer indicates an expected call of mustEmbedUnimplementedLendenServer.
func (mr *MockUnsafeLendenServerMockRecorder) mustEmbedUnimplementedLendenServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLendenServer", reflect.TypeOf((*MockUnsafeLendenServer)(nil).mustEmbedUnimplementedLendenServer))
}
