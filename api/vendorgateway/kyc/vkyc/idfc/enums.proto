syntax = "proto3";

package vendorgateway.kyc.vkyc.idfc;

option go_package = "github.com/epifi/gamma/api/vendorgateway/kyc/vkyc/idfc";
option java_package = "com.github.epifi.gamma.api.vendorgateway.kyc.vkyc.idfc";

enum ProfileStatusReviewerAction {
  PROFILE_STATUS_REVIEWER_ACTION_UNSPECIFIED = 0;
  PROFILE_STATUS_REVIEWER_ACTION_APPROVED = 1;
  PROFILE_STATUS_REVIEWER_ACTION_REJECTED = 2;
  PROFILE_STATUS_REVIEWER_ACTION_NULL = 3;
  PROFILE_STATUS_REVIEWER_ACTION_REVIEW_REQUIRED = 4;
}
