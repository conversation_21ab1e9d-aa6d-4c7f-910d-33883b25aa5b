// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/gplace/service.proto

package gplace

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetPlaceDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlaceDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlaceDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlaceDetailsRequestMultiError, or nil if none found.
func (m *GetPlaceDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlaceDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPlaceDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPlaceDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPlaceDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlaceId

	if len(errors) > 0 {
		return GetPlaceDetailsRequestMultiError(errors)
	}

	return nil
}

// GetPlaceDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetPlaceDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPlaceDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlaceDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlaceDetailsRequestMultiError) AllErrors() []error { return m }

// GetPlaceDetailsRequestValidationError is the validation error returned by
// GetPlaceDetailsRequest.Validate if the designated constraints aren't met.
type GetPlaceDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlaceDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlaceDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlaceDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlaceDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlaceDetailsRequestValidationError) ErrorName() string {
	return "GetPlaceDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlaceDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlaceDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlaceDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlaceDetailsRequestValidationError{}

// Validate checks the field values on GetPlaceDetailsResponseResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlaceDetailsResponseResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlaceDetailsResponseResult with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPlaceDetailsResponseResultMultiError, or nil if none found.
func (m *GetPlaceDetailsResponseResult) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlaceDetailsResponseResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BusinessStatus

	// no validation rules for FormattedAddress

	// no validation rules for Icon

	// no validation rules for Name

	// no validation rules for PlaceId

	if len(errors) > 0 {
		return GetPlaceDetailsResponseResultMultiError(errors)
	}

	return nil
}

// GetPlaceDetailsResponseResultMultiError is an error wrapping multiple
// validation errors returned by GetPlaceDetailsResponseResult.ValidateAll()
// if the designated constraints aren't met.
type GetPlaceDetailsResponseResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlaceDetailsResponseResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlaceDetailsResponseResultMultiError) AllErrors() []error { return m }

// GetPlaceDetailsResponseResultValidationError is the validation error
// returned by GetPlaceDetailsResponseResult.Validate if the designated
// constraints aren't met.
type GetPlaceDetailsResponseResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlaceDetailsResponseResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlaceDetailsResponseResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlaceDetailsResponseResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlaceDetailsResponseResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlaceDetailsResponseResultValidationError) ErrorName() string {
	return "GetPlaceDetailsResponseResultValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlaceDetailsResponseResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlaceDetailsResponseResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlaceDetailsResponseResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlaceDetailsResponseResultValidationError{}

// Validate checks the field values on GetPlaceDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlaceDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlaceDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlaceDetailsResponseMultiError, or nil if none found.
func (m *GetPlaceDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlaceDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPlaceDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPlaceDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPlaceDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPlaceDetailsResponseValidationError{
					field:  "Result",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPlaceDetailsResponseValidationError{
					field:  "Result",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPlaceDetailsResponseValidationError{
				field:  "Result",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPlaceDetailsResponseMultiError(errors)
	}

	return nil
}

// GetPlaceDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetPlaceDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPlaceDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlaceDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlaceDetailsResponseMultiError) AllErrors() []error { return m }

// GetPlaceDetailsResponseValidationError is the validation error returned by
// GetPlaceDetailsResponse.Validate if the designated constraints aren't met.
type GetPlaceDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlaceDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlaceDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlaceDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlaceDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlaceDetailsResponseValidationError) ErrorName() string {
	return "GetPlaceDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlaceDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlaceDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlaceDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlaceDetailsResponseValidationError{}

// Validate checks the field values on FindPlaceRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FindPlaceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FindPlaceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FindPlaceRequestMultiError, or nil if none found.
func (m *FindPlaceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FindPlaceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FindPlaceRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FindPlaceRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FindPlaceRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Input

	// no validation rules for InputType

	if all {
		switch v := interface{}(m.GetLocationBias()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FindPlaceRequestValidationError{
					field:  "LocationBias",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FindPlaceRequestValidationError{
					field:  "LocationBias",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLocationBias()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FindPlaceRequestValidationError{
				field:  "LocationBias",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FindPlaceRequestMultiError(errors)
	}

	return nil
}

// FindPlaceRequestMultiError is an error wrapping multiple validation errors
// returned by FindPlaceRequest.ValidateAll() if the designated constraints
// aren't met.
type FindPlaceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FindPlaceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FindPlaceRequestMultiError) AllErrors() []error { return m }

// FindPlaceRequestValidationError is the validation error returned by
// FindPlaceRequest.Validate if the designated constraints aren't met.
type FindPlaceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FindPlaceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FindPlaceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FindPlaceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FindPlaceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FindPlaceRequestValidationError) ErrorName() string { return "FindPlaceRequestValidationError" }

// Error satisfies the builtin error interface
func (e FindPlaceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFindPlaceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FindPlaceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FindPlaceRequestValidationError{}

// Validate checks the field values on FindPlaceResponseCandidate with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FindPlaceResponseCandidate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FindPlaceResponseCandidate with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FindPlaceResponseCandidateMultiError, or nil if none found.
func (m *FindPlaceResponseCandidate) ValidateAll() error {
	return m.validate(true)
}

func (m *FindPlaceResponseCandidate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BusinessStatus

	// no validation rules for FormattedAddress

	// no validation rules for IconUrl

	// no validation rules for Name

	// no validation rules for PlaceId

	// no validation rules for IconMaskBaseUri

	// no validation rules for IconBackgroundColor

	if all {
		switch v := interface{}(m.GetGeometry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FindPlaceResponseCandidateValidationError{
					field:  "Geometry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FindPlaceResponseCandidateValidationError{
					field:  "Geometry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeometry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FindPlaceResponseCandidateValidationError{
				field:  "Geometry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FindPlaceResponseCandidateMultiError(errors)
	}

	return nil
}

// FindPlaceResponseCandidateMultiError is an error wrapping multiple
// validation errors returned by FindPlaceResponseCandidate.ValidateAll() if
// the designated constraints aren't met.
type FindPlaceResponseCandidateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FindPlaceResponseCandidateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FindPlaceResponseCandidateMultiError) AllErrors() []error { return m }

// FindPlaceResponseCandidateValidationError is the validation error returned
// by FindPlaceResponseCandidate.Validate if the designated constraints aren't met.
type FindPlaceResponseCandidateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FindPlaceResponseCandidateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FindPlaceResponseCandidateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FindPlaceResponseCandidateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FindPlaceResponseCandidateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FindPlaceResponseCandidateValidationError) ErrorName() string {
	return "FindPlaceResponseCandidateValidationError"
}

// Error satisfies the builtin error interface
func (e FindPlaceResponseCandidateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFindPlaceResponseCandidate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FindPlaceResponseCandidateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FindPlaceResponseCandidateValidationError{}

// Validate checks the field values on FindPlaceResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FindPlaceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FindPlaceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FindPlaceResponseMultiError, or nil if none found.
func (m *FindPlaceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FindPlaceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FindPlaceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FindPlaceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FindPlaceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCandidates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FindPlaceResponseValidationError{
						field:  fmt.Sprintf("Candidates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FindPlaceResponseValidationError{
						field:  fmt.Sprintf("Candidates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FindPlaceResponseValidationError{
					field:  fmt.Sprintf("Candidates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FindPlaceResponseMultiError(errors)
	}

	return nil
}

// FindPlaceResponseMultiError is an error wrapping multiple validation errors
// returned by FindPlaceResponse.ValidateAll() if the designated constraints
// aren't met.
type FindPlaceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FindPlaceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FindPlaceResponseMultiError) AllErrors() []error { return m }

// FindPlaceResponseValidationError is the validation error returned by
// FindPlaceResponse.Validate if the designated constraints aren't met.
type FindPlaceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FindPlaceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FindPlaceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FindPlaceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FindPlaceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FindPlaceResponseValidationError) ErrorName() string {
	return "FindPlaceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FindPlaceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFindPlaceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FindPlaceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FindPlaceResponseValidationError{}
