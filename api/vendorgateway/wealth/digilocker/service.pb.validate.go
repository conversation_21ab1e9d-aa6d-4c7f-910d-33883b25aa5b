// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/wealth/digilocker/service.proto

package digilocker

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = typesv2.Gender(0)
)

// Validate checks the field values on GetAccessTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccessTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccessTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccessTokenRequestMultiError, or nil if none found.
func (m *GetAccessTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccessTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccessTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccessTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccessTokenRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AuthCode

	// no validation rules for ActorId

	switch v := m.AccessRoute.(type) {
	case *GetAccessTokenRequest_AuthorizationCode:
		if v == nil {
			err := GetAccessTokenRequestValidationError{
				field:  "AccessRoute",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AuthorizationCode
	case *GetAccessTokenRequest_RefreshToken:
		if v == nil {
			err := GetAccessTokenRequestValidationError{
				field:  "AccessRoute",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for RefreshToken
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAccessTokenRequestMultiError(errors)
	}

	return nil
}

// GetAccessTokenRequestMultiError is an error wrapping multiple validation
// errors returned by GetAccessTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAccessTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccessTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccessTokenRequestMultiError) AllErrors() []error { return m }

// GetAccessTokenRequestValidationError is the validation error returned by
// GetAccessTokenRequest.Validate if the designated constraints aren't met.
type GetAccessTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccessTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccessTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccessTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccessTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccessTokenRequestValidationError) ErrorName() string {
	return "GetAccessTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccessTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccessTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccessTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccessTokenRequestValidationError{}

// Validate checks the field values on GetAccessTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccessTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccessTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccessTokenResponseMultiError, or nil if none found.
func (m *GetAccessTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccessTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccessTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccessTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccessTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTokenResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccessTokenResponseValidationError{
					field:  "TokenResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccessTokenResponseValidationError{
					field:  "TokenResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTokenResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccessTokenResponseValidationError{
				field:  "TokenResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccessTokenResponseMultiError(errors)
	}

	return nil
}

// GetAccessTokenResponseMultiError is an error wrapping multiple validation
// errors returned by GetAccessTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAccessTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccessTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccessTokenResponseMultiError) AllErrors() []error { return m }

// GetAccessTokenResponseValidationError is the validation error returned by
// GetAccessTokenResponse.Validate if the designated constraints aren't met.
type GetAccessTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccessTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccessTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccessTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccessTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccessTokenResponseValidationError) ErrorName() string {
	return "GetAccessTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccessTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccessTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccessTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccessTokenResponseValidationError{}

// Validate checks the field values on GetListOfIssuedDocumentsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetListOfIssuedDocumentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetListOfIssuedDocumentsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetListOfIssuedDocumentsRequestMultiError, or nil if none found.
func (m *GetListOfIssuedDocumentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetListOfIssuedDocumentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetListOfIssuedDocumentsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetListOfIssuedDocumentsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetListOfIssuedDocumentsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessToken

	if len(errors) > 0 {
		return GetListOfIssuedDocumentsRequestMultiError(errors)
	}

	return nil
}

// GetListOfIssuedDocumentsRequestMultiError is an error wrapping multiple
// validation errors returned by GetListOfIssuedDocumentsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetListOfIssuedDocumentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetListOfIssuedDocumentsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetListOfIssuedDocumentsRequestMultiError) AllErrors() []error { return m }

// GetListOfIssuedDocumentsRequestValidationError is the validation error
// returned by GetListOfIssuedDocumentsRequest.Validate if the designated
// constraints aren't met.
type GetListOfIssuedDocumentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetListOfIssuedDocumentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetListOfIssuedDocumentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetListOfIssuedDocumentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetListOfIssuedDocumentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetListOfIssuedDocumentsRequestValidationError) ErrorName() string {
	return "GetListOfIssuedDocumentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetListOfIssuedDocumentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetListOfIssuedDocumentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetListOfIssuedDocumentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetListOfIssuedDocumentsRequestValidationError{}

// Validate checks the field values on GetListOfIssuedDocumentsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetListOfIssuedDocumentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetListOfIssuedDocumentsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetListOfIssuedDocumentsResponseMultiError, or nil if none found.
func (m *GetListOfIssuedDocumentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetListOfIssuedDocumentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetListOfIssuedDocumentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetListOfIssuedDocumentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetListOfIssuedDocumentsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDocList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetListOfIssuedDocumentsResponseValidationError{
						field:  fmt.Sprintf("DocList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetListOfIssuedDocumentsResponseValidationError{
						field:  fmt.Sprintf("DocList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetListOfIssuedDocumentsResponseValidationError{
					field:  fmt.Sprintf("DocList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetListOfIssuedDocumentsResponseMultiError(errors)
	}

	return nil
}

// GetListOfIssuedDocumentsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetListOfIssuedDocumentsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetListOfIssuedDocumentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetListOfIssuedDocumentsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetListOfIssuedDocumentsResponseMultiError) AllErrors() []error { return m }

// GetListOfIssuedDocumentsResponseValidationError is the validation error
// returned by GetListOfIssuedDocumentsResponse.Validate if the designated
// constraints aren't met.
type GetListOfIssuedDocumentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetListOfIssuedDocumentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetListOfIssuedDocumentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetListOfIssuedDocumentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetListOfIssuedDocumentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetListOfIssuedDocumentsResponseValidationError) ErrorName() string {
	return "GetListOfIssuedDocumentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetListOfIssuedDocumentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetListOfIssuedDocumentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetListOfIssuedDocumentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetListOfIssuedDocumentsResponseValidationError{}

// Validate checks the field values on Documents with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Documents) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Documents with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DocumentsMultiError, or nil
// if none found.
func (m *Documents) ValidateAll() error {
	return m.validate(true)
}

func (m *Documents) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocumentName

	if all {
		switch v := interface{}(m.GetIssuedDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DocumentsValidationError{
					field:  "IssuedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DocumentsValidationError{
					field:  "IssuedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIssuedDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DocumentsValidationError{
				field:  "IssuedDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Uri

	// no validation rules for Doctype

	// no validation rules for Description

	// no validation rules for IssuerId

	// no validation rules for Issuer

	if len(errors) > 0 {
		return DocumentsMultiError(errors)
	}

	return nil
}

// DocumentsMultiError is an error wrapping multiple validation errors returned
// by Documents.ValidateAll() if the designated constraints aren't met.
type DocumentsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DocumentsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DocumentsMultiError) AllErrors() []error { return m }

// DocumentsValidationError is the validation error returned by
// Documents.Validate if the designated constraints aren't met.
type DocumentsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DocumentsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DocumentsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DocumentsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DocumentsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DocumentsValidationError) ErrorName() string { return "DocumentsValidationError" }

// Error satisfies the builtin error interface
func (e DocumentsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDocuments.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DocumentsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DocumentsValidationError{}

// Validate checks the field values on GetRefreshTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRefreshTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRefreshTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRefreshTokenRequestMultiError, or nil if none found.
func (m *GetRefreshTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRefreshTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRefreshTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRefreshTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRefreshTokenRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RefreshToken

	if len(errors) > 0 {
		return GetRefreshTokenRequestMultiError(errors)
	}

	return nil
}

// GetRefreshTokenRequestMultiError is an error wrapping multiple validation
// errors returned by GetRefreshTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRefreshTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRefreshTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRefreshTokenRequestMultiError) AllErrors() []error { return m }

// GetRefreshTokenRequestValidationError is the validation error returned by
// GetRefreshTokenRequest.Validate if the designated constraints aren't met.
type GetRefreshTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRefreshTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRefreshTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRefreshTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRefreshTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRefreshTokenRequestValidationError) ErrorName() string {
	return "GetRefreshTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRefreshTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRefreshTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRefreshTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRefreshTokenRequestValidationError{}

// Validate checks the field values on GetRefreshTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRefreshTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRefreshTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRefreshTokenResponseMultiError, or nil if none found.
func (m *GetRefreshTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRefreshTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRefreshTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRefreshTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRefreshTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTokenResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRefreshTokenResponseValidationError{
					field:  "TokenResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRefreshTokenResponseValidationError{
					field:  "TokenResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTokenResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRefreshTokenResponseValidationError{
				field:  "TokenResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRefreshTokenResponseMultiError(errors)
	}

	return nil
}

// GetRefreshTokenResponseMultiError is an error wrapping multiple validation
// errors returned by GetRefreshTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRefreshTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRefreshTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRefreshTokenResponseMultiError) AllErrors() []error { return m }

// GetRefreshTokenResponseValidationError is the validation error returned by
// GetRefreshTokenResponse.Validate if the designated constraints aren't met.
type GetRefreshTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRefreshTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRefreshTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRefreshTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRefreshTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRefreshTokenResponseValidationError) ErrorName() string {
	return "GetRefreshTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRefreshTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRefreshTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRefreshTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRefreshTokenResponseValidationError{}

// Validate checks the field values on GetFileFromUriRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileFromUriRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileFromUriRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFileFromUriRequestMultiError, or nil if none found.
func (m *GetFileFromUriRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileFromUriRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFileFromUriRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFileFromUriRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFileFromUriRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessToken

	// no validation rules for Uri

	if len(errors) > 0 {
		return GetFileFromUriRequestMultiError(errors)
	}

	return nil
}

// GetFileFromUriRequestMultiError is an error wrapping multiple validation
// errors returned by GetFileFromUriRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFileFromUriRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileFromUriRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileFromUriRequestMultiError) AllErrors() []error { return m }

// GetFileFromUriRequestValidationError is the validation error returned by
// GetFileFromUriRequest.Validate if the designated constraints aren't met.
type GetFileFromUriRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileFromUriRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileFromUriRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileFromUriRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileFromUriRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileFromUriRequestValidationError) ErrorName() string {
	return "GetFileFromUriRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileFromUriRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileFromUriRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileFromUriRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileFromUriRequestValidationError{}

// Validate checks the field values on GetFileFromUriResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileFromUriResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileFromUriResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFileFromUriResponseMultiError, or nil if none found.
func (m *GetFileFromUriResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileFromUriResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFileFromUriResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFileFromUriResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFileFromUriResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDocumentFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFileFromUriResponseValidationError{
					field:  "DocumentFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFileFromUriResponseValidationError{
					field:  "DocumentFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDocumentFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFileFromUriResponseValidationError{
				field:  "DocumentFile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFileFromUriResponseMultiError(errors)
	}

	return nil
}

// GetFileFromUriResponseMultiError is an error wrapping multiple validation
// errors returned by GetFileFromUriResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFileFromUriResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileFromUriResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileFromUriResponseMultiError) AllErrors() []error { return m }

// GetFileFromUriResponseValidationError is the validation error returned by
// GetFileFromUriResponse.Validate if the designated constraints aren't met.
type GetFileFromUriResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileFromUriResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileFromUriResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileFromUriResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileFromUriResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileFromUriResponseValidationError) ErrorName() string {
	return "GetFileFromUriResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileFromUriResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileFromUriResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileFromUriResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileFromUriResponseValidationError{}

// Validate checks the field values on GetAadhaarInXmlRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAadhaarInXmlRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAadhaarInXmlRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAadhaarInXmlRequestMultiError, or nil if none found.
func (m *GetAadhaarInXmlRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAadhaarInXmlRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAadhaarInXmlRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAadhaarInXmlRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAadhaarInXmlRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessToken

	if len(errors) > 0 {
		return GetAadhaarInXmlRequestMultiError(errors)
	}

	return nil
}

// GetAadhaarInXmlRequestMultiError is an error wrapping multiple validation
// errors returned by GetAadhaarInXmlRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAadhaarInXmlRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAadhaarInXmlRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAadhaarInXmlRequestMultiError) AllErrors() []error { return m }

// GetAadhaarInXmlRequestValidationError is the validation error returned by
// GetAadhaarInXmlRequest.Validate if the designated constraints aren't met.
type GetAadhaarInXmlRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAadhaarInXmlRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAadhaarInXmlRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAadhaarInXmlRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAadhaarInXmlRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAadhaarInXmlRequestValidationError) ErrorName() string {
	return "GetAadhaarInXmlRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAadhaarInXmlRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAadhaarInXmlRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAadhaarInXmlRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAadhaarInXmlRequestValidationError{}

// Validate checks the field values on GetAadhaarInXmlResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAadhaarInXmlResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAadhaarInXmlResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAadhaarInXmlResponseMultiError, or nil if none found.
func (m *GetAadhaarInXmlResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAadhaarInXmlResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAadhaarInXmlResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "UserImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "UserImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAadhaarInXmlResponseValidationError{
				field:  "UserImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAadhaarInXmlResponseValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAadhaarInXmlResponseValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Txn

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAadhaarInXmlResponseValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycResCode

	if all {
		switch v := interface{}(m.GetTtl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Ttl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Ttl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTtl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAadhaarInXmlResponseValidationError{
				field:  "Ttl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CareOf

	// no validation rules for Landmark

	// no validation rules for Locality

	// no validation rules for MaskedAadhaarNumber

	if all {
		switch v := interface{}(m.GetTs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Ts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAadhaarInXmlResponseValidationError{
					field:  "Ts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAadhaarInXmlResponseValidationError{
				field:  "Ts",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AadhaarXmlData

	if len(errors) > 0 {
		return GetAadhaarInXmlResponseMultiError(errors)
	}

	return nil
}

// GetAadhaarInXmlResponseMultiError is an error wrapping multiple validation
// errors returned by GetAadhaarInXmlResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAadhaarInXmlResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAadhaarInXmlResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAadhaarInXmlResponseMultiError) AllErrors() []error { return m }

// GetAadhaarInXmlResponseValidationError is the validation error returned by
// GetAadhaarInXmlResponse.Validate if the designated constraints aren't met.
type GetAadhaarInXmlResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAadhaarInXmlResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAadhaarInXmlResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAadhaarInXmlResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAadhaarInXmlResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAadhaarInXmlResponseValidationError) ErrorName() string {
	return "GetAadhaarInXmlResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAadhaarInXmlResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAadhaarInXmlResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAadhaarInXmlResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAadhaarInXmlResponseValidationError{}

// Validate checks the field values on TokenResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TokenResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TokenResponseMultiError, or
// nil if none found.
func (m *TokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccessToken

	if all {
		switch v := interface{}(m.GetExpiresIn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TokenResponseValidationError{
					field:  "ExpiresIn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TokenResponseValidationError{
					field:  "ExpiresIn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiresIn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TokenResponseValidationError{
				field:  "ExpiresIn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TokenType

	// no validation rules for Scope

	// no validation rules for RefreshToken

	// no validation rules for DigilockerId

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TokenResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TokenResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TokenResponseValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TokenResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TokenResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TokenResponseValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	// no validation rules for EAadhaar

	// no validation rules for ReferenceKey

	if all {
		switch v := interface{}(m.GetMobile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TokenResponseValidationError{
					field:  "Mobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TokenResponseValidationError{
					field:  "Mobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TokenResponseValidationError{
				field:  "Mobile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TokenResponseMultiError(errors)
	}

	return nil
}

// TokenResponseMultiError is an error wrapping multiple validation errors
// returned by TokenResponse.ValidateAll() if the designated constraints
// aren't met.
type TokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TokenResponseMultiError) AllErrors() []error { return m }

// TokenResponseValidationError is the validation error returned by
// TokenResponse.Validate if the designated constraints aren't met.
type TokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TokenResponseValidationError) ErrorName() string { return "TokenResponseValidationError" }

// Error satisfies the builtin error interface
func (e TokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TokenResponseValidationError{}
