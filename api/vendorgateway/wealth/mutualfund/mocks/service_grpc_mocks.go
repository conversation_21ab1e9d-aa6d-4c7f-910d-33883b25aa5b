// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/wealth/mutualfund/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	mutualfund "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockMutualFundClient is a mock of MutualFundClient interface.
type MockMutualFundClient struct {
	ctrl     *gomock.Controller
	recorder *MockMutualFundClientMockRecorder
}

// MockMutualFundClientMockRecorder is the mock recorder for MockMutualFundClient.
type MockMutualFundClientMockRecorder struct {
	mock *MockMutualFundClient
}

// NewMockMutualFundClient creates a new mock instance.
func NewMockMutualFundClient(ctrl *gomock.Controller) *MockMutualFundClient {
	mock := &MockMutualFundClient{ctrl: ctrl}
	mock.recorder = &MockMutualFundClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMutualFundClient) EXPECT() *MockMutualFundClientMockRecorder {
	return m.recorder
}

// GetCasDocument mocks base method.
func (m *MockMutualFundClient) GetCasDocument(ctx context.Context, in *mutualfund.GetCasDocumentRequest, opts ...grpc.CallOption) (*mutualfund.GetCasDocumentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCasDocument", varargs...)
	ret0, _ := ret[0].(*mutualfund.GetCasDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCasDocument indicates an expected call of GetCasDocument.
func (mr *MockMutualFundClientMockRecorder) GetCasDocument(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCasDocument", reflect.TypeOf((*MockMutualFundClient)(nil).GetCasDocument), varargs...)
}

// GetFolioDetails mocks base method.
func (m *MockMutualFundClient) GetFolioDetails(ctx context.Context, in *mutualfund.GetFolioDetailsRequest, opts ...grpc.CallOption) (*mutualfund.GetFolioDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFolioDetails", varargs...)
	ret0, _ := ret[0].(*mutualfund.GetFolioDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFolioDetails indicates an expected call of GetFolioDetails.
func (mr *MockMutualFundClientMockRecorder) GetFolioDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFolioDetails", reflect.TypeOf((*MockMutualFundClient)(nil).GetFolioDetails), varargs...)
}

// GetMfHistoricalNavs mocks base method.
func (m *MockMutualFundClient) GetMfHistoricalNavs(ctx context.Context, in *mutualfund.GetMfHistoricalNavsRequest, opts ...grpc.CallOption) (*mutualfund.GetMfHistoricalNavsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMfHistoricalNavs", varargs...)
	ret0, _ := ret[0].(*mutualfund.GetMfHistoricalNavsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMfHistoricalNavs indicates an expected call of GetMfHistoricalNavs.
func (mr *MockMutualFundClientMockRecorder) GetMfHistoricalNavs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMfHistoricalNavs", reflect.TypeOf((*MockMutualFundClient)(nil).GetMfHistoricalNavs), varargs...)
}

// GetObsoleteFunds mocks base method.
func (m *MockMutualFundClient) GetObsoleteFunds(ctx context.Context, in *mutualfund.GetObsoleteFundsRequest, opts ...grpc.CallOption) (*mutualfund.GetObsoleteFundsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetObsoleteFunds", varargs...)
	ret0, _ := ret[0].(*mutualfund.GetObsoleteFundsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObsoleteFunds indicates an expected call of GetObsoleteFunds.
func (mr *MockMutualFundClientMockRecorder) GetObsoleteFunds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObsoleteFunds", reflect.TypeOf((*MockMutualFundClient)(nil).GetObsoleteFunds), varargs...)
}

// GetOrderFeedFileStatus mocks base method.
func (m *MockMutualFundClient) GetOrderFeedFileStatus(ctx context.Context, in *mutualfund.GetOrderFeedFileStatusRequest, opts ...grpc.CallOption) (*mutualfund.GetOrderFeedFileStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderFeedFileStatus", varargs...)
	ret0, _ := ret[0].(*mutualfund.GetOrderFeedFileStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderFeedFileStatus indicates an expected call of GetOrderFeedFileStatus.
func (mr *MockMutualFundClientMockRecorder) GetOrderFeedFileStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderFeedFileStatus", reflect.TypeOf((*MockMutualFundClient)(nil).GetOrderFeedFileStatus), varargs...)
}

// GetTransactionStatus mocks base method.
func (m *MockMutualFundClient) GetTransactionStatus(ctx context.Context, in *mutualfund.GetTransactionStatusRequest, opts ...grpc.CallOption) (*mutualfund.GetTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionStatus", varargs...)
	ret0, _ := ret[0].(*mutualfund.GetTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionStatus indicates an expected call of GetTransactionStatus.
func (mr *MockMutualFundClientMockRecorder) GetTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionStatus", reflect.TypeOf((*MockMutualFundClient)(nil).GetTransactionStatus), varargs...)
}

// ProcessElogFile mocks base method.
func (m *MockMutualFundClient) ProcessElogFile(ctx context.Context, in *mutualfund.ProcessElogFileRequest, opts ...grpc.CallOption) (*mutualfund.ProcessElogFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessElogFile", varargs...)
	ret0, _ := ret[0].(*mutualfund.ProcessElogFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessElogFile indicates an expected call of ProcessElogFile.
func (mr *MockMutualFundClientMockRecorder) ProcessElogFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessElogFile", reflect.TypeOf((*MockMutualFundClient)(nil).ProcessElogFile), varargs...)
}

// ProcessFATCAFile mocks base method.
func (m *MockMutualFundClient) ProcessFATCAFile(ctx context.Context, in *mutualfund.ProcessFATCAFileRequest, opts ...grpc.CallOption) (*mutualfund.ProcessFATCAFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessFATCAFile", varargs...)
	ret0, _ := ret[0].(*mutualfund.ProcessFATCAFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFATCAFile indicates an expected call of ProcessFATCAFile.
func (mr *MockMutualFundClientMockRecorder) ProcessFATCAFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFATCAFile", reflect.TypeOf((*MockMutualFundClient)(nil).ProcessFATCAFile), varargs...)
}

// ProcessNFTFile mocks base method.
func (m *MockMutualFundClient) ProcessNFTFile(ctx context.Context, in *mutualfund.ProcessNFTFileRequest, opts ...grpc.CallOption) (*mutualfund.ProcessNFTFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessNFTFile", varargs...)
	ret0, _ := ret[0].(*mutualfund.ProcessNFTFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessNFTFile indicates an expected call of ProcessNFTFile.
func (mr *MockMutualFundClientMockRecorder) ProcessNFTFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessNFTFile", reflect.TypeOf((*MockMutualFundClient)(nil).ProcessNFTFile), varargs...)
}

// ProcessOrderFeedFile mocks base method.
func (m *MockMutualFundClient) ProcessOrderFeedFile(ctx context.Context, in *mutualfund.ProcessOrderFeedFileRequest, opts ...grpc.CallOption) (*mutualfund.ProcessOrderFeedFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessOrderFeedFile", varargs...)
	ret0, _ := ret[0].(*mutualfund.ProcessOrderFeedFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessOrderFeedFile indicates an expected call of ProcessOrderFeedFile.
func (mr *MockMutualFundClientMockRecorder) ProcessOrderFeedFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOrderFeedFile", reflect.TypeOf((*MockMutualFundClient)(nil).ProcessOrderFeedFile), varargs...)
}

// ProcessOrderFeedFileSync mocks base method.
func (m *MockMutualFundClient) ProcessOrderFeedFileSync(ctx context.Context, in *mutualfund.ProcessOrderFeedFileSyncRequest, opts ...grpc.CallOption) (*mutualfund.ProcessOrderFeedFileSyncResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessOrderFeedFileSync", varargs...)
	ret0, _ := ret[0].(*mutualfund.ProcessOrderFeedFileSyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessOrderFeedFileSync indicates an expected call of ProcessOrderFeedFileSync.
func (mr *MockMutualFundClientMockRecorder) ProcessOrderFeedFileSync(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOrderFeedFileSync", reflect.TypeOf((*MockMutualFundClient)(nil).ProcessOrderFeedFileSync), varargs...)
}

// SubmitCasSummary mocks base method.
func (m *MockMutualFundClient) SubmitCasSummary(ctx context.Context, in *mutualfund.SubmitCasSummaryRequest, opts ...grpc.CallOption) (*mutualfund.SubmitCasSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitCasSummary", varargs...)
	ret0, _ := ret[0].(*mutualfund.SubmitCasSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitCasSummary indicates an expected call of SubmitCasSummary.
func (mr *MockMutualFundClientMockRecorder) SubmitCasSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitCasSummary", reflect.TypeOf((*MockMutualFundClient)(nil).SubmitCasSummary), varargs...)
}

// UpdateFolioEmail mocks base method.
func (m *MockMutualFundClient) UpdateFolioEmail(ctx context.Context, in *mutualfund.UpdateFolioEmailRequest, opts ...grpc.CallOption) (*mutualfund.UpdateFolioEmailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateFolioEmail", varargs...)
	ret0, _ := ret[0].(*mutualfund.UpdateFolioEmailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFolioEmail indicates an expected call of UpdateFolioEmail.
func (mr *MockMutualFundClientMockRecorder) UpdateFolioEmail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFolioEmail", reflect.TypeOf((*MockMutualFundClient)(nil).UpdateFolioEmail), varargs...)
}

// UpdateFolioMobile mocks base method.
func (m *MockMutualFundClient) UpdateFolioMobile(ctx context.Context, in *mutualfund.UpdateFolioMobileRequest, opts ...grpc.CallOption) (*mutualfund.UpdateFolioMobileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateFolioMobile", varargs...)
	ret0, _ := ret[0].(*mutualfund.UpdateFolioMobileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFolioMobile indicates an expected call of UpdateFolioMobile.
func (mr *MockMutualFundClientMockRecorder) UpdateFolioMobile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFolioMobile", reflect.TypeOf((*MockMutualFundClient)(nil).UpdateFolioMobile), varargs...)
}

// UpdateNomineeDetails mocks base method.
func (m *MockMutualFundClient) UpdateNomineeDetails(ctx context.Context, in *mutualfund.UpdateNomineeDetailsRequest, opts ...grpc.CallOption) (*mutualfund.UpdateNomineeDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateNomineeDetails", varargs...)
	ret0, _ := ret[0].(*mutualfund.UpdateNomineeDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNomineeDetails indicates an expected call of UpdateNomineeDetails.
func (mr *MockMutualFundClientMockRecorder) UpdateNomineeDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNomineeDetails", reflect.TypeOf((*MockMutualFundClient)(nil).UpdateNomineeDetails), varargs...)
}

// VerifyMfCentralOtp mocks base method.
func (m *MockMutualFundClient) VerifyMfCentralOtp(ctx context.Context, in *mutualfund.VerifyMfCentralOtpRequest, opts ...grpc.CallOption) (*mutualfund.VerifyMfCentralOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyMfCentralOtp", varargs...)
	ret0, _ := ret[0].(*mutualfund.VerifyMfCentralOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyMfCentralOtp indicates an expected call of VerifyMfCentralOtp.
func (mr *MockMutualFundClientMockRecorder) VerifyMfCentralOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyMfCentralOtp", reflect.TypeOf((*MockMutualFundClient)(nil).VerifyMfCentralOtp), varargs...)
}

// MockMutualFundServer is a mock of MutualFundServer interface.
type MockMutualFundServer struct {
	ctrl     *gomock.Controller
	recorder *MockMutualFundServerMockRecorder
}

// MockMutualFundServerMockRecorder is the mock recorder for MockMutualFundServer.
type MockMutualFundServerMockRecorder struct {
	mock *MockMutualFundServer
}

// NewMockMutualFundServer creates a new mock instance.
func NewMockMutualFundServer(ctrl *gomock.Controller) *MockMutualFundServer {
	mock := &MockMutualFundServer{ctrl: ctrl}
	mock.recorder = &MockMutualFundServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMutualFundServer) EXPECT() *MockMutualFundServerMockRecorder {
	return m.recorder
}

// GetCasDocument mocks base method.
func (m *MockMutualFundServer) GetCasDocument(arg0 context.Context, arg1 *mutualfund.GetCasDocumentRequest) (*mutualfund.GetCasDocumentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCasDocument", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.GetCasDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCasDocument indicates an expected call of GetCasDocument.
func (mr *MockMutualFundServerMockRecorder) GetCasDocument(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCasDocument", reflect.TypeOf((*MockMutualFundServer)(nil).GetCasDocument), arg0, arg1)
}

// GetFolioDetails mocks base method.
func (m *MockMutualFundServer) GetFolioDetails(arg0 context.Context, arg1 *mutualfund.GetFolioDetailsRequest) (*mutualfund.GetFolioDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFolioDetails", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.GetFolioDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFolioDetails indicates an expected call of GetFolioDetails.
func (mr *MockMutualFundServerMockRecorder) GetFolioDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFolioDetails", reflect.TypeOf((*MockMutualFundServer)(nil).GetFolioDetails), arg0, arg1)
}

// GetMfHistoricalNavs mocks base method.
func (m *MockMutualFundServer) GetMfHistoricalNavs(arg0 context.Context, arg1 *mutualfund.GetMfHistoricalNavsRequest) (*mutualfund.GetMfHistoricalNavsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMfHistoricalNavs", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.GetMfHistoricalNavsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMfHistoricalNavs indicates an expected call of GetMfHistoricalNavs.
func (mr *MockMutualFundServerMockRecorder) GetMfHistoricalNavs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMfHistoricalNavs", reflect.TypeOf((*MockMutualFundServer)(nil).GetMfHistoricalNavs), arg0, arg1)
}

// GetObsoleteFunds mocks base method.
func (m *MockMutualFundServer) GetObsoleteFunds(arg0 context.Context, arg1 *mutualfund.GetObsoleteFundsRequest) (*mutualfund.GetObsoleteFundsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObsoleteFunds", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.GetObsoleteFundsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObsoleteFunds indicates an expected call of GetObsoleteFunds.
func (mr *MockMutualFundServerMockRecorder) GetObsoleteFunds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObsoleteFunds", reflect.TypeOf((*MockMutualFundServer)(nil).GetObsoleteFunds), arg0, arg1)
}

// GetOrderFeedFileStatus mocks base method.
func (m *MockMutualFundServer) GetOrderFeedFileStatus(arg0 context.Context, arg1 *mutualfund.GetOrderFeedFileStatusRequest) (*mutualfund.GetOrderFeedFileStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderFeedFileStatus", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.GetOrderFeedFileStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderFeedFileStatus indicates an expected call of GetOrderFeedFileStatus.
func (mr *MockMutualFundServerMockRecorder) GetOrderFeedFileStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderFeedFileStatus", reflect.TypeOf((*MockMutualFundServer)(nil).GetOrderFeedFileStatus), arg0, arg1)
}

// GetTransactionStatus mocks base method.
func (m *MockMutualFundServer) GetTransactionStatus(arg0 context.Context, arg1 *mutualfund.GetTransactionStatusRequest) (*mutualfund.GetTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.GetTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionStatus indicates an expected call of GetTransactionStatus.
func (mr *MockMutualFundServerMockRecorder) GetTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionStatus", reflect.TypeOf((*MockMutualFundServer)(nil).GetTransactionStatus), arg0, arg1)
}

// ProcessElogFile mocks base method.
func (m *MockMutualFundServer) ProcessElogFile(arg0 context.Context, arg1 *mutualfund.ProcessElogFileRequest) (*mutualfund.ProcessElogFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessElogFile", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.ProcessElogFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessElogFile indicates an expected call of ProcessElogFile.
func (mr *MockMutualFundServerMockRecorder) ProcessElogFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessElogFile", reflect.TypeOf((*MockMutualFundServer)(nil).ProcessElogFile), arg0, arg1)
}

// ProcessFATCAFile mocks base method.
func (m *MockMutualFundServer) ProcessFATCAFile(arg0 context.Context, arg1 *mutualfund.ProcessFATCAFileRequest) (*mutualfund.ProcessFATCAFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessFATCAFile", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.ProcessFATCAFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFATCAFile indicates an expected call of ProcessFATCAFile.
func (mr *MockMutualFundServerMockRecorder) ProcessFATCAFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFATCAFile", reflect.TypeOf((*MockMutualFundServer)(nil).ProcessFATCAFile), arg0, arg1)
}

// ProcessNFTFile mocks base method.
func (m *MockMutualFundServer) ProcessNFTFile(arg0 context.Context, arg1 *mutualfund.ProcessNFTFileRequest) (*mutualfund.ProcessNFTFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessNFTFile", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.ProcessNFTFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessNFTFile indicates an expected call of ProcessNFTFile.
func (mr *MockMutualFundServerMockRecorder) ProcessNFTFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessNFTFile", reflect.TypeOf((*MockMutualFundServer)(nil).ProcessNFTFile), arg0, arg1)
}

// ProcessOrderFeedFile mocks base method.
func (m *MockMutualFundServer) ProcessOrderFeedFile(arg0 context.Context, arg1 *mutualfund.ProcessOrderFeedFileRequest) (*mutualfund.ProcessOrderFeedFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessOrderFeedFile", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.ProcessOrderFeedFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessOrderFeedFile indicates an expected call of ProcessOrderFeedFile.
func (mr *MockMutualFundServerMockRecorder) ProcessOrderFeedFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOrderFeedFile", reflect.TypeOf((*MockMutualFundServer)(nil).ProcessOrderFeedFile), arg0, arg1)
}

// ProcessOrderFeedFileSync mocks base method.
func (m *MockMutualFundServer) ProcessOrderFeedFileSync(arg0 context.Context, arg1 *mutualfund.ProcessOrderFeedFileSyncRequest) (*mutualfund.ProcessOrderFeedFileSyncResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessOrderFeedFileSync", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.ProcessOrderFeedFileSyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessOrderFeedFileSync indicates an expected call of ProcessOrderFeedFileSync.
func (mr *MockMutualFundServerMockRecorder) ProcessOrderFeedFileSync(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOrderFeedFileSync", reflect.TypeOf((*MockMutualFundServer)(nil).ProcessOrderFeedFileSync), arg0, arg1)
}

// SubmitCasSummary mocks base method.
func (m *MockMutualFundServer) SubmitCasSummary(arg0 context.Context, arg1 *mutualfund.SubmitCasSummaryRequest) (*mutualfund.SubmitCasSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitCasSummary", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.SubmitCasSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitCasSummary indicates an expected call of SubmitCasSummary.
func (mr *MockMutualFundServerMockRecorder) SubmitCasSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitCasSummary", reflect.TypeOf((*MockMutualFundServer)(nil).SubmitCasSummary), arg0, arg1)
}

// UpdateFolioEmail mocks base method.
func (m *MockMutualFundServer) UpdateFolioEmail(arg0 context.Context, arg1 *mutualfund.UpdateFolioEmailRequest) (*mutualfund.UpdateFolioEmailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFolioEmail", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.UpdateFolioEmailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFolioEmail indicates an expected call of UpdateFolioEmail.
func (mr *MockMutualFundServerMockRecorder) UpdateFolioEmail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFolioEmail", reflect.TypeOf((*MockMutualFundServer)(nil).UpdateFolioEmail), arg0, arg1)
}

// UpdateFolioMobile mocks base method.
func (m *MockMutualFundServer) UpdateFolioMobile(arg0 context.Context, arg1 *mutualfund.UpdateFolioMobileRequest) (*mutualfund.UpdateFolioMobileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFolioMobile", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.UpdateFolioMobileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFolioMobile indicates an expected call of UpdateFolioMobile.
func (mr *MockMutualFundServerMockRecorder) UpdateFolioMobile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFolioMobile", reflect.TypeOf((*MockMutualFundServer)(nil).UpdateFolioMobile), arg0, arg1)
}

// UpdateNomineeDetails mocks base method.
func (m *MockMutualFundServer) UpdateNomineeDetails(arg0 context.Context, arg1 *mutualfund.UpdateNomineeDetailsRequest) (*mutualfund.UpdateNomineeDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNomineeDetails", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.UpdateNomineeDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNomineeDetails indicates an expected call of UpdateNomineeDetails.
func (mr *MockMutualFundServerMockRecorder) UpdateNomineeDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNomineeDetails", reflect.TypeOf((*MockMutualFundServer)(nil).UpdateNomineeDetails), arg0, arg1)
}

// VerifyMfCentralOtp mocks base method.
func (m *MockMutualFundServer) VerifyMfCentralOtp(arg0 context.Context, arg1 *mutualfund.VerifyMfCentralOtpRequest) (*mutualfund.VerifyMfCentralOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyMfCentralOtp", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.VerifyMfCentralOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyMfCentralOtp indicates an expected call of VerifyMfCentralOtp.
func (mr *MockMutualFundServerMockRecorder) VerifyMfCentralOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyMfCentralOtp", reflect.TypeOf((*MockMutualFundServer)(nil).VerifyMfCentralOtp), arg0, arg1)
}

// MockUnsafeMutualFundServer is a mock of UnsafeMutualFundServer interface.
type MockUnsafeMutualFundServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMutualFundServerMockRecorder
}

// MockUnsafeMutualFundServerMockRecorder is the mock recorder for MockUnsafeMutualFundServer.
type MockUnsafeMutualFundServerMockRecorder struct {
	mock *MockUnsafeMutualFundServer
}

// NewMockUnsafeMutualFundServer creates a new mock instance.
func NewMockUnsafeMutualFundServer(ctrl *gomock.Controller) *MockUnsafeMutualFundServer {
	mock := &MockUnsafeMutualFundServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMutualFundServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMutualFundServer) EXPECT() *MockUnsafeMutualFundServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMutualFundServer mocks base method.
func (m *MockUnsafeMutualFundServer) mustEmbedUnimplementedMutualFundServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMutualFundServer")
}

// mustEmbedUnimplementedMutualFundServer indicates an expected call of mustEmbedUnimplementedMutualFundServer.
func (mr *MockUnsafeMutualFundServerMockRecorder) mustEmbedUnimplementedMutualFundServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMutualFundServer", reflect.TypeOf((*MockUnsafeMutualFundServer)(nil).mustEmbedUnimplementedMutualFundServer))
}
