// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/wealth/ckyc/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	ckyc "github.com/epifi/gamma/api/vendorgateway/wealth/ckyc"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCkycClient is a mock of CkycClient interface.
type MockCkycClient struct {
	ctrl     *gomock.Controller
	recorder *MockCkycClientMockRecorder
}

// MockCkycClientMockRecorder is the mock recorder for MockCkycClient.
type MockCkycClientMockRecorder struct {
	mock *MockCkycClient
}

// NewMockCkycClient creates a new mock instance.
func NewMockCkycClient(ctrl *gomock.Controller) *MockCkycClient {
	mock := &MockCkycClient{ctrl: ctrl}
	mock.recorder = &MockCkycClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCkycClient) EXPECT() *MockCkycClientMockRecorder {
	return m.recorder
}

// CkycDownload mocks base method.
func (m *MockCkycClient) CkycDownload(ctx context.Context, in *ckyc.CkycDownloadRequest, opts ...grpc.CallOption) (*ckyc.CkycDownloadResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CkycDownload", varargs...)
	ret0, _ := ret[0].(*ckyc.CkycDownloadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CkycDownload indicates an expected call of CkycDownload.
func (mr *MockCkycClientMockRecorder) CkycDownload(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CkycDownload", reflect.TypeOf((*MockCkycClient)(nil).CkycDownload), varargs...)
}

// GetCkycSearch mocks base method.
func (m *MockCkycClient) GetCkycSearch(ctx context.Context, in *ckyc.GetCkycSearchRequest, opts ...grpc.CallOption) (*ckyc.GetCkycSearchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCkycSearch", varargs...)
	ret0, _ := ret[0].(*ckyc.GetCkycSearchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCkycSearch indicates an expected call of GetCkycSearch.
func (mr *MockCkycClientMockRecorder) GetCkycSearch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCkycSearch", reflect.TypeOf((*MockCkycClient)(nil).GetCkycSearch), varargs...)
}

// MockCkycServer is a mock of CkycServer interface.
type MockCkycServer struct {
	ctrl     *gomock.Controller
	recorder *MockCkycServerMockRecorder
}

// MockCkycServerMockRecorder is the mock recorder for MockCkycServer.
type MockCkycServerMockRecorder struct {
	mock *MockCkycServer
}

// NewMockCkycServer creates a new mock instance.
func NewMockCkycServer(ctrl *gomock.Controller) *MockCkycServer {
	mock := &MockCkycServer{ctrl: ctrl}
	mock.recorder = &MockCkycServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCkycServer) EXPECT() *MockCkycServerMockRecorder {
	return m.recorder
}

// CkycDownload mocks base method.
func (m *MockCkycServer) CkycDownload(arg0 context.Context, arg1 *ckyc.CkycDownloadRequest) (*ckyc.CkycDownloadResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CkycDownload", arg0, arg1)
	ret0, _ := ret[0].(*ckyc.CkycDownloadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CkycDownload indicates an expected call of CkycDownload.
func (mr *MockCkycServerMockRecorder) CkycDownload(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CkycDownload", reflect.TypeOf((*MockCkycServer)(nil).CkycDownload), arg0, arg1)
}

// GetCkycSearch mocks base method.
func (m *MockCkycServer) GetCkycSearch(arg0 context.Context, arg1 *ckyc.GetCkycSearchRequest) (*ckyc.GetCkycSearchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCkycSearch", arg0, arg1)
	ret0, _ := ret[0].(*ckyc.GetCkycSearchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCkycSearch indicates an expected call of GetCkycSearch.
func (mr *MockCkycServerMockRecorder) GetCkycSearch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCkycSearch", reflect.TypeOf((*MockCkycServer)(nil).GetCkycSearch), arg0, arg1)
}

// MockUnsafeCkycServer is a mock of UnsafeCkycServer interface.
type MockUnsafeCkycServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCkycServerMockRecorder
}

// MockUnsafeCkycServerMockRecorder is the mock recorder for MockUnsafeCkycServer.
type MockUnsafeCkycServerMockRecorder struct {
	mock *MockUnsafeCkycServer
}

// NewMockUnsafeCkycServer creates a new mock instance.
func NewMockUnsafeCkycServer(ctrl *gomock.Controller) *MockUnsafeCkycServer {
	mock := &MockUnsafeCkycServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCkycServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCkycServer) EXPECT() *MockUnsafeCkycServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCkycServer mocks base method.
func (m *MockUnsafeCkycServer) mustEmbedUnimplementedCkycServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCkycServer")
}

// mustEmbedUnimplementedCkycServer indicates an expected call of mustEmbedUnimplementedCkycServer.
func (mr *MockUnsafeCkycServerMockRecorder) mustEmbedUnimplementedCkycServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCkycServer", reflect.TypeOf((*MockUnsafeCkycServer)(nil).mustEmbedUnimplementedCkycServer))
}
