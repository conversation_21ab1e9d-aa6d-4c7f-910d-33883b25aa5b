// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/parser/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	parser "github.com/epifi/gamma/api/vendorgateway/parser"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockParserClient is a mock of ParserClient interface.
type MockParserClient struct {
	ctrl     *gomock.Controller
	recorder *MockParserClientMockRecorder
}

// MockParserClientMockRecorder is the mock recorder for MockParserClient.
type MockParserClientMockRecorder struct {
	mock *MockParserClient
}

// NewMockParserClient creates a new mock instance.
func NewMockParserClient(ctrl *gomock.Controller) *MockParserClient {
	mock := &MockParserClient{ctrl: ctrl}
	mock.recorder = &MockParserClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockParserClient) EXPECT() *MockParserClientMockRecorder {
	return m.recorder
}

// ParseAATxn mocks base method.
func (m *MockParserClient) ParseAATxn(ctx context.Context, in *parser.ParseAATxnRequest, opts ...grpc.CallOption) (*parser.ParseAATxnResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ParseAATxn", varargs...)
	ret0, _ := ret[0].(*parser.ParseAATxnResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseAATxn indicates an expected call of ParseAATxn.
func (mr *MockParserClientMockRecorder) ParseAATxn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseAATxn", reflect.TypeOf((*MockParserClient)(nil).ParseAATxn), varargs...)
}

// ParseAATxnBulk mocks base method.
func (m *MockParserClient) ParseAATxnBulk(ctx context.Context, in *parser.ParseAATxnBulkRequest, opts ...grpc.CallOption) (*parser.ParseAATxnBulkResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ParseAATxnBulk", varargs...)
	ret0, _ := ret[0].(*parser.ParseAATxnBulkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseAATxnBulk indicates an expected call of ParseAATxnBulk.
func (mr *MockParserClientMockRecorder) ParseAATxnBulk(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseAATxnBulk", reflect.TypeOf((*MockParserClient)(nil).ParseAATxnBulk), varargs...)
}

// ParseAATxnV2 mocks base method.
func (m *MockParserClient) ParseAATxnV2(ctx context.Context, in *parser.ParseAATxnRequest, opts ...grpc.CallOption) (*parser.ParseAATxnResponseV2, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ParseAATxnV2", varargs...)
	ret0, _ := ret[0].(*parser.ParseAATxnResponseV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseAATxnV2 indicates an expected call of ParseAATxnV2.
func (mr *MockParserClientMockRecorder) ParseAATxnV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseAATxnV2", reflect.TypeOf((*MockParserClient)(nil).ParseAATxnV2), varargs...)
}

// MockParserServer is a mock of ParserServer interface.
type MockParserServer struct {
	ctrl     *gomock.Controller
	recorder *MockParserServerMockRecorder
}

// MockParserServerMockRecorder is the mock recorder for MockParserServer.
type MockParserServerMockRecorder struct {
	mock *MockParserServer
}

// NewMockParserServer creates a new mock instance.
func NewMockParserServer(ctrl *gomock.Controller) *MockParserServer {
	mock := &MockParserServer{ctrl: ctrl}
	mock.recorder = &MockParserServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockParserServer) EXPECT() *MockParserServerMockRecorder {
	return m.recorder
}

// ParseAATxn mocks base method.
func (m *MockParserServer) ParseAATxn(arg0 context.Context, arg1 *parser.ParseAATxnRequest) (*parser.ParseAATxnResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseAATxn", arg0, arg1)
	ret0, _ := ret[0].(*parser.ParseAATxnResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseAATxn indicates an expected call of ParseAATxn.
func (mr *MockParserServerMockRecorder) ParseAATxn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseAATxn", reflect.TypeOf((*MockParserServer)(nil).ParseAATxn), arg0, arg1)
}

// ParseAATxnBulk mocks base method.
func (m *MockParserServer) ParseAATxnBulk(arg0 context.Context, arg1 *parser.ParseAATxnBulkRequest) (*parser.ParseAATxnBulkResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseAATxnBulk", arg0, arg1)
	ret0, _ := ret[0].(*parser.ParseAATxnBulkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseAATxnBulk indicates an expected call of ParseAATxnBulk.
func (mr *MockParserServerMockRecorder) ParseAATxnBulk(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseAATxnBulk", reflect.TypeOf((*MockParserServer)(nil).ParseAATxnBulk), arg0, arg1)
}

// ParseAATxnV2 mocks base method.
func (m *MockParserServer) ParseAATxnV2(arg0 context.Context, arg1 *parser.ParseAATxnRequest) (*parser.ParseAATxnResponseV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseAATxnV2", arg0, arg1)
	ret0, _ := ret[0].(*parser.ParseAATxnResponseV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseAATxnV2 indicates an expected call of ParseAATxnV2.
func (mr *MockParserServerMockRecorder) ParseAATxnV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseAATxnV2", reflect.TypeOf((*MockParserServer)(nil).ParseAATxnV2), arg0, arg1)
}

// MockUnsafeParserServer is a mock of UnsafeParserServer interface.
type MockUnsafeParserServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeParserServerMockRecorder
}

// MockUnsafeParserServerMockRecorder is the mock recorder for MockUnsafeParserServer.
type MockUnsafeParserServerMockRecorder struct {
	mock *MockUnsafeParserServer
}

// NewMockUnsafeParserServer creates a new mock instance.
func NewMockUnsafeParserServer(ctrl *gomock.Controller) *MockUnsafeParserServer {
	mock := &MockUnsafeParserServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeParserServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeParserServer) EXPECT() *MockUnsafeParserServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedParserServer mocks base method.
func (m *MockUnsafeParserServer) mustEmbedUnimplementedParserServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedParserServer")
}

// mustEmbedUnimplementedParserServer indicates an expected call of mustEmbedUnimplementedParserServer.
func (mr *MockUnsafeParserServerMockRecorder) mustEmbedUnimplementedParserServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedParserServer", reflect.TypeOf((*MockUnsafeParserServer)(nil).mustEmbedUnimplementedParserServer))
}
