// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/openbanking/savings/service.proto

package savings

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.SourceOfFunds(0)
)

// Validate checks the field values on CreateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAccountRequestMultiError, or nil if none found.
func (m *CreateAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountRequestValidationError{
				field:  "DeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerName

	// no validation rules for AccountNo

	// no validation rules for MobileNo

	// no validation rules for EmailId

	if all {
		switch v := interface{}(m.GetNominee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "Nominee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "Nominee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNominee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountRequestValidationError{
				field:  "Nominee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycLevel

	// no validation rules for VendorSku

	// no validation rules for SolId

	// no validation rules for SourceOfFunds

	// no validation rules for ScholarshipFlag

	// no validation rules for DbtFlag

	if all {
		switch v := interface{}(m.GetAnnualTxnVolume()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "AnnualTxnVolume",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "AnnualTxnVolume",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualTxnVolume()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountRequestValidationError{
				field:  "AnnualTxnVolume",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PurposeOfSavingsAccount

	if len(errors) > 0 {
		return CreateAccountRequestMultiError(errors)
	}

	return nil
}

// CreateAccountRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAccountRequestMultiError) AllErrors() []error { return m }

// CreateAccountRequestValidationError is the validation error returned by
// CreateAccountRequest.Validate if the designated constraints aren't met.
type CreateAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAccountRequestValidationError) ErrorName() string {
	return "CreateAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAccountRequestValidationError{}

// Validate checks the field values on CreateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAccountResponseMultiError, or nil if none found.
func (m *CreateAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAccountResponseMultiError(errors)
	}

	return nil
}

// CreateAccountResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAccountResponseMultiError) AllErrors() []error { return m }

// CreateAccountResponseValidationError is the validation error returned by
// CreateAccountResponse.Validate if the designated constraints aren't met.
type CreateAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAccountResponseValidationError) ErrorName() string {
	return "CreateAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAccountResponseValidationError{}

// Validate checks the field values on CheckAccountStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckAccountStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckAccountStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckAccountStatusRequestMultiError, or nil if none found.
func (m *CheckAccountStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckAccountStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckAccountStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckAccountStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckAccountStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckAccountStatusRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckAccountStatusRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckAccountStatusRequestValidationError{
				field:  "DeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckAccountStatusRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckAccountStatusRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckAccountStatusRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckAccountStatusRequestMultiError(errors)
	}

	return nil
}

// CheckAccountStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CheckAccountStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type CheckAccountStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckAccountStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckAccountStatusRequestMultiError) AllErrors() []error { return m }

// CheckAccountStatusRequestValidationError is the validation error returned by
// CheckAccountStatusRequest.Validate if the designated constraints aren't met.
type CheckAccountStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckAccountStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckAccountStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckAccountStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckAccountStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckAccountStatusRequestValidationError) ErrorName() string {
	return "CheckAccountStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckAccountStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckAccountStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckAccountStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckAccountStatusRequestValidationError{}

// Validate checks the field values on CheckAccountStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckAccountStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckAccountStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckAccountStatusResponseMultiError, or nil if none found.
func (m *CheckAccountStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckAccountStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckAccountStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckAccountStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckAccountStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckAccountStatusResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckAccountStatusResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckAccountStatusResponseValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckAccountStatusResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckAccountStatusResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckAccountStatusResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckAccountStatusResponseMultiError(errors)
	}

	return nil
}

// CheckAccountStatusResponseMultiError is an error wrapping multiple
// validation errors returned by CheckAccountStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckAccountStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckAccountStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckAccountStatusResponseMultiError) AllErrors() []error { return m }

// CheckAccountStatusResponseValidationError is the validation error returned
// by CheckAccountStatusResponse.Validate if the designated constraints aren't met.
type CheckAccountStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckAccountStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckAccountStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckAccountStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckAccountStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckAccountStatusResponseValidationError) ErrorName() string {
	return "CheckAccountStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckAccountStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckAccountStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckAccountStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckAccountStatusResponseValidationError{}

// Validate checks the field values on GetBalanceRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetBalanceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBalanceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBalanceRequestMultiError, or nil if none found.
func (m *GetBalanceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBalanceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for AccountNumber

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBalanceRequestMultiError(errors)
	}

	return nil
}

// GetBalanceRequestMultiError is an error wrapping multiple validation errors
// returned by GetBalanceRequest.ValidateAll() if the designated constraints
// aren't met.
type GetBalanceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBalanceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBalanceRequestMultiError) AllErrors() []error { return m }

// GetBalanceRequestValidationError is the validation error returned by
// GetBalanceRequest.Validate if the designated constraints aren't met.
type GetBalanceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBalanceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBalanceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBalanceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBalanceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBalanceRequestValidationError) ErrorName() string {
	return "GetBalanceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBalanceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBalanceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBalanceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBalanceRequestValidationError{}

// Validate checks the field values on GetBalanceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBalanceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBalanceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBalanceResponseMultiError, or nil if none found.
func (m *GetBalanceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBalanceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceResponseValidationError{
				field:  "AvailableBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLedgerBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLedgerBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceResponseValidationError{
				field:  "LedgerBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Currency

	// no validation rules for AccountStatus

	if all {
		switch v := interface{}(m.GetBalanceAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceResponseValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceResponseValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceResponseValidationError{
				field:  "BalanceAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBalanceResponseMultiError(errors)
	}

	return nil
}

// GetBalanceResponseMultiError is an error wrapping multiple validation errors
// returned by GetBalanceResponse.ValidateAll() if the designated constraints
// aren't met.
type GetBalanceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBalanceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBalanceResponseMultiError) AllErrors() []error { return m }

// GetBalanceResponseValidationError is the validation error returned by
// GetBalanceResponse.Validate if the designated constraints aren't met.
type GetBalanceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBalanceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBalanceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBalanceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBalanceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBalanceResponseValidationError) ErrorName() string {
	return "GetBalanceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBalanceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBalanceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBalanceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBalanceResponseValidationError{}

// Validate checks the field values on GetOpeningBalanceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOpeningBalanceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOpeningBalanceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOpeningBalanceRequestMultiError, or nil if none found.
func (m *GetOpeningBalanceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOpeningBalanceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceRequestValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBalanceAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceRequestValidationError{
				field:  "BalanceAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeviceId

	// no validation rules for DeviceToken

	// no validation rules for CustomerId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsClosingBalanceUserGroup

	if len(errors) > 0 {
		return GetOpeningBalanceRequestMultiError(errors)
	}

	return nil
}

// GetOpeningBalanceRequestMultiError is an error wrapping multiple validation
// errors returned by GetOpeningBalanceRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOpeningBalanceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOpeningBalanceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOpeningBalanceRequestMultiError) AllErrors() []error { return m }

// GetOpeningBalanceRequestValidationError is the validation error returned by
// GetOpeningBalanceRequest.Validate if the designated constraints aren't met.
type GetOpeningBalanceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOpeningBalanceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOpeningBalanceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOpeningBalanceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOpeningBalanceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOpeningBalanceRequestValidationError) ErrorName() string {
	return "GetOpeningBalanceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOpeningBalanceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOpeningBalanceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOpeningBalanceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOpeningBalanceRequestValidationError{}

// Validate checks the field values on GetOpeningBalanceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOpeningBalanceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOpeningBalanceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOpeningBalanceResponseMultiError, or nil if none found.
func (m *GetOpeningBalanceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOpeningBalanceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	// no validation rules for AccountName

	if all {
		switch v := interface{}(m.GetOpeningBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "OpeningBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "OpeningBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOpeningBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceResponseValidationError{
				field:  "OpeningBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastClosedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "LastClosedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "LastClosedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastClosedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceResponseValidationError{
				field:  "LastClosedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBankEodAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "BankEodAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "BankEodAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankEodAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceResponseValidationError{
				field:  "BankEodAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOpeningBalanceResponseMultiError(errors)
	}

	return nil
}

// GetOpeningBalanceResponseMultiError is an error wrapping multiple validation
// errors returned by GetOpeningBalanceResponse.ValidateAll() if the
// designated constraints aren't met.
type GetOpeningBalanceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOpeningBalanceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOpeningBalanceResponseMultiError) AllErrors() []error { return m }

// GetOpeningBalanceResponseValidationError is the validation error returned by
// GetOpeningBalanceResponse.Validate if the designated constraints aren't met.
type GetOpeningBalanceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOpeningBalanceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOpeningBalanceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOpeningBalanceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOpeningBalanceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOpeningBalanceResponseValidationError) ErrorName() string {
	return "GetOpeningBalanceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOpeningBalanceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOpeningBalanceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOpeningBalanceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOpeningBalanceResponseValidationError{}

// Validate checks the field values on GetBalanceV1Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBalanceV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBalanceV1Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBalanceV1RequestMultiError, or nil if none found.
func (m *GetBalanceV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBalanceV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceV1RequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceV1RequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceV1RequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for AccountNumber

	if len(errors) > 0 {
		return GetBalanceV1RequestMultiError(errors)
	}

	return nil
}

// GetBalanceV1RequestMultiError is an error wrapping multiple validation
// errors returned by GetBalanceV1Request.ValidateAll() if the designated
// constraints aren't met.
type GetBalanceV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBalanceV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBalanceV1RequestMultiError) AllErrors() []error { return m }

// GetBalanceV1RequestValidationError is the validation error returned by
// GetBalanceV1Request.Validate if the designated constraints aren't met.
type GetBalanceV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBalanceV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBalanceV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBalanceV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBalanceV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBalanceV1RequestValidationError) ErrorName() string {
	return "GetBalanceV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBalanceV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBalanceV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBalanceV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBalanceV1RequestValidationError{}

// Validate checks the field values on GetBalanceV1Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBalanceV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBalanceV1Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBalanceV1ResponseMultiError, or nil if none found.
func (m *GetBalanceV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBalanceV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceV1ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCustomerName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "CustomerName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "CustomerName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceV1ResponseValidationError{
				field:  "CustomerName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLedgerBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLedgerBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceV1ResponseValidationError{
				field:  "LedgerBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceV1ResponseValidationError{
				field:  "AvailableBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClearanceBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "ClearanceBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "ClearanceBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClearanceBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceV1ResponseValidationError{
				field:  "ClearanceBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLienBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "LienBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "LienBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLienBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceV1ResponseValidationError{
				field:  "LienBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBalanceAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBalanceV1ResponseValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBalanceV1ResponseValidationError{
				field:  "BalanceAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FreezeStatus

	// no validation rules for FreezeReason

	if len(errors) > 0 {
		return GetBalanceV1ResponseMultiError(errors)
	}

	return nil
}

// GetBalanceV1ResponseMultiError is an error wrapping multiple validation
// errors returned by GetBalanceV1Response.ValidateAll() if the designated
// constraints aren't met.
type GetBalanceV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBalanceV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBalanceV1ResponseMultiError) AllErrors() []error { return m }

// GetBalanceV1ResponseValidationError is the validation error returned by
// GetBalanceV1Response.Validate if the designated constraints aren't met.
type GetBalanceV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBalanceV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBalanceV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBalanceV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBalanceV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBalanceV1ResponseValidationError) ErrorName() string {
	return "GetBalanceV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBalanceV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBalanceV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBalanceV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBalanceV1ResponseValidationError{}

// Validate checks the field values on UpdateNomineeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateNomineeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNomineeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateNomineeRequestMultiError, or nil if none found.
func (m *UpdateNomineeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNomineeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNomineeRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNomineeRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNomineeRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReqType

	// no validation rules for ReqId

	// no validation rules for Foracid

	// no validation rules for ServiceReqId

	// no validation rules for EkycCrrn

	// no validation rules for NomineeName

	// no validation rules for NomineeRegNo

	// no validation rules for NomineeRelType

	// no validation rules for NomineeMinorFlag

	// no validation rules for NomineeDob

	// no validation rules for NomineeAddrLine1

	// no validation rules for NomineeAddrLine2

	// no validation rules for NomineeAddrLine3

	// no validation rules for NomineeCity

	// no validation rules for NomineeState

	// no validation rules for NomineeCountry

	// no validation rules for NomineePostalCode

	// no validation rules for GuardianCode

	// no validation rules for GuardianName

	// no validation rules for Channel

	// no validation rules for ReserveFreeText1

	// no validation rules for ReserveFreeText2

	// no validation rules for ReserveFreeText3

	// no validation rules for ReserveFreeText4

	// no validation rules for ReserveFreeText5

	// no validation rules for ReserveFreeText6

	// no validation rules for ReserveFreeText7

	// no validation rules for ReserveFreeTextt8

	// no validation rules for ReserveFreeText9

	// no validation rules for ReserveFreeText10

	if len(errors) > 0 {
		return UpdateNomineeRequestMultiError(errors)
	}

	return nil
}

// UpdateNomineeRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateNomineeRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateNomineeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNomineeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNomineeRequestMultiError) AllErrors() []error { return m }

// UpdateNomineeRequestValidationError is the validation error returned by
// UpdateNomineeRequest.Validate if the designated constraints aren't met.
type UpdateNomineeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNomineeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNomineeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNomineeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNomineeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNomineeRequestValidationError) ErrorName() string {
	return "UpdateNomineeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNomineeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNomineeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNomineeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNomineeRequestValidationError{}

// Validate checks the field values on UpdateNomineeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateNomineeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNomineeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateNomineeResponseMultiError, or nil if none found.
func (m *UpdateNomineeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNomineeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNomineeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNomineeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNomineeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for CbsResponse

	// no validation rules for CbsStatus

	if len(errors) > 0 {
		return UpdateNomineeResponseMultiError(errors)
	}

	return nil
}

// UpdateNomineeResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateNomineeResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateNomineeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNomineeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNomineeResponseMultiError) AllErrors() []error { return m }

// UpdateNomineeResponseValidationError is the validation error returned by
// UpdateNomineeResponse.Validate if the designated constraints aren't met.
type UpdateNomineeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNomineeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNomineeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNomineeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNomineeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNomineeResponseValidationError) ErrorName() string {
	return "UpdateNomineeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNomineeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNomineeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNomineeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNomineeResponseValidationError{}
