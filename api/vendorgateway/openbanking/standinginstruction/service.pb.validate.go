// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/openbanking/standinginstruction/service.proto

package standinginstruction

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	payment "github.com/epifi/gamma/api/order/payment"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = payment.PaymentProtocol(0)
)

// Validate checks the field values on SIParams with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SIParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SIParams with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SIParamsMultiError, or nil
// if none found.
func (m *SIParams) ValidateAll() error {
	return m.validate(true)
}

func (m *SIParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmountMaxLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIParamsValidationError{
					field:  "AmountMaxLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIParamsValidationError{
					field:  "AmountMaxLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountMaxLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIParamsValidationError{
				field:  "AmountMaxLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValidityStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIParamsValidationError{
					field:  "ValidityStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIParamsValidationError{
					field:  "ValidityStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidityStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIParamsValidationError{
				field:  "ValidityStartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValidityEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIParamsValidationError{
					field:  "ValidityEndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIParamsValidationError{
					field:  "ValidityEndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidityEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIParamsValidationError{
				field:  "ValidityEndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurrencePattern

	// no validation rules for NumberOfOccurrences

	if len(errors) > 0 {
		return SIParamsMultiError(errors)
	}

	return nil
}

// SIParamsMultiError is an error wrapping multiple validation errors returned
// by SIParams.ValidateAll() if the designated constraints aren't met.
type SIParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SIParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SIParamsMultiError) AllErrors() []error { return m }

// SIParamsValidationError is the validation error returned by
// SIParams.Validate if the designated constraints aren't met.
type SIParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SIParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SIParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SIParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SIParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SIParamsValidationError) ErrorName() string { return "SIParamsValidationError" }

// Error satisfies the builtin error interface
func (e SIParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSIParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SIParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SIParamsValidationError{}

// Validate checks the field values on CreateRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateRequestMultiError, or
// nil if none found.
func (m *CreateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetRequestId()); l < 1 || l > 100 {
		err := CreateRequestValidationError{
			field:  "RequestId",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTransTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "TransTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "TransTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRequestValidationError{
				field:  "TransTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Protocol

	if all {
		switch v := interface{}(m.GetRemitter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "Remitter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "Remitter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRemitter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRequestValidationError{
				field:  "Remitter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBeneficiary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "Beneficiary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "Beneficiary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBeneficiary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRequestValidationError{
				field:  "Beneficiary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSiParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "SiParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRequestValidationError{
					field:  "SiParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSiParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRequestValidationError{
				field:  "SiParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserRemarks

	if len(errors) > 0 {
		return CreateRequestMultiError(errors)
	}

	return nil
}

// CreateRequestMultiError is an error wrapping multiple validation errors
// returned by CreateRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRequestMultiError) AllErrors() []error { return m }

// CreateRequestValidationError is the validation error returned by
// CreateRequest.Validate if the designated constraints aren't met.
type CreateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRequestValidationError) ErrorName() string { return "CreateRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRequestValidationError{}

// Validate checks the field values on CreateResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateResponseMultiError,
// or nil if none found.
func (m *CreateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TokenizedSiToken

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawStatusCode

	// no validation rules for RawStatusDescription

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return CreateResponseMultiError(errors)
	}

	return nil
}

// CreateResponseMultiError is an error wrapping multiple validation errors
// returned by CreateResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateResponseMultiError) AllErrors() []error { return m }

// CreateResponseValidationError is the validation error returned by
// CreateResponse.Validate if the designated constraints aren't met.
type CreateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateResponseValidationError) ErrorName() string { return "CreateResponseValidationError" }

// Error satisfies the builtin error interface
func (e CreateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateResponseValidationError{}

// Validate checks the field values on ExecuteRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExecuteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExecuteRequestMultiError,
// or nil if none found.
func (m *ExecuteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SiToken

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Protocol

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExecuteRequestMultiError(errors)
	}

	return nil
}

// ExecuteRequestMultiError is an error wrapping multiple validation errors
// returned by ExecuteRequest.ValidateAll() if the designated constraints
// aren't met.
type ExecuteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteRequestMultiError) AllErrors() []error { return m }

// ExecuteRequestValidationError is the validation error returned by
// ExecuteRequest.Validate if the designated constraints aren't met.
type ExecuteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteRequestValidationError) ErrorName() string { return "ExecuteRequestValidationError" }

// Error satisfies the builtin error interface
func (e ExecuteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteRequestValidationError{}

// Validate checks the field values on ExecuteResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExecuteResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteResponseMultiError, or nil if none found.
func (m *ExecuteResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawStatusCode

	// no validation rules for RawStatusDescription

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return ExecuteResponseMultiError(errors)
	}

	return nil
}

// ExecuteResponseMultiError is an error wrapping multiple validation errors
// returned by ExecuteResponse.ValidateAll() if the designated constraints
// aren't met.
type ExecuteResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteResponseMultiError) AllErrors() []error { return m }

// ExecuteResponseValidationError is the validation error returned by
// ExecuteResponse.Validate if the designated constraints aren't met.
type ExecuteResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteResponseValidationError) ErrorName() string { return "ExecuteResponseValidationError" }

// Error satisfies the builtin error interface
func (e ExecuteResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteResponseValidationError{}

// Validate checks the field values on ModifyRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ModifyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ModifyRequestMultiError, or
// nil if none found.
func (m *ModifyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SiToken

	if all {
		switch v := interface{}(m.GetSiParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRequestValidationError{
					field:  "SiParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRequestValidationError{
					field:  "SiParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSiParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRequestValidationError{
				field:  "SiParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ModifyRequestMultiError(errors)
	}

	return nil
}

// ModifyRequestMultiError is an error wrapping multiple validation errors
// returned by ModifyRequest.ValidateAll() if the designated constraints
// aren't met.
type ModifyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyRequestMultiError) AllErrors() []error { return m }

// ModifyRequestValidationError is the validation error returned by
// ModifyRequest.Validate if the designated constraints aren't met.
type ModifyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyRequestValidationError) ErrorName() string { return "ModifyRequestValidationError" }

// Error satisfies the builtin error interface
func (e ModifyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyRequestValidationError{}

// Validate checks the field values on ModifyResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ModifyResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ModifyResponseMultiError,
// or nil if none found.
func (m *ModifyResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawStatusCode

	// no validation rules for RawStatusDescription

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return ModifyResponseMultiError(errors)
	}

	return nil
}

// ModifyResponseMultiError is an error wrapping multiple validation errors
// returned by ModifyResponse.ValidateAll() if the designated constraints
// aren't met.
type ModifyResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyResponseMultiError) AllErrors() []error { return m }

// ModifyResponseValidationError is the validation error returned by
// ModifyResponse.Validate if the designated constraints aren't met.
type ModifyResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyResponseValidationError) ErrorName() string { return "ModifyResponseValidationError" }

// Error satisfies the builtin error interface
func (e ModifyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyResponseValidationError{}

// Validate checks the field values on RevokeRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RevokeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RevokeRequestMultiError, or
// nil if none found.
func (m *RevokeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RevokeRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RevokeRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RevokeRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RevokeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RevokeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RevokeRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for SiToken

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RevokeRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RevokeRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RevokeRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RevokeRequestMultiError(errors)
	}

	return nil
}

// RevokeRequestMultiError is an error wrapping multiple validation errors
// returned by RevokeRequest.ValidateAll() if the designated constraints
// aren't met.
type RevokeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeRequestMultiError) AllErrors() []error { return m }

// RevokeRequestValidationError is the validation error returned by
// RevokeRequest.Validate if the designated constraints aren't met.
type RevokeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeRequestValidationError) ErrorName() string { return "RevokeRequestValidationError" }

// Error satisfies the builtin error interface
func (e RevokeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeRequestValidationError{}

// Validate checks the field values on RevokeResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RevokeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RevokeResponseMultiError,
// or nil if none found.
func (m *RevokeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RevokeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RevokeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RevokeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawStatusCode

	// no validation rules for RawStatusDescription

	// no validation rules for InternalStatusCode

	if len(errors) > 0 {
		return RevokeResponseMultiError(errors)
	}

	return nil
}

// RevokeResponseMultiError is an error wrapping multiple validation errors
// returned by RevokeResponse.ValidateAll() if the designated constraints
// aren't met.
type RevokeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeResponseMultiError) AllErrors() []error { return m }

// RevokeResponseValidationError is the validation error returned by
// RevokeResponse.Validate if the designated constraints aren't met.
type RevokeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeResponseValidationError) ErrorName() string { return "RevokeResponseValidationError" }

// Error satisfies the builtin error interface
func (e RevokeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeResponseValidationError{}

// Validate checks the field values on CreateRequest_AccountDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRequest_AccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRequest_AccountDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRequest_AccountDetailsMultiError, or nil if none found.
func (m *CreateRequest_AccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRequest_AccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRequest_AccountDetailsValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRequest_AccountDetailsValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRequest_AccountDetailsValidationError{
				field:  "AccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpiVirtualId

	if len(errors) > 0 {
		return CreateRequest_AccountDetailsMultiError(errors)
	}

	return nil
}

// CreateRequest_AccountDetailsMultiError is an error wrapping multiple
// validation errors returned by CreateRequest_AccountDetails.ValidateAll() if
// the designated constraints aren't met.
type CreateRequest_AccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRequest_AccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRequest_AccountDetailsMultiError) AllErrors() []error { return m }

// CreateRequest_AccountDetailsValidationError is the validation error returned
// by CreateRequest_AccountDetails.Validate if the designated constraints
// aren't met.
type CreateRequest_AccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRequest_AccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRequest_AccountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRequest_AccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRequest_AccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRequest_AccountDetailsValidationError) ErrorName() string {
	return "CreateRequest_AccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRequest_AccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRequest_AccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRequest_AccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRequest_AccountDetailsValidationError{}
