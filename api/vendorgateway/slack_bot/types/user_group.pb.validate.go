// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/slack_bot/types/user_group.proto

package types

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UserGroup with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserGroup) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserGroup with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserGroupMultiError, or nil
// if none found.
func (m *UserGroup) ValidateAll() error {
	return m.validate(true)
}

func (m *UserGroup) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TeamId

	// no validation rules for GroupName

	// no validation rules for Handle

	if len(errors) > 0 {
		return UserGroupMultiError(errors)
	}

	return nil
}

// UserGroupMultiError is an error wrapping multiple validation errors returned
// by UserGroup.ValidateAll() if the designated constraints aren't met.
type UserGroupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserGroupMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserGroupMultiError) AllErrors() []error { return m }

// UserGroupValidationError is the validation error returned by
// UserGroup.Validate if the designated constraints aren't met.
type UserGroupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserGroupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserGroupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserGroupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserGroupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserGroupValidationError) ErrorName() string { return "UserGroupValidationError" }

// Error satisfies the builtin error interface
func (e UserGroupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserGroup.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserGroupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserGroupValidationError{}
