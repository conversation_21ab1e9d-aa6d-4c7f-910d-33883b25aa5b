// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/vkyccall/service.proto

package vkyccall

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateRoomRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateRoomRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRoomRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRoomRequestMultiError, or nil if none found.
func (m *CreateRoomRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRoomRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRoomRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRoomRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRoomRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.CustomRoomId != nil {
		// no validation rules for CustomRoomId
	}

	if m.AutoStartConfig != nil {

		if all {
			switch v := interface{}(m.GetAutoStartConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRoomRequestValidationError{
						field:  "AutoStartConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRoomRequestValidationError{
						field:  "AutoStartConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAutoStartConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRoomRequestValidationError{
					field:  "AutoStartConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.AutoCloseConfig != nil {

		if all {
			switch v := interface{}(m.GetAutoCloseConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRoomRequestValidationError{
						field:  "AutoCloseConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRoomRequestValidationError{
						field:  "AutoCloseConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAutoCloseConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRoomRequestValidationError{
					field:  "AutoCloseConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateRoomRequestMultiError(errors)
	}

	return nil
}

// CreateRoomRequestMultiError is an error wrapping multiple validation errors
// returned by CreateRoomRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateRoomRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRoomRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRoomRequestMultiError) AllErrors() []error { return m }

// CreateRoomRequestValidationError is the validation error returned by
// CreateRoomRequest.Validate if the designated constraints aren't met.
type CreateRoomRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRoomRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRoomRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRoomRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRoomRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRoomRequestValidationError) ErrorName() string {
	return "CreateRoomRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRoomRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRoomRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRoomRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRoomRequestValidationError{}

// Validate checks the field values on ValidateRoomRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateRoomRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateRoomRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateRoomRequestMultiError, or nil if none found.
func (m *ValidateRoomRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateRoomRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateRoomRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateRoomRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateRoomRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	if len(errors) > 0 {
		return ValidateRoomRequestMultiError(errors)
	}

	return nil
}

// ValidateRoomRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateRoomRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateRoomRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateRoomRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateRoomRequestMultiError) AllErrors() []error { return m }

// ValidateRoomRequestValidationError is the validation error returned by
// ValidateRoomRequest.Validate if the designated constraints aren't met.
type ValidateRoomRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateRoomRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateRoomRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateRoomRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateRoomRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateRoomRequestValidationError) ErrorName() string {
	return "ValidateRoomRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateRoomRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateRoomRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateRoomRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateRoomRequestValidationError{}

// Validate checks the field values on EndSessionRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EndSessionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EndSessionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EndSessionRequestMultiError, or nil if none found.
func (m *EndSessionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EndSessionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EndSessionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EndSessionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EndSessionRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	if m.SessionId != nil {
		// no validation rules for SessionId
	}

	if len(errors) > 0 {
		return EndSessionRequestMultiError(errors)
	}

	return nil
}

// EndSessionRequestMultiError is an error wrapping multiple validation errors
// returned by EndSessionRequest.ValidateAll() if the designated constraints
// aren't met.
type EndSessionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EndSessionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EndSessionRequestMultiError) AllErrors() []error { return m }

// EndSessionRequestValidationError is the validation error returned by
// EndSessionRequest.Validate if the designated constraints aren't met.
type EndSessionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EndSessionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EndSessionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EndSessionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EndSessionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EndSessionRequestValidationError) ErrorName() string {
	return "EndSessionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EndSessionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEndSessionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EndSessionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EndSessionRequestValidationError{}

// Validate checks the field values on RemoveParticipantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveParticipantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveParticipantRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveParticipantRequestMultiError, or nil if none found.
func (m *RemoveParticipantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveParticipantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RemoveParticipantRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RemoveParticipantRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RemoveParticipantRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	// no validation rules for ParticipantId

	if len(errors) > 0 {
		return RemoveParticipantRequestMultiError(errors)
	}

	return nil
}

// RemoveParticipantRequestMultiError is an error wrapping multiple validation
// errors returned by RemoveParticipantRequest.ValidateAll() if the designated
// constraints aren't met.
type RemoveParticipantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveParticipantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveParticipantRequestMultiError) AllErrors() []error { return m }

// RemoveParticipantRequestValidationError is the validation error returned by
// RemoveParticipantRequest.Validate if the designated constraints aren't met.
type RemoveParticipantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveParticipantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveParticipantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveParticipantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveParticipantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveParticipantRequestValidationError) ErrorName() string {
	return "RemoveParticipantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveParticipantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveParticipantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveParticipantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveParticipantRequestValidationError{}

// Validate checks the field values on DeactivateRoomRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivateRoomRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateRoomRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivateRoomRequestMultiError, or nil if none found.
func (m *DeactivateRoomRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateRoomRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateRoomRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateRoomRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateRoomRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	if len(errors) > 0 {
		return DeactivateRoomRequestMultiError(errors)
	}

	return nil
}

// DeactivateRoomRequestMultiError is an error wrapping multiple validation
// errors returned by DeactivateRoomRequest.ValidateAll() if the designated
// constraints aren't met.
type DeactivateRoomRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateRoomRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateRoomRequestMultiError) AllErrors() []error { return m }

// DeactivateRoomRequestValidationError is the validation error returned by
// DeactivateRoomRequest.Validate if the designated constraints aren't met.
type DeactivateRoomRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateRoomRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateRoomRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateRoomRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateRoomRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateRoomRequestValidationError) ErrorName() string {
	return "DeactivateRoomRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateRoomRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateRoomRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateRoomRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateRoomRequestValidationError{}

// Validate checks the field values on FetchRoomRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FetchRoomRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchRoomRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchRoomRequestMultiError, or nil if none found.
func (m *FetchRoomRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchRoomRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchRoomRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchRoomRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchRoomRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	if len(errors) > 0 {
		return FetchRoomRequestMultiError(errors)
	}

	return nil
}

// FetchRoomRequestMultiError is an error wrapping multiple validation errors
// returned by FetchRoomRequest.ValidateAll() if the designated constraints
// aren't met.
type FetchRoomRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchRoomRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchRoomRequestMultiError) AllErrors() []error { return m }

// FetchRoomRequestValidationError is the validation error returned by
// FetchRoomRequest.Validate if the designated constraints aren't met.
type FetchRoomRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchRoomRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchRoomRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchRoomRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchRoomRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchRoomRequestValidationError) ErrorName() string { return "FetchRoomRequestValidationError" }

// Error satisfies the builtin error interface
func (e FetchRoomRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchRoomRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchRoomRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchRoomRequestValidationError{}

// Validate checks the field values on FetchParticipantsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchParticipantsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchParticipantsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchParticipantsRequestMultiError, or nil if none found.
func (m *FetchParticipantsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchParticipantsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchParticipantsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchParticipantsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchParticipantsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SessionId

	if len(errors) > 0 {
		return FetchParticipantsRequestMultiError(errors)
	}

	return nil
}

// FetchParticipantsRequestMultiError is an error wrapping multiple validation
// errors returned by FetchParticipantsRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchParticipantsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchParticipantsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchParticipantsRequestMultiError) AllErrors() []error { return m }

// FetchParticipantsRequestValidationError is the validation error returned by
// FetchParticipantsRequest.Validate if the designated constraints aren't met.
type FetchParticipantsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchParticipantsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchParticipantsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchParticipantsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchParticipantsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchParticipantsRequestValidationError) ErrorName() string {
	return "FetchParticipantsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchParticipantsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchParticipantsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchParticipantsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchParticipantsRequestValidationError{}

// Validate checks the field values on CreateRoomResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRoomResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRoomResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRoomResponseMultiError, or nil if none found.
func (m *CreateRoomResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRoomResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRoomResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRoomResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRoomResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	// no validation rules for CustomRoomId

	// no validation rules for UserId

	// no validation rules for Disabled

	if all {
		switch v := interface{}(m.GetTimestamps()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRoomResponseValidationError{
					field:  "Timestamps",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRoomResponseValidationError{
					field:  "Timestamps",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamps()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRoomResponseValidationError{
				field:  "Timestamps",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetLinks()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRoomResponseValidationError{
					field:  "Links",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRoomResponseValidationError{
					field:  "Links",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLinks()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRoomResponseValidationError{
				field:  "Links",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRoomResponseMultiError(errors)
	}

	return nil
}

// CreateRoomResponseMultiError is an error wrapping multiple validation errors
// returned by CreateRoomResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateRoomResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRoomResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRoomResponseMultiError) AllErrors() []error { return m }

// CreateRoomResponseValidationError is the validation error returned by
// CreateRoomResponse.Validate if the designated constraints aren't met.
type CreateRoomResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRoomResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRoomResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRoomResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRoomResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRoomResponseValidationError) ErrorName() string {
	return "CreateRoomResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRoomResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRoomResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRoomResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRoomResponseValidationError{}

// Validate checks the field values on ValidateRoomResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateRoomResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateRoomResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateRoomResponseMultiError, or nil if none found.
func (m *ValidateRoomResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateRoomResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateRoomResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateRoomResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateRoomResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	// no validation rules for CustomRoomId

	// no validation rules for UserId

	// no validation rules for Disabled

	if all {
		switch v := interface{}(m.GetTimestamps()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateRoomResponseValidationError{
					field:  "Timestamps",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateRoomResponseValidationError{
					field:  "Timestamps",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamps()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateRoomResponseValidationError{
				field:  "Timestamps",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetLinks()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateRoomResponseValidationError{
					field:  "Links",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateRoomResponseValidationError{
					field:  "Links",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLinks()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateRoomResponseValidationError{
				field:  "Links",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateRoomResponseMultiError(errors)
	}

	return nil
}

// ValidateRoomResponseMultiError is an error wrapping multiple validation
// errors returned by ValidateRoomResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateRoomResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateRoomResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateRoomResponseMultiError) AllErrors() []error { return m }

// ValidateRoomResponseValidationError is the validation error returned by
// ValidateRoomResponse.Validate if the designated constraints aren't met.
type ValidateRoomResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateRoomResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateRoomResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateRoomResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateRoomResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateRoomResponseValidationError) ErrorName() string {
	return "ValidateRoomResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateRoomResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateRoomResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateRoomResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateRoomResponseValidationError{}

// Validate checks the field values on DeactivateRoomResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivateRoomResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateRoomResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivateRoomResponseMultiError, or nil if none found.
func (m *DeactivateRoomResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateRoomResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateRoomResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateRoomResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateRoomResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWebhook()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateRoomResponseValidationError{
					field:  "Webhook",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateRoomResponseValidationError{
					field:  "Webhook",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWebhook()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateRoomResponseValidationError{
				field:  "Webhook",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Disabled

	// no validation rules for MeetingId

	// no validation rules for UserMeetingId

	// no validation rules for UserId

	if all {
		switch v := interface{}(m.GetTimestamps()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateRoomResponseValidationError{
					field:  "Timestamps",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateRoomResponseValidationError{
					field:  "Timestamps",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamps()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateRoomResponseValidationError{
				field:  "Timestamps",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateRoomResponseValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateRoomResponseValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateRoomResponseValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Id

	if len(errors) > 0 {
		return DeactivateRoomResponseMultiError(errors)
	}

	return nil
}

// DeactivateRoomResponseMultiError is an error wrapping multiple validation
// errors returned by DeactivateRoomResponse.ValidateAll() if the designated
// constraints aren't met.
type DeactivateRoomResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateRoomResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateRoomResponseMultiError) AllErrors() []error { return m }

// DeactivateRoomResponseValidationError is the validation error returned by
// DeactivateRoomResponse.Validate if the designated constraints aren't met.
type DeactivateRoomResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateRoomResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateRoomResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateRoomResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateRoomResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateRoomResponseValidationError) ErrorName() string {
	return "DeactivateRoomResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateRoomResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateRoomResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateRoomResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateRoomResponseValidationError{}

// Validate checks the field values on EndSessionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EndSessionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EndSessionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EndSessionResponseMultiError, or nil if none found.
func (m *EndSessionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EndSessionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EndSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EndSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EndSessionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Start

	for idx, item := range m.GetParticipants() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EndSessionResponseValidationError{
						field:  fmt.Sprintf("Participants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EndSessionResponseValidationError{
						field:  fmt.Sprintf("Participants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EndSessionResponseValidationError{
					field:  fmt.Sprintf("Participants[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Id

	// no validation rules for RoomId

	// no validation rules for MeetingStatus

	if all {
		switch v := interface{}(m.GetLinks()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EndSessionResponseValidationError{
					field:  "Links",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EndSessionResponseValidationError{
					field:  "Links",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLinks()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EndSessionResponseValidationError{
				field:  "Links",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.End != nil {
		// no validation rules for End
	}

	if len(errors) > 0 {
		return EndSessionResponseMultiError(errors)
	}

	return nil
}

// EndSessionResponseMultiError is an error wrapping multiple validation errors
// returned by EndSessionResponse.ValidateAll() if the designated constraints
// aren't met.
type EndSessionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EndSessionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EndSessionResponseMultiError) AllErrors() []error { return m }

// EndSessionResponseValidationError is the validation error returned by
// EndSessionResponse.Validate if the designated constraints aren't met.
type EndSessionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EndSessionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EndSessionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EndSessionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EndSessionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EndSessionResponseValidationError) ErrorName() string {
	return "EndSessionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EndSessionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEndSessionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EndSessionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EndSessionResponseValidationError{}

// Validate checks the field values on RemoveParticipantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveParticipantResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveParticipantResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveParticipantResponseMultiError, or nil if none found.
func (m *RemoveParticipantResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveParticipantResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RemoveParticipantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RemoveParticipantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RemoveParticipantResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Message

	if len(errors) > 0 {
		return RemoveParticipantResponseMultiError(errors)
	}

	return nil
}

// RemoveParticipantResponseMultiError is an error wrapping multiple validation
// errors returned by RemoveParticipantResponse.ValidateAll() if the
// designated constraints aren't met.
type RemoveParticipantResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveParticipantResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveParticipantResponseMultiError) AllErrors() []error { return m }

// RemoveParticipantResponseValidationError is the validation error returned by
// RemoveParticipantResponse.Validate if the designated constraints aren't met.
type RemoveParticipantResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveParticipantResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveParticipantResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveParticipantResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveParticipantResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveParticipantResponseValidationError) ErrorName() string {
	return "RemoveParticipantResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveParticipantResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveParticipantResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveParticipantResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveParticipantResponseValidationError{}

// Validate checks the field values on StartRecordingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartRecordingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartRecordingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartRecordingRequestMultiError, or nil if none found.
func (m *StartRecordingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StartRecordingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartRecordingRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartRecordingRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartRecordingRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	if all {
		switch v := interface{}(m.GetRecording()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartRecordingRequestValidationError{
					field:  "Recording",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartRecordingRequestValidationError{
					field:  "Recording",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecording()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartRecordingRequestValidationError{
				field:  "Recording",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StartRecordingRequestMultiError(errors)
	}

	return nil
}

// StartRecordingRequestMultiError is an error wrapping multiple validation
// errors returned by StartRecordingRequest.ValidateAll() if the designated
// constraints aren't met.
type StartRecordingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartRecordingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartRecordingRequestMultiError) AllErrors() []error { return m }

// StartRecordingRequestValidationError is the validation error returned by
// StartRecordingRequest.Validate if the designated constraints aren't met.
type StartRecordingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartRecordingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartRecordingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartRecordingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartRecordingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartRecordingRequestValidationError) ErrorName() string {
	return "StartRecordingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StartRecordingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartRecordingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartRecordingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartRecordingRequestValidationError{}

// Validate checks the field values on StartRecordingResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartRecordingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartRecordingResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartRecordingResponseMultiError, or nil if none found.
func (m *StartRecordingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StartRecordingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartRecordingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartRecordingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartRecordingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Message

	if len(errors) > 0 {
		return StartRecordingResponseMultiError(errors)
	}

	return nil
}

// StartRecordingResponseMultiError is an error wrapping multiple validation
// errors returned by StartRecordingResponse.ValidateAll() if the designated
// constraints aren't met.
type StartRecordingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartRecordingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartRecordingResponseMultiError) AllErrors() []error { return m }

// StartRecordingResponseValidationError is the validation error returned by
// StartRecordingResponse.Validate if the designated constraints aren't met.
type StartRecordingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartRecordingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartRecordingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartRecordingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartRecordingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartRecordingResponseValidationError) ErrorName() string {
	return "StartRecordingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StartRecordingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartRecordingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartRecordingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartRecordingResponseValidationError{}

// Validate checks the field values on StopRecordingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StopRecordingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopRecordingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StopRecordingRequestMultiError, or nil if none found.
func (m *StopRecordingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StopRecordingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StopRecordingRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StopRecordingRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StopRecordingRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	if len(errors) > 0 {
		return StopRecordingRequestMultiError(errors)
	}

	return nil
}

// StopRecordingRequestMultiError is an error wrapping multiple validation
// errors returned by StopRecordingRequest.ValidateAll() if the designated
// constraints aren't met.
type StopRecordingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopRecordingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopRecordingRequestMultiError) AllErrors() []error { return m }

// StopRecordingRequestValidationError is the validation error returned by
// StopRecordingRequest.Validate if the designated constraints aren't met.
type StopRecordingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopRecordingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopRecordingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopRecordingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopRecordingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopRecordingRequestValidationError) ErrorName() string {
	return "StopRecordingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StopRecordingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopRecordingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopRecordingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopRecordingRequestValidationError{}

// Validate checks the field values on StopRecordingResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StopRecordingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopRecordingResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StopRecordingResponseMultiError, or nil if none found.
func (m *StopRecordingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StopRecordingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StopRecordingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StopRecordingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StopRecordingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Message

	if len(errors) > 0 {
		return StopRecordingResponseMultiError(errors)
	}

	return nil
}

// StopRecordingResponseMultiError is an error wrapping multiple validation
// errors returned by StopRecordingResponse.ValidateAll() if the designated
// constraints aren't met.
type StopRecordingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopRecordingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopRecordingResponseMultiError) AllErrors() []error { return m }

// StopRecordingResponseValidationError is the validation error returned by
// StopRecordingResponse.Validate if the designated constraints aren't met.
type StopRecordingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopRecordingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopRecordingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopRecordingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopRecordingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopRecordingResponseValidationError) ErrorName() string {
	return "StopRecordingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StopRecordingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopRecordingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopRecordingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopRecordingResponseValidationError{}

// Validate checks the field values on GenerateJwtRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateJwtRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateJwtRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateJwtRequestMultiError, or nil if none found.
func (m *GenerateJwtRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateJwtRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateJwtRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateJwtRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateJwtRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	// no validation rules for ParticipantId

	// no validation rules for Role

	if len(errors) > 0 {
		return GenerateJwtRequestMultiError(errors)
	}

	return nil
}

// GenerateJwtRequestMultiError is an error wrapping multiple validation errors
// returned by GenerateJwtRequest.ValidateAll() if the designated constraints
// aren't met.
type GenerateJwtRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateJwtRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateJwtRequestMultiError) AllErrors() []error { return m }

// GenerateJwtRequestValidationError is the validation error returned by
// GenerateJwtRequest.Validate if the designated constraints aren't met.
type GenerateJwtRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateJwtRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateJwtRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateJwtRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateJwtRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateJwtRequestValidationError) ErrorName() string {
	return "GenerateJwtRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateJwtRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateJwtRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateJwtRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateJwtRequestValidationError{}

// Validate checks the field values on GenerateJwtResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateJwtResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateJwtResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateJwtResponseMultiError, or nil if none found.
func (m *GenerateJwtResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateJwtResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateJwtResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateJwtResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateJwtResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	if len(errors) > 0 {
		return GenerateJwtResponseMultiError(errors)
	}

	return nil
}

// GenerateJwtResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateJwtResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateJwtResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateJwtResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateJwtResponseMultiError) AllErrors() []error { return m }

// GenerateJwtResponseValidationError is the validation error returned by
// GenerateJwtResponse.Validate if the designated constraints aren't met.
type GenerateJwtResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateJwtResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateJwtResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateJwtResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateJwtResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateJwtResponseValidationError) ErrorName() string {
	return "GenerateJwtResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateJwtResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateJwtResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateJwtResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateJwtResponseValidationError{}

// Validate checks the field values on FetchActiveParticipantsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchActiveParticipantsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchActiveParticipantsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchActiveParticipantsRequestMultiError, or nil if none found.
func (m *FetchActiveParticipantsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchActiveParticipantsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchActiveParticipantsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchActiveParticipantsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchActiveParticipantsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SessionId

	if len(errors) > 0 {
		return FetchActiveParticipantsRequestMultiError(errors)
	}

	return nil
}

// FetchActiveParticipantsRequestMultiError is an error wrapping multiple
// validation errors returned by FetchActiveParticipantsRequest.ValidateAll()
// if the designated constraints aren't met.
type FetchActiveParticipantsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchActiveParticipantsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchActiveParticipantsRequestMultiError) AllErrors() []error { return m }

// FetchActiveParticipantsRequestValidationError is the validation error
// returned by FetchActiveParticipantsRequest.Validate if the designated
// constraints aren't met.
type FetchActiveParticipantsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchActiveParticipantsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchActiveParticipantsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchActiveParticipantsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchActiveParticipantsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchActiveParticipantsRequestValidationError) ErrorName() string {
	return "FetchActiveParticipantsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchActiveParticipantsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchActiveParticipantsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchActiveParticipantsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchActiveParticipantsRequestValidationError{}

// Validate checks the field values on FetchActiveParticipantsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchActiveParticipantsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchActiveParticipantsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchActiveParticipantsResponseMultiError, or nil if none found.
func (m *FetchActiveParticipantsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchActiveParticipantsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchActiveParticipantsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchActiveParticipantsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchActiveParticipantsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NumberOfActiveParticipants

	if len(errors) > 0 {
		return FetchActiveParticipantsResponseMultiError(errors)
	}

	return nil
}

// FetchActiveParticipantsResponseMultiError is an error wrapping multiple
// validation errors returned by FetchActiveParticipantsResponse.ValidateAll()
// if the designated constraints aren't met.
type FetchActiveParticipantsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchActiveParticipantsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchActiveParticipantsResponseMultiError) AllErrors() []error { return m }

// FetchActiveParticipantsResponseValidationError is the validation error
// returned by FetchActiveParticipantsResponse.Validate if the designated
// constraints aren't met.
type FetchActiveParticipantsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchActiveParticipantsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchActiveParticipantsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchActiveParticipantsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchActiveParticipantsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchActiveParticipantsResponseValidationError) ErrorName() string {
	return "FetchActiveParticipantsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchActiveParticipantsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchActiveParticipantsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchActiveParticipantsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchActiveParticipantsResponseValidationError{}

// Validate checks the field values on FetchLastSessionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchLastSessionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchLastSessionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchLastSessionRequestMultiError, or nil if none found.
func (m *FetchLastSessionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchLastSessionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchLastSessionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchLastSessionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchLastSessionRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RoomId

	if len(errors) > 0 {
		return FetchLastSessionRequestMultiError(errors)
	}

	return nil
}

// FetchLastSessionRequestMultiError is an error wrapping multiple validation
// errors returned by FetchLastSessionRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchLastSessionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchLastSessionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchLastSessionRequestMultiError) AllErrors() []error { return m }

// FetchLastSessionRequestValidationError is the validation error returned by
// FetchLastSessionRequest.Validate if the designated constraints aren't met.
type FetchLastSessionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchLastSessionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchLastSessionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchLastSessionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchLastSessionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchLastSessionRequestValidationError) ErrorName() string {
	return "FetchLastSessionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchLastSessionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchLastSessionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchLastSessionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchLastSessionRequestValidationError{}

// Validate checks the field values on FetchLastSessionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchLastSessionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchLastSessionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchLastSessionResponseMultiError, or nil if none found.
func (m *FetchLastSessionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchLastSessionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchLastSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchLastSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchLastSessionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LastSessionId

	if len(errors) > 0 {
		return FetchLastSessionResponseMultiError(errors)
	}

	return nil
}

// FetchLastSessionResponseMultiError is an error wrapping multiple validation
// errors returned by FetchLastSessionResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchLastSessionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchLastSessionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchLastSessionResponseMultiError) AllErrors() []error { return m }

// FetchLastSessionResponseValidationError is the validation error returned by
// FetchLastSessionResponse.Validate if the designated constraints aren't met.
type FetchLastSessionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchLastSessionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchLastSessionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchLastSessionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchLastSessionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchLastSessionResponseValidationError) ErrorName() string {
	return "FetchLastSessionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchLastSessionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchLastSessionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchLastSessionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchLastSessionResponseValidationError{}
