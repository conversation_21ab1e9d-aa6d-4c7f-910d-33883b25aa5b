// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/casper/pagination.proto

package casper

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PageContextResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageContextResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageContextResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageContextResponseMultiError, or nil if none found.
func (m *PageContextResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PageContextResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BeforeToken

	// no validation rules for HasBefore

	// no validation rules for AfterToken

	// no validation rules for HasAfter

	if len(errors) > 0 {
		return PageContextResponseMultiError(errors)
	}

	return nil
}

// PageContextResponseMultiError is an error wrapping multiple validation
// errors returned by PageContextResponse.ValidateAll() if the designated
// constraints aren't met.
type PageContextResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageContextResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageContextResponseMultiError) AllErrors() []error { return m }

// PageContextResponseValidationError is the validation error returned by
// PageContextResponse.Validate if the designated constraints aren't met.
type PageContextResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageContextResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageContextResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageContextResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageContextResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageContextResponseValidationError) ErrorName() string {
	return "PageContextResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PageContextResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageContextResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageContextResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageContextResponseValidationError{}

// Validate checks the field values on PageContextRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageContextRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageContextRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageContextRequestMultiError, or nil if none found.
func (m *PageContextRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageContextRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageSize

	switch v := m.Token.(type) {
	case *PageContextRequest_BeforeToken:
		if v == nil {
			err := PageContextRequestValidationError{
				field:  "Token",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for BeforeToken
	case *PageContextRequest_AfterToken:
		if v == nil {
			err := PageContextRequestValidationError{
				field:  "Token",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AfterToken
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PageContextRequestMultiError(errors)
	}

	return nil
}

// PageContextRequestMultiError is an error wrapping multiple validation errors
// returned by PageContextRequest.ValidateAll() if the designated constraints
// aren't met.
type PageContextRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageContextRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageContextRequestMultiError) AllErrors() []error { return m }

// PageContextRequestValidationError is the validation error returned by
// PageContextRequest.Validate if the designated constraints aren't met.
type PageContextRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageContextRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageContextRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageContextRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageContextRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageContextRequestValidationError) ErrorName() string {
	return "PageContextRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PageContextRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageContextRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageContextRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageContextRequestValidationError{}
