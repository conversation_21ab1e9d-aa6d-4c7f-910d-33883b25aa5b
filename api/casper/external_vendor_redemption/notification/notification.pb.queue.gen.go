// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/casper/external_vendor_redemption/notification
package notification

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessFiStoreOrderNotificationEventMethod = "ProcessFiStoreOrderNotificationEvent"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &OrderNotificationEvent{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *OrderNotificationEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessFiStoreOrderNotificationEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessFiStoreOrderNotificationEventMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessFiStoreOrderNotificationEventMethod)
}
