// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/casper/offer_catalog_service.proto

package casper

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOfferRequestMultiError, or nil if none found.
func (m *CreateOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for Price

	// no validation rules for OfferType

	for idx, item := range m.GetImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateOfferRequestValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateOfferRequestValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateOfferRequestValidationError{
					field:  fmt.Sprintf("Images[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTnc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOfferRequestValidationError{
					field:  "Tnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOfferRequestValidationError{
					field:  "Tnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTnc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOfferRequestValidationError{
				field:  "Tnc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RedemptionMode

	// no validation rules for VendorName

	if all {
		switch v := interface{}(m.GetVendorOfferMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOfferRequestValidationError{
					field:  "VendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOfferRequestValidationError{
					field:  "VendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorOfferMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOfferRequestValidationError{
				field:  "VendorOfferMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOfferRequestValidationError{
					field:  "OfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOfferRequestValidationError{
					field:  "OfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOfferRequestValidationError{
				field:  "OfferMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetOfferAdditionalDetails() == nil {
		err := CreateOfferRequestValidationError{
			field:  "OfferAdditionalDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOfferAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOfferRequestValidationError{
					field:  "OfferAdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOfferRequestValidationError{
					field:  "OfferAdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOfferRequestValidationError{
				field:  "OfferAdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CategoryTag

	// no validation rules for SubCategoryTag

	if len(errors) > 0 {
		return CreateOfferRequestMultiError(errors)
	}

	return nil
}

// CreateOfferRequestMultiError is an error wrapping multiple validation errors
// returned by CreateOfferRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOfferRequestMultiError) AllErrors() []error { return m }

// CreateOfferRequestValidationError is the validation error returned by
// CreateOfferRequest.Validate if the designated constraints aren't met.
type CreateOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOfferRequestValidationError) ErrorName() string {
	return "CreateOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOfferRequestValidationError{}

// Validate checks the field values on CreateOfferResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOfferResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOfferResponseMultiError, or nil if none found.
func (m *CreateOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferId

	for idx, item := range m.GetValidationFailureInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateOfferResponseValidationError{
						field:  fmt.Sprintf("ValidationFailureInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateOfferResponseValidationError{
						field:  fmt.Sprintf("ValidationFailureInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateOfferResponseValidationError{
					field:  fmt.Sprintf("ValidationFailureInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateOfferResponseMultiError(errors)
	}

	return nil
}

// CreateOfferResponseMultiError is an error wrapping multiple validation
// errors returned by CreateOfferResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOfferResponseMultiError) AllErrors() []error { return m }

// CreateOfferResponseValidationError is the validation error returned by
// CreateOfferResponse.Validate if the designated constraints aren't met.
type CreateOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOfferResponseValidationError) ErrorName() string {
	return "CreateOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOfferResponseValidationError{}

// Validate checks the field values on UpdateOfferDisplayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateOfferDisplayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateOfferDisplayRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateOfferDisplayRequestMultiError, or nil if none found.
func (m *UpdateOfferDisplayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOfferDisplayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	// no validation rules for Name

	// no validation rules for Desc

	for idx, item := range m.GetImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateOfferDisplayRequestValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateOfferDisplayRequestValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateOfferDisplayRequestValidationError{
					field:  fmt.Sprintf("Images[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTnc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateOfferDisplayRequestValidationError{
					field:  "Tnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateOfferDisplayRequestValidationError{
					field:  "Tnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTnc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateOfferDisplayRequestValidationError{
				field:  "Tnc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateOfferDisplayRequestValidationError{
					field:  "OfferAdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateOfferDisplayRequestValidationError{
					field:  "OfferAdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateOfferDisplayRequestValidationError{
				field:  "OfferAdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CategoryTag

	// no validation rules for SubCategoryTag

	if len(errors) > 0 {
		return UpdateOfferDisplayRequestMultiError(errors)
	}

	return nil
}

// UpdateOfferDisplayRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateOfferDisplayRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateOfferDisplayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOfferDisplayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOfferDisplayRequestMultiError) AllErrors() []error { return m }

// UpdateOfferDisplayRequestValidationError is the validation error returned by
// UpdateOfferDisplayRequest.Validate if the designated constraints aren't met.
type UpdateOfferDisplayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOfferDisplayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOfferDisplayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOfferDisplayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOfferDisplayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOfferDisplayRequestValidationError) ErrorName() string {
	return "UpdateOfferDisplayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOfferDisplayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOfferDisplayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOfferDisplayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOfferDisplayRequestValidationError{}

// Validate checks the field values on UpdateOfferDisplayResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateOfferDisplayResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateOfferDisplayResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateOfferDisplayResponseMultiError, or nil if none found.
func (m *UpdateOfferDisplayResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOfferDisplayResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateOfferDisplayResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateOfferDisplayResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateOfferDisplayResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateOfferDisplayResponseMultiError(errors)
	}

	return nil
}

// UpdateOfferDisplayResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateOfferDisplayResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateOfferDisplayResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOfferDisplayResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOfferDisplayResponseMultiError) AllErrors() []error { return m }

// UpdateOfferDisplayResponseValidationError is the validation error returned
// by UpdateOfferDisplayResponse.Validate if the designated constraints aren't met.
type UpdateOfferDisplayResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOfferDisplayResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOfferDisplayResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOfferDisplayResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOfferDisplayResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOfferDisplayResponseValidationError) ErrorName() string {
	return "UpdateOfferDisplayResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOfferDisplayResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOfferDisplayResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOfferDisplayResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOfferDisplayResponseValidationError{}

// Validate checks the field values on GetOfferDetailsByIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOfferDetailsByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOfferDetailsByIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOfferDetailsByIdRequestMultiError, or nil if none found.
func (m *GetOfferDetailsByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOfferDetailsByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	if len(errors) > 0 {
		return GetOfferDetailsByIdRequestMultiError(errors)
	}

	return nil
}

// GetOfferDetailsByIdRequestMultiError is an error wrapping multiple
// validation errors returned by GetOfferDetailsByIdRequest.ValidateAll() if
// the designated constraints aren't met.
type GetOfferDetailsByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOfferDetailsByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOfferDetailsByIdRequestMultiError) AllErrors() []error { return m }

// GetOfferDetailsByIdRequestValidationError is the validation error returned
// by GetOfferDetailsByIdRequest.Validate if the designated constraints aren't met.
type GetOfferDetailsByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOfferDetailsByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOfferDetailsByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOfferDetailsByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOfferDetailsByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOfferDetailsByIdRequestValidationError) ErrorName() string {
	return "GetOfferDetailsByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOfferDetailsByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOfferDetailsByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOfferDetailsByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOfferDetailsByIdRequestValidationError{}

// Validate checks the field values on GetOfferDetailsByIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOfferDetailsByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOfferDetailsByIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOfferDetailsByIdResponseMultiError, or nil if none found.
func (m *GetOfferDetailsByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOfferDetailsByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOfferDetailsByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOfferDetailsByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOfferDetailsByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOfferDetailsByIdResponseValidationError{
					field:  "Offer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOfferDetailsByIdResponseValidationError{
					field:  "Offer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOfferDetailsByIdResponseValidationError{
				field:  "Offer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOfferDetailsByIdResponseMultiError(errors)
	}

	return nil
}

// GetOfferDetailsByIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetOfferDetailsByIdResponse.ValidateAll() if
// the designated constraints aren't met.
type GetOfferDetailsByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOfferDetailsByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOfferDetailsByIdResponseMultiError) AllErrors() []error { return m }

// GetOfferDetailsByIdResponseValidationError is the validation error returned
// by GetOfferDetailsByIdResponse.Validate if the designated constraints
// aren't met.
type GetOfferDetailsByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOfferDetailsByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOfferDetailsByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOfferDetailsByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOfferDetailsByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOfferDetailsByIdResponseValidationError) ErrorName() string {
	return "GetOfferDetailsByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOfferDetailsByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOfferDetailsByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOfferDetailsByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOfferDetailsByIdResponseValidationError{}

// Validate checks the field values on GetBulkOfferDetailsByIdsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBulkOfferDetailsByIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBulkOfferDetailsByIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBulkOfferDetailsByIdsRequestMultiError, or nil if none found.
func (m *GetBulkOfferDetailsByIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBulkOfferDetailsByIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RedemptionMode

	// no validation rules for WithUpdatedVendorOfferDetails

	if len(errors) > 0 {
		return GetBulkOfferDetailsByIdsRequestMultiError(errors)
	}

	return nil
}

// GetBulkOfferDetailsByIdsRequestMultiError is an error wrapping multiple
// validation errors returned by GetBulkOfferDetailsByIdsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetBulkOfferDetailsByIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBulkOfferDetailsByIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBulkOfferDetailsByIdsRequestMultiError) AllErrors() []error { return m }

// GetBulkOfferDetailsByIdsRequestValidationError is the validation error
// returned by GetBulkOfferDetailsByIdsRequest.Validate if the designated
// constraints aren't met.
type GetBulkOfferDetailsByIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBulkOfferDetailsByIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBulkOfferDetailsByIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBulkOfferDetailsByIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBulkOfferDetailsByIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBulkOfferDetailsByIdsRequestValidationError) ErrorName() string {
	return "GetBulkOfferDetailsByIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBulkOfferDetailsByIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBulkOfferDetailsByIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBulkOfferDetailsByIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBulkOfferDetailsByIdsRequestValidationError{}

// Validate checks the field values on GetBulkOfferDetailsByIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetBulkOfferDetailsByIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBulkOfferDetailsByIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBulkOfferDetailsByIdsResponseMultiError, or nil if none found.
func (m *GetBulkOfferDetailsByIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBulkOfferDetailsByIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBulkOfferDetailsByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBulkOfferDetailsByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBulkOfferDetailsByIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBulkOfferDetailsByIdsResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBulkOfferDetailsByIdsResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBulkOfferDetailsByIdsResponseValidationError{
					field:  fmt.Sprintf("Offers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetBulkOfferDetailsByIdsResponseMultiError(errors)
	}

	return nil
}

// GetBulkOfferDetailsByIdsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetBulkOfferDetailsByIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetBulkOfferDetailsByIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBulkOfferDetailsByIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBulkOfferDetailsByIdsResponseMultiError) AllErrors() []error { return m }

// GetBulkOfferDetailsByIdsResponseValidationError is the validation error
// returned by GetBulkOfferDetailsByIdsResponse.Validate if the designated
// constraints aren't met.
type GetBulkOfferDetailsByIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBulkOfferDetailsByIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBulkOfferDetailsByIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBulkOfferDetailsByIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBulkOfferDetailsByIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBulkOfferDetailsByIdsResponseValidationError) ErrorName() string {
	return "GetBulkOfferDetailsByIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBulkOfferDetailsByIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBulkOfferDetailsByIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBulkOfferDetailsByIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBulkOfferDetailsByIdsResponseValidationError{}

// Validate checks the field values on DeleteOfferByIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteOfferByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteOfferByIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteOfferByIdRequestMultiError, or nil if none found.
func (m *DeleteOfferByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteOfferByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	if len(errors) > 0 {
		return DeleteOfferByIdRequestMultiError(errors)
	}

	return nil
}

// DeleteOfferByIdRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteOfferByIdRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteOfferByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteOfferByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteOfferByIdRequestMultiError) AllErrors() []error { return m }

// DeleteOfferByIdRequestValidationError is the validation error returned by
// DeleteOfferByIdRequest.Validate if the designated constraints aren't met.
type DeleteOfferByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteOfferByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteOfferByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteOfferByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteOfferByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteOfferByIdRequestValidationError) ErrorName() string {
	return "DeleteOfferByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteOfferByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteOfferByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteOfferByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteOfferByIdRequestValidationError{}

// Validate checks the field values on DeleteOfferByIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteOfferByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteOfferByIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteOfferByIdResponseMultiError, or nil if none found.
func (m *DeleteOfferByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteOfferByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteOfferByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteOfferByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteOfferByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteOfferByIdResponseMultiError(errors)
	}

	return nil
}

// DeleteOfferByIdResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteOfferByIdResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteOfferByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteOfferByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteOfferByIdResponseMultiError) AllErrors() []error { return m }

// DeleteOfferByIdResponseValidationError is the validation error returned by
// DeleteOfferByIdResponse.Validate if the designated constraints aren't met.
type DeleteOfferByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteOfferByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteOfferByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteOfferByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteOfferByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteOfferByIdResponseValidationError) ErrorName() string {
	return "DeleteOfferByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteOfferByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteOfferByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteOfferByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteOfferByIdResponseValidationError{}

// Validate checks the field values on
// GetExternalVendorDynamicWebpageUrlRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetExternalVendorDynamicWebpageUrlRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExternalVendorDynamicWebpageUrlRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetExternalVendorDynamicWebpageUrlRequestMultiError, or nil if none found.
func (m *GetExternalVendorDynamicWebpageUrlRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExternalVendorDynamicWebpageUrlRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for VendorName

	// no validation rules for TargetUrl

	if len(errors) > 0 {
		return GetExternalVendorDynamicWebpageUrlRequestMultiError(errors)
	}

	return nil
}

// GetExternalVendorDynamicWebpageUrlRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetExternalVendorDynamicWebpageUrlRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExternalVendorDynamicWebpageUrlRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExternalVendorDynamicWebpageUrlRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExternalVendorDynamicWebpageUrlRequestMultiError) AllErrors() []error { return m }

// GetExternalVendorDynamicWebpageUrlRequestValidationError is the validation
// error returned by GetExternalVendorDynamicWebpageUrlRequest.Validate if the
// designated constraints aren't met.
type GetExternalVendorDynamicWebpageUrlRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExternalVendorDynamicWebpageUrlRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExternalVendorDynamicWebpageUrlRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExternalVendorDynamicWebpageUrlRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExternalVendorDynamicWebpageUrlRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExternalVendorDynamicWebpageUrlRequestValidationError) ErrorName() string {
	return "GetExternalVendorDynamicWebpageUrlRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExternalVendorDynamicWebpageUrlRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExternalVendorDynamicWebpageUrlRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExternalVendorDynamicWebpageUrlRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExternalVendorDynamicWebpageUrlRequestValidationError{}

// Validate checks the field values on
// GetExternalVendorDynamicWebpageUrlResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetExternalVendorDynamicWebpageUrlResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExternalVendorDynamicWebpageUrlResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetExternalVendorDynamicWebpageUrlResponseMultiError, or nil if none found.
func (m *GetExternalVendorDynamicWebpageUrlResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExternalVendorDynamicWebpageUrlResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExternalVendorDynamicWebpageUrlResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExternalVendorDynamicWebpageUrlResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExternalVendorDynamicWebpageUrlResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WebpageUrl

	if len(errors) > 0 {
		return GetExternalVendorDynamicWebpageUrlResponseMultiError(errors)
	}

	return nil
}

// GetExternalVendorDynamicWebpageUrlResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetExternalVendorDynamicWebpageUrlResponse.ValidateAll() if the designated
// constraints aren't met.
type GetExternalVendorDynamicWebpageUrlResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExternalVendorDynamicWebpageUrlResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExternalVendorDynamicWebpageUrlResponseMultiError) AllErrors() []error { return m }

// GetExternalVendorDynamicWebpageUrlResponseValidationError is the validation
// error returned by GetExternalVendorDynamicWebpageUrlResponse.Validate if
// the designated constraints aren't met.
type GetExternalVendorDynamicWebpageUrlResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExternalVendorDynamicWebpageUrlResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExternalVendorDynamicWebpageUrlResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExternalVendorDynamicWebpageUrlResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExternalVendorDynamicWebpageUrlResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExternalVendorDynamicWebpageUrlResponseValidationError) ErrorName() string {
	return "GetExternalVendorDynamicWebpageUrlResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExternalVendorDynamicWebpageUrlResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExternalVendorDynamicWebpageUrlResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExternalVendorDynamicWebpageUrlResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExternalVendorDynamicWebpageUrlResponseValidationError{}

// Validate checks the field values on
// CreateOfferResponse_ValidationFailureInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateOfferResponse_ValidationFailureInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateOfferResponse_ValidationFailureInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateOfferResponse_ValidationFailureInfoMultiError, or nil if none found.
func (m *CreateOfferResponse_ValidationFailureInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOfferResponse_ValidationFailureInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FailureMessage

	if len(errors) > 0 {
		return CreateOfferResponse_ValidationFailureInfoMultiError(errors)
	}

	return nil
}

// CreateOfferResponse_ValidationFailureInfoMultiError is an error wrapping
// multiple validation errors returned by
// CreateOfferResponse_ValidationFailureInfo.ValidateAll() if the designated
// constraints aren't met.
type CreateOfferResponse_ValidationFailureInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOfferResponse_ValidationFailureInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOfferResponse_ValidationFailureInfoMultiError) AllErrors() []error { return m }

// CreateOfferResponse_ValidationFailureInfoValidationError is the validation
// error returned by CreateOfferResponse_ValidationFailureInfo.Validate if the
// designated constraints aren't met.
type CreateOfferResponse_ValidationFailureInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOfferResponse_ValidationFailureInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOfferResponse_ValidationFailureInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOfferResponse_ValidationFailureInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOfferResponse_ValidationFailureInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOfferResponse_ValidationFailureInfoValidationError) ErrorName() string {
	return "CreateOfferResponse_ValidationFailureInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOfferResponse_ValidationFailureInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOfferResponse_ValidationFailureInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOfferResponse_ValidationFailureInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOfferResponse_ValidationFailureInfoValidationError{}
