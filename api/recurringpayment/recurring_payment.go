package recurringpayment

import (
	"time"

	"github.com/epifi/be-common/pkg/pagination"

	rpEnumsPb "github.com/epifi/gamma/api/recurringpayment/enums"
)

type RecurringPaymentRows []*RecurringPayment

func (rs RecurringPaymentRows) Slice(start, end int) pagination.Rows { return rs[start:end] }
func (rs RecurringPaymentRows) GetTimestamp(index int) time.Time     { return rs[index].CreatedAt.AsTime() }
func (rs RecurringPaymentRows) Size() int                            { return len(rs) }

// ToNewRecurringPaymentState converts the RecurringPaymentState to rpEnumsPb.RecurringPaymentState
// This method is added to maintain forward compatibility with the new rpEnumsPb.RecurringPaymentState as RecurringPaymentState is deprecated now.
func (r RecurringPaymentState) ToNewRecurringPaymentState() rpEnumsPb.RecurringPaymentState {
	newRpState, ok := rpEnumsPb.RecurringPaymentState_name[int32(r)]
	if !ok {
		return rpEnumsPb.RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED
	}
	return rpEnumsPb.RecurringPaymentState(rpEnumsPb.RecurringPaymentState_value[newRpState])
}

// GetRecurringPaymentStateFromNewRecurringPaymentState returns RecurringPaymentState from rpEnumsPb.RecurringPaymentState
// This method is added to maintain backward compatibility with the old RecurringPaymentState which is now deprecated.
func GetRecurringPaymentStateFromNewRecurringPaymentState(newRpState rpEnumsPb.RecurringPaymentState) RecurringPaymentState {
	oldRpState, ok := RecurringPaymentState_name[int32(newRpState)]
	if !ok {
		return RecurringPaymentState_RECURRING_PAYMENT_STATE_UNSPECIFIED
	}
	return RecurringPaymentState(RecurringPaymentState_value[oldRpState])
}
