// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/bankcust/event.proto

package bankcust

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on BankCustomerUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankCustomerUpdateEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankCustomerUpdateEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankCustomerUpdateEventMultiError, or nil if none found.
func (m *BankCustomerUpdateEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *BankCustomerUpdateEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankCustomerUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankCustomerUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankCustomerUpdateEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EventId

	if all {
		switch v := interface{}(m.GetEventTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankCustomerUpdateEventValidationError{
					field:  "EventTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankCustomerUpdateEventValidationError{
					field:  "EventTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankCustomerUpdateEventValidationError{
				field:  "EventTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for Vendor

	if len(errors) > 0 {
		return BankCustomerUpdateEventMultiError(errors)
	}

	return nil
}

// BankCustomerUpdateEventMultiError is an error wrapping multiple validation
// errors returned by BankCustomerUpdateEvent.ValidateAll() if the designated
// constraints aren't met.
type BankCustomerUpdateEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankCustomerUpdateEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankCustomerUpdateEventMultiError) AllErrors() []error { return m }

// BankCustomerUpdateEventValidationError is the validation error returned by
// BankCustomerUpdateEvent.Validate if the designated constraints aren't met.
type BankCustomerUpdateEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankCustomerUpdateEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankCustomerUpdateEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankCustomerUpdateEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankCustomerUpdateEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankCustomerUpdateEventValidationError) ErrorName() string {
	return "BankCustomerUpdateEventValidationError"
}

// Error satisfies the builtin error interface
func (e BankCustomerUpdateEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankCustomerUpdateEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankCustomerUpdateEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankCustomerUpdateEventValidationError{}
