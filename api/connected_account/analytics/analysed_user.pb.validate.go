// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/connected_account/analytics/analysed_user.proto

package analytics

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on AnalysedUser with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AnalysedUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnalysedUser with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AnalysedUserMultiError, or
// nil if none found.
func (m *AnalysedUser) ValidateAll() error {
	return m.validate(true)
}

func (m *AnalysedUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetAnalysis()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysedUserValidationError{
					field:  "Analysis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysedUserValidationError{
					field:  "Analysis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalysis()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysedUserValidationError{
				field:  "Analysis",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysedUserValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysedUserValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysedUserValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysedUserValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysedUserValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysedUserValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysedUserValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysedUserValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysedUserValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AnalysedUserMultiError(errors)
	}

	return nil
}

// AnalysedUserMultiError is an error wrapping multiple validation errors
// returned by AnalysedUser.ValidateAll() if the designated constraints aren't met.
type AnalysedUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalysedUserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalysedUserMultiError) AllErrors() []error { return m }

// AnalysedUserValidationError is the validation error returned by
// AnalysedUser.Validate if the designated constraints aren't met.
type AnalysedUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalysedUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalysedUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalysedUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalysedUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalysedUserValidationError) ErrorName() string { return "AnalysedUserValidationError" }

// Error satisfies the builtin error interface
func (e AnalysedUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalysedUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalysedUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalysedUserValidationError{}

// Validate checks the field values on Analysis with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Analysis) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Analysis with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AnalysisMultiError, or nil
// if none found.
func (m *Analysis) ValidateAll() error {
	return m.validate(true)
}

func (m *Analysis) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for L1AnalysisFilePath

	if all {
		switch v := interface{}(m.GetL1AnalysisCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisValidationError{
					field:  "L1AnalysisCompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisValidationError{
					field:  "L1AnalysisCompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetL1AnalysisCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisValidationError{
				field:  "L1AnalysisCompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for L2AnalysisFilePath

	if all {
		switch v := interface{}(m.GetL2AnalysisCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisValidationError{
					field:  "L2AnalysisCompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisValidationError{
					field:  "L2AnalysisCompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetL2AnalysisCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisValidationError{
				field:  "L2AnalysisCompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserStatistics()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisValidationError{
					field:  "UserStatistics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisValidationError{
					field:  "UserStatistics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserStatistics()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisValidationError{
				field:  "UserStatistics",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetAccountAnalyses()))
		i := 0
		for key := range m.GetAccountAnalyses() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetAccountAnalyses()[key]
			_ = val

			// no validation rules for AccountAnalyses[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, AnalysisValidationError{
							field:  fmt.Sprintf("AccountAnalyses[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, AnalysisValidationError{
							field:  fmt.Sprintf("AccountAnalyses[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return AnalysisValidationError{
						field:  fmt.Sprintf("AccountAnalyses[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return AnalysisMultiError(errors)
	}

	return nil
}

// AnalysisMultiError is an error wrapping multiple validation errors returned
// by Analysis.ValidateAll() if the designated constraints aren't met.
type AnalysisMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalysisMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalysisMultiError) AllErrors() []error { return m }

// AnalysisValidationError is the validation error returned by
// Analysis.Validate if the designated constraints aren't met.
type AnalysisValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalysisValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalysisValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalysisValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalysisValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalysisValidationError) ErrorName() string { return "AnalysisValidationError" }

// Error satisfies the builtin error interface
func (e AnalysisValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalysis.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalysisValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalysisValidationError{}

// Validate checks the field values on UserStatistics with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserStatistics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserStatistics with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserStatisticsMultiError,
// or nil if none found.
func (m *UserStatistics) ValidateAll() error {
	return m.validate(true)
}

func (m *UserStatistics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UserStatisticsMultiError(errors)
	}

	return nil
}

// UserStatisticsMultiError is an error wrapping multiple validation errors
// returned by UserStatistics.ValidateAll() if the designated constraints
// aren't met.
type UserStatisticsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserStatisticsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserStatisticsMultiError) AllErrors() []error { return m }

// UserStatisticsValidationError is the validation error returned by
// UserStatistics.Validate if the designated constraints aren't met.
type UserStatisticsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserStatisticsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserStatisticsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserStatisticsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserStatisticsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserStatisticsValidationError) ErrorName() string { return "UserStatisticsValidationError" }

// Error satisfies the builtin error interface
func (e UserStatisticsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserStatistics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserStatisticsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserStatisticsValidationError{}

// Validate checks the field values on AccountAnalysis with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountAnalysis) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountAnalysis with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountAnalysisMultiError, or nil if none found.
func (m *AccountAnalysis) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountAnalysis) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFirstAnalysedTransactionTs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountAnalysisValidationError{
					field:  "FirstAnalysedTransactionTs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountAnalysisValidationError{
					field:  "FirstAnalysedTransactionTs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFirstAnalysedTransactionTs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountAnalysisValidationError{
				field:  "FirstAnalysedTransactionTs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastAnalysedTransactionTs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountAnalysisValidationError{
					field:  "LastAnalysedTransactionTs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountAnalysisValidationError{
					field:  "LastAnalysedTransactionTs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastAnalysedTransactionTs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountAnalysisValidationError{
				field:  "LastAnalysedTransactionTs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatistics()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountAnalysisValidationError{
					field:  "Statistics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountAnalysisValidationError{
					field:  "Statistics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatistics()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountAnalysisValidationError{
				field:  "Statistics",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountAnalysisMultiError(errors)
	}

	return nil
}

// AccountAnalysisMultiError is an error wrapping multiple validation errors
// returned by AccountAnalysis.ValidateAll() if the designated constraints
// aren't met.
type AccountAnalysisMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountAnalysisMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountAnalysisMultiError) AllErrors() []error { return m }

// AccountAnalysisValidationError is the validation error returned by
// AccountAnalysis.Validate if the designated constraints aren't met.
type AccountAnalysisValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountAnalysisValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountAnalysisValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountAnalysisValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountAnalysisValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountAnalysisValidationError) ErrorName() string { return "AccountAnalysisValidationError" }

// Error satisfies the builtin error interface
func (e AccountAnalysisValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountAnalysis.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountAnalysisValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountAnalysisValidationError{}

// Validate checks the field values on AccountStatistics with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountStatistics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountStatistics with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountStatisticsMultiError, or nil if none found.
func (m *AccountStatistics) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountStatistics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsSalaryAccount

	if all {
		switch v := interface{}(m.GetMonthlySalary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountStatisticsValidationError{
					field:  "MonthlySalary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountStatisticsValidationError{
					field:  "MonthlySalary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlySalary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountStatisticsValidationError{
				field:  "MonthlySalary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountStatisticsMultiError(errors)
	}

	return nil
}

// AccountStatisticsMultiError is an error wrapping multiple validation errors
// returned by AccountStatistics.ValidateAll() if the designated constraints
// aren't met.
type AccountStatisticsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountStatisticsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountStatisticsMultiError) AllErrors() []error { return m }

// AccountStatisticsValidationError is the validation error returned by
// AccountStatistics.Validate if the designated constraints aren't met.
type AccountStatisticsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountStatisticsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountStatisticsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountStatisticsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountStatisticsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountStatisticsValidationError) ErrorName() string {
	return "AccountStatisticsValidationError"
}

// Error satisfies the builtin error interface
func (e AccountStatisticsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountStatistics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountStatisticsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountStatisticsValidationError{}
