// Code generated by MockGen. DO NOT EDIT.
// Source: api/investment/mutualfund/order/reverse_feed/consumer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consumer "github.com/epifi/gamma/api/investment/mutualfund/order/reverse_feed/consumer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockReverseFeedConsumerClient is a mock of ReverseFeedConsumerClient interface.
type MockReverseFeedConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockReverseFeedConsumerClientMockRecorder
}

// MockReverseFeedConsumerClientMockRecorder is the mock recorder for MockReverseFeedConsumerClient.
type MockReverseFeedConsumerClientMockRecorder struct {
	mock *MockReverseFeedConsumerClient
}

// NewMockReverseFeedConsumerClient creates a new mock instance.
func NewMockReverseFeedConsumerClient(ctrl *gomock.Controller) *MockReverseFeedConsumerClient {
	mock := &MockReverseFeedConsumerClient{ctrl: ctrl}
	mock.recorder = &MockReverseFeedConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReverseFeedConsumerClient) EXPECT() *MockReverseFeedConsumerClientMockRecorder {
	return m.recorder
}

// ProcessFeedFile mocks base method.
func (m *MockReverseFeedConsumerClient) ProcessFeedFile(ctx context.Context, in *consumer.ProcessFeedFileRequest, opts ...grpc.CallOption) (*consumer.ProcessFeedFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessFeedFile", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessFeedFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFeedFile indicates an expected call of ProcessFeedFile.
func (mr *MockReverseFeedConsumerClientMockRecorder) ProcessFeedFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFeedFile", reflect.TypeOf((*MockReverseFeedConsumerClient)(nil).ProcessFeedFile), varargs...)
}

// MockReverseFeedConsumerServer is a mock of ReverseFeedConsumerServer interface.
type MockReverseFeedConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockReverseFeedConsumerServerMockRecorder
}

// MockReverseFeedConsumerServerMockRecorder is the mock recorder for MockReverseFeedConsumerServer.
type MockReverseFeedConsumerServerMockRecorder struct {
	mock *MockReverseFeedConsumerServer
}

// NewMockReverseFeedConsumerServer creates a new mock instance.
func NewMockReverseFeedConsumerServer(ctrl *gomock.Controller) *MockReverseFeedConsumerServer {
	mock := &MockReverseFeedConsumerServer{ctrl: ctrl}
	mock.recorder = &MockReverseFeedConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReverseFeedConsumerServer) EXPECT() *MockReverseFeedConsumerServerMockRecorder {
	return m.recorder
}

// ProcessFeedFile mocks base method.
func (m *MockReverseFeedConsumerServer) ProcessFeedFile(arg0 context.Context, arg1 *consumer.ProcessFeedFileRequest) (*consumer.ProcessFeedFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessFeedFile", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessFeedFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFeedFile indicates an expected call of ProcessFeedFile.
func (mr *MockReverseFeedConsumerServerMockRecorder) ProcessFeedFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFeedFile", reflect.TypeOf((*MockReverseFeedConsumerServer)(nil).ProcessFeedFile), arg0, arg1)
}

// MockUnsafeReverseFeedConsumerServer is a mock of UnsafeReverseFeedConsumerServer interface.
type MockUnsafeReverseFeedConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeReverseFeedConsumerServerMockRecorder
}

// MockUnsafeReverseFeedConsumerServerMockRecorder is the mock recorder for MockUnsafeReverseFeedConsumerServer.
type MockUnsafeReverseFeedConsumerServerMockRecorder struct {
	mock *MockUnsafeReverseFeedConsumerServer
}

// NewMockUnsafeReverseFeedConsumerServer creates a new mock instance.
func NewMockUnsafeReverseFeedConsumerServer(ctrl *gomock.Controller) *MockUnsafeReverseFeedConsumerServer {
	mock := &MockUnsafeReverseFeedConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeReverseFeedConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeReverseFeedConsumerServer) EXPECT() *MockUnsafeReverseFeedConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedReverseFeedConsumerServer mocks base method.
func (m *MockUnsafeReverseFeedConsumerServer) mustEmbedUnimplementedReverseFeedConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedReverseFeedConsumerServer")
}

// mustEmbedUnimplementedReverseFeedConsumerServer indicates an expected call of mustEmbedUnimplementedReverseFeedConsumerServer.
func (mr *MockUnsafeReverseFeedConsumerServerMockRecorder) mustEmbedUnimplementedReverseFeedConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedReverseFeedConsumerServer", reflect.TypeOf((*MockUnsafeReverseFeedConsumerServer)(nil).mustEmbedUnimplementedReverseFeedConsumerServer))
}
