// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/investment/mutualfund/order/order_reconciliation_updates.proto

package order

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OrderReconUpdates keeps track of all the updates done on mf_orders table because of Reconciliation script.
type OrderReconciliationUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OrderId   string                 `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	FolioId   string                 `protobuf:"bytes,3,opt,name=folio_id,json=folioId,proto3" json:"folio_id,omitempty"`
	Amount    *money.Money           `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	Units     float64                `protobuf:"fixed64,5,opt,name=units,proto3" json:"units,omitempty"`
	Nav       *money.Money           `protobuf:"bytes,6,opt,name=nav,proto3" json:"nav,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// order last updated time stamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// order deleted time stamp
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *OrderReconciliationUpdate) Reset() {
	*x = OrderReconciliationUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_order_reconciliation_updates_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderReconciliationUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderReconciliationUpdate) ProtoMessage() {}

func (x *OrderReconciliationUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_order_reconciliation_updates_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderReconciliationUpdate.ProtoReflect.Descriptor instead.
func (*OrderReconciliationUpdate) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDescGZIP(), []int{0}
}

func (x *OrderReconciliationUpdate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrderReconciliationUpdate) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *OrderReconciliationUpdate) GetFolioId() string {
	if x != nil {
		return x.FolioId
	}
	return ""
}

func (x *OrderReconciliationUpdate) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *OrderReconciliationUpdate) GetUnits() float64 {
	if x != nil {
		return x.Units
	}
	return 0
}

func (x *OrderReconciliationUpdate) GetNav() *money.Money {
	if x != nil {
		return x.Nav
	}
	return nil
}

func (x *OrderReconciliationUpdate) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *OrderReconciliationUpdate) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *OrderReconciliationUpdate) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_investment_mutualfund_order_order_reconciliation_updates_proto protoreflect.FileDescriptor

var file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDesc = []byte{
	0x0a, 0x42, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xfa, 0x02, 0x0a, 0x19, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69,
	0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05,
	0x75, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x24, 0x0a, 0x03, 0x6e, 0x61, 0x76, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x03, 0x6e, 0x61, 0x76, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x70, 0x0a, 0x36,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDescOnce sync.Once
	file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDescData = file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDesc
)

func file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDescGZIP() []byte {
	file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDescOnce.Do(func() {
		file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDescData)
	})
	return file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDescData
}

var file_api_investment_mutualfund_order_order_reconciliation_updates_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_investment_mutualfund_order_order_reconciliation_updates_proto_goTypes = []interface{}{
	(*OrderReconciliationUpdate)(nil), // 0: api.investment.mutualfund.order.OrderReconciliationUpdate
	(*money.Money)(nil),               // 1: google.type.Money
	(*timestamppb.Timestamp)(nil),     // 2: google.protobuf.Timestamp
}
var file_api_investment_mutualfund_order_order_reconciliation_updates_proto_depIdxs = []int32{
	1, // 0: api.investment.mutualfund.order.OrderReconciliationUpdate.amount:type_name -> google.type.Money
	1, // 1: api.investment.mutualfund.order.OrderReconciliationUpdate.nav:type_name -> google.type.Money
	2, // 2: api.investment.mutualfund.order.OrderReconciliationUpdate.created_at:type_name -> google.protobuf.Timestamp
	2, // 3: api.investment.mutualfund.order.OrderReconciliationUpdate.updated_at:type_name -> google.protobuf.Timestamp
	2, // 4: api.investment.mutualfund.order.OrderReconciliationUpdate.deleted_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_investment_mutualfund_order_order_reconciliation_updates_proto_init() }
func file_api_investment_mutualfund_order_order_reconciliation_updates_proto_init() {
	if File_api_investment_mutualfund_order_order_reconciliation_updates_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_investment_mutualfund_order_order_reconciliation_updates_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderReconciliationUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_investment_mutualfund_order_order_reconciliation_updates_proto_goTypes,
		DependencyIndexes: file_api_investment_mutualfund_order_order_reconciliation_updates_proto_depIdxs,
		MessageInfos:      file_api_investment_mutualfund_order_order_reconciliation_updates_proto_msgTypes,
	}.Build()
	File_api_investment_mutualfund_order_order_reconciliation_updates_proto = out.File
	file_api_investment_mutualfund_order_order_reconciliation_updates_proto_rawDesc = nil
	file_api_investment_mutualfund_order_order_reconciliation_updates_proto_goTypes = nil
	file_api_investment_mutualfund_order_order_reconciliation_updates_proto_depIdxs = nil
}
