// Code generated by MockGen. DO NOT EDIT.
// Source: api/investment/mutualfund/payment_handler/consumer/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consumer "github.com/epifi/gamma/api/investment/mutualfund/payment_handler/consumer"
	order "github.com/epifi/gamma/api/order"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPaymentUpdateEventProcessorClient is a mock of PaymentUpdateEventProcessorClient interface.
type MockPaymentUpdateEventProcessorClient struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentUpdateEventProcessorClientMockRecorder
}

// MockPaymentUpdateEventProcessorClientMockRecorder is the mock recorder for MockPaymentUpdateEventProcessorClient.
type MockPaymentUpdateEventProcessorClientMockRecorder struct {
	mock *MockPaymentUpdateEventProcessorClient
}

// NewMockPaymentUpdateEventProcessorClient creates a new mock instance.
func NewMockPaymentUpdateEventProcessorClient(ctrl *gomock.Controller) *MockPaymentUpdateEventProcessorClient {
	mock := &MockPaymentUpdateEventProcessorClient{ctrl: ctrl}
	mock.recorder = &MockPaymentUpdateEventProcessorClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentUpdateEventProcessorClient) EXPECT() *MockPaymentUpdateEventProcessorClientMockRecorder {
	return m.recorder
}

// UpdatePaymentStatusFromOMS mocks base method.
func (m *MockPaymentUpdateEventProcessorClient) UpdatePaymentStatusFromOMS(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*consumer.UpdatePaymentStatusFromOMSResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePaymentStatusFromOMS", varargs...)
	ret0, _ := ret[0].(*consumer.UpdatePaymentStatusFromOMSResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePaymentStatusFromOMS indicates an expected call of UpdatePaymentStatusFromOMS.
func (mr *MockPaymentUpdateEventProcessorClientMockRecorder) UpdatePaymentStatusFromOMS(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePaymentStatusFromOMS", reflect.TypeOf((*MockPaymentUpdateEventProcessorClient)(nil).UpdatePaymentStatusFromOMS), varargs...)
}

// MockPaymentUpdateEventProcessorServer is a mock of PaymentUpdateEventProcessorServer interface.
type MockPaymentUpdateEventProcessorServer struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentUpdateEventProcessorServerMockRecorder
}

// MockPaymentUpdateEventProcessorServerMockRecorder is the mock recorder for MockPaymentUpdateEventProcessorServer.
type MockPaymentUpdateEventProcessorServerMockRecorder struct {
	mock *MockPaymentUpdateEventProcessorServer
}

// NewMockPaymentUpdateEventProcessorServer creates a new mock instance.
func NewMockPaymentUpdateEventProcessorServer(ctrl *gomock.Controller) *MockPaymentUpdateEventProcessorServer {
	mock := &MockPaymentUpdateEventProcessorServer{ctrl: ctrl}
	mock.recorder = &MockPaymentUpdateEventProcessorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentUpdateEventProcessorServer) EXPECT() *MockPaymentUpdateEventProcessorServerMockRecorder {
	return m.recorder
}

// UpdatePaymentStatusFromOMS mocks base method.
func (m *MockPaymentUpdateEventProcessorServer) UpdatePaymentStatusFromOMS(arg0 context.Context, arg1 *order.OrderUpdate) (*consumer.UpdatePaymentStatusFromOMSResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePaymentStatusFromOMS", arg0, arg1)
	ret0, _ := ret[0].(*consumer.UpdatePaymentStatusFromOMSResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePaymentStatusFromOMS indicates an expected call of UpdatePaymentStatusFromOMS.
func (mr *MockPaymentUpdateEventProcessorServerMockRecorder) UpdatePaymentStatusFromOMS(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePaymentStatusFromOMS", reflect.TypeOf((*MockPaymentUpdateEventProcessorServer)(nil).UpdatePaymentStatusFromOMS), arg0, arg1)
}

// MockUnsafePaymentUpdateEventProcessorServer is a mock of UnsafePaymentUpdateEventProcessorServer interface.
type MockUnsafePaymentUpdateEventProcessorServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafePaymentUpdateEventProcessorServerMockRecorder
}

// MockUnsafePaymentUpdateEventProcessorServerMockRecorder is the mock recorder for MockUnsafePaymentUpdateEventProcessorServer.
type MockUnsafePaymentUpdateEventProcessorServerMockRecorder struct {
	mock *MockUnsafePaymentUpdateEventProcessorServer
}

// NewMockUnsafePaymentUpdateEventProcessorServer creates a new mock instance.
func NewMockUnsafePaymentUpdateEventProcessorServer(ctrl *gomock.Controller) *MockUnsafePaymentUpdateEventProcessorServer {
	mock := &MockUnsafePaymentUpdateEventProcessorServer{ctrl: ctrl}
	mock.recorder = &MockUnsafePaymentUpdateEventProcessorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafePaymentUpdateEventProcessorServer) EXPECT() *MockUnsafePaymentUpdateEventProcessorServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedPaymentUpdateEventProcessorServer mocks base method.
func (m *MockUnsafePaymentUpdateEventProcessorServer) mustEmbedUnimplementedPaymentUpdateEventProcessorServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPaymentUpdateEventProcessorServer")
}

// mustEmbedUnimplementedPaymentUpdateEventProcessorServer indicates an expected call of mustEmbedUnimplementedPaymentUpdateEventProcessorServer.
func (mr *MockUnsafePaymentUpdateEventProcessorServerMockRecorder) mustEmbedUnimplementedPaymentUpdateEventProcessorServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPaymentUpdateEventProcessorServer", reflect.TypeOf((*MockUnsafePaymentUpdateEventProcessorServer)(nil).mustEmbedUnimplementedPaymentUpdateEventProcessorServer))
}
