// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/investment/dynamic_ui_element/service.proto

package dynamic_ui_element

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DynamicUIElementService_CreateOrUpdateVariant_FullMethodName                 = "/api.investment.dynamic_ui_element.DynamicUIElementService/CreateOrUpdateVariant"
	DynamicUIElementService_UpdateDynamicUIElementEvaluatorConfig_FullMethodName = "/api.investment.dynamic_ui_element.DynamicUIElementService/UpdateDynamicUIElementEvaluatorConfig"
	DynamicUIElementService_EvaluateAndFetchDynamicUIElement_FullMethodName      = "/api.investment.dynamic_ui_element.DynamicUIElementService/EvaluateAndFetchDynamicUIElement"
)

// DynamicUIElementServiceClient is the client API for DynamicUIElementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DynamicUIElementServiceClient interface {
	// CreateOrUpdateVariant
	// 1) creates if the variant_name is not present in the table.
	// 2) updates the content json if the variant name is present in the config.
	// this rpc also returns the DynamicUIElementVariant object after creating/updating.
	CreateOrUpdateVariant(ctx context.Context, in *CreateOrUpdateVariantRequest, opts ...grpc.CallOption) (*CreateOrUpdateVariantResponse, error)
	// UpdateDynamicUIElementEvaluatorConfig updates the DynamicUIElementEvaluatorConfig for a screen and a usecase
	// for eg. evaluator expressions with a variant name can be added here.
	UpdateDynamicUIElementEvaluatorConfig(ctx context.Context, in *UpdateDynamicUIElementEvaluatorConfigRequest, opts ...grpc.CallOption) (*UpdateDynamicUIElementEvaluatorConfigResponse, error)
	// EvaluateAndFetchDynamicUIElement rpc fetches the dynamic ui element for a screen, usecase and after evaluating the expression, returns
	// DynamicUIElement apt for that case.
	// for eg. investment landing + banner + ac1, this rpc evaluates the dynamic ui element to be shown and returns the same.
	EvaluateAndFetchDynamicUIElement(ctx context.Context, in *EvaluateAndFetchDynamicUIElementRequest, opts ...grpc.CallOption) (*EvaluateAndFetchDynamicUIElementResponse, error)
}

type dynamicUIElementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDynamicUIElementServiceClient(cc grpc.ClientConnInterface) DynamicUIElementServiceClient {
	return &dynamicUIElementServiceClient{cc}
}

func (c *dynamicUIElementServiceClient) CreateOrUpdateVariant(ctx context.Context, in *CreateOrUpdateVariantRequest, opts ...grpc.CallOption) (*CreateOrUpdateVariantResponse, error) {
	out := new(CreateOrUpdateVariantResponse)
	err := c.cc.Invoke(ctx, DynamicUIElementService_CreateOrUpdateVariant_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dynamicUIElementServiceClient) UpdateDynamicUIElementEvaluatorConfig(ctx context.Context, in *UpdateDynamicUIElementEvaluatorConfigRequest, opts ...grpc.CallOption) (*UpdateDynamicUIElementEvaluatorConfigResponse, error) {
	out := new(UpdateDynamicUIElementEvaluatorConfigResponse)
	err := c.cc.Invoke(ctx, DynamicUIElementService_UpdateDynamicUIElementEvaluatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dynamicUIElementServiceClient) EvaluateAndFetchDynamicUIElement(ctx context.Context, in *EvaluateAndFetchDynamicUIElementRequest, opts ...grpc.CallOption) (*EvaluateAndFetchDynamicUIElementResponse, error) {
	out := new(EvaluateAndFetchDynamicUIElementResponse)
	err := c.cc.Invoke(ctx, DynamicUIElementService_EvaluateAndFetchDynamicUIElement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DynamicUIElementServiceServer is the server API for DynamicUIElementService service.
// All implementations should embed UnimplementedDynamicUIElementServiceServer
// for forward compatibility
type DynamicUIElementServiceServer interface {
	// CreateOrUpdateVariant
	// 1) creates if the variant_name is not present in the table.
	// 2) updates the content json if the variant name is present in the config.
	// this rpc also returns the DynamicUIElementVariant object after creating/updating.
	CreateOrUpdateVariant(context.Context, *CreateOrUpdateVariantRequest) (*CreateOrUpdateVariantResponse, error)
	// UpdateDynamicUIElementEvaluatorConfig updates the DynamicUIElementEvaluatorConfig for a screen and a usecase
	// for eg. evaluator expressions with a variant name can be added here.
	UpdateDynamicUIElementEvaluatorConfig(context.Context, *UpdateDynamicUIElementEvaluatorConfigRequest) (*UpdateDynamicUIElementEvaluatorConfigResponse, error)
	// EvaluateAndFetchDynamicUIElement rpc fetches the dynamic ui element for a screen, usecase and after evaluating the expression, returns
	// DynamicUIElement apt for that case.
	// for eg. investment landing + banner + ac1, this rpc evaluates the dynamic ui element to be shown and returns the same.
	EvaluateAndFetchDynamicUIElement(context.Context, *EvaluateAndFetchDynamicUIElementRequest) (*EvaluateAndFetchDynamicUIElementResponse, error)
}

// UnimplementedDynamicUIElementServiceServer should be embedded to have forward compatible implementations.
type UnimplementedDynamicUIElementServiceServer struct {
}

func (UnimplementedDynamicUIElementServiceServer) CreateOrUpdateVariant(context.Context, *CreateOrUpdateVariantRequest) (*CreateOrUpdateVariantResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateVariant not implemented")
}
func (UnimplementedDynamicUIElementServiceServer) UpdateDynamicUIElementEvaluatorConfig(context.Context, *UpdateDynamicUIElementEvaluatorConfigRequest) (*UpdateDynamicUIElementEvaluatorConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDynamicUIElementEvaluatorConfig not implemented")
}
func (UnimplementedDynamicUIElementServiceServer) EvaluateAndFetchDynamicUIElement(context.Context, *EvaluateAndFetchDynamicUIElementRequest) (*EvaluateAndFetchDynamicUIElementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EvaluateAndFetchDynamicUIElement not implemented")
}

// UnsafeDynamicUIElementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DynamicUIElementServiceServer will
// result in compilation errors.
type UnsafeDynamicUIElementServiceServer interface {
	mustEmbedUnimplementedDynamicUIElementServiceServer()
}

func RegisterDynamicUIElementServiceServer(s grpc.ServiceRegistrar, srv DynamicUIElementServiceServer) {
	s.RegisterService(&DynamicUIElementService_ServiceDesc, srv)
}

func _DynamicUIElementService_CreateOrUpdateVariant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateVariantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DynamicUIElementServiceServer).CreateOrUpdateVariant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DynamicUIElementService_CreateOrUpdateVariant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DynamicUIElementServiceServer).CreateOrUpdateVariant(ctx, req.(*CreateOrUpdateVariantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DynamicUIElementService_UpdateDynamicUIElementEvaluatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDynamicUIElementEvaluatorConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DynamicUIElementServiceServer).UpdateDynamicUIElementEvaluatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DynamicUIElementService_UpdateDynamicUIElementEvaluatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DynamicUIElementServiceServer).UpdateDynamicUIElementEvaluatorConfig(ctx, req.(*UpdateDynamicUIElementEvaluatorConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DynamicUIElementService_EvaluateAndFetchDynamicUIElement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EvaluateAndFetchDynamicUIElementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DynamicUIElementServiceServer).EvaluateAndFetchDynamicUIElement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DynamicUIElementService_EvaluateAndFetchDynamicUIElement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DynamicUIElementServiceServer).EvaluateAndFetchDynamicUIElement(ctx, req.(*EvaluateAndFetchDynamicUIElementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DynamicUIElementService_ServiceDesc is the grpc.ServiceDesc for DynamicUIElementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DynamicUIElementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.investment.dynamic_ui_element.DynamicUIElementService",
	HandlerType: (*DynamicUIElementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrUpdateVariant",
			Handler:    _DynamicUIElementService_CreateOrUpdateVariant_Handler,
		},
		{
			MethodName: "UpdateDynamicUIElementEvaluatorConfig",
			Handler:    _DynamicUIElementService_UpdateDynamicUIElementEvaluatorConfig_Handler,
		},
		{
			MethodName: "EvaluateAndFetchDynamicUIElement",
			Handler:    _DynamicUIElementService_EvaluateAndFetchDynamicUIElement_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/investment/dynamic_ui_element/service.proto",
}
