// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/preapprovedloan/enums/lms.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the LmsPartner in string format in DB
func (p LmsPartner) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LmsPartner while reading from DB
func (p *LmsPartner) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LmsPartner_value[val]
	if !ok {
		return fmt.Errorf("unexpected LmsPartner value: %s", val)
	}
	*p = LmsPartner(valInt)
	return nil
}

// Marshaler interface implementation for LmsPartner
func (x LmsPartner) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LmsPartner
func (x *LmsPartner) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LmsPartner(LmsPartner_value[val])
	return nil
}
