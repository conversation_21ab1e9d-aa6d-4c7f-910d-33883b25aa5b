syntax = "proto3";

package pan.epan.consumer;

import "api/queue/consumer_headers.proto";
import "api/pan/epan/consumer/epan_notification_event.proto";
import "api/kyc/vkyc/event/event.proto";

option go_package = "github.com/epifi/gamma/api/pan/epan/consumer";
option java_package = "com.github.epifi.gamma.api.pan.epan.consumer";

service Consumer {
  // ProcessEPANComms will consume the notification event and send notification (PN, SMS and Whatsapp etc)
  rpc ProcessEPANComms (EPANCommsEvent) returns (ProcessEPANCommsResponse) {}

  // ProcessVKYCCallCompletedEvent consumes the vkyc call completed event published by the kyc service.
  // On receiving the event, we will upload the epan file on sftp server
  rpc ProcessVKYCCallCompletedEvent(kyc.vkyc.event.VKYCCallCompletedEvent) returns (ProcessVKYCCallCompletedEventResponse);
}

message ProcessEPANCommsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessVKYCCallCompletedEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
