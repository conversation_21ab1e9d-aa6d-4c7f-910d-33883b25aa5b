//go:generate gen_queue_pb
syntax = "proto3";

package paymentinstrument;

import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/paymentinstrument/consumer";
option java_package = "com.github.epifi.gamma.api.paymentinstrument.consumer";

service Consumer {

  // PurgeAaAccountPiRelation purges the aa account pi relation for the given account id
  rpc PurgeAaAccountPiRelation(PurgeAaAccountPiRelationRequest) returns (PurgeAaAccountPiRelationResponse) {};

  // PurgeWealthPis purges the pi for given piIds if the ownership is wealth and the pi is not getting used in any other trasnaction
  rpc PurgeWealthPis(PurgeWealthPisRequest) returns (PurgeWealthPisResponse) {};
}

message PurgeAaAccountPiRelationRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  // account id for which the relation needs to be deleted
  string account_id = 2;
}

message PurgeAaAccountPiRelationResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message PurgeWealthPisRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  // pi ids for which the wealth pis needs to be deleted
  repeated string pi_ids = 2;
}

message PurgeWealthPisResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}
