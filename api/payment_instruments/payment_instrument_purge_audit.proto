// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package paymentinstrument;

import "api/payment_instruments/payment_instrument.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/paymentinstrument";
option java_package = "com.github.epifi.gamma.api.paymentinstrument";

// PaymentInstrumentPurgeAudit stores audit record for payment instrument purging against a pi id
message PaymentInstrumentPurgeAudit {
  string pi_id = 1;

  paymentinstrument.PaymentInstrument payload = 2;

  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  google.protobuf.Timestamp deleted_at = 5;
}
