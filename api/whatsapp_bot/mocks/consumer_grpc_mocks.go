// Code generated by MockGen. DO NOT EDIT.
// Source: api/./whatsapp_bot/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	whatsapp_bot "github.com/epifi/gamma/api/whatsapp_bot"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockWhatsappBotConsumerClient is a mock of WhatsappBotConsumerClient interface.
type MockWhatsappBotConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockWhatsappBotConsumerClientMockRecorder
}

// MockWhatsappBotConsumerClientMockRecorder is the mock recorder for MockWhatsappBotConsumerClient.
type MockWhatsappBotConsumerClientMockRecorder struct {
	mock *MockWhatsappBotConsumerClient
}

// NewMockWhatsappBotConsumerClient creates a new mock instance.
func NewMockWhatsappBotConsumerClient(ctrl *gomock.Controller) *MockWhatsappBotConsumerClient {
	mock := &MockWhatsappBotConsumerClient{ctrl: ctrl}
	mock.recorder = &MockWhatsappBotConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWhatsappBotConsumerClient) EXPECT() *MockWhatsappBotConsumerClientMockRecorder {
	return m.recorder
}

// ProcessUserMessage mocks base method.
func (m *MockWhatsappBotConsumerClient) ProcessUserMessage(ctx context.Context, in *whatsapp_bot.ProcessUserMessageRequest, opts ...grpc.CallOption) (*whatsapp_bot.ProcessUserMessageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessUserMessage", varargs...)
	ret0, _ := ret[0].(*whatsapp_bot.ProcessUserMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessUserMessage indicates an expected call of ProcessUserMessage.
func (mr *MockWhatsappBotConsumerClientMockRecorder) ProcessUserMessage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessUserMessage", reflect.TypeOf((*MockWhatsappBotConsumerClient)(nil).ProcessUserMessage), varargs...)
}

// MockWhatsappBotConsumerServer is a mock of WhatsappBotConsumerServer interface.
type MockWhatsappBotConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockWhatsappBotConsumerServerMockRecorder
}

// MockWhatsappBotConsumerServerMockRecorder is the mock recorder for MockWhatsappBotConsumerServer.
type MockWhatsappBotConsumerServerMockRecorder struct {
	mock *MockWhatsappBotConsumerServer
}

// NewMockWhatsappBotConsumerServer creates a new mock instance.
func NewMockWhatsappBotConsumerServer(ctrl *gomock.Controller) *MockWhatsappBotConsumerServer {
	mock := &MockWhatsappBotConsumerServer{ctrl: ctrl}
	mock.recorder = &MockWhatsappBotConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWhatsappBotConsumerServer) EXPECT() *MockWhatsappBotConsumerServerMockRecorder {
	return m.recorder
}

// ProcessUserMessage mocks base method.
func (m *MockWhatsappBotConsumerServer) ProcessUserMessage(arg0 context.Context, arg1 *whatsapp_bot.ProcessUserMessageRequest) (*whatsapp_bot.ProcessUserMessageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessUserMessage", arg0, arg1)
	ret0, _ := ret[0].(*whatsapp_bot.ProcessUserMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessUserMessage indicates an expected call of ProcessUserMessage.
func (mr *MockWhatsappBotConsumerServerMockRecorder) ProcessUserMessage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessUserMessage", reflect.TypeOf((*MockWhatsappBotConsumerServer)(nil).ProcessUserMessage), arg0, arg1)
}

// MockUnsafeWhatsappBotConsumerServer is a mock of UnsafeWhatsappBotConsumerServer interface.
type MockUnsafeWhatsappBotConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeWhatsappBotConsumerServerMockRecorder
}

// MockUnsafeWhatsappBotConsumerServerMockRecorder is the mock recorder for MockUnsafeWhatsappBotConsumerServer.
type MockUnsafeWhatsappBotConsumerServerMockRecorder struct {
	mock *MockUnsafeWhatsappBotConsumerServer
}

// NewMockUnsafeWhatsappBotConsumerServer creates a new mock instance.
func NewMockUnsafeWhatsappBotConsumerServer(ctrl *gomock.Controller) *MockUnsafeWhatsappBotConsumerServer {
	mock := &MockUnsafeWhatsappBotConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeWhatsappBotConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeWhatsappBotConsumerServer) EXPECT() *MockUnsafeWhatsappBotConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedWhatsappBotConsumerServer mocks base method.
func (m *MockUnsafeWhatsappBotConsumerServer) mustEmbedUnimplementedWhatsappBotConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedWhatsappBotConsumerServer")
}

// mustEmbedUnimplementedWhatsappBotConsumerServer indicates an expected call of mustEmbedUnimplementedWhatsappBotConsumerServer.
func (mr *MockUnsafeWhatsappBotConsumerServerMockRecorder) mustEmbedUnimplementedWhatsappBotConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedWhatsappBotConsumerServer", reflect.TypeOf((*MockUnsafeWhatsappBotConsumerServer)(nil).mustEmbedUnimplementedWhatsappBotConsumerServer))
}
