syntax = "proto3";

package api.pay.internationalfundtransfer.forex;

import "api/rpc/status.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/pay/internationalfundtransfer/forex";
option java_package = "com.github.epifi.gamma.api.pay.internationalfundtransfer.forex";

service ForexService {
  // UpdateForexDealUsage updates the amount of a forex deal used up.
  // The client request ID is used to uniquely identify the request, and no action is taken if it is received again.
  // Currently, the client request ID is not validated to ensure only certain clients can consume a forex deal.
  // To revert consumption, clients should send the same client request ID with different update type.
  // The amount is expected to be in the currency of the deal (not in INR).
  // E.g., for a remittance request from US stocks, the client request ID of the remittance request is used to
  // consume/reverse a deal.
  // Tech Debt:
  // 1. Each deal consumption should be persisted as a separate entity,
  // and should be mapped to a remittance order. This order id (instead of client request id) should
  // be used to ensure that a consumption/reversal request is not processed more than once. Currently, this RPC
  // accepts any random string as a client request id and the onus is on callers to ensure that it is unique.
  // 2. The consumption can fail if there are parallel requests to consume the same deal and the deal does not
  // have enough balance left to consume. Ideally, the RPC should figure out the deal to consume,
  // and consume the amount from deal after taking a lock on it, instead of taking the forex deal id as input.
  rpc UpdateForexDealUsage (UpdateForexDealUsageRequest) returns (UpdateForexDealUsageResponse);
}

enum ForexDealUsageUpdateType {
  FOREX_DEAL_USAGE_UPDATE_TYPE_UNSPECIFIED = 0;

  // For consuming an amount from a forex deal
  FOREX_DEAL_USAGE_UPDATE_TYPE_CONSUME = 1;

  // For reverting an amount consumed earlier from a forex deal
  FOREX_DEAL_USAGE_UPDATE_TYPE_REVERT = 2;
}

message UpdateForexDealUsageRequest {
  // A unique identifier of a request to consume or revert some amount of a forex deal.
  string client_req_id = 1 [(validate.rules).string = {min_len: 1}];

  ForexDealUsageUpdateType update_type = 2 [(validate.rules).enum = {not_in: [0]}];

  // A unique identifier of a forex deal.
  string forex_deal_id = 3 [(validate.rules).string = {min_len: 1}];

  // The amount to be consumed or reverted.
  google.type.Money remittance_amount = 4 [(validate.rules).message.required = true];
}

message UpdateForexDealUsageResponse {
  rpc.Status status = 1;
}
