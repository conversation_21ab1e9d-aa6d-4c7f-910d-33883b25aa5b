syntax = "proto3";

package api.usstocks.tax.documentparams;

import "api/usstocks/tax/wrapper/moneywrapper.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/tax/documentparams";
option java_package = "com.github.epifi.gamma.api.usstocks.tax.documentparams";

// refer PRD for more details: https://docs.google.com/document/d/1dj4Nk82RAnwQn-Ukrq6MnWf_Qnt0GBWzo2EIf0SsldE/edit#heading=h.ctsx33f7u4wk
message ScheduleTRParams {
  repeated TaxPaidOutsideIndiaDetails tax_paid_outside_india_details = 1;
}

message TaxPaidOutsideIndiaDetails {
  string country_name = 1;
  usstocks.tax.wrapper.UsdInrWrapper total_tax_paid_outside_india = 2;
}
