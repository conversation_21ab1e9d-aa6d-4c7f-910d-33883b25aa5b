syntax = "proto3";

package api.usstocks.tax;

import "api/vendorgateway/stocks/service.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/tax";
option java_package = "com.github.epifi.gamma.api.usstocks.tax";

// CorporateAction represents a single corporate event happening to a company/etf
message CorporateAction {
  oneof corporate_action {
    vendorgateway.stocks.ReverseSplit reverse_split = 1;
    vendorgateway.stocks.ForwardSplit forward_split = 2;
    vendorgateway.stocks.UnitSplit unit_split = 3;
    vendorgateway.stocks.CashDividend cash_dividend = 4;
    vendorgateway.stocks.StockDividend stock_dividend = 5;
    vendorgateway.stocks.SpinOff spin_off = 6;
    vendorgateway.stocks.CashMerger cash_merger = 7;
    vendorgateway.stocks.StockMerger stock_merger = 8;
    vendorgateway.stocks.StockAndCashMerger stock_and_cash_merger = 9;
    vendorgateway.stocks.Redemption redemption = 10;
    vendorgateway.stocks.NameChange name_change = 11;
    vendorgateway.stocks.WorthLessRemoval worthless_removal = 12;
    vendorgateway.stocks.RightsDistribution rights_distribution = 13;
  }
}
