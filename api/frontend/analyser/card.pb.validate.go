// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/analyser/card.proto

package analyser

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ExpandableCard with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExpandableCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExpandableCard with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExpandableCardMultiError,
// or nil if none found.
func (m *ExpandableCard) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpandableCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExpandedView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableCardValidationError{
					field:  "ExpandedView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableCardValidationError{
					field:  "ExpandedView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpandedView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableCardValidationError{
				field:  "ExpandedView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCollapsedView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableCardValidationError{
					field:  "CollapsedView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableCardValidationError{
					field:  "CollapsedView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollapsedView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableCardValidationError{
				field:  "CollapsedView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsExpanded

	// no validation rules for IsEnabled

	if len(errors) > 0 {
		return ExpandableCardMultiError(errors)
	}

	return nil
}

// ExpandableCardMultiError is an error wrapping multiple validation errors
// returned by ExpandableCard.ValidateAll() if the designated constraints
// aren't met.
type ExpandableCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpandableCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpandableCardMultiError) AllErrors() []error { return m }

// ExpandableCardValidationError is the validation error returned by
// ExpandableCard.Validate if the designated constraints aren't met.
type ExpandableCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpandableCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExpandableCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExpandableCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExpandableCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExpandableCardValidationError) ErrorName() string { return "ExpandableCardValidationError" }

// Error satisfies the builtin error interface
func (e ExpandableCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpandableCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpandableCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpandableCardValidationError{}

// Validate checks the field values on ExpandedCard with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExpandedCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExpandedCard with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExpandedCardMultiError, or
// nil if none found.
func (m *ExpandedCard) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpandedCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandedCardValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandedCardValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandedCardValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandedCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandedCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandedCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandedCardValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandedCardValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandedCardValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCardComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExpandedCardValidationError{
						field:  fmt.Sprintf("CardComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExpandedCardValidationError{
						field:  fmt.Sprintf("CardComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExpandedCardValidationError{
					field:  fmt.Sprintf("CardComponents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetIconV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandedCardValidationError{
					field:  "IconV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandedCardValidationError{
					field:  "IconV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIconV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandedCardValidationError{
				field:  "IconV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExpandedCardMultiError(errors)
	}

	return nil
}

// ExpandedCardMultiError is an error wrapping multiple validation errors
// returned by ExpandedCard.ValidateAll() if the designated constraints aren't met.
type ExpandedCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpandedCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpandedCardMultiError) AllErrors() []error { return m }

// ExpandedCardValidationError is the validation error returned by
// ExpandedCard.Validate if the designated constraints aren't met.
type ExpandedCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpandedCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExpandedCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExpandedCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExpandedCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExpandedCardValidationError) ErrorName() string { return "ExpandedCardValidationError" }

// Error satisfies the builtin error interface
func (e ExpandedCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpandedCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpandedCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpandedCardValidationError{}

// Validate checks the field values on CollapsedCard with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CollapsedCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollapsedCard with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CollapsedCardMultiError, or
// nil if none found.
func (m *CollapsedCard) ValidateAll() error {
	return m.validate(true)
}

func (m *CollapsedCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollapsedCardValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollapsedCardValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollapsedCardValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollapsedCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollapsedCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollapsedCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollapsedCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollapsedCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollapsedCardValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTag() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollapsedCardValidationError{
						field:  fmt.Sprintf("Tag[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollapsedCardValidationError{
						field:  fmt.Sprintf("Tag[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollapsedCardValidationError{
					field:  fmt.Sprintf("Tag[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetIconV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollapsedCardValidationError{
					field:  "IconV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollapsedCardValidationError{
					field:  "IconV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIconV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollapsedCardValidationError{
				field:  "IconV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollapsedCardMultiError(errors)
	}

	return nil
}

// CollapsedCardMultiError is an error wrapping multiple validation errors
// returned by CollapsedCard.ValidateAll() if the designated constraints
// aren't met.
type CollapsedCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollapsedCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollapsedCardMultiError) AllErrors() []error { return m }

// CollapsedCardValidationError is the validation error returned by
// CollapsedCard.Validate if the designated constraints aren't met.
type CollapsedCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollapsedCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollapsedCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollapsedCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollapsedCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollapsedCardValidationError) ErrorName() string { return "CollapsedCardValidationError" }

// Error satisfies the builtin error interface
func (e CollapsedCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollapsedCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollapsedCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollapsedCardValidationError{}

// Validate checks the field values on CardComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CardComponentMultiError, or
// nil if none found.
func (m *CardComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *CardComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	switch v := m.Component.(type) {
	case *CardComponent_Visualisation:
		if v == nil {
			err := CardComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVisualisation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardComponentValidationError{
						field:  "Visualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardComponentValidationError{
						field:  "Visualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVisualisation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardComponentValidationError{
					field:  "Visualisation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CardComponent_StaticVisualisation:
		if v == nil {
			err := CardComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStaticVisualisation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardComponentValidationError{
						field:  "StaticVisualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardComponentValidationError{
						field:  "StaticVisualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStaticVisualisation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardComponentValidationError{
					field:  "StaticVisualisation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CardComponentMultiError(errors)
	}

	return nil
}

// CardComponentMultiError is an error wrapping multiple validation errors
// returned by CardComponent.ValidateAll() if the designated constraints
// aren't met.
type CardComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardComponentMultiError) AllErrors() []error { return m }

// CardComponentValidationError is the validation error returned by
// CardComponent.Validate if the designated constraints aren't met.
type CardComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardComponentValidationError) ErrorName() string { return "CardComponentValidationError" }

// Error satisfies the builtin error interface
func (e CardComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardComponentValidationError{}
