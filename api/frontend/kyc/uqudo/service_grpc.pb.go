// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/kyc/uqudo/service.proto

package uqudo

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Uqudo_GetAccessToken_FullMethodName                    = "/frontend.kyc.uqudo.Uqudo/GetAccessToken"
	Uqudo_ProcessEmiratesIdVerificationData_FullMethodName = "/frontend.kyc.uqudo.Uqudo/ProcessEmiratesIdVerificationData"
	Uqudo_ProcessVerificationData_FullMethodName           = "/frontend.kyc.uqudo.Uqudo/ProcessVerificationData"
)

// UqudoClient is the client API for Uqudo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UqudoClient interface {
	// RPC which provides the access token required to initialize the Uqudo SDK
	GetAccessToken(ctx context.Context, in *GetAccessTokenRequest, opts ...grpc.CallOption) (*GetAccessTokenResponse, error)
	// RPC which processes emirates ID verification data received by client upon successful verification
	ProcessEmiratesIdVerificationData(ctx context.Context, in *ProcessEmiratesIdVerificationDataRequest, opts ...grpc.CallOption) (*ProcessEmiratesIdVerificationDataResponse, error)
	// RPC which processes verification data received by client upon successful verification of any document
	ProcessVerificationData(ctx context.Context, in *ProcessVerificationDataRequest, opts ...grpc.CallOption) (*ProcessVerificationDataResponse, error)
}

type uqudoClient struct {
	cc grpc.ClientConnInterface
}

func NewUqudoClient(cc grpc.ClientConnInterface) UqudoClient {
	return &uqudoClient{cc}
}

func (c *uqudoClient) GetAccessToken(ctx context.Context, in *GetAccessTokenRequest, opts ...grpc.CallOption) (*GetAccessTokenResponse, error) {
	out := new(GetAccessTokenResponse)
	err := c.cc.Invoke(ctx, Uqudo_GetAccessToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uqudoClient) ProcessEmiratesIdVerificationData(ctx context.Context, in *ProcessEmiratesIdVerificationDataRequest, opts ...grpc.CallOption) (*ProcessEmiratesIdVerificationDataResponse, error) {
	out := new(ProcessEmiratesIdVerificationDataResponse)
	err := c.cc.Invoke(ctx, Uqudo_ProcessEmiratesIdVerificationData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uqudoClient) ProcessVerificationData(ctx context.Context, in *ProcessVerificationDataRequest, opts ...grpc.CallOption) (*ProcessVerificationDataResponse, error) {
	out := new(ProcessVerificationDataResponse)
	err := c.cc.Invoke(ctx, Uqudo_ProcessVerificationData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UqudoServer is the server API for Uqudo service.
// All implementations should embed UnimplementedUqudoServer
// for forward compatibility
type UqudoServer interface {
	// RPC which provides the access token required to initialize the Uqudo SDK
	GetAccessToken(context.Context, *GetAccessTokenRequest) (*GetAccessTokenResponse, error)
	// RPC which processes emirates ID verification data received by client upon successful verification
	ProcessEmiratesIdVerificationData(context.Context, *ProcessEmiratesIdVerificationDataRequest) (*ProcessEmiratesIdVerificationDataResponse, error)
	// RPC which processes verification data received by client upon successful verification of any document
	ProcessVerificationData(context.Context, *ProcessVerificationDataRequest) (*ProcessVerificationDataResponse, error)
}

// UnimplementedUqudoServer should be embedded to have forward compatible implementations.
type UnimplementedUqudoServer struct {
}

func (UnimplementedUqudoServer) GetAccessToken(context.Context, *GetAccessTokenRequest) (*GetAccessTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccessToken not implemented")
}
func (UnimplementedUqudoServer) ProcessEmiratesIdVerificationData(context.Context, *ProcessEmiratesIdVerificationDataRequest) (*ProcessEmiratesIdVerificationDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessEmiratesIdVerificationData not implemented")
}
func (UnimplementedUqudoServer) ProcessVerificationData(context.Context, *ProcessVerificationDataRequest) (*ProcessVerificationDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessVerificationData not implemented")
}

// UnsafeUqudoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UqudoServer will
// result in compilation errors.
type UnsafeUqudoServer interface {
	mustEmbedUnimplementedUqudoServer()
}

func RegisterUqudoServer(s grpc.ServiceRegistrar, srv UqudoServer) {
	s.RegisterService(&Uqudo_ServiceDesc, srv)
}

func _Uqudo_GetAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccessTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UqudoServer).GetAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Uqudo_GetAccessToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UqudoServer).GetAccessToken(ctx, req.(*GetAccessTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Uqudo_ProcessEmiratesIdVerificationData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessEmiratesIdVerificationDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UqudoServer).ProcessEmiratesIdVerificationData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Uqudo_ProcessEmiratesIdVerificationData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UqudoServer).ProcessEmiratesIdVerificationData(ctx, req.(*ProcessEmiratesIdVerificationDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Uqudo_ProcessVerificationData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessVerificationDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UqudoServer).ProcessVerificationData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Uqudo_ProcessVerificationData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UqudoServer).ProcessVerificationData(ctx, req.(*ProcessVerificationDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Uqudo_ServiceDesc is the grpc.ServiceDesc for Uqudo service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Uqudo_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.kyc.uqudo.Uqudo",
	HandlerType: (*UqudoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAccessToken",
			Handler:    _Uqudo_GetAccessToken_Handler,
		},
		{
			MethodName: "ProcessEmiratesIdVerificationData",
			Handler:    _Uqudo_ProcessEmiratesIdVerificationData_Handler,
		},
		{
			MethodName: "ProcessVerificationData",
			Handler:    _Uqudo_ProcessVerificationData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/kyc/uqudo/service.proto",
}
