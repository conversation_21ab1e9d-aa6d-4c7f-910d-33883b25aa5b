package secrets

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/api/typesv2/ui"
)

type InvestmentBehaviourStickersComponentBuilder struct {
	investmentBehaviourStickersComponent *InvestmentBehaviourStickersComponent
}

func NewInvestmentBehaviourStickersComponentBuilder() *InvestmentBehaviourStickersComponentBuilder {
	return &InvestmentBehaviourStickersComponentBuilder{
		investmentBehaviourStickersComponent: &InvestmentBehaviourStickersComponent{},
	}
}

func (i *InvestmentBehaviourStickersComponentBuilder) SetComponentType(componentType string) *InvestmentBehaviourStickersComponentBuilder {
	if componentType == "" {
		return i
	}
	if i.investmentBehaviourStickersComponent == nil {
		i.investmentBehaviourStickersComponent = &InvestmentBehaviourStickersComponent{}
	}
	i.investmentBehaviourStickersComponent.ComponentType = componentType
	return i
}

func (i *InvestmentBehaviourStickersComponentBuilder) SetTitle(title *ui.IconTextComponent) *InvestmentBehaviourStickersComponentBuilder {
	if title == nil {
		return i
	}
	if i.investmentBehaviourStickersComponent == nil {
		i.investmentBehaviourStickersComponent = &InvestmentBehaviourStickersComponent{}
	}
	i.investmentBehaviourStickersComponent.Title = title
	return i
}

func (i *InvestmentBehaviourStickersComponentBuilder) SetDescription(description *commontypes.Text) *InvestmentBehaviourStickersComponentBuilder {
	if description == nil {
		return i
	}
	if i.investmentBehaviourStickersComponent == nil {
		i.investmentBehaviourStickersComponent = &InvestmentBehaviourStickersComponent{}
	}
	i.investmentBehaviourStickersComponent.Description = description
	return i
}

func (i *InvestmentBehaviourStickersComponentBuilder) SetStickerImage(stickerImage *commontypes.VisualElement) *InvestmentBehaviourStickersComponentBuilder {
	if stickerImage == nil {
		return i
	}
	if i.investmentBehaviourStickersComponent == nil {
		i.investmentBehaviourStickersComponent = &InvestmentBehaviourStickersComponent{}
	}
	i.investmentBehaviourStickersComponent.StickerImage = stickerImage
	return i
}

func (i *InvestmentBehaviourStickersComponentBuilder) SetBackgroundImage(backgroundImage *commontypes.VisualElement) *InvestmentBehaviourStickersComponentBuilder {
	if backgroundImage == nil {
		return i
	}
	if i.investmentBehaviourStickersComponent == nil {
		i.investmentBehaviourStickersComponent = &InvestmentBehaviourStickersComponent{}
	}
	i.investmentBehaviourStickersComponent.BackgroundImage = backgroundImage
	return i
}

func (i *InvestmentBehaviourStickersComponentBuilder) SetCta(cta *ui.IconTextComponent) *InvestmentBehaviourStickersComponentBuilder {
	if cta == nil {
		return i
	}
	if i.investmentBehaviourStickersComponent == nil {
		i.investmentBehaviourStickersComponent = &InvestmentBehaviourStickersComponent{}
	}
	i.investmentBehaviourStickersComponent.Cta = cta
	return i
}

func (i *InvestmentBehaviourStickersComponentBuilder) SetBorderColour(borderColour *widget.BackgroundColour) *InvestmentBehaviourStickersComponentBuilder {
	if borderColour == nil {
		return i
	}
	if i.investmentBehaviourStickersComponent == nil {
		i.investmentBehaviourStickersComponent = &InvestmentBehaviourStickersComponent{}
	}
	i.investmentBehaviourStickersComponent.BorderColour = borderColour
	return i
}

func (i *InvestmentBehaviourStickersComponentBuilder) Build() *InvestmentBehaviourStickersComponent {
	return i.investmentBehaviourStickersComponent
}
