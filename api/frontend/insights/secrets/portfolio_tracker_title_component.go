package secrets

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/typesv2/ui"
)

type PortfolioTrackerTitleComponentBuilder struct {
	portfolioTrackerTitleComponent *PortfolioTrackerTitleComponent
}

func NewPortfolioTrackerTitleComponentBuilder() *PortfolioTrackerTitleComponentBuilder {
	return &PortfolioTrackerTitleComponentBuilder{
		portfolioTrackerTitleComponent: &PortfolioTrackerTitleComponent{},
	}
}

func (p *PortfolioTrackerTitleComponentBuilder) SetTitle(title string, fontColour string) *PortfolioTrackerTitleComponentBuilder {
	if title == "" {
		return p
	}
	if p.portfolioTrackerTitleComponent == nil {
		p.portfolioTrackerTitleComponent = &PortfolioTrackerTitleComponent{}
	}
	p.portfolioTrackerTitleComponent.Title = commontypes.GetTextFromStringFontColourFontStyle(title, fontColour, commontypes.FontStyle_DISPLAY_2XL).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)
	return p
}

func (p *PortfolioTrackerTitleComponentBuilder) SetSubtitle(subtitle string, fontColour string) *PortfolioTrackerTitleComponentBuilder {
	if subtitle == "" {
		return p
	}
	if p.portfolioTrackerTitleComponent == nil {
		p.portfolioTrackerTitleComponent = &PortfolioTrackerTitleComponent{}
	}
	p.portfolioTrackerTitleComponent.SubTitle = commontypes.GetTextFromStringFontColourFontStyle(subtitle, fontColour, commontypes.FontStyle_BODY_XS)
	return p
}

func (p *PortfolioTrackerTitleComponentBuilder) SetTAndCComponent(tAndC *ui.IconTextComponent) *PortfolioTrackerTitleComponentBuilder {
	if tAndC == nil {
		return p
	}
	if p.portfolioTrackerTitleComponent == nil {
		p.portfolioTrackerTitleComponent = &PortfolioTrackerTitleComponent{}
	}
	p.portfolioTrackerTitleComponent.TAndCComponent = tAndC
	return p
}

func (p *PortfolioTrackerTitleComponentBuilder) AddCta(cta *ui.IconTextComponent) *PortfolioTrackerTitleComponentBuilder {
	if cta == nil {
		return p
	}
	if p.portfolioTrackerTitleComponent == nil {
		p.portfolioTrackerTitleComponent = &PortfolioTrackerTitleComponent{
			Ctas: []*ui.IconTextComponent{},
		}
	}
	if p.portfolioTrackerTitleComponent.GetCtas() == nil {
		p.portfolioTrackerTitleComponent.Ctas = []*ui.IconTextComponent{}
	}
	p.portfolioTrackerTitleComponent.Ctas = append(p.portfolioTrackerTitleComponent.GetCtas(), cta)
	return p
}

func (p *PortfolioTrackerTitleComponentBuilder) SetBackgroundImage(backgroundImage *commontypes.VisualElement) *PortfolioTrackerTitleComponentBuilder {
	if backgroundImage == nil {
		return p
	}
	if p.portfolioTrackerTitleComponent == nil {
		p.portfolioTrackerTitleComponent = &PortfolioTrackerTitleComponent{}
	}
	p.portfolioTrackerTitleComponent.BackgroundImage = backgroundImage
	return p
}

func (p *PortfolioTrackerTitleComponentBuilder) Build() *PortfolioTrackerTitleComponent {
	return p.portfolioTrackerTitleComponent
}
