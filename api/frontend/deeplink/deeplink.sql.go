package deeplink

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
// Value implements driver.Valuer interface
// It stores data as string in DB
func (d *Deeplink) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte, got %T", input)
		return err
	}

	if err := backport.SafeUnmarshal(protojson.Unmarshal, val, d); err != nil {
		return err
	}
	return nil
}

func (d *Deeplink) Value() (driver.Value, error) {
	if d == nil {
		return nil, nil
	}
	jsonBytes, err := protojson.Marshal(d)
	if err != nil {
		return nil, err
	}
	return jsonBytes, nil
}

func (d *Deeplink) UnmarshalJSON(marshalledData []byte) error {
	return protojson.Unmarshal(marshalledData, d)
}

func (d *Deeplink) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(d)
}

// Valuer interface implementation for storing the data in string format in DB
func (o Screen) Value() (driver.Value, error) {
	return o.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (o *Screen) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := Screen_value[val]
	*o = Screen(valInt)
	return nil
}

func (ot *Screen) UnmarshalJSON(marshalledData []byte) error {
	var val string
	err := json.Unmarshal(marshalledData, &val)
	if err != nil {
		return err
	}
	valInt := Screen_value[val]
	*ot = Screen(valInt)
	return nil
}

// MarshalJSON implements the Marshaler interface
func (ot Screen) MarshalJSON() ([]byte, error) {
	xStr := ot.String()
	return json.Marshal(xStr)
}
