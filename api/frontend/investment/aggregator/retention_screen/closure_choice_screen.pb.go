// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/investment/aggregator/retention_screen/closure_choice_screen.proto

package retention_screen

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-48088&mode=design&t=FfLeU9YHbe1R5ek9-0
// Screenshot: https://drive.google.com/file/d/1jOkMC3Wzq5HGmAcFbKGNy3owiO48tEEA/view
type ClosureChoicesScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleVisualElement *common.VisualElement `protobuf:"bytes,1,opt,name=title_visual_element,json=titleVisualElement,proto3" json:"title_visual_element,omitempty"`
	// For eg: Close Emergency deposit?
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// For eg: Before you go... Here are the benefits your deposit comes with
	Subtitle *common.Text `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-48088&mode=design&t=FfLeU9YHbe1R5ek9-0
	ChoiceComponents []*ClosureChoiceComponent `protobuf:"bytes,4,rep,name=choice_components,json=choiceComponents,proto3" json:"choice_components,omitempty"`
	SwipeComponent   *SwipeComponent           `protobuf:"bytes,5,opt,name=swipe_component,json=swipeComponent,proto3" json:"swipe_component,omitempty"`
}

func (x *ClosureChoicesScreen) Reset() {
	*x = ClosureChoicesScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClosureChoicesScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClosureChoicesScreen) ProtoMessage() {}

func (x *ClosureChoicesScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClosureChoicesScreen.ProtoReflect.Descriptor instead.
func (*ClosureChoicesScreen) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDescGZIP(), []int{0}
}

func (x *ClosureChoicesScreen) GetTitleVisualElement() *common.VisualElement {
	if x != nil {
		return x.TitleVisualElement
	}
	return nil
}

func (x *ClosureChoicesScreen) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ClosureChoicesScreen) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *ClosureChoicesScreen) GetChoiceComponents() []*ClosureChoiceComponent {
	if x != nil {
		return x.ChoiceComponents
	}
	return nil
}

func (x *ClosureChoicesScreen) GetSwipeComponent() *SwipeComponent {
	if x != nil {
		return x.SwipeComponent
	}
	return nil
}

// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-48088&mode=design&t=FfLeU9YHbe1R5ek9-0
// ScreenShot: https://drive.google.com/file/d/1jOkMC3Wzq5HGmAcFbKGNy3owiO48tEEA/view
type ClosureChoiceComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// For eg: Close now & get interest worth
	Header *common.Text `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// For eg: ₹320 at 4.2% 3.2% p.a
	Body *ui.IconTextComponent `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	// 1% of the interest will be deducted as a pre-closure penalty
	Footer *ui.IconTextComponent `protobuf:"bytes,3,opt,name=footer,proto3" json:"footer,omitempty"`
	// deeplink to which user should be redirected to on selection this choice
	NavigationDeeplink *deeplink.Deeplink    `protobuf:"bytes,4,opt,name=navigation_deeplink,json=navigationDeeplink,proto3" json:"navigation_deeplink,omitempty"`
	NextIcon           *common.VisualElement `protobuf:"bytes,5,opt,name=next_icon,json=nextIcon,proto3" json:"next_icon,omitempty"`
	// background color for the choice
	BgColor string `protobuf:"bytes,6,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// background shadow of the nudge card
	Shadows []*ui.Shadow `protobuf:"bytes,7,rep,name=shadows,proto3" json:"shadows,omitempty"`
}

func (x *ClosureChoiceComponent) Reset() {
	*x = ClosureChoiceComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClosureChoiceComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClosureChoiceComponent) ProtoMessage() {}

func (x *ClosureChoiceComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClosureChoiceComponent.ProtoReflect.Descriptor instead.
func (*ClosureChoiceComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDescGZIP(), []int{1}
}

func (x *ClosureChoiceComponent) GetHeader() *common.Text {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ClosureChoiceComponent) GetBody() *ui.IconTextComponent {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *ClosureChoiceComponent) GetFooter() *ui.IconTextComponent {
	if x != nil {
		return x.Footer
	}
	return nil
}

func (x *ClosureChoiceComponent) GetNavigationDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.NavigationDeeplink
	}
	return nil
}

func (x *ClosureChoiceComponent) GetNextIcon() *common.VisualElement {
	if x != nil {
		return x.NextIcon
	}
	return nil
}

func (x *ClosureChoiceComponent) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *ClosureChoiceComponent) GetShadows() []*ui.Shadow {
	if x != nil {
		return x.Shadows
	}
	return nil
}

var File_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto protoreflect.FileDescriptor

var file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDesc = []byte{
	0x0a, 0x4f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x6f, 0x72, 0x2f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x2f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x68,
	0x6f, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x49,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x6f, 0x72, 0x2f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x2f, 0x73, 0x77, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x03, 0x0a, 0x14, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x12, 0x53, 0x0a, 0x14, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x76, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x12, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x74, 0x0a,
	0x11, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x61,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x10, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x68, 0x0a, 0x0f, 0x73, 0x77, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65,
	0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x53,
	0x77, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x73,
	0x77, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0x97, 0x03,
	0x0a, 0x16, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x12, 0x39, 0x0a, 0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x13,
	0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x12, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x3e, 0x0a, 0x09, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x08, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x30, 0x0a, 0x07, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x52, 0x07,
	0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x73, 0x42, 0x98, 0x01, 0x0a, 0x4a, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5a, 0x4a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f,
	0x72, 0x2f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDescOnce sync.Once
	file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDescData = file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDesc
)

func file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDescGZIP() []byte {
	file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDescOnce.Do(func() {
		file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDescData)
	})
	return file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDescData
}

var file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_goTypes = []interface{}{
	(*ClosureChoicesScreen)(nil),   // 0: frontend.investment.aggregator.retention_screen.ClosureChoicesScreen
	(*ClosureChoiceComponent)(nil), // 1: frontend.investment.aggregator.retention_screen.ClosureChoiceComponent
	(*common.VisualElement)(nil),   // 2: api.typesv2.common.VisualElement
	(*common.Text)(nil),            // 3: api.typesv2.common.Text
	(*SwipeComponent)(nil),         // 4: frontend.investment.aggregator.retention_screen.SwipeComponent
	(*ui.IconTextComponent)(nil),   // 5: api.typesv2.ui.IconTextComponent
	(*deeplink.Deeplink)(nil),      // 6: frontend.deeplink.Deeplink
	(*ui.Shadow)(nil),              // 7: api.typesv2.ui.Shadow
}
var file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_depIdxs = []int32{
	2,  // 0: frontend.investment.aggregator.retention_screen.ClosureChoicesScreen.title_visual_element:type_name -> api.typesv2.common.VisualElement
	3,  // 1: frontend.investment.aggregator.retention_screen.ClosureChoicesScreen.title:type_name -> api.typesv2.common.Text
	3,  // 2: frontend.investment.aggregator.retention_screen.ClosureChoicesScreen.subtitle:type_name -> api.typesv2.common.Text
	1,  // 3: frontend.investment.aggregator.retention_screen.ClosureChoicesScreen.choice_components:type_name -> frontend.investment.aggregator.retention_screen.ClosureChoiceComponent
	4,  // 4: frontend.investment.aggregator.retention_screen.ClosureChoicesScreen.swipe_component:type_name -> frontend.investment.aggregator.retention_screen.SwipeComponent
	3,  // 5: frontend.investment.aggregator.retention_screen.ClosureChoiceComponent.header:type_name -> api.typesv2.common.Text
	5,  // 6: frontend.investment.aggregator.retention_screen.ClosureChoiceComponent.body:type_name -> api.typesv2.ui.IconTextComponent
	5,  // 7: frontend.investment.aggregator.retention_screen.ClosureChoiceComponent.footer:type_name -> api.typesv2.ui.IconTextComponent
	6,  // 8: frontend.investment.aggregator.retention_screen.ClosureChoiceComponent.navigation_deeplink:type_name -> frontend.deeplink.Deeplink
	2,  // 9: frontend.investment.aggregator.retention_screen.ClosureChoiceComponent.next_icon:type_name -> api.typesv2.common.VisualElement
	7,  // 10: frontend.investment.aggregator.retention_screen.ClosureChoiceComponent.shadows:type_name -> api.typesv2.ui.Shadow
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() {
	file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_init()
}
func file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_init() {
	if File_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto != nil {
		return
	}
	file_api_frontend_investment_aggregator_retention_screen_swipe_component_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClosureChoicesScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClosureChoiceComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_goTypes,
		DependencyIndexes: file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_depIdxs,
		MessageInfos:      file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_msgTypes,
	}.Build()
	File_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto = out.File
	file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_rawDesc = nil
	file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_goTypes = nil
	file_api_frontend_investment_aggregator_retention_screen_closure_choice_screen_proto_depIdxs = nil
}
