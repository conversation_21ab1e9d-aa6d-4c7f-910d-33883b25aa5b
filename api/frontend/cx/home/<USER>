// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/cx/home/<USER>

package home

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on HomeSupportTicket with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HomeSupportTicket) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HomeSupportTicket with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HomeSupportTicketMultiError, or nil if none found.
func (m *HomeSupportTicket) ValidateAll() error {
	return m.validate(true)
}

func (m *HomeSupportTicket) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCreatedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "CreatedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "CreatedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HomeSupportTicketValidationError{
				field:  "CreatedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicketDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "TicketDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "TicketDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HomeSupportTicketValidationError{
				field:  "TicketDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicketActionImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "TicketActionImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "TicketActionImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketActionImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HomeSupportTicketValidationError{
				field:  "TicketActionImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicketBorderColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "TicketBorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "TicketBorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketBorderColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HomeSupportTicketValidationError{
				field:  "TicketBorderColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicketBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "TicketBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HomeSupportTicketValidationError{
					field:  "TicketBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HomeSupportTicketValidationError{
				field:  "TicketBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HomeSupportTicketMultiError(errors)
	}

	return nil
}

// HomeSupportTicketMultiError is an error wrapping multiple validation errors
// returned by HomeSupportTicket.ValidateAll() if the designated constraints
// aren't met.
type HomeSupportTicketMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HomeSupportTicketMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HomeSupportTicketMultiError) AllErrors() []error { return m }

// HomeSupportTicketValidationError is the validation error returned by
// HomeSupportTicket.Validate if the designated constraints aren't met.
type HomeSupportTicketValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HomeSupportTicketValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HomeSupportTicketValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HomeSupportTicketValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HomeSupportTicketValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HomeSupportTicketValidationError) ErrorName() string {
	return "HomeSupportTicketValidationError"
}

// Error satisfies the builtin error interface
func (e HomeSupportTicketValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHomeSupportTicket.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HomeSupportTicketValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HomeSupportTicketValidationError{}
