// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/pay/service.proto

package pay

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pay_search_screen_v2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay/pay_search_screen_v2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pay_search_screen_v2.PaySearchScreenType(0)
)

// Valida<PERSON> checks the field values on GetPayWidgetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPayWidgetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPayWidgetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPayWidgetRequestMultiError, or nil if none found.
func (m *GetPayWidgetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPayWidgetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPayWidgetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPayWidgetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPayWidgetRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPayWidgetRequestMultiError(errors)
	}

	return nil
}

// GetPayWidgetRequestMultiError is an error wrapping multiple validation
// errors returned by GetPayWidgetRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPayWidgetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPayWidgetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPayWidgetRequestMultiError) AllErrors() []error { return m }

// GetPayWidgetRequestValidationError is the validation error returned by
// GetPayWidgetRequest.Validate if the designated constraints aren't met.
type GetPayWidgetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPayWidgetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPayWidgetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPayWidgetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPayWidgetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPayWidgetRequestValidationError) ErrorName() string {
	return "GetPayWidgetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPayWidgetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPayWidgetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPayWidgetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPayWidgetRequestValidationError{}

// Validate checks the field values on GetPayWidgetResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPayWidgetResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPayWidgetResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPayWidgetResponseMultiError, or nil if none found.
func (m *GetPayWidgetResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPayWidgetResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPayWidgetResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPayWidgetResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPayWidgetResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayWidget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPayWidgetResponseValidationError{
					field:  "PayWidget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPayWidgetResponseValidationError{
					field:  "PayWidget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayWidget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPayWidgetResponseValidationError{
				field:  "PayWidget",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPayWidgetResponseMultiError(errors)
	}

	return nil
}

// GetPayWidgetResponseMultiError is an error wrapping multiple validation
// errors returned by GetPayWidgetResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPayWidgetResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPayWidgetResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPayWidgetResponseMultiError) AllErrors() []error { return m }

// GetPayWidgetResponseValidationError is the validation error returned by
// GetPayWidgetResponse.Validate if the designated constraints aren't met.
type GetPayWidgetResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPayWidgetResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPayWidgetResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPayWidgetResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPayWidgetResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPayWidgetResponseValidationError) ErrorName() string {
	return "GetPayWidgetResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPayWidgetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPayWidgetResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPayWidgetResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPayWidgetResponseValidationError{}

// Validate checks the field values on
// InitiateAuthForBeneficiaryActivationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InitiateAuthForBeneficiaryActivationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateAuthForBeneficiaryActivationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// InitiateAuthForBeneficiaryActivationRequestMultiError, or nil if none found.
func (m *InitiateAuthForBeneficiaryActivationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateAuthForBeneficiaryActivationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAuthForBeneficiaryActivationRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAuthForBeneficiaryActivationRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAuthForBeneficiaryActivationRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorFrom

	// no validation rules for PiTo

	// no validation rules for AuthMode

	if len(errors) > 0 {
		return InitiateAuthForBeneficiaryActivationRequestMultiError(errors)
	}

	return nil
}

// InitiateAuthForBeneficiaryActivationRequestMultiError is an error wrapping
// multiple validation errors returned by
// InitiateAuthForBeneficiaryActivationRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateAuthForBeneficiaryActivationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateAuthForBeneficiaryActivationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateAuthForBeneficiaryActivationRequestMultiError) AllErrors() []error { return m }

// InitiateAuthForBeneficiaryActivationRequestValidationError is the validation
// error returned by InitiateAuthForBeneficiaryActivationRequest.Validate if
// the designated constraints aren't met.
type InitiateAuthForBeneficiaryActivationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateAuthForBeneficiaryActivationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateAuthForBeneficiaryActivationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateAuthForBeneficiaryActivationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateAuthForBeneficiaryActivationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateAuthForBeneficiaryActivationRequestValidationError) ErrorName() string {
	return "InitiateAuthForBeneficiaryActivationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateAuthForBeneficiaryActivationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateAuthForBeneficiaryActivationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateAuthForBeneficiaryActivationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateAuthForBeneficiaryActivationRequestValidationError{}

// Validate checks the field values on
// InitiateAuthForBeneficiaryActivationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InitiateAuthForBeneficiaryActivationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateAuthForBeneficiaryActivationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// InitiateAuthForBeneficiaryActivationResponseMultiError, or nil if none found.
func (m *InitiateAuthForBeneficiaryActivationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateAuthForBeneficiaryActivationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAuthForBeneficiaryActivationResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAuthForBeneficiaryActivationResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAuthForBeneficiaryActivationResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAuthForBeneficiaryActivationResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAuthForBeneficiaryActivationResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAuthForBeneficiaryActivationResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateAuthForBeneficiaryActivationResponseMultiError(errors)
	}

	return nil
}

// InitiateAuthForBeneficiaryActivationResponseMultiError is an error wrapping
// multiple validation errors returned by
// InitiateAuthForBeneficiaryActivationResponse.ValidateAll() if the
// designated constraints aren't met.
type InitiateAuthForBeneficiaryActivationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateAuthForBeneficiaryActivationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateAuthForBeneficiaryActivationResponseMultiError) AllErrors() []error { return m }

// InitiateAuthForBeneficiaryActivationResponseValidationError is the
// validation error returned by
// InitiateAuthForBeneficiaryActivationResponse.Validate if the designated
// constraints aren't met.
type InitiateAuthForBeneficiaryActivationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateAuthForBeneficiaryActivationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateAuthForBeneficiaryActivationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateAuthForBeneficiaryActivationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateAuthForBeneficiaryActivationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateAuthForBeneficiaryActivationResponseValidationError) ErrorName() string {
	return "InitiateAuthForBeneficiaryActivationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateAuthForBeneficiaryActivationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateAuthForBeneficiaryActivationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateAuthForBeneficiaryActivationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateAuthForBeneficiaryActivationResponseValidationError{}

// Validate checks the field values on
// GetBeneficiaryActivationPostAuthActionRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetBeneficiaryActivationPostAuthActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetBeneficiaryActivationPostAuthActionRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetBeneficiaryActivationPostAuthActionRequestMultiError, or nil if none found.
func (m *GetBeneficiaryActivationPostAuthActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBeneficiaryActivationPostAuthActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBeneficiaryActivationPostAuthActionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBeneficiaryActivationPostAuthActionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBeneficiaryActivationPostAuthActionRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return GetBeneficiaryActivationPostAuthActionRequestMultiError(errors)
	}

	return nil
}

// GetBeneficiaryActivationPostAuthActionRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetBeneficiaryActivationPostAuthActionRequest.ValidateAll() if the
// designated constraints aren't met.
type GetBeneficiaryActivationPostAuthActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBeneficiaryActivationPostAuthActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBeneficiaryActivationPostAuthActionRequestMultiError) AllErrors() []error { return m }

// GetBeneficiaryActivationPostAuthActionRequestValidationError is the
// validation error returned by
// GetBeneficiaryActivationPostAuthActionRequest.Validate if the designated
// constraints aren't met.
type GetBeneficiaryActivationPostAuthActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBeneficiaryActivationPostAuthActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBeneficiaryActivationPostAuthActionRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetBeneficiaryActivationPostAuthActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBeneficiaryActivationPostAuthActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBeneficiaryActivationPostAuthActionRequestValidationError) ErrorName() string {
	return "GetBeneficiaryActivationPostAuthActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBeneficiaryActivationPostAuthActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBeneficiaryActivationPostAuthActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBeneficiaryActivationPostAuthActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBeneficiaryActivationPostAuthActionRequestValidationError{}

// Validate checks the field values on
// GetBeneficiaryActivationPostAuthActionResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetBeneficiaryActivationPostAuthActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetBeneficiaryActivationPostAuthActionResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetBeneficiaryActivationPostAuthActionResponseMultiError, or nil if none found.
func (m *GetBeneficiaryActivationPostAuthActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBeneficiaryActivationPostAuthActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBeneficiaryActivationPostAuthActionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBeneficiaryActivationPostAuthActionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBeneficiaryActivationPostAuthActionResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCooldownBottomSheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBeneficiaryActivationPostAuthActionResponseValidationError{
					field:  "CooldownBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBeneficiaryActivationPostAuthActionResponseValidationError{
					field:  "CooldownBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCooldownBottomSheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBeneficiaryActivationPostAuthActionResponseValidationError{
				field:  "CooldownBottomSheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBeneficiaryActivationPostAuthActionResponseMultiError(errors)
	}

	return nil
}

// GetBeneficiaryActivationPostAuthActionResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetBeneficiaryActivationPostAuthActionResponse.ValidateAll() if the
// designated constraints aren't met.
type GetBeneficiaryActivationPostAuthActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBeneficiaryActivationPostAuthActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBeneficiaryActivationPostAuthActionResponseMultiError) AllErrors() []error { return m }

// GetBeneficiaryActivationPostAuthActionResponseValidationError is the
// validation error returned by
// GetBeneficiaryActivationPostAuthActionResponse.Validate if the designated
// constraints aren't met.
type GetBeneficiaryActivationPostAuthActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBeneficiaryActivationPostAuthActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBeneficiaryActivationPostAuthActionResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetBeneficiaryActivationPostAuthActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBeneficiaryActivationPostAuthActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBeneficiaryActivationPostAuthActionResponseValidationError) ErrorName() string {
	return "GetBeneficiaryActivationPostAuthActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBeneficiaryActivationPostAuthActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBeneficiaryActivationPostAuthActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBeneficiaryActivationPostAuthActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBeneficiaryActivationPostAuthActionResponseValidationError{}

// Validate checks the field values on GetPayLandingScreenDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPayLandingScreenDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPayLandingScreenDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPayLandingScreenDetailsRequestMultiError, or nil if none found.
func (m *GetPayLandingScreenDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPayLandingScreenDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPayLandingScreenDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPayLandingScreenDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPayLandingScreenDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPayLandingScreenDetailsRequestMultiError(errors)
	}

	return nil
}

// GetPayLandingScreenDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPayLandingScreenDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPayLandingScreenDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPayLandingScreenDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPayLandingScreenDetailsRequestMultiError) AllErrors() []error { return m }

// GetPayLandingScreenDetailsRequestValidationError is the validation error
// returned by GetPayLandingScreenDetailsRequest.Validate if the designated
// constraints aren't met.
type GetPayLandingScreenDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPayLandingScreenDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPayLandingScreenDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPayLandingScreenDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPayLandingScreenDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPayLandingScreenDetailsRequestValidationError) ErrorName() string {
	return "GetPayLandingScreenDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPayLandingScreenDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPayLandingScreenDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPayLandingScreenDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPayLandingScreenDetailsRequestValidationError{}

// Validate checks the field values on GetPayLandingScreenDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPayLandingScreenDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPayLandingScreenDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPayLandingScreenDetailsResponseMultiError, or nil if none found.
func (m *GetPayLandingScreenDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPayLandingScreenDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPayLandingScreenDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPayLandingScreenDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPayLandingScreenDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayLandingScreenLayout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPayLandingScreenDetailsResponseValidationError{
					field:  "PayLandingScreenLayout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPayLandingScreenDetailsResponseValidationError{
					field:  "PayLandingScreenLayout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayLandingScreenLayout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPayLandingScreenDetailsResponseValidationError{
				field:  "PayLandingScreenLayout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayLandingPopUp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPayLandingScreenDetailsResponseValidationError{
					field:  "PayLandingPopUp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPayLandingScreenDetailsResponseValidationError{
					field:  "PayLandingPopUp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayLandingPopUp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPayLandingScreenDetailsResponseValidationError{
				field:  "PayLandingPopUp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPayLandingScreenDetailsResponseMultiError(errors)
	}

	return nil
}

// GetPayLandingScreenDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetPayLandingScreenDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPayLandingScreenDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPayLandingScreenDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPayLandingScreenDetailsResponseMultiError) AllErrors() []error { return m }

// GetPayLandingScreenDetailsResponseValidationError is the validation error
// returned by GetPayLandingScreenDetailsResponse.Validate if the designated
// constraints aren't met.
type GetPayLandingScreenDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPayLandingScreenDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPayLandingScreenDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPayLandingScreenDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPayLandingScreenDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPayLandingScreenDetailsResponseValidationError) ErrorName() string {
	return "GetPayLandingScreenDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPayLandingScreenDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPayLandingScreenDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPayLandingScreenDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPayLandingScreenDetailsResponseValidationError{}

// Validate checks the field values on StartPennyDropRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartPennyDropRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartPennyDropRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartPennyDropRequestMultiError, or nil if none found.
func (m *StartPennyDropRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StartPennyDropRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartPennyDropRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartPennyDropRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartPennyDropRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBankAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartPennyDropRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartPennyDropRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartPennyDropRequestValidationError{
				field:  "BankAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientIdentificationMetadata

	if len(errors) > 0 {
		return StartPennyDropRequestMultiError(errors)
	}

	return nil
}

// StartPennyDropRequestMultiError is an error wrapping multiple validation
// errors returned by StartPennyDropRequest.ValidateAll() if the designated
// constraints aren't met.
type StartPennyDropRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartPennyDropRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartPennyDropRequestMultiError) AllErrors() []error { return m }

// StartPennyDropRequestValidationError is the validation error returned by
// StartPennyDropRequest.Validate if the designated constraints aren't met.
type StartPennyDropRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartPennyDropRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartPennyDropRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartPennyDropRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartPennyDropRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartPennyDropRequestValidationError) ErrorName() string {
	return "StartPennyDropRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StartPennyDropRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartPennyDropRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartPennyDropRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartPennyDropRequestValidationError{}

// Validate checks the field values on BankAccountDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankAccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankAccountDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankAccountDetailsMultiError, or nil if none found.
func (m *BankAccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *BankAccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BankName

	// no validation rules for IfscCode

	// no validation rules for AccountNumber

	// no validation rules for AccountHolderName

	if len(errors) > 0 {
		return BankAccountDetailsMultiError(errors)
	}

	return nil
}

// BankAccountDetailsMultiError is an error wrapping multiple validation errors
// returned by BankAccountDetails.ValidateAll() if the designated constraints
// aren't met.
type BankAccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankAccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankAccountDetailsMultiError) AllErrors() []error { return m }

// BankAccountDetailsValidationError is the validation error returned by
// BankAccountDetails.Validate if the designated constraints aren't met.
type BankAccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankAccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankAccountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankAccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankAccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankAccountDetailsValidationError) ErrorName() string {
	return "BankAccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e BankAccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankAccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankAccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankAccountDetailsValidationError{}

// Validate checks the field values on StartPennyDropResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartPennyDropResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartPennyDropResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartPennyDropResponseMultiError, or nil if none found.
func (m *StartPennyDropResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StartPennyDropResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartPennyDropResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartPennyDropResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartPennyDropResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartPennyDropResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartPennyDropResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartPennyDropResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StartPennyDropResponseMultiError(errors)
	}

	return nil
}

// StartPennyDropResponseMultiError is an error wrapping multiple validation
// errors returned by StartPennyDropResponse.ValidateAll() if the designated
// constraints aren't met.
type StartPennyDropResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartPennyDropResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartPennyDropResponseMultiError) AllErrors() []error { return m }

// StartPennyDropResponseValidationError is the validation error returned by
// StartPennyDropResponse.Validate if the designated constraints aren't met.
type StartPennyDropResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartPennyDropResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartPennyDropResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartPennyDropResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartPennyDropResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartPennyDropResponseValidationError) ErrorName() string {
	return "StartPennyDropResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StartPennyDropResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartPennyDropResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartPennyDropResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartPennyDropResponseValidationError{}

// Validate checks the field values on GetPennyDropStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPennyDropStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPennyDropStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPennyDropStatusRequestMultiError, or nil if none found.
func (m *GetPennyDropStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPennyDropStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPennyDropStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPennyDropStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPennyDropStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	// no validation rules for RetryAttemptNumber

	if len(errors) > 0 {
		return GetPennyDropStatusRequestMultiError(errors)
	}

	return nil
}

// GetPennyDropStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetPennyDropStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type GetPennyDropStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPennyDropStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPennyDropStatusRequestMultiError) AllErrors() []error { return m }

// GetPennyDropStatusRequestValidationError is the validation error returned by
// GetPennyDropStatusRequest.Validate if the designated constraints aren't met.
type GetPennyDropStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPennyDropStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPennyDropStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPennyDropStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPennyDropStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPennyDropStatusRequestValidationError) ErrorName() string {
	return "GetPennyDropStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPennyDropStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPennyDropStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPennyDropStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPennyDropStatusRequestValidationError{}

// Validate checks the field values on GetPennyDropStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPennyDropStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPennyDropStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPennyDropStatusResponseMultiError, or nil if none found.
func (m *GetPennyDropStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPennyDropStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPennyDropStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPennyDropStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPennyDropStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPennyDropStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPennyDropStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPennyDropStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPennyDropStatusResponseMultiError(errors)
	}

	return nil
}

// GetPennyDropStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetPennyDropStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type GetPennyDropStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPennyDropStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPennyDropStatusResponseMultiError) AllErrors() []error { return m }

// GetPennyDropStatusResponseValidationError is the validation error returned
// by GetPennyDropStatusResponse.Validate if the designated constraints aren't met.
type GetPennyDropStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPennyDropStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPennyDropStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPennyDropStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPennyDropStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPennyDropStatusResponseValidationError) ErrorName() string {
	return "GetPennyDropStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPennyDropStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPennyDropStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPennyDropStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPennyDropStatusResponseValidationError{}

// Validate checks the field values on GetPaySearchScreenV2DetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPaySearchScreenV2DetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaySearchScreenV2DetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaySearchScreenV2DetailsRequestMultiError, or nil if none found.
func (m *GetPaySearchScreenV2DetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaySearchScreenV2DetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaySearchScreenV2DetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaySearchScreenV2DetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaySearchScreenV2DetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScreenType

	if len(errors) > 0 {
		return GetPaySearchScreenV2DetailsRequestMultiError(errors)
	}

	return nil
}

// GetPaySearchScreenV2DetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPaySearchScreenV2DetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaySearchScreenV2DetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaySearchScreenV2DetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaySearchScreenV2DetailsRequestMultiError) AllErrors() []error { return m }

// GetPaySearchScreenV2DetailsRequestValidationError is the validation error
// returned by GetPaySearchScreenV2DetailsRequest.Validate if the designated
// constraints aren't met.
type GetPaySearchScreenV2DetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaySearchScreenV2DetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaySearchScreenV2DetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaySearchScreenV2DetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaySearchScreenV2DetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaySearchScreenV2DetailsRequestValidationError) ErrorName() string {
	return "GetPaySearchScreenV2DetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaySearchScreenV2DetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaySearchScreenV2DetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaySearchScreenV2DetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaySearchScreenV2DetailsRequestValidationError{}

// Validate checks the field values on GetPaySearchScreenV2DetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPaySearchScreenV2DetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaySearchScreenV2DetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaySearchScreenV2DetailsResponseMultiError, or nil if none found.
func (m *GetPaySearchScreenV2DetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaySearchScreenV2DetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaySearchScreenV2DetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaySearchScreenV2DetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaySearchScreenV2DetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaySearchV2Layout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaySearchScreenV2DetailsResponseValidationError{
					field:  "PaySearchV2Layout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaySearchScreenV2DetailsResponseValidationError{
					field:  "PaySearchV2Layout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaySearchV2Layout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaySearchScreenV2DetailsResponseValidationError{
				field:  "PaySearchV2Layout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaySearchScreenV2DetailsResponseMultiError(errors)
	}

	return nil
}

// GetPaySearchScreenV2DetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetPaySearchScreenV2DetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPaySearchScreenV2DetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaySearchScreenV2DetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaySearchScreenV2DetailsResponseMultiError) AllErrors() []error { return m }

// GetPaySearchScreenV2DetailsResponseValidationError is the validation error
// returned by GetPaySearchScreenV2DetailsResponse.Validate if the designated
// constraints aren't met.
type GetPaySearchScreenV2DetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaySearchScreenV2DetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaySearchScreenV2DetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaySearchScreenV2DetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaySearchScreenV2DetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaySearchScreenV2DetailsResponseValidationError) ErrorName() string {
	return "GetPaySearchScreenV2DetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaySearchScreenV2DetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaySearchScreenV2DetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaySearchScreenV2DetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaySearchScreenV2DetailsResponseValidationError{}

// Validate checks the field values on SearchTransactionParticipantsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SearchTransactionParticipantsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchTransactionParticipantsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchTransactionParticipantsRequestMultiError, or nil if none found.
func (m *SearchTransactionParticipantsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchTransactionParticipantsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchTransactionParticipantsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchTransactionParticipantsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchTransactionParticipantsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SearchQuery

	if len(errors) > 0 {
		return SearchTransactionParticipantsRequestMultiError(errors)
	}

	return nil
}

// SearchTransactionParticipantsRequestMultiError is an error wrapping multiple
// validation errors returned by
// SearchTransactionParticipantsRequest.ValidateAll() if the designated
// constraints aren't met.
type SearchTransactionParticipantsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchTransactionParticipantsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchTransactionParticipantsRequestMultiError) AllErrors() []error { return m }

// SearchTransactionParticipantsRequestValidationError is the validation error
// returned by SearchTransactionParticipantsRequest.Validate if the designated
// constraints aren't met.
type SearchTransactionParticipantsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchTransactionParticipantsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchTransactionParticipantsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchTransactionParticipantsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchTransactionParticipantsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchTransactionParticipantsRequestValidationError) ErrorName() string {
	return "SearchTransactionParticipantsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SearchTransactionParticipantsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchTransactionParticipantsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchTransactionParticipantsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchTransactionParticipantsRequestValidationError{}

// Validate checks the field values on SearchTransactionParticipantsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SearchTransactionParticipantsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchTransactionParticipantsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchTransactionParticipantsResponseMultiError, or nil if none found.
func (m *SearchTransactionParticipantsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchTransactionParticipantsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchTransactionParticipantsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSearchResults() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchTransactionParticipantsResponseValidationError{
						field:  fmt.Sprintf("SearchResults[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchTransactionParticipantsResponseValidationError{
						field:  fmt.Sprintf("SearchResults[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchTransactionParticipantsResponseValidationError{
					field:  fmt.Sprintf("SearchResults[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchTransactionParticipantsResponseMultiError(errors)
	}

	return nil
}

// SearchTransactionParticipantsResponseMultiError is an error wrapping
// multiple validation errors returned by
// SearchTransactionParticipantsResponse.ValidateAll() if the designated
// constraints aren't met.
type SearchTransactionParticipantsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchTransactionParticipantsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchTransactionParticipantsResponseMultiError) AllErrors() []error { return m }

// SearchTransactionParticipantsResponseValidationError is the validation error
// returned by SearchTransactionParticipantsResponse.Validate if the
// designated constraints aren't met.
type SearchTransactionParticipantsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchTransactionParticipantsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchTransactionParticipantsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchTransactionParticipantsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchTransactionParticipantsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchTransactionParticipantsResponseValidationError) ErrorName() string {
	return "SearchTransactionParticipantsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SearchTransactionParticipantsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchTransactionParticipantsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchTransactionParticipantsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchTransactionParticipantsResponseValidationError{}

// Validate checks the field values on GetAMBScreenDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAMBScreenDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAMBScreenDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAMBScreenDetailsRequestMultiError, or nil if none found.
func (m *GetAMBScreenDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAMBScreenDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBScreenDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBScreenDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBScreenDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAMBScreenDetailsRequestMultiError(errors)
	}

	return nil
}

// GetAMBScreenDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetAMBScreenDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAMBScreenDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAMBScreenDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAMBScreenDetailsRequestMultiError) AllErrors() []error { return m }

// GetAMBScreenDetailsRequestValidationError is the validation error returned
// by GetAMBScreenDetailsRequest.Validate if the designated constraints aren't met.
type GetAMBScreenDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAMBScreenDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAMBScreenDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAMBScreenDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAMBScreenDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAMBScreenDetailsRequestValidationError) ErrorName() string {
	return "GetAMBScreenDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAMBScreenDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAMBScreenDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAMBScreenDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAMBScreenDetailsRequestValidationError{}

// Validate checks the field values on GetAMBScreenDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAMBScreenDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAMBScreenDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAMBScreenDetailsResponseMultiError, or nil if none found.
func (m *GetAMBScreenDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAMBScreenDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBScreenDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBScreenDetailsResponseValidationError{
				field:  "Section",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAMBScreenDetailsResponseMultiError(errors)
	}

	return nil
}

// GetAMBScreenDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetAMBScreenDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAMBScreenDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAMBScreenDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAMBScreenDetailsResponseMultiError) AllErrors() []error { return m }

// GetAMBScreenDetailsResponseValidationError is the validation error returned
// by GetAMBScreenDetailsResponse.Validate if the designated constraints
// aren't met.
type GetAMBScreenDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAMBScreenDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAMBScreenDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAMBScreenDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAMBScreenDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAMBScreenDetailsResponseValidationError) ErrorName() string {
	return "GetAMBScreenDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAMBScreenDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAMBScreenDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAMBScreenDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAMBScreenDetailsResponseValidationError{}

// Validate checks the field values on
// SearchTransactionParticipantsResponse_SearchResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchTransactionParticipantsResponse_SearchResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SearchTransactionParticipantsResponse_SearchResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// SearchTransactionParticipantsResponse_SearchResultMultiError, or nil if
// none found.
func (m *SearchTransactionParticipantsResponse_SearchResult) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchTransactionParticipantsResponse_SearchResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponse_SearchResultValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponse_SearchResultValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchTransactionParticipantsResponse_SearchResultValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponse_SearchResultValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponse_SearchResultValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchTransactionParticipantsResponse_SearchResultValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponse_SearchResultValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponse_SearchResultValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchTransactionParticipantsResponse_SearchResultValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponse_SearchResultValidationError{
					field:  "UserParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchTransactionParticipantsResponse_SearchResultValidationError{
					field:  "UserParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchTransactionParticipantsResponse_SearchResultValidationError{
				field:  "UserParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Source

	if len(errors) > 0 {
		return SearchTransactionParticipantsResponse_SearchResultMultiError(errors)
	}

	return nil
}

// SearchTransactionParticipantsResponse_SearchResultMultiError is an error
// wrapping multiple validation errors returned by
// SearchTransactionParticipantsResponse_SearchResult.ValidateAll() if the
// designated constraints aren't met.
type SearchTransactionParticipantsResponse_SearchResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchTransactionParticipantsResponse_SearchResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchTransactionParticipantsResponse_SearchResultMultiError) AllErrors() []error { return m }

// SearchTransactionParticipantsResponse_SearchResultValidationError is the
// validation error returned by
// SearchTransactionParticipantsResponse_SearchResult.Validate if the
// designated constraints aren't met.
type SearchTransactionParticipantsResponse_SearchResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchTransactionParticipantsResponse_SearchResultValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e SearchTransactionParticipantsResponse_SearchResultValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SearchTransactionParticipantsResponse_SearchResultValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e SearchTransactionParticipantsResponse_SearchResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchTransactionParticipantsResponse_SearchResultValidationError) ErrorName() string {
	return "SearchTransactionParticipantsResponse_SearchResultValidationError"
}

// Error satisfies the builtin error interface
func (e SearchTransactionParticipantsResponse_SearchResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchTransactionParticipantsResponse_SearchResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchTransactionParticipantsResponse_SearchResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchTransactionParticipantsResponse_SearchResultValidationError{}
