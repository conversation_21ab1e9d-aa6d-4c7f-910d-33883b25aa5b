syntax = "proto3";

package firefly.activity;

import "api/celestial/activity/header.proto";
import "api/firefly/enums/enums.proto";
import "api/firefly/internal/card_request.proto";
import "api/firefly/internal/card_request_stage.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/user/onboarding/internal/onboarding_details.proto";

option go_package = "github.com/epifi/gamma/api/firefly/activity";
option java_package = "com.github.epifi.gamma.api.firefly.activity";

message PerformOnboardingStageRequest {
  celestial.activity.RequestHeader request_header = 1;
  user.onboarding.OnboardingStage onboarding_stage = 2;
  firefly.enums.CardRequestStageName card_request_stage_name = 3;
}

message PerformOnboardingStageResponse {
  celestial.activity.ResponseHeader response_header = 1;
  firefly.CardRequest card_request = 2;
  user.onboarding.OnboardingState onboarding_state = 3;
  firefly.CardRequestStage card_request_stage = 4;
  frontend.deeplink.Deeplink next_action = 5;
}

message PerformOnboardingStageAsyncRequest {
  celestial.activity.RequestHeader request_header = 1;
  firefly.enums.CardRequestStageName card_request_stage_name = 3;
}

message PerformOnboardingStageAsyncResponse {
  celestial.activity.ResponseHeader response_header = 1;
  bool fail_permanently = 2;
}
