syntax = "proto3";

package api.typesv2.ui;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";

// This message is used to render the footer section of the screen
// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=443-17527&t=isFrTg4ffaSJtnj5-4
message FooterDetails {
  typesv2.common.Text text = 1;
  typesv2.common.VisualElement visual_element = 2;
  typesv2.common.ui.widget.BackgroundColour background_colour = 3;
}
