// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/fittt/screen_options.proto

package fittt

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on
// GetUpdateInvestmentSipsWithConsentScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUpdateInvestmentSipsWithConsentScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUpdateInvestmentSipsWithConsentScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetUpdateInvestmentSipsWithConsentScreenOptionsMultiError, or nil if none found.
func (m *GetUpdateInvestmentSipsWithConsentScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUpdateInvestmentSipsWithConsentScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UseCase

	if len(errors) > 0 {
		return GetUpdateInvestmentSipsWithConsentScreenOptionsMultiError(errors)
	}

	return nil
}

// GetUpdateInvestmentSipsWithConsentScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// GetUpdateInvestmentSipsWithConsentScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type GetUpdateInvestmentSipsWithConsentScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUpdateInvestmentSipsWithConsentScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUpdateInvestmentSipsWithConsentScreenOptionsMultiError) AllErrors() []error { return m }

// GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError is the
// validation error returned by
// GetUpdateInvestmentSipsWithConsentScreenOptions.Validate if the designated
// constraints aren't met.
type GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError) ErrorName() string {
	return "GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUpdateInvestmentSipsWithConsentScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUpdateInvestmentSipsWithConsentScreenOptionsValidationError{}

// Validate checks the field values on UpdateInvestmentSipsWithConsentScreen
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateInvestmentSipsWithConsentScreen) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateInvestmentSipsWithConsentScreen
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateInvestmentSipsWithConsentScreenMultiError, or nil if none found.
func (m *UpdateInvestmentSipsWithConsentScreen) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateInvestmentSipsWithConsentScreen) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatusBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "StatusBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "StatusBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreenValidationError{
				field:  "StatusBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreenValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreenValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTncBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "TncBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "TncBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTncBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreenValidationError{
				field:  "TncBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActionCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
						field:  fmt.Sprintf("ActionCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
						field:  fmt.Sprintf("ActionCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  fmt.Sprintf("ActionCards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetConsentTncCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "ConsentTncCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "ConsentTncCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentTncCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreenValidationError{
				field:  "ConsentTncCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmptyCardText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "EmptyCardText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreenValidationError{
					field:  "EmptyCardText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmptyCardText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreenValidationError{
				field:  "EmptyCardText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateInvestmentSipsWithConsentScreenMultiError(errors)
	}

	return nil
}

// UpdateInvestmentSipsWithConsentScreenMultiError is an error wrapping
// multiple validation errors returned by
// UpdateInvestmentSipsWithConsentScreen.ValidateAll() if the designated
// constraints aren't met.
type UpdateInvestmentSipsWithConsentScreenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateInvestmentSipsWithConsentScreenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateInvestmentSipsWithConsentScreenMultiError) AllErrors() []error { return m }

// UpdateInvestmentSipsWithConsentScreenValidationError is the validation error
// returned by UpdateInvestmentSipsWithConsentScreen.Validate if the
// designated constraints aren't met.
type UpdateInvestmentSipsWithConsentScreenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateInvestmentSipsWithConsentScreenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateInvestmentSipsWithConsentScreenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateInvestmentSipsWithConsentScreenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateInvestmentSipsWithConsentScreenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateInvestmentSipsWithConsentScreenValidationError) ErrorName() string {
	return "UpdateInvestmentSipsWithConsentScreenValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateInvestmentSipsWithConsentScreenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateInvestmentSipsWithConsentScreen.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateInvestmentSipsWithConsentScreenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateInvestmentSipsWithConsentScreenValidationError{}

// Validate checks the field values on USStocksSummaryScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *USStocksSummaryScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on USStocksSummaryScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// USStocksSummaryScreenOptionsMultiError, or nil if none found.
func (m *USStocksSummaryScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *USStocksSummaryScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStocksSummaryScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStocksSummaryScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStocksSummaryScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStocksSummaryScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStocksSummaryScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStocksSummaryScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDetailsSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, USStocksSummaryScreenOptionsValidationError{
						field:  fmt.Sprintf("DetailsSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, USStocksSummaryScreenOptionsValidationError{
						field:  fmt.Sprintf("DetailsSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return USStocksSummaryScreenOptionsValidationError{
					field:  fmt.Sprintf("DetailsSections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetMandateDetailsSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStocksSummaryScreenOptionsValidationError{
					field:  "MandateDetailsSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStocksSummaryScreenOptionsValidationError{
					field:  "MandateDetailsSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMandateDetailsSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStocksSummaryScreenOptionsValidationError{
				field:  "MandateDetailsSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNoteSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStocksSummaryScreenOptionsValidationError{
					field:  "NoteSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStocksSummaryScreenOptionsValidationError{
					field:  "NoteSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNoteSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStocksSummaryScreenOptionsValidationError{
				field:  "NoteSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return USStocksSummaryScreenOptionsMultiError(errors)
	}

	return nil
}

// USStocksSummaryScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by USStocksSummaryScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type USStocksSummaryScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m USStocksSummaryScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m USStocksSummaryScreenOptionsMultiError) AllErrors() []error { return m }

// USStocksSummaryScreenOptionsValidationError is the validation error returned
// by USStocksSummaryScreenOptions.Validate if the designated constraints
// aren't met.
type USStocksSummaryScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e USStocksSummaryScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e USStocksSummaryScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e USStocksSummaryScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e USStocksSummaryScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e USStocksSummaryScreenOptionsValidationError) ErrorName() string {
	return "USStocksSummaryScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e USStocksSummaryScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUSStocksSummaryScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = USStocksSummaryScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = USStocksSummaryScreenOptionsValidationError{}

// Validate checks the field values on USStockSummaryDetailsSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *USStockSummaryDetailsSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on USStockSummaryDetailsSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// USStockSummaryDetailsSectionMultiError, or nil if none found.
func (m *USStockSummaryDetailsSection) ValidateAll() error {
	return m.validate(true)
}

func (m *USStockSummaryDetailsSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLineItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, USStockSummaryDetailsSectionValidationError{
						field:  fmt.Sprintf("LineItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, USStockSummaryDetailsSectionValidationError{
						field:  fmt.Sprintf("LineItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return USStockSummaryDetailsSectionValidationError{
					field:  fmt.Sprintf("LineItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStockSummaryDetailsSectionValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStockSummaryDetailsSectionValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStockSummaryDetailsSectionValidationError{
				field:  "BgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BorderColor

	if len(errors) > 0 {
		return USStockSummaryDetailsSectionMultiError(errors)
	}

	return nil
}

// USStockSummaryDetailsSectionMultiError is an error wrapping multiple
// validation errors returned by USStockSummaryDetailsSection.ValidateAll() if
// the designated constraints aren't met.
type USStockSummaryDetailsSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m USStockSummaryDetailsSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m USStockSummaryDetailsSectionMultiError) AllErrors() []error { return m }

// USStockSummaryDetailsSectionValidationError is the validation error returned
// by USStockSummaryDetailsSection.Validate if the designated constraints
// aren't met.
type USStockSummaryDetailsSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e USStockSummaryDetailsSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e USStockSummaryDetailsSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e USStockSummaryDetailsSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e USStockSummaryDetailsSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e USStockSummaryDetailsSectionValidationError) ErrorName() string {
	return "USStockSummaryDetailsSectionValidationError"
}

// Error satisfies the builtin error interface
func (e USStockSummaryDetailsSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUSStockSummaryDetailsSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = USStockSummaryDetailsSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = USStockSummaryDetailsSectionValidationError{}

// Validate checks the field values on USStockSummaryDetailsLineItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *USStockSummaryDetailsLineItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on USStockSummaryDetailsLineItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// USStockSummaryDetailsLineItemMultiError, or nil if none found.
func (m *USStockSummaryDetailsLineItem) ValidateAll() error {
	return m.validate(true)
}

func (m *USStockSummaryDetailsLineItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeadingContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStockSummaryDetailsLineItemValidationError{
					field:  "LeadingContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStockSummaryDetailsLineItemValidationError{
					field:  "LeadingContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeadingContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStockSummaryDetailsLineItemValidationError{
				field:  "LeadingContent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTrailingContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStockSummaryDetailsLineItemValidationError{
					field:  "TrailingContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStockSummaryDetailsLineItemValidationError{
					field:  "TrailingContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrailingContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStockSummaryDetailsLineItemValidationError{
				field:  "TrailingContent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsHighlighted

	if all {
		switch v := interface{}(m.GetLeadingContentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStockSummaryDetailsLineItemValidationError{
					field:  "LeadingContentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStockSummaryDetailsLineItemValidationError{
					field:  "LeadingContentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeadingContentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStockSummaryDetailsLineItemValidationError{
				field:  "LeadingContentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return USStockSummaryDetailsLineItemMultiError(errors)
	}

	return nil
}

// USStockSummaryDetailsLineItemMultiError is an error wrapping multiple
// validation errors returned by USStockSummaryDetailsLineItem.ValidateAll()
// if the designated constraints aren't met.
type USStockSummaryDetailsLineItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m USStockSummaryDetailsLineItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m USStockSummaryDetailsLineItemMultiError) AllErrors() []error { return m }

// USStockSummaryDetailsLineItemValidationError is the validation error
// returned by USStockSummaryDetailsLineItem.Validate if the designated
// constraints aren't met.
type USStockSummaryDetailsLineItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e USStockSummaryDetailsLineItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e USStockSummaryDetailsLineItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e USStockSummaryDetailsLineItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e USStockSummaryDetailsLineItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e USStockSummaryDetailsLineItemValidationError) ErrorName() string {
	return "USStockSummaryDetailsLineItemValidationError"
}

// Error satisfies the builtin error interface
func (e USStockSummaryDetailsLineItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUSStockSummaryDetailsLineItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = USStockSummaryDetailsLineItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = USStockSummaryDetailsLineItemValidationError{}

// Validate checks the field values on USStockMandateDetailsSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *USStockMandateDetailsSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on USStockMandateDetailsSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// USStockMandateDetailsSectionMultiError, or nil if none found.
func (m *USStockMandateDetailsSection) ValidateAll() error {
	return m.validate(true)
}

func (m *USStockMandateDetailsSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStockMandateDetailsSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStockMandateDetailsSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStockMandateDetailsSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSectionItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, USStockMandateDetailsSectionValidationError{
						field:  fmt.Sprintf("SectionItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, USStockMandateDetailsSectionValidationError{
						field:  fmt.Sprintf("SectionItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return USStockMandateDetailsSectionValidationError{
					field:  fmt.Sprintf("SectionItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return USStockMandateDetailsSectionMultiError(errors)
	}

	return nil
}

// USStockMandateDetailsSectionMultiError is an error wrapping multiple
// validation errors returned by USStockMandateDetailsSection.ValidateAll() if
// the designated constraints aren't met.
type USStockMandateDetailsSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m USStockMandateDetailsSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m USStockMandateDetailsSectionMultiError) AllErrors() []error { return m }

// USStockMandateDetailsSectionValidationError is the validation error returned
// by USStockMandateDetailsSection.Validate if the designated constraints
// aren't met.
type USStockMandateDetailsSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e USStockMandateDetailsSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e USStockMandateDetailsSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e USStockMandateDetailsSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e USStockMandateDetailsSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e USStockMandateDetailsSectionValidationError) ErrorName() string {
	return "USStockMandateDetailsSectionValidationError"
}

// Error satisfies the builtin error interface
func (e USStockMandateDetailsSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUSStockMandateDetailsSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = USStockMandateDetailsSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = USStockMandateDetailsSectionValidationError{}

// Validate checks the field values on USStockSummaryNoteSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *USStockSummaryNoteSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on USStockSummaryNoteSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// USStockSummaryNoteSectionMultiError, or nil if none found.
func (m *USStockSummaryNoteSection) ValidateAll() error {
	return m.validate(true)
}

func (m *USStockSummaryNoteSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USStockSummaryNoteSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USStockSummaryNoteSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USStockSummaryNoteSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetListItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, USStockSummaryNoteSectionValidationError{
						field:  fmt.Sprintf("ListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, USStockSummaryNoteSectionValidationError{
						field:  fmt.Sprintf("ListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return USStockSummaryNoteSectionValidationError{
					field:  fmt.Sprintf("ListItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ListType

	if len(errors) > 0 {
		return USStockSummaryNoteSectionMultiError(errors)
	}

	return nil
}

// USStockSummaryNoteSectionMultiError is an error wrapping multiple validation
// errors returned by USStockSummaryNoteSection.ValidateAll() if the
// designated constraints aren't met.
type USStockSummaryNoteSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m USStockSummaryNoteSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m USStockSummaryNoteSectionMultiError) AllErrors() []error { return m }

// USStockSummaryNoteSectionValidationError is the validation error returned by
// USStockSummaryNoteSection.Validate if the designated constraints aren't met.
type USStockSummaryNoteSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e USStockSummaryNoteSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e USStockSummaryNoteSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e USStockSummaryNoteSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e USStockSummaryNoteSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e USStockSummaryNoteSectionValidationError) ErrorName() string {
	return "USStockSummaryNoteSectionValidationError"
}

// Error satisfies the builtin error interface
func (e USStockSummaryNoteSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUSStockSummaryNoteSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = USStockSummaryNoteSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = USStockSummaryNoteSectionValidationError{}

// Validate checks the field values on SISetupScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SISetupScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SISetupScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SISetupScreenOptionsMultiError, or nil if none found.
func (m *SISetupScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SISetupScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SISetupScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SISetupScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SISetupScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayeeName

	// no validation rules for PayeeProfileUrl

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SISetupScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SISetupScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SISetupScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SISetupScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SISetupScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SISetupScreenOptionsValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Option.(type) {
	case *SISetupScreenOptions_SiCreateParams:
		if v == nil {
			err := SISetupScreenOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSiCreateParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SISetupScreenOptionsValidationError{
						field:  "SiCreateParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SISetupScreenOptionsValidationError{
						field:  "SiCreateParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSiCreateParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SISetupScreenOptionsValidationError{
					field:  "SiCreateParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SISetupScreenOptions_SiUpdateParams:
		if v == nil {
			err := SISetupScreenOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSiUpdateParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SISetupScreenOptionsValidationError{
						field:  "SiUpdateParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SISetupScreenOptionsValidationError{
						field:  "SiUpdateParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSiUpdateParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SISetupScreenOptionsValidationError{
					field:  "SiUpdateParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SISetupScreenOptionsMultiError(errors)
	}

	return nil
}

// SISetupScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by SISetupScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type SISetupScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SISetupScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SISetupScreenOptionsMultiError) AllErrors() []error { return m }

// SISetupScreenOptionsValidationError is the validation error returned by
// SISetupScreenOptions.Validate if the designated constraints aren't met.
type SISetupScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SISetupScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SISetupScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SISetupScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SISetupScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SISetupScreenOptionsValidationError) ErrorName() string {
	return "SISetupScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SISetupScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSISetupScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SISetupScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SISetupScreenOptionsValidationError{}

// Validate checks the field values on SICreateParams with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SICreateParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SICreateParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SICreateParamsMultiError,
// or nil if none found.
func (m *SICreateParams) ValidateAll() error {
	return m.validate(true)
}

func (m *SICreateParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SICreateParamsValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SICreateParamsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurrenceRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "RecurrenceRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "RecurrenceRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurrenceRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SICreateParamsValidationError{
				field:  "RecurrenceRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SICreateParamsValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SICreateParamsValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SICreateParamsValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remark

	if len(errors) > 0 {
		return SICreateParamsMultiError(errors)
	}

	return nil
}

// SICreateParamsMultiError is an error wrapping multiple validation errors
// returned by SICreateParams.ValidateAll() if the designated constraints
// aren't met.
type SICreateParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SICreateParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SICreateParamsMultiError) AllErrors() []error { return m }

// SICreateParamsValidationError is the validation error returned by
// SICreateParams.Validate if the designated constraints aren't met.
type SICreateParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SICreateParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SICreateParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SICreateParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SICreateParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SICreateParamsValidationError) ErrorName() string { return "SICreateParamsValidationError" }

// Error satisfies the builtin error interface
func (e SICreateParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSICreateParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SICreateParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SICreateParamsValidationError{}

// Validate checks the field values on SIUpdateParams with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SIUpdateParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SIUpdateParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SIUpdateParamsMultiError,
// or nil if none found.
func (m *SIUpdateParams) ValidateAll() error {
	return m.validate(true)
}

func (m *SIUpdateParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIUpdateParamsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIUpdateParamsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIUpdateParamsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SIUpdateParamsValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SIUpdateParamsValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SIUpdateParamsValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SIUpdateParamsMultiError(errors)
	}

	return nil
}

// SIUpdateParamsMultiError is an error wrapping multiple validation errors
// returned by SIUpdateParams.ValidateAll() if the designated constraints
// aren't met.
type SIUpdateParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SIUpdateParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SIUpdateParamsMultiError) AllErrors() []error { return m }

// SIUpdateParamsValidationError is the validation error returned by
// SIUpdateParams.Validate if the designated constraints aren't met.
type SIUpdateParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SIUpdateParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SIUpdateParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SIUpdateParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SIUpdateParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SIUpdateParamsValidationError) ErrorName() string { return "SIUpdateParamsValidationError" }

// Error satisfies the builtin error interface
func (e SIUpdateParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSIUpdateParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SIUpdateParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SIUpdateParamsValidationError{}

// Validate checks the field values on
// UpdateInvestmentSipsWithConsentScreen_ActionCard with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateInvestmentSipsWithConsentScreen_ActionCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateInvestmentSipsWithConsentScreen_ActionCard with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// UpdateInvestmentSipsWithConsentScreen_ActionCardMultiError, or nil if none found.
func (m *UpdateInvestmentSipsWithConsentScreen_ActionCard) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateInvestmentSipsWithConsentScreen_ActionCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
				field:  "LeftIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSubTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
						field:  fmt.Sprintf("SubTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
						field:  fmt.Sprintf("SubTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
					field:  fmt.Sprintf("SubTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
					field:  "CardDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
					field:  "CardDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{
				field:  "CardDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateInvestmentSipsWithConsentScreen_ActionCardMultiError(errors)
	}

	return nil
}

// UpdateInvestmentSipsWithConsentScreen_ActionCardMultiError is an error
// wrapping multiple validation errors returned by
// UpdateInvestmentSipsWithConsentScreen_ActionCard.ValidateAll() if the
// designated constraints aren't met.
type UpdateInvestmentSipsWithConsentScreen_ActionCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateInvestmentSipsWithConsentScreen_ActionCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateInvestmentSipsWithConsentScreen_ActionCardMultiError) AllErrors() []error { return m }

// UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError is the
// validation error returned by
// UpdateInvestmentSipsWithConsentScreen_ActionCard.Validate if the designated
// constraints aren't met.
type UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError) ErrorName() string {
	return "UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateInvestmentSipsWithConsentScreen_ActionCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateInvestmentSipsWithConsentScreen_ActionCardValidationError{}

// Validate checks the field values on
// UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCard with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCard with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardMultiError, or nil
// if none found.
func (m *UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCard) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCheckboxItem()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError{
					field:  "CheckboxItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError{
					field:  "CheckboxItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCheckboxItem()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError{
				field:  "CheckboxItem",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardMultiError(errors)
	}

	return nil
}

// UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardMultiError is an
// error wrapping multiple validation errors returned by
// UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCard.ValidateAll() if
// the designated constraints aren't met.
type UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardMultiError) AllErrors() []error {
	return m
}

// UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError is
// the validation error returned by
// UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCard.Validate if the
// designated constraints aren't met.
type UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError) ErrorName() string {
	return "UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateInvestmentSipsWithConsentScreen_ConsentAndTncCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateInvestmentSipsWithConsentScreen_ConsentAndTncCardValidationError{}
