// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/frontend/nominee_screen_options.proto

package frontend

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NomineeScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NomineeScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NomineeScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NomineeScreenOptionsMultiError, or nil if none found.
func (m *NomineeScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *NomineeScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NomineeScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NomineeScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NomineeScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Entrypoint

	for idx, item := range m.GetNomineeDocuments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NomineeScreenOptionsValidationError{
						field:  fmt.Sprintf("NomineeDocuments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NomineeScreenOptionsValidationError{
						field:  fmt.Sprintf("NomineeDocuments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NomineeScreenOptionsValidationError{
					field:  fmt.Sprintf("NomineeDocuments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NomineeScreenOptionsMultiError(errors)
	}

	return nil
}

// NomineeScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by NomineeScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type NomineeScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NomineeScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NomineeScreenOptionsMultiError) AllErrors() []error { return m }

// NomineeScreenOptionsValidationError is the validation error returned by
// NomineeScreenOptions.Validate if the designated constraints aren't met.
type NomineeScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NomineeScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NomineeScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NomineeScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NomineeScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NomineeScreenOptionsValidationError) ErrorName() string {
	return "NomineeScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e NomineeScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNomineeScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NomineeScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NomineeScreenOptionsValidationError{}

// Validate checks the field values on NomineeScreenOptions_NomineeDocument
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *NomineeScreenOptions_NomineeDocument) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NomineeScreenOptions_NomineeDocument
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// NomineeScreenOptions_NomineeDocumentMultiError, or nil if none found.
func (m *NomineeScreenOptions_NomineeDocument) ValidateAll() error {
	return m.validate(true)
}

func (m *NomineeScreenOptions_NomineeDocument) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocumentType

	// no validation rules for DocumentName

	// no validation rules for DocumentInputPlaceHolder

	// no validation rules for MaxInputLength

	for idx, item := range m.GetValidator() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NomineeScreenOptions_NomineeDocumentValidationError{
						field:  fmt.Sprintf("Validator[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NomineeScreenOptions_NomineeDocumentValidationError{
						field:  fmt.Sprintf("Validator[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NomineeScreenOptions_NomineeDocumentValidationError{
					field:  fmt.Sprintf("Validator[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NomineeScreenOptions_NomineeDocumentMultiError(errors)
	}

	return nil
}

// NomineeScreenOptions_NomineeDocumentMultiError is an error wrapping multiple
// validation errors returned by
// NomineeScreenOptions_NomineeDocument.ValidateAll() if the designated
// constraints aren't met.
type NomineeScreenOptions_NomineeDocumentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NomineeScreenOptions_NomineeDocumentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NomineeScreenOptions_NomineeDocumentMultiError) AllErrors() []error { return m }

// NomineeScreenOptions_NomineeDocumentValidationError is the validation error
// returned by NomineeScreenOptions_NomineeDocument.Validate if the designated
// constraints aren't met.
type NomineeScreenOptions_NomineeDocumentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NomineeScreenOptions_NomineeDocumentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NomineeScreenOptions_NomineeDocumentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NomineeScreenOptions_NomineeDocumentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NomineeScreenOptions_NomineeDocumentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NomineeScreenOptions_NomineeDocumentValidationError) ErrorName() string {
	return "NomineeScreenOptions_NomineeDocumentValidationError"
}

// Error satisfies the builtin error interface
func (e NomineeScreenOptions_NomineeDocumentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNomineeScreenOptions_NomineeDocument.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NomineeScreenOptions_NomineeDocumentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NomineeScreenOptions_NomineeDocumentValidationError{}
