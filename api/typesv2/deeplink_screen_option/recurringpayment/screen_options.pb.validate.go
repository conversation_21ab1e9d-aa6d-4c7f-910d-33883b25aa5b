// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/recurringpayment/screen_options.proto

package recurringpayment

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RecurringPaymentPollingScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RecurringPaymentPollingScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecurringPaymentPollingScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecurringPaymentPollingScreenOptionsMultiError, or nil if none found.
func (m *RecurringPaymentPollingScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *RecurringPaymentPollingScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentPollingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentPollingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentPollingScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	if all {
		switch v := interface{}(m.GetPollDelay()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentPollingScreenOptionsValidationError{
					field:  "PollDelay",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentPollingScreenOptionsValidationError{
					field:  "PollDelay",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPollDelay()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentPollingScreenOptionsValidationError{
				field:  "PollDelay",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PollAttempt

	if all {
		switch v := interface{}(m.GetPollingText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentPollingScreenOptionsValidationError{
					field:  "PollingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentPollingScreenOptionsValidationError{
					field:  "PollingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPollingText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentPollingScreenOptionsValidationError{
				field:  "PollingText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecurringPaymentPollingScreenOptionsMultiError(errors)
	}

	return nil
}

// RecurringPaymentPollingScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// RecurringPaymentPollingScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type RecurringPaymentPollingScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurringPaymentPollingScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurringPaymentPollingScreenOptionsMultiError) AllErrors() []error { return m }

// RecurringPaymentPollingScreenOptionsValidationError is the validation error
// returned by RecurringPaymentPollingScreenOptions.Validate if the designated
// constraints aren't met.
type RecurringPaymentPollingScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurringPaymentPollingScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecurringPaymentPollingScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecurringPaymentPollingScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecurringPaymentPollingScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecurringPaymentPollingScreenOptionsValidationError) ErrorName() string {
	return "RecurringPaymentPollingScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e RecurringPaymentPollingScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurringPaymentPollingScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurringPaymentPollingScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurringPaymentPollingScreenOptionsValidationError{}

// Validate checks the field values on
// InitiateRecurringPaymentCreationAuthorisationScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateRecurringPaymentCreationAuthorisationScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateRecurringPaymentCreationAuthorisationScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateRecurringPaymentCreationAuthorisationScreenOptionsMultiError, or
// nil if none found.
func (m *InitiateRecurringPaymentCreationAuthorisationScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateRecurringPaymentCreationAuthorisationScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurringPaymentId

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return InitiateRecurringPaymentCreationAuthorisationScreenOptionsMultiError(errors)
	}

	return nil
}

// InitiateRecurringPaymentCreationAuthorisationScreenOptionsMultiError is an
// error wrapping multiple validation errors returned by
// InitiateRecurringPaymentCreationAuthorisationScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type InitiateRecurringPaymentCreationAuthorisationScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateRecurringPaymentCreationAuthorisationScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateRecurringPaymentCreationAuthorisationScreenOptionsMultiError) AllErrors() []error {
	return m
}

// InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError is
// the validation error returned by
// InitiateRecurringPaymentCreationAuthorisationScreenOptions.Validate if the
// designated constraints aren't met.
type InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError) ErrorName() string {
	return "InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateRecurringPaymentCreationAuthorisationScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateRecurringPaymentCreationAuthorisationScreenOptionsValidationError{}
