syntax = "proto3";

package api.analyser.variables.userdeclarations;

import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/analyser/variables/userdeclarations";
option java_package = "com.github.epifi.gamma.api.analyser.variables.userdeclarations";


message MonthlyIncome {
  google.type.Money monthly_income = 1;
  // this will help during edit of the user declared monthly salary
  string external_id = 2;
}
