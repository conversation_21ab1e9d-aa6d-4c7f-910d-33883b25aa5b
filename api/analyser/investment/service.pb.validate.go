// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/analyser/investment/service.proto

package investment

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/analyser/enums"

	model "github.com/epifi/gamma/api/analyser/investment/model"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.TimeDurationType(0)

	_ = model.TaskName(0)
)

// Validate checks the field values on GetTaskStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTaskStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTaskStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTaskStatusRequestMultiError, or nil if none found.
func (m *GetTaskStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTaskStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetTaskStatusRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.GetBy.(type) {
	case *GetTaskStatusRequest_TaskId:
		if v == nil {
			err := GetTaskStatusRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for TaskId
	case *GetTaskStatusRequest_TaskName:
		if v == nil {
			err := GetTaskStatusRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for TaskName
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetTaskStatusRequestMultiError(errors)
	}

	return nil
}

// GetTaskStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetTaskStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTaskStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTaskStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTaskStatusRequestMultiError) AllErrors() []error { return m }

// GetTaskStatusRequestValidationError is the validation error returned by
// GetTaskStatusRequest.Validate if the designated constraints aren't met.
type GetTaskStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTaskStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTaskStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTaskStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTaskStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTaskStatusRequestValidationError) ErrorName() string {
	return "GetTaskStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTaskStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTaskStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTaskStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTaskStatusRequestValidationError{}

// Validate checks the field values on GetTaskStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTaskStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTaskStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTaskStatusResponseMultiError, or nil if none found.
func (m *GetTaskStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTaskStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTaskStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTaskStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTaskStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TaskStatus

	if len(errors) > 0 {
		return GetTaskStatusResponseMultiError(errors)
	}

	return nil
}

// GetTaskStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetTaskStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTaskStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTaskStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTaskStatusResponseMultiError) AllErrors() []error { return m }

// GetTaskStatusResponseValidationError is the validation error returned by
// GetTaskStatusResponse.Validate if the designated constraints aren't met.
type GetTaskStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTaskStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTaskStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTaskStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTaskStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTaskStatusResponseValidationError) ErrorName() string {
	return "GetTaskStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTaskStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTaskStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTaskStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTaskStatusResponseValidationError{}

// Validate checks the field values on InitiateTasksRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateTasksRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateTasksRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateTasksRequestMultiError, or nil if none found.
func (m *InitiateTasksRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateTasksRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := InitiateTasksRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InitiateTasksRequestValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InitiateTasksRequestValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InitiateTasksRequestValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Priority

	if len(errors) > 0 {
		return InitiateTasksRequestMultiError(errors)
	}

	return nil
}

// InitiateTasksRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateTasksRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateTasksRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateTasksRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateTasksRequestMultiError) AllErrors() []error { return m }

// InitiateTasksRequestValidationError is the validation error returned by
// InitiateTasksRequest.Validate if the designated constraints aren't met.
type InitiateTasksRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateTasksRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateTasksRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateTasksRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateTasksRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateTasksRequestValidationError) ErrorName() string {
	return "InitiateTasksRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateTasksRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateTasksRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateTasksRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateTasksRequestValidationError{}

// Validate checks the field values on InitiateTasksResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateTasksResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateTasksResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateTasksResponseMultiError, or nil if none found.
func (m *InitiateTasksResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateTasksResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateTasksResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateTasksResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateTasksResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateTasksResponseMultiError(errors)
	}

	return nil
}

// InitiateTasksResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateTasksResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateTasksResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateTasksResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateTasksResponseMultiError) AllErrors() []error { return m }

// InitiateTasksResponseValidationError is the validation error returned by
// InitiateTasksResponse.Validate if the designated constraints aren't met.
type InitiateTasksResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateTasksResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateTasksResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateTasksResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateTasksResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateTasksResponseValidationError) ErrorName() string {
	return "InitiateTasksResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateTasksResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateTasksResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateTasksResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateTasksResponseValidationError{}

// Validate checks the field values on TaskDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskDetailsMultiError, or
// nil if none found.
func (m *TaskDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _TaskDetails_TaskName_NotInLookup[m.GetTaskName()]; ok {
		err := TaskDetailsValidationError{
			field:  "TaskName",
			reason: "value must not be in list [TASK_NAME_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTaskParams() == nil {
		err := TaskDetailsValidationError{
			field:  "TaskParams",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTaskParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TaskDetailsValidationError{
					field:  "TaskParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TaskDetailsValidationError{
					field:  "TaskParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTaskParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TaskDetailsValidationError{
				field:  "TaskParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TaskDetailsMultiError(errors)
	}

	return nil
}

// TaskDetailsMultiError is an error wrapping multiple validation errors
// returned by TaskDetails.ValidateAll() if the designated constraints aren't met.
type TaskDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskDetailsMultiError) AllErrors() []error { return m }

// TaskDetailsValidationError is the validation error returned by
// TaskDetails.Validate if the designated constraints aren't met.
type TaskDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskDetailsValidationError) ErrorName() string { return "TaskDetailsValidationError" }

// Error satisfies the builtin error interface
func (e TaskDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskDetailsValidationError{}

var _TaskDetails_TaskName_NotInLookup = map[model.TaskName]struct{}{
	0: {},
}

// Validate checks the field values on GetMFPortfolioHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMFPortfolioHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMFPortfolioHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMFPortfolioHistoryRequestMultiError, or nil if none found.
func (m *GetMFPortfolioHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMFPortfolioHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetMFPortfolioHistoryRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFromDate() == nil {
		err := GetMFPortfolioHistoryRequestValidationError{
			field:  "FromDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMFPortfolioHistoryRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMFPortfolioHistoryRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMFPortfolioHistoryRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetToDate() == nil {
		err := GetMFPortfolioHistoryRequestValidationError{
			field:  "ToDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMFPortfolioHistoryRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMFPortfolioHistoryRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMFPortfolioHistoryRequestValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _GetMFPortfolioHistoryRequest_TimeDuration_NotInLookup[m.GetTimeDuration()]; ok {
		err := GetMFPortfolioHistoryRequestValidationError{
			field:  "TimeDuration",
			reason: "value must not be in list [TIME_DURATION_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetMFPortfolioHistoryRequestMultiError(errors)
	}

	return nil
}

// GetMFPortfolioHistoryRequestMultiError is an error wrapping multiple
// validation errors returned by GetMFPortfolioHistoryRequest.ValidateAll() if
// the designated constraints aren't met.
type GetMFPortfolioHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMFPortfolioHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMFPortfolioHistoryRequestMultiError) AllErrors() []error { return m }

// GetMFPortfolioHistoryRequestValidationError is the validation error returned
// by GetMFPortfolioHistoryRequest.Validate if the designated constraints
// aren't met.
type GetMFPortfolioHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMFPortfolioHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMFPortfolioHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMFPortfolioHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMFPortfolioHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMFPortfolioHistoryRequestValidationError) ErrorName() string {
	return "GetMFPortfolioHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMFPortfolioHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMFPortfolioHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMFPortfolioHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMFPortfolioHistoryRequestValidationError{}

var _GetMFPortfolioHistoryRequest_TimeDuration_NotInLookup = map[enums.TimeDurationType]struct{}{
	0: {},
}

// Validate checks the field values on GetMFPortfolioHistoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMFPortfolioHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMFPortfolioHistoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetMFPortfolioHistoryResponseMultiError, or nil if none found.
func (m *GetMFPortfolioHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMFPortfolioHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMFPortfolioHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMFPortfolioHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMFPortfolioHistoryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetMFPortfolioHistoryResponseValidationError{
						field:  fmt.Sprintf("Details[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetMFPortfolioHistoryResponseValidationError{
						field:  fmt.Sprintf("Details[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetMFPortfolioHistoryResponseValidationError{
					field:  fmt.Sprintf("Details[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetEnrichedPortfolioDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMFPortfolioHistoryResponseValidationError{
					field:  "EnrichedPortfolioDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMFPortfolioHistoryResponseValidationError{
					field:  "EnrichedPortfolioDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnrichedPortfolioDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMFPortfolioHistoryResponseValidationError{
				field:  "EnrichedPortfolioDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMFPortfolioHistoryResponseMultiError(errors)
	}

	return nil
}

// GetMFPortfolioHistoryResponseMultiError is an error wrapping multiple
// validation errors returned by GetMFPortfolioHistoryResponse.ValidateAll()
// if the designated constraints aren't met.
type GetMFPortfolioHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMFPortfolioHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMFPortfolioHistoryResponseMultiError) AllErrors() []error { return m }

// GetMFPortfolioHistoryResponseValidationError is the validation error
// returned by GetMFPortfolioHistoryResponse.Validate if the designated
// constraints aren't met.
type GetMFPortfolioHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMFPortfolioHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMFPortfolioHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMFPortfolioHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMFPortfolioHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMFPortfolioHistoryResponseValidationError) ErrorName() string {
	return "GetMFPortfolioHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMFPortfolioHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMFPortfolioHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMFPortfolioHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMFPortfolioHistoryResponseValidationError{}

// Validate checks the field values on MFPortfolioDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MFPortfolioDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MFPortfolioDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MFPortfolioDetailsMultiError, or nil if none found.
func (m *MFPortfolioDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MFPortfolioDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHistory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFPortfolioDetailsValidationError{
					field:  "History",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFPortfolioDetailsValidationError{
					field:  "History",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHistory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFPortfolioDetailsValidationError{
				field:  "History",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChangesInDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFPortfolioDetailsValidationError{
					field:  "ChangesInDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFPortfolioDetailsValidationError{
					field:  "ChangesInDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChangesInDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFPortfolioDetailsValidationError{
				field:  "ChangesInDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MFPortfolioDetailsMultiError(errors)
	}

	return nil
}

// MFPortfolioDetailsMultiError is an error wrapping multiple validation errors
// returned by MFPortfolioDetails.ValidateAll() if the designated constraints
// aren't met.
type MFPortfolioDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MFPortfolioDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MFPortfolioDetailsMultiError) AllErrors() []error { return m }

// MFPortfolioDetailsValidationError is the validation error returned by
// MFPortfolioDetails.Validate if the designated constraints aren't met.
type MFPortfolioDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MFPortfolioDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MFPortfolioDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MFPortfolioDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MFPortfolioDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MFPortfolioDetailsValidationError) ErrorName() string {
	return "MFPortfolioDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e MFPortfolioDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMFPortfolioDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MFPortfolioDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MFPortfolioDetailsValidationError{}

// Validate checks the field values on PortfolioChanges with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PortfolioChanges) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortfolioChanges with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PortfolioChangesMultiError, or nil if none found.
func (m *PortfolioChanges) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioChanges) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInvestedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioChangesValidationError{
					field:  "InvestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioChangesValidationError{
					field:  "InvestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioChangesValidationError{
				field:  "InvestedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWithdrawnAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioChangesValidationError{
					field:  "WithdrawnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioChangesValidationError{
					field:  "WithdrawnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWithdrawnAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioChangesValidationError{
				field:  "WithdrawnAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPortfolioChange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioChangesValidationError{
					field:  "PortfolioChange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioChangesValidationError{
					field:  "PortfolioChange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPortfolioChange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioChangesValidationError{
				field:  "PortfolioChange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPortfolioGrowthAbsolute()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioChangesValidationError{
					field:  "PortfolioGrowthAbsolute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioChangesValidationError{
					field:  "PortfolioGrowthAbsolute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPortfolioGrowthAbsolute()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioChangesValidationError{
				field:  "PortfolioGrowthAbsolute",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PortfolioGrowthPercentage

	if len(errors) > 0 {
		return PortfolioChangesMultiError(errors)
	}

	return nil
}

// PortfolioChangesMultiError is an error wrapping multiple validation errors
// returned by PortfolioChanges.ValidateAll() if the designated constraints
// aren't met.
type PortfolioChangesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioChangesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioChangesMultiError) AllErrors() []error { return m }

// PortfolioChangesValidationError is the validation error returned by
// PortfolioChanges.Validate if the designated constraints aren't met.
type PortfolioChangesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioChangesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioChangesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioChangesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioChangesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioChangesValidationError) ErrorName() string { return "PortfolioChangesValidationError" }

// Error satisfies the builtin error interface
func (e PortfolioChangesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioChanges.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioChangesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioChangesValidationError{}

// Validate checks the field values on GetMFSchemeAnalyticsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMFSchemeAnalyticsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMFSchemeAnalyticsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMFSchemeAnalyticsRequestMultiError, or nil if none found.
func (m *GetMFSchemeAnalyticsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMFSchemeAnalyticsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetMFSchemeAnalyticsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetMFSchemeAnalyticsRequestMultiError(errors)
	}

	return nil
}

// GetMFSchemeAnalyticsRequestMultiError is an error wrapping multiple
// validation errors returned by GetMFSchemeAnalyticsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetMFSchemeAnalyticsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMFSchemeAnalyticsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMFSchemeAnalyticsRequestMultiError) AllErrors() []error { return m }

// GetMFSchemeAnalyticsRequestValidationError is the validation error returned
// by GetMFSchemeAnalyticsRequest.Validate if the designated constraints
// aren't met.
type GetMFSchemeAnalyticsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMFSchemeAnalyticsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMFSchemeAnalyticsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMFSchemeAnalyticsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMFSchemeAnalyticsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMFSchemeAnalyticsRequestValidationError) ErrorName() string {
	return "GetMFSchemeAnalyticsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMFSchemeAnalyticsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMFSchemeAnalyticsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMFSchemeAnalyticsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMFSchemeAnalyticsRequestValidationError{}

// Validate checks the field values on GetMFSchemeAnalyticsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMFSchemeAnalyticsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMFSchemeAnalyticsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMFSchemeAnalyticsResponseMultiError, or nil if none found.
func (m *GetMFSchemeAnalyticsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMFSchemeAnalyticsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMFSchemeAnalyticsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMFSchemeAnalyticsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMFSchemeAnalyticsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSchemes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetMFSchemeAnalyticsResponseValidationError{
						field:  fmt.Sprintf("Schemes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetMFSchemeAnalyticsResponseValidationError{
						field:  fmt.Sprintf("Schemes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetMFSchemeAnalyticsResponseValidationError{
					field:  fmt.Sprintf("Schemes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPortfolio()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMFSchemeAnalyticsResponseValidationError{
					field:  "Portfolio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMFSchemeAnalyticsResponseValidationError{
					field:  "Portfolio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPortfolio()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMFSchemeAnalyticsResponseValidationError{
				field:  "Portfolio",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMFSchemeAnalyticsResponseMultiError(errors)
	}

	return nil
}

// GetMFSchemeAnalyticsResponseMultiError is an error wrapping multiple
// validation errors returned by GetMFSchemeAnalyticsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetMFSchemeAnalyticsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMFSchemeAnalyticsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMFSchemeAnalyticsResponseMultiError) AllErrors() []error { return m }

// GetMFSchemeAnalyticsResponseValidationError is the validation error returned
// by GetMFSchemeAnalyticsResponse.Validate if the designated constraints
// aren't met.
type GetMFSchemeAnalyticsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMFSchemeAnalyticsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMFSchemeAnalyticsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMFSchemeAnalyticsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMFSchemeAnalyticsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMFSchemeAnalyticsResponseValidationError) ErrorName() string {
	return "GetMFSchemeAnalyticsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMFSchemeAnalyticsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMFSchemeAnalyticsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMFSchemeAnalyticsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMFSchemeAnalyticsResponseValidationError{}

// Validate checks the field values on EnrichedMfSchemeAnalytics with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnrichedMfSchemeAnalytics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnrichedMfSchemeAnalytics with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnrichedMfSchemeAnalyticsMultiError, or nil if none found.
func (m *EnrichedMfSchemeAnalytics) ValidateAll() error {
	return m.validate(true)
}

func (m *EnrichedMfSchemeAnalytics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAnalytics()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnrichedMfSchemeAnalyticsValidationError{
					field:  "Analytics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnrichedMfSchemeAnalyticsValidationError{
					field:  "Analytics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalytics()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnrichedMfSchemeAnalyticsValidationError{
				field:  "Analytics",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnrichedMfSchemeAnalyticsMultiError(errors)
	}

	return nil
}

// EnrichedMfSchemeAnalyticsMultiError is an error wrapping multiple validation
// errors returned by EnrichedMfSchemeAnalytics.ValidateAll() if the
// designated constraints aren't met.
type EnrichedMfSchemeAnalyticsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnrichedMfSchemeAnalyticsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnrichedMfSchemeAnalyticsMultiError) AllErrors() []error { return m }

// EnrichedMfSchemeAnalyticsValidationError is the validation error returned by
// EnrichedMfSchemeAnalytics.Validate if the designated constraints aren't met.
type EnrichedMfSchemeAnalyticsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnrichedMfSchemeAnalyticsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnrichedMfSchemeAnalyticsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnrichedMfSchemeAnalyticsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnrichedMfSchemeAnalyticsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnrichedMfSchemeAnalyticsValidationError) ErrorName() string {
	return "EnrichedMfSchemeAnalyticsValidationError"
}

// Error satisfies the builtin error interface
func (e EnrichedMfSchemeAnalyticsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnrichedMfSchemeAnalytics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnrichedMfSchemeAnalyticsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnrichedMfSchemeAnalyticsValidationError{}

// Validate checks the field values on EnrichedMFPortfolioAnalytics with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnrichedMFPortfolioAnalytics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnrichedMFPortfolioAnalytics with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnrichedMFPortfolioAnalyticsMultiError, or nil if none found.
func (m *EnrichedMFPortfolioAnalytics) ValidateAll() error {
	return m.validate(true)
}

func (m *EnrichedMFPortfolioAnalytics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPortfolio()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnrichedMFPortfolioAnalyticsValidationError{
					field:  "Portfolio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnrichedMFPortfolioAnalyticsValidationError{
					field:  "Portfolio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPortfolio()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnrichedMFPortfolioAnalyticsValidationError{
				field:  "Portfolio",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnrichedMFPortfolioAnalyticsMultiError(errors)
	}

	return nil
}

// EnrichedMFPortfolioAnalyticsMultiError is an error wrapping multiple
// validation errors returned by EnrichedMFPortfolioAnalytics.ValidateAll() if
// the designated constraints aren't met.
type EnrichedMFPortfolioAnalyticsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnrichedMFPortfolioAnalyticsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnrichedMFPortfolioAnalyticsMultiError) AllErrors() []error { return m }

// EnrichedMFPortfolioAnalyticsValidationError is the validation error returned
// by EnrichedMFPortfolioAnalytics.Validate if the designated constraints
// aren't met.
type EnrichedMFPortfolioAnalyticsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnrichedMFPortfolioAnalyticsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnrichedMFPortfolioAnalyticsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnrichedMFPortfolioAnalyticsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnrichedMFPortfolioAnalyticsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnrichedMFPortfolioAnalyticsValidationError) ErrorName() string {
	return "EnrichedMFPortfolioAnalyticsValidationError"
}

// Error satisfies the builtin error interface
func (e EnrichedMFPortfolioAnalyticsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnrichedMFPortfolioAnalytics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnrichedMFPortfolioAnalyticsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnrichedMFPortfolioAnalyticsValidationError{}

// Validate checks the field values on DeleteAllMfPortfolioHistoryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteAllMfPortfolioHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAllMfPortfolioHistoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteAllMfPortfolioHistoryRequestMultiError, or nil if none found.
func (m *DeleteAllMfPortfolioHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAllMfPortfolioHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := DeleteAllMfPortfolioHistoryRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteAllMfPortfolioHistoryRequestMultiError(errors)
	}

	return nil
}

// DeleteAllMfPortfolioHistoryRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeleteAllMfPortfolioHistoryRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteAllMfPortfolioHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAllMfPortfolioHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAllMfPortfolioHistoryRequestMultiError) AllErrors() []error { return m }

// DeleteAllMfPortfolioHistoryRequestValidationError is the validation error
// returned by DeleteAllMfPortfolioHistoryRequest.Validate if the designated
// constraints aren't met.
type DeleteAllMfPortfolioHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAllMfPortfolioHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAllMfPortfolioHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAllMfPortfolioHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAllMfPortfolioHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAllMfPortfolioHistoryRequestValidationError) ErrorName() string {
	return "DeleteAllMfPortfolioHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAllMfPortfolioHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAllMfPortfolioHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAllMfPortfolioHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAllMfPortfolioHistoryRequestValidationError{}

// Validate checks the field values on DeleteAllMfPortfolioHistoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteAllMfPortfolioHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAllMfPortfolioHistoryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteAllMfPortfolioHistoryResponseMultiError, or nil if none found.
func (m *DeleteAllMfPortfolioHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAllMfPortfolioHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteAllMfPortfolioHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteAllMfPortfolioHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteAllMfPortfolioHistoryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteAllMfPortfolioHistoryResponseMultiError(errors)
	}

	return nil
}

// DeleteAllMfPortfolioHistoryResponseMultiError is an error wrapping multiple
// validation errors returned by
// DeleteAllMfPortfolioHistoryResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteAllMfPortfolioHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAllMfPortfolioHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAllMfPortfolioHistoryResponseMultiError) AllErrors() []error { return m }

// DeleteAllMfPortfolioHistoryResponseValidationError is the validation error
// returned by DeleteAllMfPortfolioHistoryResponse.Validate if the designated
// constraints aren't met.
type DeleteAllMfPortfolioHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAllMfPortfolioHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAllMfPortfolioHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAllMfPortfolioHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAllMfPortfolioHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAllMfPortfolioHistoryResponseValidationError) ErrorName() string {
	return "DeleteAllMfPortfolioHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAllMfPortfolioHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAllMfPortfolioHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAllMfPortfolioHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAllMfPortfolioHistoryResponseValidationError{}
