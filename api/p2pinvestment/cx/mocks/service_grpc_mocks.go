// Code generated by MockGen. DO NOT EDIT.
// Source: api/./p2pinvestment/cx/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	cx "github.com/epifi/gamma/api/p2pinvestment/cx"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCxClient is a mock of CxClient interface.
type MockCxClient struct {
	ctrl     *gomock.Controller
	recorder *MockCxClientMockRecorder
}

// MockCxClientMockRecorder is the mock recorder for MockCxClient.
type MockCxClientMockRecorder struct {
	mock *MockCxClient
}

// NewMockCxClient creates a new mock instance.
func NewMockCxClient(ctrl *gomock.Controller) *MockCxClient {
	mock := &MockCxClient{ctrl: ctrl}
	mock.recorder = &MockCxClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCxClient) EXPECT() *MockCxClientMockRecorder {
	return m.recorder
}

// GetInvestmentSummary mocks base method.
func (m *MockCxClient) GetInvestmentSummary(ctx context.Context, in *cx.GetInvestmentSummaryRequest, opts ...grpc.CallOption) (*cx.GetInvestmentSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInvestmentSummary", varargs...)
	ret0, _ := ret[0].(*cx.GetInvestmentSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentSummary indicates an expected call of GetInvestmentSummary.
func (mr *MockCxClientMockRecorder) GetInvestmentSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentSummary", reflect.TypeOf((*MockCxClient)(nil).GetInvestmentSummary), varargs...)
}

// GetInvestor mocks base method.
func (m *MockCxClient) GetInvestor(ctx context.Context, in *cx.GetInvestorRequest, opts ...grpc.CallOption) (*cx.GetInvestorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInvestor", varargs...)
	ret0, _ := ret[0].(*cx.GetInvestorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestor indicates an expected call of GetInvestor.
func (mr *MockCxClientMockRecorder) GetInvestor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestor", reflect.TypeOf((*MockCxClient)(nil).GetInvestor), varargs...)
}

// MockCxServer is a mock of CxServer interface.
type MockCxServer struct {
	ctrl     *gomock.Controller
	recorder *MockCxServerMockRecorder
}

// MockCxServerMockRecorder is the mock recorder for MockCxServer.
type MockCxServerMockRecorder struct {
	mock *MockCxServer
}

// NewMockCxServer creates a new mock instance.
func NewMockCxServer(ctrl *gomock.Controller) *MockCxServer {
	mock := &MockCxServer{ctrl: ctrl}
	mock.recorder = &MockCxServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCxServer) EXPECT() *MockCxServerMockRecorder {
	return m.recorder
}

// GetInvestmentSummary mocks base method.
func (m *MockCxServer) GetInvestmentSummary(arg0 context.Context, arg1 *cx.GetInvestmentSummaryRequest) (*cx.GetInvestmentSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvestmentSummary", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetInvestmentSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentSummary indicates an expected call of GetInvestmentSummary.
func (mr *MockCxServerMockRecorder) GetInvestmentSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentSummary", reflect.TypeOf((*MockCxServer)(nil).GetInvestmentSummary), arg0, arg1)
}

// GetInvestor mocks base method.
func (m *MockCxServer) GetInvestor(arg0 context.Context, arg1 *cx.GetInvestorRequest) (*cx.GetInvestorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvestor", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetInvestorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestor indicates an expected call of GetInvestor.
func (mr *MockCxServerMockRecorder) GetInvestor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestor", reflect.TypeOf((*MockCxServer)(nil).GetInvestor), arg0, arg1)
}

// MockUnsafeCxServer is a mock of UnsafeCxServer interface.
type MockUnsafeCxServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCxServerMockRecorder
}

// MockUnsafeCxServerMockRecorder is the mock recorder for MockUnsafeCxServer.
type MockUnsafeCxServerMockRecorder struct {
	mock *MockUnsafeCxServer
}

// NewMockUnsafeCxServer creates a new mock instance.
func NewMockUnsafeCxServer(ctrl *gomock.Controller) *MockUnsafeCxServer {
	mock := &MockUnsafeCxServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCxServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCxServer) EXPECT() *MockUnsafeCxServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCxServer mocks base method.
func (m *MockUnsafeCxServer) mustEmbedUnimplementedCxServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCxServer")
}

// mustEmbedUnimplementedCxServer indicates an expected call of mustEmbedUnimplementedCxServer.
func (mr *MockUnsafeCxServerMockRecorder) mustEmbedUnimplementedCxServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCxServer", reflect.TypeOf((*MockUnsafeCxServer)(nil).mustEmbedUnimplementedCxServer))
}
