syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

// https://epifi.slack.com/files/U0410M4D3TM/F078L4C4BB5/nomination_update_api_version_1.0.0.docx
message UpdateNomineeRequest {

  // ADD / ENQUIRY
string req_type = 1 [json_name = "reqType"];
  // Unique identifier (account numberwith sol id)
string foracid = 2 [json_name = "foracid"];
  // Unique request id for each request
string request_id = 3 [json_name = "requestId"];
  // Unique request id for each request
string service_req_id = 4 [json_name = "serviceReqId"];
  // ekyc rrn number received from aadhaar ekyc
string ekyc_crrn = 5 [json_name = "EKYCrrn"];
  // Nominee name
string nominee_name = 6 [json_name = "nomineeName"];
  // Nominee registration number
string nominee_reg_no = 7 [json_name = "nomineeRegno"];
  // Nominee relation to customer
string nominee_rel_type = 8 [json_name = "nomineeRelType"];
  // Y/N (whetherNominee is a Minor)
string nominee_minor_flag = 9 [json_name = "nomineeMinorFlag"];
  // Nominee date of birth
string nominee_dob = 10 [json_name = "nomineeDob"];
  // Nominee Addr line 1
string nominee_addr_line1 = 11 [json_name = "nomineeAddrLine1"];
  // Nominee Addr line 2
string nominee_addr_line2 = 12 [json_name = "nomineeAddrLine2"];
  // Nominee Addr line 3
string nominee_addr_line3 = 13 [json_name = "nomineeAddrLine3"];
  // Nominee City Code
string nominee_city = 14 [json_name = "nomineeCity"];
  // Nominee state code
string nominee_state = 15 [json_name = "nomineeState"];
  // Nominee country code
string nominee_country = 16 [json_name = "nomineeCountry"];
  // Nominee Pin Code
string nominee_postal_code = 17 [json_name = "nomineePostalCode"];
  // if Nominee is Minor
string guardian_code = 18 [json_name = "guardianCode"];
  // if Nominee is Minor
string guardian_name = 19 [json_name = "guardianName"];
  // to identify which channel
string channel = 20 [json_name = "channel"];
string reserve_free_text1 = 21 [json_name = "reserveFreetext1"];
string reserve_free_text2 = 22 [json_name = "reserveFreetext2"];
string reserve_free_text3 = 23 [json_name = "reserveFreetext3"];
string reserve_free_text4 = 24 [json_name = "reserveFreetext4"];
string reserve_free_text5 = 25 [json_name = "reserveFreetext5"];
string reserve_free_text6 = 26 [json_name = "reserveFreetext6"];
string reserve_free_text7 = 27 [json_name = "reserveFreetext7"];
string reserve_free_textt8 = 28 [json_name = "reserveFreetext8"];
string reserve_free_text9 = 29 [json_name = "reserveFreetext9"];
string reserve_free_text10 = 30 [json_name = "reserveFreetext10"];
}

message UpdateNomineeResponse{
// Unique request id for each request
string request_id=1 [json_name = "requestId"];
// Status code indicating success, failure or error
string status=2 [json_name = "status"];
// Status message
string message=3 [json_name = "message"];
// SUCCESS/FAILURE
string cbs_response=4 [json_name = "cbsResponse"];
// Response message from CBS
string cbs_status=5 [json_name = "cbsStatus"];  //"cbsResponse\":\"\",\"cbsStatus\":\"\",\"message\":\"XXXX\",\"requestId\":\"\",\"status\":\
}

