// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/federal/generateotp.proto

package federal

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GenerateOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOTPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOTPRequestMultiError, or nil if none found.
func (m *GenerateOTPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOTPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for ServiceAccessId

	// no validation rules for ServiceAccessCode

	// no validation rules for RequestId

	// no validation rules for DeviceId

	// no validation rules for UserProfileId

	// no validation rules for DeviceToken

	// no validation rules for AccountNumber

	// no validation rules for Email

	// no validation rules for MobileNumber

	if len(errors) > 0 {
		return GenerateOTPRequestMultiError(errors)
	}

	return nil
}

// GenerateOTPRequestMultiError is an error wrapping multiple validation errors
// returned by GenerateOTPRequest.ValidateAll() if the designated constraints
// aren't met.
type GenerateOTPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOTPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOTPRequestMultiError) AllErrors() []error { return m }

// GenerateOTPRequestValidationError is the validation error returned by
// GenerateOTPRequest.Validate if the designated constraints aren't met.
type GenerateOTPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOTPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOTPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOTPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOTPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOTPRequestValidationError) ErrorName() string {
	return "GenerateOTPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOTPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOTPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOTPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOTPRequestValidationError{}

// Validate checks the field values on GenerateOTPResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOTPResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOTPResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOTPResponseMultiError, or nil if none found.
func (m *GenerateOTPResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOTPResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for RequestId

	// no validation rules for DeviceToken

	// no validation rules for TransTimeStamp

	// no validation rules for Response

	// no validation rules for Reason

	if len(errors) > 0 {
		return GenerateOTPResponseMultiError(errors)
	}

	return nil
}

// GenerateOTPResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateOTPResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateOTPResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOTPResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOTPResponseMultiError) AllErrors() []error { return m }

// GenerateOTPResponseValidationError is the validation error returned by
// GenerateOTPResponse.Validate if the designated constraints aren't met.
type GenerateOTPResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOTPResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOTPResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOTPResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOTPResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOTPResponseValidationError) ErrorName() string {
	return "GenerateOTPResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOTPResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOTPResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOTPResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOTPResponseValidationError{}
