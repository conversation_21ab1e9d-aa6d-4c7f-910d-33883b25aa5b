// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/m2p/partnersdk.proto

package partnersdk

import (
	creditcard "github.com/epifi/gamma/api/vendors/m2p/lending/creditcard"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSessionParamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Device ID of the client that has initiated the request (mandatory)
	DeviceId string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// Determines if the client is refreshing the session params
	// (or) if this is fresh session registration
	//
	// A session parameter is active for 30 days from the time
	// it is created and client is expected to refresh the
	// parameters post this duration
	//
	// Client can make use of this parameter to refresh the parameters
	Refresh       bool                                   `protobuf:"varint,2,opt,name=refresh,proto3" json:"refresh,omitempty"`
	PartnerBank   string                                 `protobuf:"bytes,3,opt,name=partner_bank,json=partnerBank,proto3" json:"partner_bank,omitempty"`
	SessionParams *GetSessionParamsRequest_SessionParams `protobuf:"bytes,4,opt,name=session_params,json=session,proto3" json:"session_params,omitempty"`
	// App platform
	Platform string `protobuf:"bytes,5,opt,name=platform,proto3" json:"platform,omitempty"`
}

func (x *GetSessionParamsRequest) Reset() {
	*x = GetSessionParamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_m2p_partnersdk_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionParamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionParamsRequest) ProtoMessage() {}

func (x *GetSessionParamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_m2p_partnersdk_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionParamsRequest.ProtoReflect.Descriptor instead.
func (*GetSessionParamsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_m2p_partnersdk_proto_rawDescGZIP(), []int{0}
}

func (x *GetSessionParamsRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetSessionParamsRequest) GetRefresh() bool {
	if x != nil {
		return x.Refresh
	}
	return false
}

func (x *GetSessionParamsRequest) GetPartnerBank() string {
	if x != nil {
		return x.PartnerBank
	}
	return ""
}

func (x *GetSessionParamsRequest) GetSessionParams() *GetSessionParamsRequest_SessionParams {
	if x != nil {
		return x.SessionParams
	}
	return nil
}

func (x *GetSessionParamsRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type GetSessionParamsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result     string                 `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Exception  *creditcard.Exception  `protobuf:"bytes,2,opt,name=exception,proto3" json:"exception,omitempty"`
	Pagination *creditcard.Pagination `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetSessionParamsResponse) Reset() {
	*x = GetSessionParamsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_m2p_partnersdk_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionParamsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionParamsResponse) ProtoMessage() {}

func (x *GetSessionParamsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_m2p_partnersdk_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionParamsResponse.ProtoReflect.Descriptor instead.
func (*GetSessionParamsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_m2p_partnersdk_proto_rawDescGZIP(), []int{1}
}

func (x *GetSessionParamsResponse) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *GetSessionParamsResponse) GetException() *creditcard.Exception {
	if x != nil {
		return x.Exception
	}
	return nil
}

func (x *GetSessionParamsResponse) GetPagination() *creditcard.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// List of session parameters that helps the partner bank
// to authenticate the request
// Used to created `credblock` string
type GetSessionParamsRequest_SessionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier of the partner bank's key
	// that is used in the process of generating
	// the session attributes
	KeyId string `protobuf:"bytes,1,opt,name=key_id,json=encryptionKeyId,proto3" json:"key_id,omitempty"`
	// Initial session keys to register the client app
	// with the partner bank
	// Base64 encoded
	EncryptedKeys string `protobuf:"bytes,2,opt,name=encrypted_keys,json=keys,proto3" json:"encrypted_keys,omitempty"`
	// HMAC of keys
	// If HMAC verification fails, the request will be rejected
	// Base64 encoded
	Hmac string `protobuf:"bytes,3,opt,name=hmac,proto3" json:"hmac,omitempty"`
	// Ephemeral public key that is used in
	// Elliptic curve's Diffie hellman(ECDH) key agreement protocol
	// to generate the keys to encrypt the payload i.e., keys
	// Base64 encoded
	EcdhPk string `protobuf:"bytes,4,opt,name=ecdh_pk,json=publicKey,proto3" json:"ecdh_pk,omitempty"`
}

func (x *GetSessionParamsRequest_SessionParams) Reset() {
	*x = GetSessionParamsRequest_SessionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_m2p_partnersdk_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionParamsRequest_SessionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionParamsRequest_SessionParams) ProtoMessage() {}

func (x *GetSessionParamsRequest_SessionParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_m2p_partnersdk_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionParamsRequest_SessionParams.ProtoReflect.Descriptor instead.
func (*GetSessionParamsRequest_SessionParams) Descriptor() ([]byte, []int) {
	return file_api_vendors_m2p_partnersdk_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetSessionParamsRequest_SessionParams) GetKeyId() string {
	if x != nil {
		return x.KeyId
	}
	return ""
}

func (x *GetSessionParamsRequest_SessionParams) GetEncryptedKeys() string {
	if x != nil {
		return x.EncryptedKeys
	}
	return ""
}

func (x *GetSessionParamsRequest_SessionParams) GetHmac() string {
	if x != nil {
		return x.Hmac
	}
	return ""
}

func (x *GetSessionParamsRequest_SessionParams) GetEcdhPk() string {
	if x != nil {
		return x.EcdhPk
	}
	return ""
}

type GetSessionParamsResponse_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// HMAC key that is to be used by the app
	// to generate HMAC for all the request payloads
	// between app and SDK
	// Base64 encoded
	K0 string `protobuf:"bytes,1,opt,name=k0,proto3" json:"k0,omitempty"`
	// ECDH's Public key to be registered with the SDK
	// Encrypted value is to be passed to SDK as it is
	// Base64 encoded
	EncPublicKey string `protobuf:"bytes,2,opt,name=encPublicKey,proto3" json:"encPublicKey,omitempty"`
	// Signature of the payload
	// Signature is to be passed to SDK as it is
	// Base64 encoded
	Signature string `protobuf:"bytes,3,opt,name=signature,proto3" json:"signature,omitempty"`
	// There are multiple keys using which a partner bank
	// can sign a payload. Key ID identified the key
	// using which a payload has been signed
	// Signature's Key ID is to be passed to SDK as it is
	// Base64 encoded
	SignatureKeyId string `protobuf:"bytes,4,opt,name=signatureKeyId,proto3" json:"signatureKeyId,omitempty"`
}

func (x *GetSessionParamsResponse_Result) Reset() {
	*x = GetSessionParamsResponse_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_m2p_partnersdk_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionParamsResponse_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionParamsResponse_Result) ProtoMessage() {}

func (x *GetSessionParamsResponse_Result) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_m2p_partnersdk_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionParamsResponse_Result.ProtoReflect.Descriptor instead.
func (*GetSessionParamsResponse_Result) Descriptor() ([]byte, []int) {
	return file_api_vendors_m2p_partnersdk_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetSessionParamsResponse_Result) GetK0() string {
	if x != nil {
		return x.K0
	}
	return ""
}

func (x *GetSessionParamsResponse_Result) GetEncPublicKey() string {
	if x != nil {
		return x.EncPublicKey
	}
	return ""
}

func (x *GetSessionParamsResponse_Result) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *GetSessionParamsResponse_Result) GetSignatureKeyId() string {
	if x != nil {
		return x.SignatureKeyId
	}
	return ""
}

var File_api_vendors_m2p_partnersdk_proto protoreflect.FileDescriptor

var file_api_vendors_m2p_partnersdk_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x32,
	0x70, 0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x16, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x32, 0x70, 0x2e,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x32, 0x70, 0x2f, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xef, 0x02, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x5e, 0x0a, 0x0e, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x32,
	0x70, 0x2e, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x52, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x1a, 0x7e, 0x0a, 0x0d, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0e, 0x65, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6d, 0x61, 0x63, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6d, 0x61, 0x63, 0x12, 0x1a, 0x0a, 0x07, 0x65,
	0x63, 0x64, 0x68, 0x5f, 0x70, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x22, 0xb6, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x09,
	0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x32, 0x70, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x09, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x32, 0x70, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x82, 0x01, 0x0a, 0x06,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6b, 0x30, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x6b, 0x30, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x63, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e,
	0x63, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x4b, 0x65, 0x79, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4b, 0x65, 0x79, 0x49, 0x64,
	0x42, 0x66, 0x0a, 0x31, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x32, 0x70, 0x2e, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x73, 0x64, 0x6b, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x32, 0x70, 0x2f, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x64, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_m2p_partnersdk_proto_rawDescOnce sync.Once
	file_api_vendors_m2p_partnersdk_proto_rawDescData = file_api_vendors_m2p_partnersdk_proto_rawDesc
)

func file_api_vendors_m2p_partnersdk_proto_rawDescGZIP() []byte {
	file_api_vendors_m2p_partnersdk_proto_rawDescOnce.Do(func() {
		file_api_vendors_m2p_partnersdk_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_m2p_partnersdk_proto_rawDescData)
	})
	return file_api_vendors_m2p_partnersdk_proto_rawDescData
}

var file_api_vendors_m2p_partnersdk_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_vendors_m2p_partnersdk_proto_goTypes = []interface{}{
	(*GetSessionParamsRequest)(nil),               // 0: vendors.m2p.partnersdk.GetSessionParamsRequest
	(*GetSessionParamsResponse)(nil),              // 1: vendors.m2p.partnersdk.GetSessionParamsResponse
	(*GetSessionParamsRequest_SessionParams)(nil), // 2: vendors.m2p.partnersdk.GetSessionParamsRequest.SessionParams
	(*GetSessionParamsResponse_Result)(nil),       // 3: vendors.m2p.partnersdk.GetSessionParamsResponse.Result
	(*creditcard.Exception)(nil),                  // 4: vendors.m2p.lending.Exception
	(*creditcard.Pagination)(nil),                 // 5: vendors.m2p.lending.Pagination
}
var file_api_vendors_m2p_partnersdk_proto_depIdxs = []int32{
	2, // 0: vendors.m2p.partnersdk.GetSessionParamsRequest.session_params:type_name -> vendors.m2p.partnersdk.GetSessionParamsRequest.SessionParams
	4, // 1: vendors.m2p.partnersdk.GetSessionParamsResponse.exception:type_name -> vendors.m2p.lending.Exception
	5, // 2: vendors.m2p.partnersdk.GetSessionParamsResponse.pagination:type_name -> vendors.m2p.lending.Pagination
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_vendors_m2p_partnersdk_proto_init() }
func file_api_vendors_m2p_partnersdk_proto_init() {
	if File_api_vendors_m2p_partnersdk_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_m2p_partnersdk_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionParamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_m2p_partnersdk_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionParamsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_m2p_partnersdk_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionParamsRequest_SessionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_m2p_partnersdk_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionParamsResponse_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_m2p_partnersdk_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_m2p_partnersdk_proto_goTypes,
		DependencyIndexes: file_api_vendors_m2p_partnersdk_proto_depIdxs,
		MessageInfos:      file_api_vendors_m2p_partnersdk_proto_msgTypes,
	}.Build()
	File_api_vendors_m2p_partnersdk_proto = out.File
	file_api_vendors_m2p_partnersdk_proto_rawDesc = nil
	file_api_vendors_m2p_partnersdk_proto_goTypes = nil
	file_api_vendors_m2p_partnersdk_proto_depIdxs = nil
}
