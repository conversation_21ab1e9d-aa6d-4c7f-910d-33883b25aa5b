// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/fiftyfin/link_pan.proto

package fiftyfin

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LinkPanRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LinkPanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkPanRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LinkPanRequestMultiError,
// or nil if none found.
func (m *LinkPanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkPanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for IdType

	// no validation rules for IdNumber

	// no validation rules for PanName

	// no validation rules for Dob

	if len(errors) > 0 {
		return LinkPanRequestMultiError(errors)
	}

	return nil
}

// LinkPanRequestMultiError is an error wrapping multiple validation errors
// returned by LinkPanRequest.ValidateAll() if the designated constraints
// aren't met.
type LinkPanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkPanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkPanRequestMultiError) AllErrors() []error { return m }

// LinkPanRequestValidationError is the validation error returned by
// LinkPanRequest.Validate if the designated constraints aren't met.
type LinkPanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkPanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkPanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkPanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkPanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkPanRequestValidationError) ErrorName() string { return "LinkPanRequestValidationError" }

// Error satisfies the builtin error interface
func (e LinkPanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkPanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkPanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkPanRequestValidationError{}

// Validate checks the field values on LinkPanResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LinkPanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkPanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkPanResponseMultiError, or nil if none found.
func (m *LinkPanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkPanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Detail

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LinkPanResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LinkPanResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LinkPanResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LinkPanResponseMultiError(errors)
	}

	return nil
}

// LinkPanResponseMultiError is an error wrapping multiple validation errors
// returned by LinkPanResponse.ValidateAll() if the designated constraints
// aren't met.
type LinkPanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkPanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkPanResponseMultiError) AllErrors() []error { return m }

// LinkPanResponseValidationError is the validation error returned by
// LinkPanResponse.Validate if the designated constraints aren't met.
type LinkPanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkPanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkPanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkPanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkPanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkPanResponseValidationError) ErrorName() string { return "LinkPanResponseValidationError" }

// Error satisfies the builtin error interface
func (e LinkPanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkPanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkPanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkPanResponseValidationError{}

// Validate checks the field values on LinkPanV2Response with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LinkPanV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkPanV2Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkPanV2ResponseMultiError, or nil if none found.
func (m *LinkPanV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkPanV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Detail

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LinkPanV2ResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LinkPanV2ResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LinkPanV2ResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LinkPanV2ResponseMultiError(errors)
	}

	return nil
}

// LinkPanV2ResponseMultiError is an error wrapping multiple validation errors
// returned by LinkPanV2Response.ValidateAll() if the designated constraints
// aren't met.
type LinkPanV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkPanV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkPanV2ResponseMultiError) AllErrors() []error { return m }

// LinkPanV2ResponseValidationError is the validation error returned by
// LinkPanV2Response.Validate if the designated constraints aren't met.
type LinkPanV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkPanV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkPanV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkPanV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkPanV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkPanV2ResponseValidationError) ErrorName() string {
	return "LinkPanV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LinkPanV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkPanV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkPanV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkPanV2ResponseValidationError{}

// Validate checks the field values on LinkPanResponse_PanData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkPanResponse_PanData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkPanResponse_PanData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkPanResponse_PanDataMultiError, or nil if none found.
func (m *LinkPanResponse_PanData) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkPanResponse_PanData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for NameProvided

	// no validation rules for RegisteredName

	// no validation rules for FatherName

	// no validation rules for Valid

	// no validation rules for Message

	// no validation rules for NameMatchScore

	// no validation rules for NameMatchResult

	if len(errors) > 0 {
		return LinkPanResponse_PanDataMultiError(errors)
	}

	return nil
}

// LinkPanResponse_PanDataMultiError is an error wrapping multiple validation
// errors returned by LinkPanResponse_PanData.ValidateAll() if the designated
// constraints aren't met.
type LinkPanResponse_PanDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkPanResponse_PanDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkPanResponse_PanDataMultiError) AllErrors() []error { return m }

// LinkPanResponse_PanDataValidationError is the validation error returned by
// LinkPanResponse_PanData.Validate if the designated constraints aren't met.
type LinkPanResponse_PanDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkPanResponse_PanDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkPanResponse_PanDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkPanResponse_PanDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkPanResponse_PanDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkPanResponse_PanDataValidationError) ErrorName() string {
	return "LinkPanResponse_PanDataValidationError"
}

// Error satisfies the builtin error interface
func (e LinkPanResponse_PanDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkPanResponse_PanData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkPanResponse_PanDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkPanResponse_PanDataValidationError{}

// Validate checks the field values on LinkPanResponse_LinkData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkPanResponse_LinkData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkPanResponse_LinkData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkPanResponse_LinkDataMultiError, or nil if none found.
func (m *LinkPanResponse_LinkData) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkPanResponse_LinkData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for DocumentType

	// no validation rules for Document

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LinkPanResponse_LinkDataValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LinkPanResponse_LinkDataValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LinkPanResponse_LinkDataValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsActive

	if len(errors) > 0 {
		return LinkPanResponse_LinkDataMultiError(errors)
	}

	return nil
}

// LinkPanResponse_LinkDataMultiError is an error wrapping multiple validation
// errors returned by LinkPanResponse_LinkData.ValidateAll() if the designated
// constraints aren't met.
type LinkPanResponse_LinkDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkPanResponse_LinkDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkPanResponse_LinkDataMultiError) AllErrors() []error { return m }

// LinkPanResponse_LinkDataValidationError is the validation error returned by
// LinkPanResponse_LinkData.Validate if the designated constraints aren't met.
type LinkPanResponse_LinkDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkPanResponse_LinkDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkPanResponse_LinkDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkPanResponse_LinkDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkPanResponse_LinkDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkPanResponse_LinkDataValidationError) ErrorName() string {
	return "LinkPanResponse_LinkDataValidationError"
}

// Error satisfies the builtin error interface
func (e LinkPanResponse_LinkDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkPanResponse_LinkData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkPanResponse_LinkDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkPanResponse_LinkDataValidationError{}

// Validate checks the field values on LinkPanV2Response_PanDataV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkPanV2Response_PanDataV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkPanV2Response_PanDataV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkPanV2Response_PanDataV2MultiError, or nil if none found.
func (m *LinkPanV2Response_PanDataV2) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkPanV2Response_PanDataV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AadhaarSeedingStatus

	// no validation rules for NameAsPerPanMatch

	// no validation rules for Category

	// no validation rules for Status

	// no validation rules for DateOfBirthMatch

	// no validation rules for FullName

	// no validation rules for Dob

	if len(errors) > 0 {
		return LinkPanV2Response_PanDataV2MultiError(errors)
	}

	return nil
}

// LinkPanV2Response_PanDataV2MultiError is an error wrapping multiple
// validation errors returned by LinkPanV2Response_PanDataV2.ValidateAll() if
// the designated constraints aren't met.
type LinkPanV2Response_PanDataV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkPanV2Response_PanDataV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkPanV2Response_PanDataV2MultiError) AllErrors() []error { return m }

// LinkPanV2Response_PanDataV2ValidationError is the validation error returned
// by LinkPanV2Response_PanDataV2.Validate if the designated constraints
// aren't met.
type LinkPanV2Response_PanDataV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkPanV2Response_PanDataV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkPanV2Response_PanDataV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkPanV2Response_PanDataV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkPanV2Response_PanDataV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkPanV2Response_PanDataV2ValidationError) ErrorName() string {
	return "LinkPanV2Response_PanDataV2ValidationError"
}

// Error satisfies the builtin error interface
func (e LinkPanV2Response_PanDataV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkPanV2Response_PanDataV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkPanV2Response_PanDataV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkPanV2Response_PanDataV2ValidationError{}

// Validate checks the field values on LinkPanV2Response_LinkData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkPanV2Response_LinkData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkPanV2Response_LinkData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkPanV2Response_LinkDataMultiError, or nil if none found.
func (m *LinkPanV2Response_LinkData) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkPanV2Response_LinkData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for DocumentType

	// no validation rules for Document

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LinkPanV2Response_LinkDataValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LinkPanV2Response_LinkDataValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LinkPanV2Response_LinkDataValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsActive

	// no validation rules for Id

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return LinkPanV2Response_LinkDataMultiError(errors)
	}

	return nil
}

// LinkPanV2Response_LinkDataMultiError is an error wrapping multiple
// validation errors returned by LinkPanV2Response_LinkData.ValidateAll() if
// the designated constraints aren't met.
type LinkPanV2Response_LinkDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkPanV2Response_LinkDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkPanV2Response_LinkDataMultiError) AllErrors() []error { return m }

// LinkPanV2Response_LinkDataValidationError is the validation error returned
// by LinkPanV2Response_LinkData.Validate if the designated constraints aren't met.
type LinkPanV2Response_LinkDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkPanV2Response_LinkDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkPanV2Response_LinkDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkPanV2Response_LinkDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkPanV2Response_LinkDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkPanV2Response_LinkDataValidationError) ErrorName() string {
	return "LinkPanV2Response_LinkDataValidationError"
}

// Error satisfies the builtin error interface
func (e LinkPanV2Response_LinkDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkPanV2Response_LinkData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkPanV2Response_LinkDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkPanV2Response_LinkDataValidationError{}
