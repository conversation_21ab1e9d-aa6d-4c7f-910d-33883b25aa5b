syntax = "proto3";

package vendors.cibil;

import "api/vendors/cibil/common.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendors/cibil";
option java_package = "com.github.epifi.gamma.api.vendors.cibil";

message AtlasGetCustomerAssetsRequest {
  GetCustomerAssetsRequest get_customer_assets_request = 1 [json_name = "GetCustomerAssetsRequest"];
}

message GetCustomerAssetsRequest {
  string site_name = 1 [json_name = "SiteName"];
  string account_name = 2 [json_name = "AccountName"];
  string account_code = 3 [json_name = "AccountCode"];
  string client_key = 4 [json_name = "ClientKey"];
  string request_key = 5 [json_name = "RequestKey"];
  string partner_customer_id = 6 [json_name = "PartnerCustomerId"];
  string legal_copy_status = 7 [json_name = "LegalCopyStatus"];
}

message AtlasGetCustomerAssetsResponse {
  GetCustomerAssetsResponse get_customer_assets_response = 1 [json_name = "GetCustomerAssetsResponse"];
}

message GetCustomerAssetsResponse {
  string response_status = 1 [json_name = "ResponseStatus"];
  string response_key = 2 [json_name = "ResponseKey"];
  google.protobuf.Struct get_customer_assets_success = 3 [json_name = "GetCustomerAssetsSuccess"];
  AtlasError get_customer_assets_error = 4 [json_name = "GetCustomerAssetsError"];
}
