// Code generated by MockGen. DO NOT EDIT.
// Source: api/cx/data_collector/payment_instruments/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	payment_instruments "github.com/epifi/gamma/api/cx/data_collector/payment_instruments"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCustomerPIClient is a mock of CustomerPIClient interface.
type MockCustomerPIClient struct {
	ctrl     *gomock.Controller
	recorder *MockCustomerPIClientMockRecorder
}

// MockCustomerPIClientMockRecorder is the mock recorder for MockCustomerPIClient.
type MockCustomerPIClientMockRecorder struct {
	mock *MockCustomerPIClient
}

// NewMockCustomerPIClient creates a new mock instance.
func NewMockCustomerPIClient(ctrl *gomock.Controller) *MockCustomerPIClient {
	mock := &MockCustomerPIClient{ctrl: ctrl}
	mock.recorder = &MockCustomerPIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCustomerPIClient) EXPECT() *MockCustomerPIClientMockRecorder {
	return m.recorder
}

// DisableOrEnableVPA mocks base method.
func (m *MockCustomerPIClient) DisableOrEnableVPA(ctx context.Context, in *payment_instruments.DisableOrEnableVPARequest, opts ...grpc.CallOption) (*payment_instruments.DisableOrEnableVPAResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DisableOrEnableVPA", varargs...)
	ret0, _ := ret[0].(*payment_instruments.DisableOrEnableVPAResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableOrEnableVPA indicates an expected call of DisableOrEnableVPA.
func (mr *MockCustomerPIClientMockRecorder) DisableOrEnableVPA(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableOrEnableVPA", reflect.TypeOf((*MockCustomerPIClient)(nil).DisableOrEnableVPA), varargs...)
}

// GetUPIDetails mocks base method.
func (m *MockCustomerPIClient) GetUPIDetails(ctx context.Context, in *payment_instruments.GetUPIDetailsRequest, opts ...grpc.CallOption) (*payment_instruments.GetUPIDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUPIDetails", varargs...)
	ret0, _ := ret[0].(*payment_instruments.GetUPIDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUPIDetails indicates an expected call of GetUPIDetails.
func (mr *MockCustomerPIClientMockRecorder) GetUPIDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUPIDetails", reflect.TypeOf((*MockCustomerPIClient)(nil).GetUPIDetails), varargs...)
}

// GetUpiInfo mocks base method.
func (m *MockCustomerPIClient) GetUpiInfo(ctx context.Context, in *payment_instruments.GetUpiInfoRequest, opts ...grpc.CallOption) (*payment_instruments.GetUpiInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUpiInfo", varargs...)
	ret0, _ := ret[0].(*payment_instruments.GetUpiInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiInfo indicates an expected call of GetUpiInfo.
func (mr *MockCustomerPIClientMockRecorder) GetUpiInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiInfo", reflect.TypeOf((*MockCustomerPIClient)(nil).GetUpiInfo), varargs...)
}

// MockCustomerPIServer is a mock of CustomerPIServer interface.
type MockCustomerPIServer struct {
	ctrl     *gomock.Controller
	recorder *MockCustomerPIServerMockRecorder
}

// MockCustomerPIServerMockRecorder is the mock recorder for MockCustomerPIServer.
type MockCustomerPIServerMockRecorder struct {
	mock *MockCustomerPIServer
}

// NewMockCustomerPIServer creates a new mock instance.
func NewMockCustomerPIServer(ctrl *gomock.Controller) *MockCustomerPIServer {
	mock := &MockCustomerPIServer{ctrl: ctrl}
	mock.recorder = &MockCustomerPIServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCustomerPIServer) EXPECT() *MockCustomerPIServerMockRecorder {
	return m.recorder
}

// DisableOrEnableVPA mocks base method.
func (m *MockCustomerPIServer) DisableOrEnableVPA(arg0 context.Context, arg1 *payment_instruments.DisableOrEnableVPARequest) (*payment_instruments.DisableOrEnableVPAResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableOrEnableVPA", arg0, arg1)
	ret0, _ := ret[0].(*payment_instruments.DisableOrEnableVPAResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableOrEnableVPA indicates an expected call of DisableOrEnableVPA.
func (mr *MockCustomerPIServerMockRecorder) DisableOrEnableVPA(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableOrEnableVPA", reflect.TypeOf((*MockCustomerPIServer)(nil).DisableOrEnableVPA), arg0, arg1)
}

// GetUPIDetails mocks base method.
func (m *MockCustomerPIServer) GetUPIDetails(arg0 context.Context, arg1 *payment_instruments.GetUPIDetailsRequest) (*payment_instruments.GetUPIDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUPIDetails", arg0, arg1)
	ret0, _ := ret[0].(*payment_instruments.GetUPIDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUPIDetails indicates an expected call of GetUPIDetails.
func (mr *MockCustomerPIServerMockRecorder) GetUPIDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUPIDetails", reflect.TypeOf((*MockCustomerPIServer)(nil).GetUPIDetails), arg0, arg1)
}

// GetUpiInfo mocks base method.
func (m *MockCustomerPIServer) GetUpiInfo(arg0 context.Context, arg1 *payment_instruments.GetUpiInfoRequest) (*payment_instruments.GetUpiInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpiInfo", arg0, arg1)
	ret0, _ := ret[0].(*payment_instruments.GetUpiInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiInfo indicates an expected call of GetUpiInfo.
func (mr *MockCustomerPIServerMockRecorder) GetUpiInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiInfo", reflect.TypeOf((*MockCustomerPIServer)(nil).GetUpiInfo), arg0, arg1)
}

// MockUnsafeCustomerPIServer is a mock of UnsafeCustomerPIServer interface.
type MockUnsafeCustomerPIServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCustomerPIServerMockRecorder
}

// MockUnsafeCustomerPIServerMockRecorder is the mock recorder for MockUnsafeCustomerPIServer.
type MockUnsafeCustomerPIServerMockRecorder struct {
	mock *MockUnsafeCustomerPIServer
}

// NewMockUnsafeCustomerPIServer creates a new mock instance.
func NewMockUnsafeCustomerPIServer(ctrl *gomock.Controller) *MockUnsafeCustomerPIServer {
	mock := &MockUnsafeCustomerPIServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCustomerPIServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCustomerPIServer) EXPECT() *MockUnsafeCustomerPIServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCustomerPIServer mocks base method.
func (m *MockUnsafeCustomerPIServer) mustEmbedUnimplementedCustomerPIServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCustomerPIServer")
}

// mustEmbedUnimplementedCustomerPIServer indicates an expected call of mustEmbedUnimplementedCustomerPIServer.
func (mr *MockUnsafeCustomerPIServerMockRecorder) mustEmbedUnimplementedCustomerPIServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCustomerPIServer", reflect.TypeOf((*MockUnsafeCustomerPIServer)(nil).mustEmbedUnimplementedCustomerPIServer))
}
