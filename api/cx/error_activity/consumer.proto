syntax = "proto3";

package cx.error_activity;

import "api/event/rudder_event.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/cx/error_activity";
option java_package = "com.github.epifi.gamma.api.cx.error_activity";

service Consumer {
  // ProcessEvent RPC consumes rudder event as a part of [cx_activity] Kafka consumer group
  // Consumption will be done from Kafka topics having prefix [events]
  // this consumer will act as an entry point for any rudder events to be processed as part of Error Activity
  rpc ProcessEvent (event.RudderEvent) returns (ProcessEventResponse) {}
}

message ProcessEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
