// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/cx/ticket/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessFreshdeskTicketEventMethod      = "ProcessFreshdeskTicketEvent"
	ProcessTicketReconciliationEventMethod = "ProcessTicketReconciliationEvent"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &ProcessFreshdeskTicketEventRequest{}
var _ queue.ConsumerRequest = &ProcessTicketReconciliationEventRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessFreshdeskTicketEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessTicketReconciliationEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessFreshdeskTicketEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessFreshdeskTicketEventMethodToSubscriber(subscriber queue.Subscriber, srv TicketConsumerServer) {
	subscriber.RegisterService(&TicketConsumer_ServiceDesc, srv, ProcessFreshdeskTicketEventMethod)
}

// RegisterProcessTicketReconciliationEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessTicketReconciliationEventMethodToSubscriber(subscriber queue.Subscriber, srv TicketConsumerServer) {
	subscriber.RegisterService(&TicketConsumer_ServiceDesc, srv, ProcessTicketReconciliationEventMethod)
}
