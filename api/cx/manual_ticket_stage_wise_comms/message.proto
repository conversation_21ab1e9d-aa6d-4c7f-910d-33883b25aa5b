syntax = "proto3";

package cx.manual_ticket_stage_wise_comms;

import "api/cx/watson/watson_client.proto";

option go_package = "github.com/epifi/gamma/api/cx/manual_ticket_stage_wise_comms";
option java_package = "com.github.epifi.gamma.api.cx.manual_ticket_stage_wise_comms";

message ManualTicketStatusCommsConfig {
  // boolean value indicating if comms are currently enabled for this issue category id
  bool is_comms_enabled = 1;
  // time interval specifying the duration for triggering comms again on this particular status
  string comms_interval = 2;
  // The maximum number of comms which can be sent on this status
  int32 max_num_of_comms = 3;
  // list of comms to be sent for this status
  repeated watson.CommsDetail comms_details = 4;
  // initial delay after which the first comms will be sent for corresponding ticket status
  string comms_delay = 5;
}

message ManualTicketStageBasedCommsDetails {
  // ticket status to config mapping
  map<string, ManualTicketStatusCommsConfig> ticket_status_comms_config_map = 1;
  ManualTicketStatusCommsConfig sla_breach_comms_config = 2;
}
