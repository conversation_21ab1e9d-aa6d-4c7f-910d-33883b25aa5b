// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/chat/bot/workflow/faq.proto

package workflow

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Article cx service object to be returned to calling service
// not using vg defined article object here since cx service should return cx proto object
type Article struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the category
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// description of the article
	// data in this field will contain serialized deeplink if any of them is present in the text
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *Article) Reset() {
	*x = Article{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_bot_workflow_faq_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Article) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Article) ProtoMessage() {}

func (x *Article) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_bot_workflow_faq_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Article.ProtoReflect.Descriptor instead.
func (*Article) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_bot_workflow_faq_proto_rawDescGZIP(), []int{0}
}

func (x *Article) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Article) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// FaqData: data which has to be return to client
type FaqData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of article objects
	ArticleList []*Article `protobuf:"bytes,1,rep,name=article_list,json=articleList,proto3" json:"article_list,omitempty"`
}

func (x *FaqData) Reset() {
	*x = FaqData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_bot_workflow_faq_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqData) ProtoMessage() {}

func (x *FaqData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_bot_workflow_faq_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqData.ProtoReflect.Descriptor instead.
func (*FaqData) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_bot_workflow_faq_proto_rawDescGZIP(), []int{1}
}

func (x *FaqData) GetArticleList() []*Article {
	if x != nil {
		return x.ArticleList
	}
	return nil
}

// FaqParameters specifies list of article ids which has to be accepted
type FaqParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of article id
	ArticleIdList []int64 `protobuf:"varint,1,rep,packed,name=article_id_list,json=articleIdList,proto3" json:"article_id_list,omitempty"`
}

func (x *FaqParameters) Reset() {
	*x = FaqParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_bot_workflow_faq_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqParameters) ProtoMessage() {}

func (x *FaqParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_bot_workflow_faq_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqParameters.ProtoReflect.Descriptor instead.
func (*FaqParameters) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_bot_workflow_faq_proto_rawDescGZIP(), []int{2}
}

func (x *FaqParameters) GetArticleIdList() []int64 {
	if x != nil {
		return x.ArticleIdList
	}
	return nil
}

var File_api_cx_chat_bot_workflow_faq_proto protoreflect.FileDescriptor

var file_api_cx_chat_bot_workflow_faq_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f,
	0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x66, 0x61, 0x71, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f,
	0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x41, 0x0a, 0x07, 0x41, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4b, 0x0a,
	0x07, 0x46, 0x61, 0x71, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x0b, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x37, 0x0a, 0x0d, 0x46, 0x61,
	0x71, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_chat_bot_workflow_faq_proto_rawDescOnce sync.Once
	file_api_cx_chat_bot_workflow_faq_proto_rawDescData = file_api_cx_chat_bot_workflow_faq_proto_rawDesc
)

func file_api_cx_chat_bot_workflow_faq_proto_rawDescGZIP() []byte {
	file_api_cx_chat_bot_workflow_faq_proto_rawDescOnce.Do(func() {
		file_api_cx_chat_bot_workflow_faq_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_chat_bot_workflow_faq_proto_rawDescData)
	})
	return file_api_cx_chat_bot_workflow_faq_proto_rawDescData
}

var file_api_cx_chat_bot_workflow_faq_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_cx_chat_bot_workflow_faq_proto_goTypes = []interface{}{
	(*Article)(nil),       // 0: cx.chat.bot.workflow.Article
	(*FaqData)(nil),       // 1: cx.chat.bot.workflow.FaqData
	(*FaqParameters)(nil), // 2: cx.chat.bot.workflow.FaqParameters
}
var file_api_cx_chat_bot_workflow_faq_proto_depIdxs = []int32{
	0, // 0: cx.chat.bot.workflow.FaqData.article_list:type_name -> cx.chat.bot.workflow.Article
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_cx_chat_bot_workflow_faq_proto_init() }
func file_api_cx_chat_bot_workflow_faq_proto_init() {
	if File_api_cx_chat_bot_workflow_faq_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_cx_chat_bot_workflow_faq_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Article); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_chat_bot_workflow_faq_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_chat_bot_workflow_faq_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_chat_bot_workflow_faq_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_chat_bot_workflow_faq_proto_goTypes,
		DependencyIndexes: file_api_cx_chat_bot_workflow_faq_proto_depIdxs,
		MessageInfos:      file_api_cx_chat_bot_workflow_faq_proto_msgTypes,
	}.Build()
	File_api_cx_chat_bot_workflow_faq_proto = out.File
	file_api_cx_chat_bot_workflow_faq_proto_rawDesc = nil
	file_api_cx_chat_bot_workflow_faq_proto_goTypes = nil
	file_api_cx_chat_bot_workflow_faq_proto_depIdxs = nil
}
