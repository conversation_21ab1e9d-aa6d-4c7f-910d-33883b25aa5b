// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/chat/bot/workflow/create_ticket.proto

package workflow

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateTicketResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTicketResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTicketResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTicketResultMultiError, or nil if none found.
func (m *CreateTicketResult) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketResultValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketResultValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketResultValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTicketResultMultiError(errors)
	}

	return nil
}

// CreateTicketResultMultiError is an error wrapping multiple validation errors
// returned by CreateTicketResult.ValidateAll() if the designated constraints
// aren't met.
type CreateTicketResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketResultMultiError) AllErrors() []error { return m }

// CreateTicketResultValidationError is the validation error returned by
// CreateTicketResult.Validate if the designated constraints aren't met.
type CreateTicketResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketResultValidationError) ErrorName() string {
	return "CreateTicketResultValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketResultValidationError{}

// Validate checks the field values on CreateTicketParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTicketParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTicketParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTicketParamsMultiError, or nil if none found.
func (m *CreateTicketParams) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketParamsValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketParamsValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketParamsValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTicketParamsMultiError(errors)
	}

	return nil
}

// CreateTicketParamsMultiError is an error wrapping multiple validation errors
// returned by CreateTicketParams.ValidateAll() if the designated constraints
// aren't met.
type CreateTicketParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketParamsMultiError) AllErrors() []error { return m }

// CreateTicketParamsValidationError is the validation error returned by
// CreateTicketParams.Validate if the designated constraints aren't met.
type CreateTicketParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketParamsValidationError) ErrorName() string {
	return "CreateTicketParamsValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketParamsValidationError{}
