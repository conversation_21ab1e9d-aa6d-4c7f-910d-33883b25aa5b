// Code generated by MockGen. DO NOT EDIT.
// Source: api/cx/developer/actions/consumer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consumer "github.com/epifi/gamma/api/cx/developer/actions/consumer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDevActionsConsumerClient is a mock of DevActionsConsumerClient interface.
type MockDevActionsConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockDevActionsConsumerClientMockRecorder
}

// MockDevActionsConsumerClientMockRecorder is the mock recorder for MockDevActionsConsumerClient.
type MockDevActionsConsumerClientMockRecorder struct {
	mock *MockDevActionsConsumerClient
}

// NewMockDevActionsConsumerClient creates a new mock instance.
func NewMockDevActionsConsumerClient(ctrl *gomock.Controller) *MockDevActionsConsumerClient {
	mock := &MockDevActionsConsumerClient{ctrl: ctrl}
	mock.recorder = &MockDevActionsConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevActionsConsumerClient) EXPECT() *MockDevActionsConsumerClientMockRecorder {
	return m.recorder
}

// ProcessDelayedAction mocks base method.
func (m *MockDevActionsConsumerClient) ProcessDelayedAction(ctx context.Context, in *consumer.ProcessDelayedActionRequest, opts ...grpc.CallOption) (*consumer.ProcessDelayedActionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessDelayedAction", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessDelayedActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessDelayedAction indicates an expected call of ProcessDelayedAction.
func (mr *MockDevActionsConsumerClientMockRecorder) ProcessDelayedAction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDelayedAction", reflect.TypeOf((*MockDevActionsConsumerClient)(nil).ProcessDelayedAction), varargs...)
}

// MockDevActionsConsumerServer is a mock of DevActionsConsumerServer interface.
type MockDevActionsConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockDevActionsConsumerServerMockRecorder
}

// MockDevActionsConsumerServerMockRecorder is the mock recorder for MockDevActionsConsumerServer.
type MockDevActionsConsumerServerMockRecorder struct {
	mock *MockDevActionsConsumerServer
}

// NewMockDevActionsConsumerServer creates a new mock instance.
func NewMockDevActionsConsumerServer(ctrl *gomock.Controller) *MockDevActionsConsumerServer {
	mock := &MockDevActionsConsumerServer{ctrl: ctrl}
	mock.recorder = &MockDevActionsConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevActionsConsumerServer) EXPECT() *MockDevActionsConsumerServerMockRecorder {
	return m.recorder
}

// ProcessDelayedAction mocks base method.
func (m *MockDevActionsConsumerServer) ProcessDelayedAction(arg0 context.Context, arg1 *consumer.ProcessDelayedActionRequest) (*consumer.ProcessDelayedActionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessDelayedAction", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessDelayedActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessDelayedAction indicates an expected call of ProcessDelayedAction.
func (mr *MockDevActionsConsumerServerMockRecorder) ProcessDelayedAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessDelayedAction", reflect.TypeOf((*MockDevActionsConsumerServer)(nil).ProcessDelayedAction), arg0, arg1)
}

// MockUnsafeDevActionsConsumerServer is a mock of UnsafeDevActionsConsumerServer interface.
type MockUnsafeDevActionsConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDevActionsConsumerServerMockRecorder
}

// MockUnsafeDevActionsConsumerServerMockRecorder is the mock recorder for MockUnsafeDevActionsConsumerServer.
type MockUnsafeDevActionsConsumerServerMockRecorder struct {
	mock *MockUnsafeDevActionsConsumerServer
}

// NewMockUnsafeDevActionsConsumerServer creates a new mock instance.
func NewMockUnsafeDevActionsConsumerServer(ctrl *gomock.Controller) *MockUnsafeDevActionsConsumerServer {
	mock := &MockUnsafeDevActionsConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDevActionsConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDevActionsConsumerServer) EXPECT() *MockUnsafeDevActionsConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDevActionsConsumerServer mocks base method.
func (m *MockUnsafeDevActionsConsumerServer) mustEmbedUnimplementedDevActionsConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDevActionsConsumerServer")
}

// mustEmbedUnimplementedDevActionsConsumerServer indicates an expected call of mustEmbedUnimplementedDevActionsConsumerServer.
func (mr *MockUnsafeDevActionsConsumerServerMockRecorder) mustEmbedUnimplementedDevActionsConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDevActionsConsumerServer", reflect.TypeOf((*MockUnsafeDevActionsConsumerServer)(nil).mustEmbedUnimplementedDevActionsConsumerServer))
}
