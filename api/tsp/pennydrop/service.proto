syntax = "proto3";

package tsp.pennydrop;

option go_package = "github.com/epifi/gamma/api/tsp/pennydrop";
option java_package = "com.github.epifi.gamma.api.tsp.pennydrop";

import "api/tsp/user.proto";
import "api/tsp/bank_account_details.proto";
import "api/rpc/status.proto";
import "api/tsp/pennydrop/enums.proto";
import "api/typesv2/common/bank_account_details.proto";


service PennyDrop{
  // StartPennyDrop: Rpc to initiate penydrop for the given user and account.
  rpc StartPennyDrop(StartPennyDropRequest) returns (StartPennyDropResponse);
  // GetPennyDropStatus: Rpc to get the pennydrop status for the given user and client_request_id.
  rpc GetPennyDropStatus(GetPennyDropStatusRequest) returns (GetPennyDropStatusResponse);
}

message StartPennyDropRequest{
  User user = 1;
  // deprecated, use concrete_bank_account_details instead.
  BankAccountDetails bank_account_details = 2;
  // request id we will get from client and will be used for orchestration later.
  string client_request_id = 3;
  api.typesv2.common.BankAccountDetails concrete_bank_account_details = 4;
}

message StartPennyDropResponse{
  rpc.Status status = 1;
}

message GetPennyDropStatusRequest{
  User user = 1;
  string client_request_id = 2;
  // in case we want to allow only a specific number of retries for a given client request id.
  int32 retry_attempt_number = 3;
}

message GetPennyDropStatusResponse{
  rpc.Status status = 1;
  PennyDropStatus penny_drop_status = 2;
  // payment instrument id created in the process of penny drop
  string payment_instrument_id = 3;
}
