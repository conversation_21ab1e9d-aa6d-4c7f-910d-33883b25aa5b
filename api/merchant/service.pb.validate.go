// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/merchant/service.proto

package merchant

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	gplace "github.com/epifi/gamma/api/vendorgateway/gplace"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = gplace.GPlaceType(0)
)

// Validate checks the field values on GetMerchantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMerchantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMerchantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMerchantRequestMultiError, or nil if none found.
func (m *GetMerchantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetMerchantRequest_PiId:
		if v == nil {
			err := GetMerchantRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PiId
	case *GetMerchantRequest_MerchantId:
		if v == nil {
			err := GetMerchantRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for MerchantId
	case *GetMerchantRequest_DsMerchantId:
		if v == nil {
			err := GetMerchantRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DsMerchantId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetMerchantRequestMultiError(errors)
	}

	return nil
}

// GetMerchantRequestMultiError is an error wrapping multiple validation errors
// returned by GetMerchantRequest.ValidateAll() if the designated constraints
// aren't met.
type GetMerchantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantRequestMultiError) AllErrors() []error { return m }

// GetMerchantRequestValidationError is the validation error returned by
// GetMerchantRequest.Validate if the designated constraints aren't met.
type GetMerchantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantRequestValidationError) ErrorName() string {
	return "GetMerchantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantRequestValidationError{}

// Validate checks the field values on GetMerchantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMerchantResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMerchantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMerchantResponseMultiError, or nil if none found.
func (m *GetMerchantResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMerchantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMerchantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMerchantResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMerchant()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMerchantResponseValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMerchantResponseValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchant()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMerchantResponseValidationError{
				field:  "Merchant",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMerchantResponseMultiError(errors)
	}

	return nil
}

// GetMerchantResponseMultiError is an error wrapping multiple validation
// errors returned by GetMerchantResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMerchantResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantResponseMultiError) AllErrors() []error { return m }

// GetMerchantResponseValidationError is the validation error returned by
// GetMerchantResponse.Validate if the designated constraints aren't met.
type GetMerchantResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantResponseValidationError) ErrorName() string {
	return "GetMerchantResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantResponseValidationError{}

// Validate checks the field values on CreateMerchantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMerchantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMerchantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMerchantRequestMultiError, or nil if none found.
func (m *CreateMerchantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMerchantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LegalName

	// no validation rules for BrandName

	// no validation rules for LogoUrl

	// no validation rules for PiId

	if len(errors) > 0 {
		return CreateMerchantRequestMultiError(errors)
	}

	return nil
}

// CreateMerchantRequestMultiError is an error wrapping multiple validation
// errors returned by CreateMerchantRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateMerchantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMerchantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMerchantRequestMultiError) AllErrors() []error { return m }

// CreateMerchantRequestValidationError is the validation error returned by
// CreateMerchantRequest.Validate if the designated constraints aren't met.
type CreateMerchantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMerchantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMerchantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMerchantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMerchantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMerchantRequestValidationError) ErrorName() string {
	return "CreateMerchantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMerchantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMerchantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMerchantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMerchantRequestValidationError{}

// Validate checks the field values on CreateMerchantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMerchantResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMerchantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMerchantResponseMultiError, or nil if none found.
func (m *CreateMerchantResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMerchantResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMerchantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMerchantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMerchantResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MerchantId

	if len(errors) > 0 {
		return CreateMerchantResponseMultiError(errors)
	}

	return nil
}

// CreateMerchantResponseMultiError is an error wrapping multiple validation
// errors returned by CreateMerchantResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateMerchantResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMerchantResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMerchantResponseMultiError) AllErrors() []error { return m }

// CreateMerchantResponseValidationError is the validation error returned by
// CreateMerchantResponse.Validate if the designated constraints aren't met.
type CreateMerchantResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMerchantResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMerchantResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMerchantResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMerchantResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMerchantResponseValidationError) ErrorName() string {
	return "CreateMerchantResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMerchantResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMerchantResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMerchantResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMerchantResponseValidationError{}

// Validate checks the field values on GetMerchantsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMerchantsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMerchantsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMerchantsRequestMultiError, or nil if none found.
func (m *GetMerchantsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetMerchantsRequest_PiIdentifier_:
		if v == nil {
			err := GetMerchantsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPiIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetMerchantsRequestValidationError{
						field:  "PiIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetMerchantsRequestValidationError{
						field:  "PiIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPiIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetMerchantsRequestValidationError{
					field:  "PiIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetMerchantsRequest_MerchantIdentifier_:
		if v == nil {
			err := GetMerchantsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMerchantIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetMerchantsRequestValidationError{
						field:  "MerchantIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetMerchantsRequestValidationError{
						field:  "MerchantIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMerchantIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetMerchantsRequestValidationError{
					field:  "MerchantIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetMerchantsRequestMultiError(errors)
	}

	return nil
}

// GetMerchantsRequestMultiError is an error wrapping multiple validation
// errors returned by GetMerchantsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMerchantsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantsRequestMultiError) AllErrors() []error { return m }

// GetMerchantsRequestValidationError is the validation error returned by
// GetMerchantsRequest.Validate if the designated constraints aren't met.
type GetMerchantsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantsRequestValidationError) ErrorName() string {
	return "GetMerchantsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantsRequestValidationError{}

// Validate checks the field values on GetMerchantsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMerchantsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMerchantsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMerchantsResponseMultiError, or nil if none found.
func (m *GetMerchantsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMerchantsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMerchantsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMerchantsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMerchants() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetMerchantsResponseValidationError{
						field:  fmt.Sprintf("Merchants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetMerchantsResponseValidationError{
						field:  fmt.Sprintf("Merchants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetMerchantsResponseValidationError{
					field:  fmt.Sprintf("Merchants[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetMerchantsResponseMultiError(errors)
	}

	return nil
}

// GetMerchantsResponseMultiError is an error wrapping multiple validation
// errors returned by GetMerchantsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMerchantsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantsResponseMultiError) AllErrors() []error { return m }

// GetMerchantsResponseValidationError is the validation error returned by
// GetMerchantsResponse.Validate if the designated constraints aren't met.
type GetMerchantsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantsResponseValidationError) ErrorName() string {
	return "GetMerchantsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantsResponseValidationError{}

// Validate checks the field values on GetPisByMerchantIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPisByMerchantIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPisByMerchantIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPisByMerchantIdRequestMultiError, or nil if none found.
func (m *GetPisByMerchantIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPisByMerchantIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MerchantId

	// no validation rules for Limit

	if len(errors) > 0 {
		return GetPisByMerchantIdRequestMultiError(errors)
	}

	return nil
}

// GetPisByMerchantIdRequestMultiError is an error wrapping multiple validation
// errors returned by GetPisByMerchantIdRequest.ValidateAll() if the
// designated constraints aren't met.
type GetPisByMerchantIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPisByMerchantIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPisByMerchantIdRequestMultiError) AllErrors() []error { return m }

// GetPisByMerchantIdRequestValidationError is the validation error returned by
// GetPisByMerchantIdRequest.Validate if the designated constraints aren't met.
type GetPisByMerchantIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPisByMerchantIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPisByMerchantIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPisByMerchantIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPisByMerchantIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPisByMerchantIdRequestValidationError) ErrorName() string {
	return "GetPisByMerchantIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPisByMerchantIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPisByMerchantIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPisByMerchantIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPisByMerchantIdRequestValidationError{}

// Validate checks the field values on GetPisByMerchantIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPisByMerchantIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPisByMerchantIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPisByMerchantIdResponseMultiError, or nil if none found.
func (m *GetPisByMerchantIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPisByMerchantIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPisByMerchantIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPisByMerchantIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPisByMerchantIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPisByMerchantIdResponseMultiError(errors)
	}

	return nil
}

// GetPisByMerchantIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetPisByMerchantIdResponse.ValidateAll() if
// the designated constraints aren't met.
type GetPisByMerchantIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPisByMerchantIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPisByMerchantIdResponseMultiError) AllErrors() []error { return m }

// GetPisByMerchantIdResponseValidationError is the validation error returned
// by GetPisByMerchantIdResponse.Validate if the designated constraints aren't met.
type GetPisByMerchantIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPisByMerchantIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPisByMerchantIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPisByMerchantIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPisByMerchantIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPisByMerchantIdResponseValidationError) ErrorName() string {
	return "GetPisByMerchantIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPisByMerchantIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPisByMerchantIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPisByMerchantIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPisByMerchantIdResponseValidationError{}

// Validate checks the field values on CreateCardMerchantInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCardMerchantInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCardMerchantInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateCardMerchantInfoRequestMultiError, or nil if none found.
func (m *CreateCardMerchantInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCardMerchantInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for PiId

	// no validation rules for Mcc

	// no validation rules for Mid

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCardMerchantInfoRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCardMerchantInfoRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCardMerchantInfoRequestValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Tid

	// no validation rules for Acquirer

	if len(errors) > 0 {
		return CreateCardMerchantInfoRequestMultiError(errors)
	}

	return nil
}

// CreateCardMerchantInfoRequestMultiError is an error wrapping multiple
// validation errors returned by CreateCardMerchantInfoRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateCardMerchantInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCardMerchantInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCardMerchantInfoRequestMultiError) AllErrors() []error { return m }

// CreateCardMerchantInfoRequestValidationError is the validation error
// returned by CreateCardMerchantInfoRequest.Validate if the designated
// constraints aren't met.
type CreateCardMerchantInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCardMerchantInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCardMerchantInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCardMerchantInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCardMerchantInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCardMerchantInfoRequestValidationError) ErrorName() string {
	return "CreateCardMerchantInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCardMerchantInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCardMerchantInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCardMerchantInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCardMerchantInfoRequestValidationError{}

// Validate checks the field values on CreateCardMerchantInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCardMerchantInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCardMerchantInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateCardMerchantInfoResponseMultiError, or nil if none found.
func (m *CreateCardMerchantInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCardMerchantInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCardMerchantInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCardMerchantInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCardMerchantInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCardMerchantInfoResponseMultiError(errors)
	}

	return nil
}

// CreateCardMerchantInfoResponseMultiError is an error wrapping multiple
// validation errors returned by CreateCardMerchantInfoResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateCardMerchantInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCardMerchantInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCardMerchantInfoResponseMultiError) AllErrors() []error { return m }

// CreateCardMerchantInfoResponseValidationError is the validation error
// returned by CreateCardMerchantInfoResponse.Validate if the designated
// constraints aren't met.
type CreateCardMerchantInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCardMerchantInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCardMerchantInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCardMerchantInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCardMerchantInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCardMerchantInfoResponseValidationError) ErrorName() string {
	return "CreateCardMerchantInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCardMerchantInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCardMerchantInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCardMerchantInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCardMerchantInfoResponseValidationError{}

// Validate checks the field values on GetCardMerchantInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardMerchantInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardMerchantInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardMerchantInfoRequestMultiError, or nil if none found.
func (m *GetCardMerchantInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardMerchantInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PiId

	if len(errors) > 0 {
		return GetCardMerchantInfoRequestMultiError(errors)
	}

	return nil
}

// GetCardMerchantInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetCardMerchantInfoRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCardMerchantInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardMerchantInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardMerchantInfoRequestMultiError) AllErrors() []error { return m }

// GetCardMerchantInfoRequestValidationError is the validation error returned
// by GetCardMerchantInfoRequest.Validate if the designated constraints aren't met.
type GetCardMerchantInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardMerchantInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardMerchantInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardMerchantInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardMerchantInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardMerchantInfoRequestValidationError) ErrorName() string {
	return "GetCardMerchantInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardMerchantInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardMerchantInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardMerchantInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardMerchantInfoRequestValidationError{}

// Validate checks the field values on GetCardMerchantInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardMerchantInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardMerchantInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardMerchantInfoResponseMultiError, or nil if none found.
func (m *GetCardMerchantInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardMerchantInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardMerchantInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardMerchantInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardMerchantInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Mcc

	// no validation rules for Mid

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardMerchantInfoResponseValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardMerchantInfoResponseValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardMerchantInfoResponseValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Tid

	// no validation rules for Acquirer

	if len(errors) > 0 {
		return GetCardMerchantInfoResponseMultiError(errors)
	}

	return nil
}

// GetCardMerchantInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetCardMerchantInfoResponse.ValidateAll() if
// the designated constraints aren't met.
type GetCardMerchantInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardMerchantInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardMerchantInfoResponseMultiError) AllErrors() []error { return m }

// GetCardMerchantInfoResponseValidationError is the validation error returned
// by GetCardMerchantInfoResponse.Validate if the designated constraints
// aren't met.
type GetCardMerchantInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardMerchantInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardMerchantInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardMerchantInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardMerchantInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardMerchantInfoResponseValidationError) ErrorName() string {
	return "GetCardMerchantInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardMerchantInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardMerchantInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardMerchantInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardMerchantInfoResponseValidationError{}

// Validate checks the field values on GetKnownMerchantByActorIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetKnownMerchantByActorIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKnownMerchantByActorIdsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetKnownMerchantByActorIdsRequestMultiError, or nil if none found.
func (m *GetKnownMerchantByActorIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKnownMerchantByActorIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetActorIds()) < 1 {
		err := GetKnownMerchantByActorIdsRequestValidationError{
			field:  "ActorIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetKnownMerchantByActorIdsRequestMultiError(errors)
	}

	return nil
}

// GetKnownMerchantByActorIdsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetKnownMerchantByActorIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetKnownMerchantByActorIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKnownMerchantByActorIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKnownMerchantByActorIdsRequestMultiError) AllErrors() []error { return m }

// GetKnownMerchantByActorIdsRequestValidationError is the validation error
// returned by GetKnownMerchantByActorIdsRequest.Validate if the designated
// constraints aren't met.
type GetKnownMerchantByActorIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKnownMerchantByActorIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKnownMerchantByActorIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKnownMerchantByActorIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKnownMerchantByActorIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKnownMerchantByActorIdsRequestValidationError) ErrorName() string {
	return "GetKnownMerchantByActorIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKnownMerchantByActorIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKnownMerchantByActorIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKnownMerchantByActorIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKnownMerchantByActorIdsRequestValidationError{}

// Validate checks the field values on GetKnownMerchantByActorIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetKnownMerchantByActorIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKnownMerchantByActorIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetKnownMerchantByActorIdsResponseMultiError, or nil if none found.
func (m *GetKnownMerchantByActorIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKnownMerchantByActorIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKnownMerchantByActorIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKnownMerchantByActorIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKnownMerchantByActorIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorsMerchantMap

	if len(errors) > 0 {
		return GetKnownMerchantByActorIdsResponseMultiError(errors)
	}

	return nil
}

// GetKnownMerchantByActorIdsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetKnownMerchantByActorIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetKnownMerchantByActorIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKnownMerchantByActorIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKnownMerchantByActorIdsResponseMultiError) AllErrors() []error { return m }

// GetKnownMerchantByActorIdsResponseValidationError is the validation error
// returned by GetKnownMerchantByActorIdsResponse.Validate if the designated
// constraints aren't met.
type GetKnownMerchantByActorIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKnownMerchantByActorIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKnownMerchantByActorIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKnownMerchantByActorIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKnownMerchantByActorIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKnownMerchantByActorIdsResponseValidationError) ErrorName() string {
	return "GetKnownMerchantByActorIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetKnownMerchantByActorIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKnownMerchantByActorIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKnownMerchantByActorIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKnownMerchantByActorIdsResponseValidationError{}

// Validate checks the field values on GetActorIdForKnownMerchantsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetActorIdForKnownMerchantsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorIdForKnownMerchantsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetActorIdForKnownMerchantsRequestMultiError, or nil if none found.
func (m *GetActorIdForKnownMerchantsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorIdForKnownMerchantsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetMerchants()) < 1 {
		err := GetActorIdForKnownMerchantsRequestValidationError{
			field:  "Merchants",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetActorIdForKnownMerchantsRequestMultiError(errors)
	}

	return nil
}

// GetActorIdForKnownMerchantsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetActorIdForKnownMerchantsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetActorIdForKnownMerchantsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorIdForKnownMerchantsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorIdForKnownMerchantsRequestMultiError) AllErrors() []error { return m }

// GetActorIdForKnownMerchantsRequestValidationError is the validation error
// returned by GetActorIdForKnownMerchantsRequest.Validate if the designated
// constraints aren't met.
type GetActorIdForKnownMerchantsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorIdForKnownMerchantsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorIdForKnownMerchantsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorIdForKnownMerchantsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorIdForKnownMerchantsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorIdForKnownMerchantsRequestValidationError) ErrorName() string {
	return "GetActorIdForKnownMerchantsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorIdForKnownMerchantsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorIdForKnownMerchantsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorIdForKnownMerchantsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorIdForKnownMerchantsRequestValidationError{}

// Validate checks the field values on GetActorIdForKnownMerchantsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetActorIdForKnownMerchantsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorIdForKnownMerchantsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetActorIdForKnownMerchantsResponseMultiError, or nil if none found.
func (m *GetActorIdForKnownMerchantsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorIdForKnownMerchantsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorIdForKnownMerchantsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorIdForKnownMerchantsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorIdForKnownMerchantsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMerchantActorsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetActorIdForKnownMerchantsResponseValidationError{
						field:  fmt.Sprintf("MerchantActorsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetActorIdForKnownMerchantsResponseValidationError{
						field:  fmt.Sprintf("MerchantActorsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetActorIdForKnownMerchantsResponseValidationError{
					field:  fmt.Sprintf("MerchantActorsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetActorIdForKnownMerchantsResponseMultiError(errors)
	}

	return nil
}

// GetActorIdForKnownMerchantsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetActorIdForKnownMerchantsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetActorIdForKnownMerchantsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorIdForKnownMerchantsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorIdForKnownMerchantsResponseMultiError) AllErrors() []error { return m }

// GetActorIdForKnownMerchantsResponseValidationError is the validation error
// returned by GetActorIdForKnownMerchantsResponse.Validate if the designated
// constraints aren't met.
type GetActorIdForKnownMerchantsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorIdForKnownMerchantsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorIdForKnownMerchantsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorIdForKnownMerchantsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorIdForKnownMerchantsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorIdForKnownMerchantsResponseValidationError) ErrorName() string {
	return "GetActorIdForKnownMerchantsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorIdForKnownMerchantsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorIdForKnownMerchantsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorIdForKnownMerchantsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorIdForKnownMerchantsResponseValidationError{}

// Validate checks the field values on GetKnownMerchantByIdsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKnownMerchantByIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKnownMerchantByIdsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKnownMerchantByIdsRequestMultiError, or nil if none found.
func (m *GetKnownMerchantByIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKnownMerchantByIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetMerchantIds()) < 1 {
		err := GetKnownMerchantByIdsRequestValidationError{
			field:  "MerchantIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetKnownMerchantByIdsRequestMultiError(errors)
	}

	return nil
}

// GetKnownMerchantByIdsRequestMultiError is an error wrapping multiple
// validation errors returned by GetKnownMerchantByIdsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetKnownMerchantByIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKnownMerchantByIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKnownMerchantByIdsRequestMultiError) AllErrors() []error { return m }

// GetKnownMerchantByIdsRequestValidationError is the validation error returned
// by GetKnownMerchantByIdsRequest.Validate if the designated constraints
// aren't met.
type GetKnownMerchantByIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKnownMerchantByIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKnownMerchantByIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKnownMerchantByIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKnownMerchantByIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKnownMerchantByIdsRequestValidationError) ErrorName() string {
	return "GetKnownMerchantByIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKnownMerchantByIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKnownMerchantByIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKnownMerchantByIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKnownMerchantByIdsRequestValidationError{}

// Validate checks the field values on GetKnownMerchantByIdsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKnownMerchantByIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKnownMerchantByIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetKnownMerchantByIdsResponseMultiError, or nil if none found.
func (m *GetKnownMerchantByIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKnownMerchantByIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKnownMerchantByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKnownMerchantByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKnownMerchantByIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IdMerchantMap

	if len(errors) > 0 {
		return GetKnownMerchantByIdsResponseMultiError(errors)
	}

	return nil
}

// GetKnownMerchantByIdsResponseMultiError is an error wrapping multiple
// validation errors returned by GetKnownMerchantByIdsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetKnownMerchantByIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKnownMerchantByIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKnownMerchantByIdsResponseMultiError) AllErrors() []error { return m }

// GetKnownMerchantByIdsResponseValidationError is the validation error
// returned by GetKnownMerchantByIdsResponse.Validate if the designated
// constraints aren't met.
type GetKnownMerchantByIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKnownMerchantByIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKnownMerchantByIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKnownMerchantByIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKnownMerchantByIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKnownMerchantByIdsResponseValidationError) ErrorName() string {
	return "GetKnownMerchantByIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetKnownMerchantByIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKnownMerchantByIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKnownMerchantByIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKnownMerchantByIdsResponseValidationError{}

// Validate checks the field values on GetIdForKnownMerchantsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetIdForKnownMerchantsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetIdForKnownMerchantsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetIdForKnownMerchantsRequestMultiError, or nil if none found.
func (m *GetIdForKnownMerchantsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetIdForKnownMerchantsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetMerchants()) < 1 {
		err := GetIdForKnownMerchantsRequestValidationError{
			field:  "Merchants",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetIdForKnownMerchantsRequestMultiError(errors)
	}

	return nil
}

// GetIdForKnownMerchantsRequestMultiError is an error wrapping multiple
// validation errors returned by GetIdForKnownMerchantsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetIdForKnownMerchantsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetIdForKnownMerchantsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetIdForKnownMerchantsRequestMultiError) AllErrors() []error { return m }

// GetIdForKnownMerchantsRequestValidationError is the validation error
// returned by GetIdForKnownMerchantsRequest.Validate if the designated
// constraints aren't met.
type GetIdForKnownMerchantsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetIdForKnownMerchantsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetIdForKnownMerchantsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetIdForKnownMerchantsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetIdForKnownMerchantsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetIdForKnownMerchantsRequestValidationError) ErrorName() string {
	return "GetIdForKnownMerchantsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetIdForKnownMerchantsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetIdForKnownMerchantsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetIdForKnownMerchantsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetIdForKnownMerchantsRequestValidationError{}

// Validate checks the field values on GetIdForKnownMerchantsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetIdForKnownMerchantsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetIdForKnownMerchantsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetIdForKnownMerchantsResponseMultiError, or nil if none found.
func (m *GetIdForKnownMerchantsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetIdForKnownMerchantsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetIdForKnownMerchantsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetIdForKnownMerchantsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetIdForKnownMerchantsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMerchantIdList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetIdForKnownMerchantsResponseValidationError{
						field:  fmt.Sprintf("MerchantIdList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetIdForKnownMerchantsResponseValidationError{
						field:  fmt.Sprintf("MerchantIdList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetIdForKnownMerchantsResponseValidationError{
					field:  fmt.Sprintf("MerchantIdList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetIdForKnownMerchantsResponseMultiError(errors)
	}

	return nil
}

// GetIdForKnownMerchantsResponseMultiError is an error wrapping multiple
// validation errors returned by GetIdForKnownMerchantsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetIdForKnownMerchantsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetIdForKnownMerchantsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetIdForKnownMerchantsResponseMultiError) AllErrors() []error { return m }

// GetIdForKnownMerchantsResponseValidationError is the validation error
// returned by GetIdForKnownMerchantsResponse.Validate if the designated
// constraints aren't met.
type GetIdForKnownMerchantsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetIdForKnownMerchantsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetIdForKnownMerchantsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetIdForKnownMerchantsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetIdForKnownMerchantsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetIdForKnownMerchantsResponseValidationError) ErrorName() string {
	return "GetIdForKnownMerchantsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetIdForKnownMerchantsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetIdForKnownMerchantsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetIdForKnownMerchantsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetIdForKnownMerchantsResponseValidationError{}

// Validate checks the field values on CheckMerchantPiRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckMerchantPiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckMerchantPiRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckMerchantPiRequestMultiError, or nil if none found.
func (m *CheckMerchantPiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMerchantPiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetPiIds()) < 1 {
		err := CheckMerchantPiRequestValidationError{
			field:  "PiIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckMerchantPiRequestMultiError(errors)
	}

	return nil
}

// CheckMerchantPiRequestMultiError is an error wrapping multiple validation
// errors returned by CheckMerchantPiRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckMerchantPiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMerchantPiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMerchantPiRequestMultiError) AllErrors() []error { return m }

// CheckMerchantPiRequestValidationError is the validation error returned by
// CheckMerchantPiRequest.Validate if the designated constraints aren't met.
type CheckMerchantPiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMerchantPiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMerchantPiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckMerchantPiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMerchantPiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMerchantPiRequestValidationError) ErrorName() string {
	return "CheckMerchantPiRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMerchantPiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMerchantPiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMerchantPiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMerchantPiRequestValidationError{}

// Validate checks the field values on CheckMerchantPiResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckMerchantPiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckMerchantPiResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckMerchantPiResponseMultiError, or nil if none found.
func (m *CheckMerchantPiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMerchantPiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMerchantPiResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMerchantPiResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMerchantPiResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckMerchantPiResponseMultiError(errors)
	}

	return nil
}

// CheckMerchantPiResponseMultiError is an error wrapping multiple validation
// errors returned by CheckMerchantPiResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckMerchantPiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMerchantPiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMerchantPiResponseMultiError) AllErrors() []error { return m }

// CheckMerchantPiResponseValidationError is the validation error returned by
// CheckMerchantPiResponse.Validate if the designated constraints aren't met.
type CheckMerchantPiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMerchantPiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMerchantPiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckMerchantPiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMerchantPiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMerchantPiResponseValidationError) ErrorName() string {
	return "CheckMerchantPiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMerchantPiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMerchantPiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMerchantPiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMerchantPiResponseValidationError{}

// Validate checks the field values on UpsertMerchantPisRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpsertMerchantPisRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertMerchantPisRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpsertMerchantPisRequestMultiError, or nil if none found.
func (m *UpsertMerchantPisRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertMerchantPisRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PiMerchantMap

	if len(errors) > 0 {
		return UpsertMerchantPisRequestMultiError(errors)
	}

	return nil
}

// UpsertMerchantPisRequestMultiError is an error wrapping multiple validation
// errors returned by UpsertMerchantPisRequest.ValidateAll() if the designated
// constraints aren't met.
type UpsertMerchantPisRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertMerchantPisRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertMerchantPisRequestMultiError) AllErrors() []error { return m }

// UpsertMerchantPisRequestValidationError is the validation error returned by
// UpsertMerchantPisRequest.Validate if the designated constraints aren't met.
type UpsertMerchantPisRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertMerchantPisRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertMerchantPisRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertMerchantPisRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertMerchantPisRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertMerchantPisRequestValidationError) ErrorName() string {
	return "UpsertMerchantPisRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertMerchantPisRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertMerchantPisRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertMerchantPisRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertMerchantPisRequestValidationError{}

// Validate checks the field values on UpsertMerchantPisResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpsertMerchantPisResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertMerchantPisResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpsertMerchantPisResponseMultiError, or nil if none found.
func (m *UpsertMerchantPisResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertMerchantPisResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertMerchantPisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertMerchantPisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertMerchantPisResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpsertMerchantPisResponseMultiError(errors)
	}

	return nil
}

// UpsertMerchantPisResponseMultiError is an error wrapping multiple validation
// errors returned by UpsertMerchantPisResponse.ValidateAll() if the
// designated constraints aren't met.
type UpsertMerchantPisResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertMerchantPisResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertMerchantPisResponseMultiError) AllErrors() []error { return m }

// UpsertMerchantPisResponseValidationError is the validation error returned by
// UpsertMerchantPisResponse.Validate if the designated constraints aren't met.
type UpsertMerchantPisResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertMerchantPisResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertMerchantPisResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertMerchantPisResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertMerchantPisResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertMerchantPisResponseValidationError) ErrorName() string {
	return "UpsertMerchantPisResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertMerchantPisResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertMerchantPisResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertMerchantPisResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertMerchantPisResponseValidationError{}

// Validate checks the field values on CreateMerchantPiGplaceDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateMerchantPiGplaceDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMerchantPiGplaceDataRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateMerchantPiGplaceDataRequestMultiError, or nil if none found.
func (m *CreateMerchantPiGplaceDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMerchantPiGplaceDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PiId

	// no validation rules for BusinessStatus

	// no validation rules for FormattedAddress

	if all {
		switch v := interface{}(m.GetGeometry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMerchantPiGplaceDataRequestValidationError{
					field:  "Geometry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMerchantPiGplaceDataRequestValidationError{
					field:  "Geometry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeometry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMerchantPiGplaceDataRequestValidationError{
				field:  "Geometry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IconUrl

	// no validation rules for IconMaskBaseUri

	// no validation rules for IconBackgroundColor

	// no validation rules for PlaceName

	// no validation rules for PlaceId

	if len(errors) > 0 {
		return CreateMerchantPiGplaceDataRequestMultiError(errors)
	}

	return nil
}

// CreateMerchantPiGplaceDataRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateMerchantPiGplaceDataRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateMerchantPiGplaceDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMerchantPiGplaceDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMerchantPiGplaceDataRequestMultiError) AllErrors() []error { return m }

// CreateMerchantPiGplaceDataRequestValidationError is the validation error
// returned by CreateMerchantPiGplaceDataRequest.Validate if the designated
// constraints aren't met.
type CreateMerchantPiGplaceDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMerchantPiGplaceDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMerchantPiGplaceDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMerchantPiGplaceDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMerchantPiGplaceDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMerchantPiGplaceDataRequestValidationError) ErrorName() string {
	return "CreateMerchantPiGplaceDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMerchantPiGplaceDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMerchantPiGplaceDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMerchantPiGplaceDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMerchantPiGplaceDataRequestValidationError{}

// Validate checks the field values on CreateMerchantPiGplaceDataResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateMerchantPiGplaceDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMerchantPiGplaceDataResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateMerchantPiGplaceDataResponseMultiError, or nil if none found.
func (m *CreateMerchantPiGplaceDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMerchantPiGplaceDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMerchantPiGplaceDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMerchantPiGplaceDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMerchantPiGplaceDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateMerchantPiGplaceDataResponseMultiError(errors)
	}

	return nil
}

// CreateMerchantPiGplaceDataResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateMerchantPiGplaceDataResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateMerchantPiGplaceDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMerchantPiGplaceDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMerchantPiGplaceDataResponseMultiError) AllErrors() []error { return m }

// CreateMerchantPiGplaceDataResponseValidationError is the validation error
// returned by CreateMerchantPiGplaceDataResponse.Validate if the designated
// constraints aren't met.
type CreateMerchantPiGplaceDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMerchantPiGplaceDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMerchantPiGplaceDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMerchantPiGplaceDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMerchantPiGplaceDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMerchantPiGplaceDataResponseValidationError) ErrorName() string {
	return "CreateMerchantPiGplaceDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMerchantPiGplaceDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMerchantPiGplaceDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMerchantPiGplaceDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMerchantPiGplaceDataResponseValidationError{}

// Validate checks the field values on UpdateByDsMerchantIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateByDsMerchantIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateByDsMerchantIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateByDsMerchantIdRequestMultiError, or nil if none found.
func (m *UpdateByDsMerchantIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateByDsMerchantIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMerchant()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateByDsMerchantIdRequestValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateByDsMerchantIdRequestValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchant()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateByDsMerchantIdRequestValidationError{
				field:  "Merchant",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateByDsMerchantIdRequestMultiError(errors)
	}

	return nil
}

// UpdateByDsMerchantIdRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateByDsMerchantIdRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateByDsMerchantIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateByDsMerchantIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateByDsMerchantIdRequestMultiError) AllErrors() []error { return m }

// UpdateByDsMerchantIdRequestValidationError is the validation error returned
// by UpdateByDsMerchantIdRequest.Validate if the designated constraints
// aren't met.
type UpdateByDsMerchantIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateByDsMerchantIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateByDsMerchantIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateByDsMerchantIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateByDsMerchantIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateByDsMerchantIdRequestValidationError) ErrorName() string {
	return "UpdateByDsMerchantIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateByDsMerchantIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateByDsMerchantIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateByDsMerchantIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateByDsMerchantIdRequestValidationError{}

// Validate checks the field values on UpdateByDsMerchantIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateByDsMerchantIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateByDsMerchantIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateByDsMerchantIdResponseMultiError, or nil if none found.
func (m *UpdateByDsMerchantIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateByDsMerchantIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateByDsMerchantIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateByDsMerchantIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateByDsMerchantIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateByDsMerchantIdResponseMultiError(errors)
	}

	return nil
}

// UpdateByDsMerchantIdResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateByDsMerchantIdResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateByDsMerchantIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateByDsMerchantIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateByDsMerchantIdResponseMultiError) AllErrors() []error { return m }

// UpdateByDsMerchantIdResponseValidationError is the validation error returned
// by UpdateByDsMerchantIdResponse.Validate if the designated constraints
// aren't met.
type UpdateByDsMerchantIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateByDsMerchantIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateByDsMerchantIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateByDsMerchantIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateByDsMerchantIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateByDsMerchantIdResponseValidationError) ErrorName() string {
	return "UpdateByDsMerchantIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateByDsMerchantIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateByDsMerchantIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateByDsMerchantIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateByDsMerchantIdResponseValidationError{}

// Validate checks the field values on UpdateRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdateRequestMultiError, or
// nil if none found.
func (m *UpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMerchant()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRequestValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRequestValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchant()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRequestValidationError{
				field:  "Merchant",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRequestMultiError(errors)
	}

	return nil
}

// UpdateRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRequestMultiError) AllErrors() []error { return m }

// UpdateRequestValidationError is the validation error returned by
// UpdateRequest.Validate if the designated constraints aren't met.
type UpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRequestValidationError) ErrorName() string { return "UpdateRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRequestValidationError{}

// Validate checks the field values on UpdateResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdateResponseMultiError,
// or nil if none found.
func (m *UpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateResponseMultiError(errors)
	}

	return nil
}

// UpdateResponseMultiError is an error wrapping multiple validation errors
// returned by UpdateResponse.ValidateAll() if the designated constraints
// aren't met.
type UpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateResponseMultiError) AllErrors() []error { return m }

// UpdateResponseValidationError is the validation error returned by
// UpdateResponse.Validate if the designated constraints aren't met.
type UpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateResponseValidationError) ErrorName() string { return "UpdateResponseValidationError" }

// Error satisfies the builtin error interface
func (e UpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateResponseValidationError{}

// Validate checks the field values on GetOldMerchantsByMerchantIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOldMerchantsByMerchantIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOldMerchantsByMerchantIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOldMerchantsByMerchantIdRequestMultiError, or nil if none found.
func (m *GetOldMerchantsByMerchantIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOldMerchantsByMerchantIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetMerchantId()) < 1 {
		err := GetOldMerchantsByMerchantIdRequestValidationError{
			field:  "MerchantId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetOldMerchantsByMerchantIdRequestMultiError(errors)
	}

	return nil
}

// GetOldMerchantsByMerchantIdRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetOldMerchantsByMerchantIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOldMerchantsByMerchantIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOldMerchantsByMerchantIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOldMerchantsByMerchantIdRequestMultiError) AllErrors() []error { return m }

// GetOldMerchantsByMerchantIdRequestValidationError is the validation error
// returned by GetOldMerchantsByMerchantIdRequest.Validate if the designated
// constraints aren't met.
type GetOldMerchantsByMerchantIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOldMerchantsByMerchantIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOldMerchantsByMerchantIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOldMerchantsByMerchantIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOldMerchantsByMerchantIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOldMerchantsByMerchantIdRequestValidationError) ErrorName() string {
	return "GetOldMerchantsByMerchantIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOldMerchantsByMerchantIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOldMerchantsByMerchantIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOldMerchantsByMerchantIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOldMerchantsByMerchantIdRequestValidationError{}

// Validate checks the field values on GetOldMerchantsByMerchantIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOldMerchantsByMerchantIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOldMerchantsByMerchantIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetOldMerchantsByMerchantIdResponseMultiError, or nil if none found.
func (m *GetOldMerchantsByMerchantIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOldMerchantsByMerchantIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOldMerchantsByMerchantIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOldMerchantsByMerchantIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOldMerchantsByMerchantIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOldMerchantsByMerchantIdResponseMultiError(errors)
	}

	return nil
}

// GetOldMerchantsByMerchantIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetOldMerchantsByMerchantIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOldMerchantsByMerchantIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOldMerchantsByMerchantIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOldMerchantsByMerchantIdResponseMultiError) AllErrors() []error { return m }

// GetOldMerchantsByMerchantIdResponseValidationError is the validation error
// returned by GetOldMerchantsByMerchantIdResponse.Validate if the designated
// constraints aren't met.
type GetOldMerchantsByMerchantIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOldMerchantsByMerchantIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOldMerchantsByMerchantIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOldMerchantsByMerchantIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOldMerchantsByMerchantIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOldMerchantsByMerchantIdResponseValidationError) ErrorName() string {
	return "GetOldMerchantsByMerchantIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOldMerchantsByMerchantIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOldMerchantsByMerchantIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOldMerchantsByMerchantIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOldMerchantsByMerchantIdResponseValidationError{}

// Validate checks the field values on GetNewMerchantIdsByOldMerchantIdsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetNewMerchantIdsByOldMerchantIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNewMerchantIdsByOldMerchantIdsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetNewMerchantIdsByOldMerchantIdsRequestMultiError, or nil if none found.
func (m *GetNewMerchantIdsByOldMerchantIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNewMerchantIdsByOldMerchantIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetNewMerchantIdsByOldMerchantIdsRequestMultiError(errors)
	}

	return nil
}

// GetNewMerchantIdsByOldMerchantIdsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetNewMerchantIdsByOldMerchantIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNewMerchantIdsByOldMerchantIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNewMerchantIdsByOldMerchantIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNewMerchantIdsByOldMerchantIdsRequestMultiError) AllErrors() []error { return m }

// GetNewMerchantIdsByOldMerchantIdsRequestValidationError is the validation
// error returned by GetNewMerchantIdsByOldMerchantIdsRequest.Validate if the
// designated constraints aren't met.
type GetNewMerchantIdsByOldMerchantIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNewMerchantIdsByOldMerchantIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNewMerchantIdsByOldMerchantIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNewMerchantIdsByOldMerchantIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNewMerchantIdsByOldMerchantIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNewMerchantIdsByOldMerchantIdsRequestValidationError) ErrorName() string {
	return "GetNewMerchantIdsByOldMerchantIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNewMerchantIdsByOldMerchantIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNewMerchantIdsByOldMerchantIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNewMerchantIdsByOldMerchantIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNewMerchantIdsByOldMerchantIdsRequestValidationError{}

// Validate checks the field values on
// GetNewMerchantIdsByOldMerchantIdsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetNewMerchantIdsByOldMerchantIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNewMerchantIdsByOldMerchantIdsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetNewMerchantIdsByOldMerchantIdsResponseMultiError, or nil if none found.
func (m *GetNewMerchantIdsByOldMerchantIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNewMerchantIdsByOldMerchantIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNewMerchantIdsByOldMerchantIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNewMerchantIdsByOldMerchantIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNewMerchantIdsByOldMerchantIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OldToNewMerchantIds

	if len(errors) > 0 {
		return GetNewMerchantIdsByOldMerchantIdsResponseMultiError(errors)
	}

	return nil
}

// GetNewMerchantIdsByOldMerchantIdsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetNewMerchantIdsByOldMerchantIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNewMerchantIdsByOldMerchantIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNewMerchantIdsByOldMerchantIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNewMerchantIdsByOldMerchantIdsResponseMultiError) AllErrors() []error { return m }

// GetNewMerchantIdsByOldMerchantIdsResponseValidationError is the validation
// error returned by GetNewMerchantIdsByOldMerchantIdsResponse.Validate if the
// designated constraints aren't met.
type GetNewMerchantIdsByOldMerchantIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNewMerchantIdsByOldMerchantIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNewMerchantIdsByOldMerchantIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNewMerchantIdsByOldMerchantIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNewMerchantIdsByOldMerchantIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNewMerchantIdsByOldMerchantIdsResponseValidationError) ErrorName() string {
	return "GetNewMerchantIdsByOldMerchantIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNewMerchantIdsByOldMerchantIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNewMerchantIdsByOldMerchantIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNewMerchantIdsByOldMerchantIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNewMerchantIdsByOldMerchantIdsResponseValidationError{}

// Validate checks the field values on GetAccountMerchantInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountMerchantInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountMerchantInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAccountMerchantInfoRequestMultiError, or nil if none found.
func (m *GetAccountMerchantInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountMerchantInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PiId

	if len(errors) > 0 {
		return GetAccountMerchantInfoRequestMultiError(errors)
	}

	return nil
}

// GetAccountMerchantInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetAccountMerchantInfoRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAccountMerchantInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountMerchantInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountMerchantInfoRequestMultiError) AllErrors() []error { return m }

// GetAccountMerchantInfoRequestValidationError is the validation error
// returned by GetAccountMerchantInfoRequest.Validate if the designated
// constraints aren't met.
type GetAccountMerchantInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountMerchantInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountMerchantInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountMerchantInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountMerchantInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountMerchantInfoRequestValidationError) ErrorName() string {
	return "GetAccountMerchantInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountMerchantInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountMerchantInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountMerchantInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountMerchantInfoRequestValidationError{}

// Validate checks the field values on GetAccountMerchantInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountMerchantInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountMerchantInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAccountMerchantInfoResponseMultiError, or nil if none found.
func (m *GetAccountMerchantInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountMerchantInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountMerchantInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountMerchantInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountMerchantInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Mcc

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountMerchantInfoResponseValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountMerchantInfoResponseValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountMerchantInfoResponseValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccountMerchantInfoResponseMultiError(errors)
	}

	return nil
}

// GetAccountMerchantInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetAccountMerchantInfoResponse.ValidateAll()
// if the designated constraints aren't met.
type GetAccountMerchantInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountMerchantInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountMerchantInfoResponseMultiError) AllErrors() []error { return m }

// GetAccountMerchantInfoResponseValidationError is the validation error
// returned by GetAccountMerchantInfoResponse.Validate if the designated
// constraints aren't met.
type GetAccountMerchantInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountMerchantInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountMerchantInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountMerchantInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountMerchantInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountMerchantInfoResponseValidationError) ErrorName() string {
	return "GetAccountMerchantInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountMerchantInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountMerchantInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountMerchantInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountMerchantInfoResponseValidationError{}

// Validate checks the field values on GetMerchantPiEntitiesByPiIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetMerchantPiEntitiesByPiIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMerchantPiEntitiesByPiIdsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetMerchantPiEntitiesByPiIdsRequestMultiError, or nil if none found.
func (m *GetMerchantPiEntitiesByPiIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantPiEntitiesByPiIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetMerchantPiEntitiesByPiIdsRequestMultiError(errors)
	}

	return nil
}

// GetMerchantPiEntitiesByPiIdsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetMerchantPiEntitiesByPiIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMerchantPiEntitiesByPiIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantPiEntitiesByPiIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantPiEntitiesByPiIdsRequestMultiError) AllErrors() []error { return m }

// GetMerchantPiEntitiesByPiIdsRequestValidationError is the validation error
// returned by GetMerchantPiEntitiesByPiIdsRequest.Validate if the designated
// constraints aren't met.
type GetMerchantPiEntitiesByPiIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantPiEntitiesByPiIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantPiEntitiesByPiIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantPiEntitiesByPiIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantPiEntitiesByPiIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantPiEntitiesByPiIdsRequestValidationError) ErrorName() string {
	return "GetMerchantPiEntitiesByPiIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantPiEntitiesByPiIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantPiEntitiesByPiIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantPiEntitiesByPiIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantPiEntitiesByPiIdsRequestValidationError{}

// Validate checks the field values on GetMerchantPiEntitiesByPiIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetMerchantPiEntitiesByPiIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMerchantPiEntitiesByPiIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetMerchantPiEntitiesByPiIdsResponseMultiError, or nil if none found.
func (m *GetMerchantPiEntitiesByPiIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantPiEntitiesByPiIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMerchantPiEntitiesByPiIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMerchantPiEntitiesByPiIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMerchantPiEntitiesByPiIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetPiToOldAndMerchantIds()))
		i := 0
		for key := range m.GetPiToOldAndMerchantIds() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetPiToOldAndMerchantIds()[key]
			_ = val

			// no validation rules for PiToOldAndMerchantIds[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetMerchantPiEntitiesByPiIdsResponseValidationError{
							field:  fmt.Sprintf("PiToOldAndMerchantIds[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetMerchantPiEntitiesByPiIdsResponseValidationError{
							field:  fmt.Sprintf("PiToOldAndMerchantIds[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetMerchantPiEntitiesByPiIdsResponseValidationError{
						field:  fmt.Sprintf("PiToOldAndMerchantIds[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetMerchantPiEntitiesByPiIdsResponseMultiError(errors)
	}

	return nil
}

// GetMerchantPiEntitiesByPiIdsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetMerchantPiEntitiesByPiIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMerchantPiEntitiesByPiIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantPiEntitiesByPiIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantPiEntitiesByPiIdsResponseMultiError) AllErrors() []error { return m }

// GetMerchantPiEntitiesByPiIdsResponseValidationError is the validation error
// returned by GetMerchantPiEntitiesByPiIdsResponse.Validate if the designated
// constraints aren't met.
type GetMerchantPiEntitiesByPiIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantPiEntitiesByPiIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantPiEntitiesByPiIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantPiEntitiesByPiIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantPiEntitiesByPiIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantPiEntitiesByPiIdsResponseValidationError) ErrorName() string {
	return "GetMerchantPiEntitiesByPiIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantPiEntitiesByPiIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantPiEntitiesByPiIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantPiEntitiesByPiIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantPiEntitiesByPiIdsResponseValidationError{}

// Validate checks the field values on UpdateOldAndNewMerchantIdByPiIdsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateOldAndNewMerchantIdByPiIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateOldAndNewMerchantIdByPiIdsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateOldAndNewMerchantIdByPiIdsRequestMultiError, or nil if none found.
func (m *UpdateOldAndNewMerchantIdByPiIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOldAndNewMerchantIdByPiIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateOldAndNewMerchantIdByPiIdsRequestMultiError(errors)
	}

	return nil
}

// UpdateOldAndNewMerchantIdByPiIdsRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateOldAndNewMerchantIdByPiIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateOldAndNewMerchantIdByPiIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOldAndNewMerchantIdByPiIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOldAndNewMerchantIdByPiIdsRequestMultiError) AllErrors() []error { return m }

// UpdateOldAndNewMerchantIdByPiIdsRequestValidationError is the validation
// error returned by UpdateOldAndNewMerchantIdByPiIdsRequest.Validate if the
// designated constraints aren't met.
type UpdateOldAndNewMerchantIdByPiIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOldAndNewMerchantIdByPiIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOldAndNewMerchantIdByPiIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOldAndNewMerchantIdByPiIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOldAndNewMerchantIdByPiIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOldAndNewMerchantIdByPiIdsRequestValidationError) ErrorName() string {
	return "UpdateOldAndNewMerchantIdByPiIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOldAndNewMerchantIdByPiIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOldAndNewMerchantIdByPiIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOldAndNewMerchantIdByPiIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOldAndNewMerchantIdByPiIdsRequestValidationError{}

// Validate checks the field values on UpdateOldAndNewMerchantIdByPiIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateOldAndNewMerchantIdByPiIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateOldAndNewMerchantIdByPiIdsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateOldAndNewMerchantIdByPiIdsResponseMultiError, or nil if none found.
func (m *UpdateOldAndNewMerchantIdByPiIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOldAndNewMerchantIdByPiIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateOldAndNewMerchantIdByPiIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateOldAndNewMerchantIdByPiIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateOldAndNewMerchantIdByPiIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateOldAndNewMerchantIdByPiIdsResponseMultiError(errors)
	}

	return nil
}

// UpdateOldAndNewMerchantIdByPiIdsResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateOldAndNewMerchantIdByPiIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateOldAndNewMerchantIdByPiIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOldAndNewMerchantIdByPiIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOldAndNewMerchantIdByPiIdsResponseMultiError) AllErrors() []error { return m }

// UpdateOldAndNewMerchantIdByPiIdsResponseValidationError is the validation
// error returned by UpdateOldAndNewMerchantIdByPiIdsResponse.Validate if the
// designated constraints aren't met.
type UpdateOldAndNewMerchantIdByPiIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOldAndNewMerchantIdByPiIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOldAndNewMerchantIdByPiIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOldAndNewMerchantIdByPiIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOldAndNewMerchantIdByPiIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOldAndNewMerchantIdByPiIdsResponseValidationError) ErrorName() string {
	return "UpdateOldAndNewMerchantIdByPiIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOldAndNewMerchantIdByPiIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOldAndNewMerchantIdByPiIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOldAndNewMerchantIdByPiIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOldAndNewMerchantIdByPiIdsResponseValidationError{}

// Validate checks the field values on UpdateNewMerchantIdByPiIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateNewMerchantIdByPiIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNewMerchantIdByPiIdsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateNewMerchantIdByPiIdsRequestMultiError, or nil if none found.
func (m *UpdateNewMerchantIdByPiIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNewMerchantIdByPiIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MerchantId

	if len(errors) > 0 {
		return UpdateNewMerchantIdByPiIdsRequestMultiError(errors)
	}

	return nil
}

// UpdateNewMerchantIdByPiIdsRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateNewMerchantIdByPiIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateNewMerchantIdByPiIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNewMerchantIdByPiIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNewMerchantIdByPiIdsRequestMultiError) AllErrors() []error { return m }

// UpdateNewMerchantIdByPiIdsRequestValidationError is the validation error
// returned by UpdateNewMerchantIdByPiIdsRequest.Validate if the designated
// constraints aren't met.
type UpdateNewMerchantIdByPiIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNewMerchantIdByPiIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNewMerchantIdByPiIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNewMerchantIdByPiIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNewMerchantIdByPiIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNewMerchantIdByPiIdsRequestValidationError) ErrorName() string {
	return "UpdateNewMerchantIdByPiIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNewMerchantIdByPiIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNewMerchantIdByPiIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNewMerchantIdByPiIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNewMerchantIdByPiIdsRequestValidationError{}

// Validate checks the field values on UpdateNewMerchantIdByPiIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateNewMerchantIdByPiIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNewMerchantIdByPiIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateNewMerchantIdByPiIdsResponseMultiError, or nil if none found.
func (m *UpdateNewMerchantIdByPiIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNewMerchantIdByPiIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNewMerchantIdByPiIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNewMerchantIdByPiIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNewMerchantIdByPiIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateNewMerchantIdByPiIdsResponseMultiError(errors)
	}

	return nil
}

// UpdateNewMerchantIdByPiIdsResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateNewMerchantIdByPiIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateNewMerchantIdByPiIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNewMerchantIdByPiIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNewMerchantIdByPiIdsResponseMultiError) AllErrors() []error { return m }

// UpdateNewMerchantIdByPiIdsResponseValidationError is the validation error
// returned by UpdateNewMerchantIdByPiIdsResponse.Validate if the designated
// constraints aren't met.
type UpdateNewMerchantIdByPiIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNewMerchantIdByPiIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNewMerchantIdByPiIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNewMerchantIdByPiIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNewMerchantIdByPiIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNewMerchantIdByPiIdsResponseValidationError) ErrorName() string {
	return "UpdateNewMerchantIdByPiIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNewMerchantIdByPiIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNewMerchantIdByPiIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNewMerchantIdByPiIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNewMerchantIdByPiIdsResponseValidationError{}

// Validate checks the field values on CreateProbableKnownMerchantRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateProbableKnownMerchantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateProbableKnownMerchantRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateProbableKnownMerchantRequestMultiError, or nil if none found.
func (m *CreateProbableKnownMerchantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateProbableKnownMerchantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MerchantId

	// no validation rules for PiId

	// no validation rules for DsMerchantId

	if len(errors) > 0 {
		return CreateProbableKnownMerchantRequestMultiError(errors)
	}

	return nil
}

// CreateProbableKnownMerchantRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateProbableKnownMerchantRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateProbableKnownMerchantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateProbableKnownMerchantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateProbableKnownMerchantRequestMultiError) AllErrors() []error { return m }

// CreateProbableKnownMerchantRequestValidationError is the validation error
// returned by CreateProbableKnownMerchantRequest.Validate if the designated
// constraints aren't met.
type CreateProbableKnownMerchantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateProbableKnownMerchantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateProbableKnownMerchantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateProbableKnownMerchantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateProbableKnownMerchantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateProbableKnownMerchantRequestValidationError) ErrorName() string {
	return "CreateProbableKnownMerchantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateProbableKnownMerchantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateProbableKnownMerchantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateProbableKnownMerchantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateProbableKnownMerchantRequestValidationError{}

// Validate checks the field values on CreateProbableKnownMerchantResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateProbableKnownMerchantResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateProbableKnownMerchantResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateProbableKnownMerchantResponseMultiError, or nil if none found.
func (m *CreateProbableKnownMerchantResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateProbableKnownMerchantResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateProbableKnownMerchantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateProbableKnownMerchantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateProbableKnownMerchantResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MerchantId

	if len(errors) > 0 {
		return CreateProbableKnownMerchantResponseMultiError(errors)
	}

	return nil
}

// CreateProbableKnownMerchantResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateProbableKnownMerchantResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateProbableKnownMerchantResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateProbableKnownMerchantResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateProbableKnownMerchantResponseMultiError) AllErrors() []error { return m }

// CreateProbableKnownMerchantResponseValidationError is the validation error
// returned by CreateProbableKnownMerchantResponse.Validate if the designated
// constraints aren't met.
type CreateProbableKnownMerchantResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateProbableKnownMerchantResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateProbableKnownMerchantResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateProbableKnownMerchantResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateProbableKnownMerchantResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateProbableKnownMerchantResponseValidationError) ErrorName() string {
	return "CreateProbableKnownMerchantResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateProbableKnownMerchantResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateProbableKnownMerchantResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateProbableKnownMerchantResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateProbableKnownMerchantResponseValidationError{}

// Validate checks the field values on
// GetProbableKnownMerchantsByMerchantIdsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetProbableKnownMerchantsByMerchantIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetProbableKnownMerchantsByMerchantIdsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetProbableKnownMerchantsByMerchantIdsRequestMultiError, or nil if none found.
func (m *GetProbableKnownMerchantsByMerchantIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProbableKnownMerchantsByMerchantIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetProbableKnownMerchantsByMerchantIdsRequestMultiError(errors)
	}

	return nil
}

// GetProbableKnownMerchantsByMerchantIdsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetProbableKnownMerchantsByMerchantIdsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetProbableKnownMerchantsByMerchantIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProbableKnownMerchantsByMerchantIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProbableKnownMerchantsByMerchantIdsRequestMultiError) AllErrors() []error { return m }

// GetProbableKnownMerchantsByMerchantIdsRequestValidationError is the
// validation error returned by
// GetProbableKnownMerchantsByMerchantIdsRequest.Validate if the designated
// constraints aren't met.
type GetProbableKnownMerchantsByMerchantIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProbableKnownMerchantsByMerchantIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProbableKnownMerchantsByMerchantIdsRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetProbableKnownMerchantsByMerchantIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProbableKnownMerchantsByMerchantIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProbableKnownMerchantsByMerchantIdsRequestValidationError) ErrorName() string {
	return "GetProbableKnownMerchantsByMerchantIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetProbableKnownMerchantsByMerchantIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProbableKnownMerchantsByMerchantIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProbableKnownMerchantsByMerchantIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProbableKnownMerchantsByMerchantIdsRequestValidationError{}

// Validate checks the field values on
// GetProbableKnownMerchantsByMerchantIdsResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetProbableKnownMerchantsByMerchantIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetProbableKnownMerchantsByMerchantIdsResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetProbableKnownMerchantsByMerchantIdsResponseMultiError, or nil if none found.
func (m *GetProbableKnownMerchantsByMerchantIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProbableKnownMerchantsByMerchantIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetProbableKnownMerchantsByMerchantIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetProbableKnownMerchantsByMerchantIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetProbableKnownMerchantsByMerchantIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetProbableKnownMerchant() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetProbableKnownMerchantsByMerchantIdsResponseValidationError{
						field:  fmt.Sprintf("ProbableKnownMerchant[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetProbableKnownMerchantsByMerchantIdsResponseValidationError{
						field:  fmt.Sprintf("ProbableKnownMerchant[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetProbableKnownMerchantsByMerchantIdsResponseValidationError{
					field:  fmt.Sprintf("ProbableKnownMerchant[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetProbableKnownMerchantsByMerchantIdsResponseMultiError(errors)
	}

	return nil
}

// GetProbableKnownMerchantsByMerchantIdsResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetProbableKnownMerchantsByMerchantIdsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetProbableKnownMerchantsByMerchantIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProbableKnownMerchantsByMerchantIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProbableKnownMerchantsByMerchantIdsResponseMultiError) AllErrors() []error { return m }

// GetProbableKnownMerchantsByMerchantIdsResponseValidationError is the
// validation error returned by
// GetProbableKnownMerchantsByMerchantIdsResponse.Validate if the designated
// constraints aren't met.
type GetProbableKnownMerchantsByMerchantIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProbableKnownMerchantsByMerchantIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProbableKnownMerchantsByMerchantIdsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetProbableKnownMerchantsByMerchantIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProbableKnownMerchantsByMerchantIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProbableKnownMerchantsByMerchantIdsResponseValidationError) ErrorName() string {
	return "GetProbableKnownMerchantsByMerchantIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetProbableKnownMerchantsByMerchantIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProbableKnownMerchantsByMerchantIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProbableKnownMerchantsByMerchantIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProbableKnownMerchantsByMerchantIdsResponseValidationError{}

// Validate checks the field values on
// SoftDeleteProbableKnownMerchantByMerchantIdsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SoftDeleteProbableKnownMerchantByMerchantIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SoftDeleteProbableKnownMerchantByMerchantIdsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// SoftDeleteProbableKnownMerchantByMerchantIdsRequestMultiError, or nil if
// none found.
func (m *SoftDeleteProbableKnownMerchantByMerchantIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SoftDeleteProbableKnownMerchantByMerchantIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SoftDeleteProbableKnownMerchantByMerchantIdsRequestMultiError(errors)
	}

	return nil
}

// SoftDeleteProbableKnownMerchantByMerchantIdsRequestMultiError is an error
// wrapping multiple validation errors returned by
// SoftDeleteProbableKnownMerchantByMerchantIdsRequest.ValidateAll() if the
// designated constraints aren't met.
type SoftDeleteProbableKnownMerchantByMerchantIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SoftDeleteProbableKnownMerchantByMerchantIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SoftDeleteProbableKnownMerchantByMerchantIdsRequestMultiError) AllErrors() []error { return m }

// SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError is the
// validation error returned by
// SoftDeleteProbableKnownMerchantByMerchantIdsRequest.Validate if the
// designated constraints aren't met.
type SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError) ErrorName() string {
	return "SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSoftDeleteProbableKnownMerchantByMerchantIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SoftDeleteProbableKnownMerchantByMerchantIdsRequestValidationError{}

// Validate checks the field values on
// SoftDeleteProbableKnownMerchantByMerchantIdsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SoftDeleteProbableKnownMerchantByMerchantIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SoftDeleteProbableKnownMerchantByMerchantIdsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// SoftDeleteProbableKnownMerchantByMerchantIdsResponseMultiError, or nil if
// none found.
func (m *SoftDeleteProbableKnownMerchantByMerchantIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SoftDeleteProbableKnownMerchantByMerchantIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SoftDeleteProbableKnownMerchantByMerchantIdsResponseMultiError(errors)
	}

	return nil
}

// SoftDeleteProbableKnownMerchantByMerchantIdsResponseMultiError is an error
// wrapping multiple validation errors returned by
// SoftDeleteProbableKnownMerchantByMerchantIdsResponse.ValidateAll() if the
// designated constraints aren't met.
type SoftDeleteProbableKnownMerchantByMerchantIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SoftDeleteProbableKnownMerchantByMerchantIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SoftDeleteProbableKnownMerchantByMerchantIdsResponseMultiError) AllErrors() []error { return m }

// SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError is the
// validation error returned by
// SoftDeleteProbableKnownMerchantByMerchantIdsResponse.Validate if the
// designated constraints aren't met.
type SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError) ErrorName() string {
	return "SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSoftDeleteProbableKnownMerchantByMerchantIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SoftDeleteProbableKnownMerchantByMerchantIdsResponseValidationError{}

// Validate checks the field values on GetMerchantsRequest_PiIdentifier with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetMerchantsRequest_PiIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMerchantsRequest_PiIdentifier with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetMerchantsRequest_PiIdentifierMultiError, or nil if none found.
func (m *GetMerchantsRequest_PiIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantsRequest_PiIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetMerchantsRequest_PiIdentifierMultiError(errors)
	}

	return nil
}

// GetMerchantsRequest_PiIdentifierMultiError is an error wrapping multiple
// validation errors returned by
// GetMerchantsRequest_PiIdentifier.ValidateAll() if the designated
// constraints aren't met.
type GetMerchantsRequest_PiIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantsRequest_PiIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantsRequest_PiIdentifierMultiError) AllErrors() []error { return m }

// GetMerchantsRequest_PiIdentifierValidationError is the validation error
// returned by GetMerchantsRequest_PiIdentifier.Validate if the designated
// constraints aren't met.
type GetMerchantsRequest_PiIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantsRequest_PiIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantsRequest_PiIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantsRequest_PiIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantsRequest_PiIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantsRequest_PiIdentifierValidationError) ErrorName() string {
	return "GetMerchantsRequest_PiIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantsRequest_PiIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantsRequest_PiIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantsRequest_PiIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantsRequest_PiIdentifierValidationError{}

// Validate checks the field values on GetMerchantsRequest_MerchantIdentifier
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetMerchantsRequest_MerchantIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetMerchantsRequest_MerchantIdentifier with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetMerchantsRequest_MerchantIdentifierMultiError, or nil if none found.
func (m *GetMerchantsRequest_MerchantIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantsRequest_MerchantIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetMerchantsRequest_MerchantIdentifierMultiError(errors)
	}

	return nil
}

// GetMerchantsRequest_MerchantIdentifierMultiError is an error wrapping
// multiple validation errors returned by
// GetMerchantsRequest_MerchantIdentifier.ValidateAll() if the designated
// constraints aren't met.
type GetMerchantsRequest_MerchantIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantsRequest_MerchantIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantsRequest_MerchantIdentifierMultiError) AllErrors() []error { return m }

// GetMerchantsRequest_MerchantIdentifierValidationError is the validation
// error returned by GetMerchantsRequest_MerchantIdentifier.Validate if the
// designated constraints aren't met.
type GetMerchantsRequest_MerchantIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantsRequest_MerchantIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantsRequest_MerchantIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantsRequest_MerchantIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantsRequest_MerchantIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantsRequest_MerchantIdentifierValidationError) ErrorName() string {
	return "GetMerchantsRequest_MerchantIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantsRequest_MerchantIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantsRequest_MerchantIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantsRequest_MerchantIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantsRequest_MerchantIdentifierValidationError{}

// Validate checks the field values on
// GetActorIdForKnownMerchantsResponse_KnownMerchantActors with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorIdForKnownMerchantsResponse_KnownMerchantActors) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetActorIdForKnownMerchantsResponse_KnownMerchantActors with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorIdForKnownMerchantsResponse_KnownMerchantActorsMultiError, or nil
// if none found.
func (m *GetActorIdForKnownMerchantsResponse_KnownMerchantActors) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorIdForKnownMerchantsResponse_KnownMerchantActors) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Merchant

	if len(errors) > 0 {
		return GetActorIdForKnownMerchantsResponse_KnownMerchantActorsMultiError(errors)
	}

	return nil
}

// GetActorIdForKnownMerchantsResponse_KnownMerchantActorsMultiError is an
// error wrapping multiple validation errors returned by
// GetActorIdForKnownMerchantsResponse_KnownMerchantActors.ValidateAll() if
// the designated constraints aren't met.
type GetActorIdForKnownMerchantsResponse_KnownMerchantActorsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorIdForKnownMerchantsResponse_KnownMerchantActorsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorIdForKnownMerchantsResponse_KnownMerchantActorsMultiError) AllErrors() []error {
	return m
}

// GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError is
// the validation error returned by
// GetActorIdForKnownMerchantsResponse_KnownMerchantActors.Validate if the
// designated constraints aren't met.
type GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError) ErrorName() string {
	return "GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorIdForKnownMerchantsResponse_KnownMerchantActors.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorIdForKnownMerchantsResponse_KnownMerchantActorsValidationError{}

// Validate checks the field values on
// GetIdForKnownMerchantsResponse_KnownMerchantIds with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetIdForKnownMerchantsResponse_KnownMerchantIds) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetIdForKnownMerchantsResponse_KnownMerchantIds with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetIdForKnownMerchantsResponse_KnownMerchantIdsMultiError, or nil if none found.
func (m *GetIdForKnownMerchantsResponse_KnownMerchantIds) ValidateAll() error {
	return m.validate(true)
}

func (m *GetIdForKnownMerchantsResponse_KnownMerchantIds) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Merchant

	if len(errors) > 0 {
		return GetIdForKnownMerchantsResponse_KnownMerchantIdsMultiError(errors)
	}

	return nil
}

// GetIdForKnownMerchantsResponse_KnownMerchantIdsMultiError is an error
// wrapping multiple validation errors returned by
// GetIdForKnownMerchantsResponse_KnownMerchantIds.ValidateAll() if the
// designated constraints aren't met.
type GetIdForKnownMerchantsResponse_KnownMerchantIdsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetIdForKnownMerchantsResponse_KnownMerchantIdsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetIdForKnownMerchantsResponse_KnownMerchantIdsMultiError) AllErrors() []error { return m }

// GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError is the
// validation error returned by
// GetIdForKnownMerchantsResponse_KnownMerchantIds.Validate if the designated
// constraints aren't met.
type GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError) ErrorName() string {
	return "GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError"
}

// Error satisfies the builtin error interface
func (e GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetIdForKnownMerchantsResponse_KnownMerchantIds.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetIdForKnownMerchantsResponse_KnownMerchantIdsValidationError{}

// Validate checks the field values on
// GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantId with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantId with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdMultiError, or nil
// if none found.
func (m *GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantId) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OldMerchantId

	// no validation rules for MerchantId

	if len(errors) > 0 {
		return GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdMultiError(errors)
	}

	return nil
}

// GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdMultiError is an
// error wrapping multiple validation errors returned by
// GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantId.ValidateAll() if
// the designated constraints aren't met.
type GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdMultiError) AllErrors() []error {
	return m
}

// GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError is
// the validation error returned by
// GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantId.Validate if the
// designated constraints aren't met.
type GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError) ErrorName() string {
	return "GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantIdValidationError{}
