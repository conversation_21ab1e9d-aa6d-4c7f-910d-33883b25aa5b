// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/salaryprogram/cx/consumer
package consumer

import (
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessOrderEventForOpsSalaryVerEligibilityRefreshMethod = "ProcessOrderEventForOpsSalaryVerEligibilityRefresh"
	ProcessSalaryDetectionEventMethod                        = "ProcessSalaryDetectionEvent"
	ProcessEmployerPiMappingUpdateEventMethod                = "ProcessEmployerPiMappingUpdateEvent"
)

// RegisterProcessOrderEventForOpsSalaryVerEligibilityRefreshMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessOrderEventForOpsSalaryVerEligibilityRefreshMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessOrderEventForOpsSalaryVerEligibilityRefreshMethod)
}

// RegisterProcessSalaryDetectionEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessSalaryDetectionEventMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessSalaryDetectionEventMethod)
}

// RegisterProcessEmployerPiMappingUpdateEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessEmployerPiMappingUpdateEventMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessEmployerPiMappingUpdateEventMethod)
}
