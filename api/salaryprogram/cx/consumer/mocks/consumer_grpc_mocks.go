// Code generated by MockGen. DO NOT EDIT.
// Source: api/salaryprogram/cx/consumer/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	event "github.com/epifi/gamma/api/employment/event"
	order "github.com/epifi/gamma/api/order"
	consumer "github.com/epifi/gamma/api/salaryprogram/cx/consumer"
	events "github.com/epifi/gamma/api/salaryprogram/events"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockConsumerClient is a mock of ConsumerClient interface.
type MockConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerClientMockRecorder
}

// MockConsumerClientMockRecorder is the mock recorder for MockConsumerClient.
type MockConsumerClientMockRecorder struct {
	mock *MockConsumerClient
}

// NewMockConsumerClient creates a new mock instance.
func NewMockConsumerClient(ctrl *gomock.Controller) *MockConsumerClient {
	mock := &MockConsumerClient{ctrl: ctrl}
	mock.recorder = &MockConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerClient) EXPECT() *MockConsumerClientMockRecorder {
	return m.recorder
}

// ProcessEmployerPiMappingUpdateEvent mocks base method.
func (m *MockConsumerClient) ProcessEmployerPiMappingUpdateEvent(ctx context.Context, in *event.EmployerPiMappingUpdateEvent, opts ...grpc.CallOption) (*consumer.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessEmployerPiMappingUpdateEvent", varargs...)
	ret0, _ := ret[0].(*consumer.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessEmployerPiMappingUpdateEvent indicates an expected call of ProcessEmployerPiMappingUpdateEvent.
func (mr *MockConsumerClientMockRecorder) ProcessEmployerPiMappingUpdateEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessEmployerPiMappingUpdateEvent", reflect.TypeOf((*MockConsumerClient)(nil).ProcessEmployerPiMappingUpdateEvent), varargs...)
}

// ProcessOrderEventForOpsSalaryVerEligibilityRefresh mocks base method.
func (m *MockConsumerClient) ProcessOrderEventForOpsSalaryVerEligibilityRefresh(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*consumer.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessOrderEventForOpsSalaryVerEligibilityRefresh", varargs...)
	ret0, _ := ret[0].(*consumer.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessOrderEventForOpsSalaryVerEligibilityRefresh indicates an expected call of ProcessOrderEventForOpsSalaryVerEligibilityRefresh.
func (mr *MockConsumerClientMockRecorder) ProcessOrderEventForOpsSalaryVerEligibilityRefresh(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOrderEventForOpsSalaryVerEligibilityRefresh", reflect.TypeOf((*MockConsumerClient)(nil).ProcessOrderEventForOpsSalaryVerEligibilityRefresh), varargs...)
}

// ProcessSalaryDetectionEvent mocks base method.
func (m *MockConsumerClient) ProcessSalaryDetectionEvent(ctx context.Context, in *events.SalaryDetectionEvent, opts ...grpc.CallOption) (*consumer.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessSalaryDetectionEvent", varargs...)
	ret0, _ := ret[0].(*consumer.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSalaryDetectionEvent indicates an expected call of ProcessSalaryDetectionEvent.
func (mr *MockConsumerClientMockRecorder) ProcessSalaryDetectionEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSalaryDetectionEvent", reflect.TypeOf((*MockConsumerClient)(nil).ProcessSalaryDetectionEvent), varargs...)
}

// MockConsumerServer is a mock of ConsumerServer interface.
type MockConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerServerMockRecorder
}

// MockConsumerServerMockRecorder is the mock recorder for MockConsumerServer.
type MockConsumerServerMockRecorder struct {
	mock *MockConsumerServer
}

// NewMockConsumerServer creates a new mock instance.
func NewMockConsumerServer(ctrl *gomock.Controller) *MockConsumerServer {
	mock := &MockConsumerServer{ctrl: ctrl}
	mock.recorder = &MockConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerServer) EXPECT() *MockConsumerServerMockRecorder {
	return m.recorder
}

// ProcessEmployerPiMappingUpdateEvent mocks base method.
func (m *MockConsumerServer) ProcessEmployerPiMappingUpdateEvent(arg0 context.Context, arg1 *event.EmployerPiMappingUpdateEvent) (*consumer.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessEmployerPiMappingUpdateEvent", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessEmployerPiMappingUpdateEvent indicates an expected call of ProcessEmployerPiMappingUpdateEvent.
func (mr *MockConsumerServerMockRecorder) ProcessEmployerPiMappingUpdateEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessEmployerPiMappingUpdateEvent", reflect.TypeOf((*MockConsumerServer)(nil).ProcessEmployerPiMappingUpdateEvent), arg0, arg1)
}

// ProcessOrderEventForOpsSalaryVerEligibilityRefresh mocks base method.
func (m *MockConsumerServer) ProcessOrderEventForOpsSalaryVerEligibilityRefresh(arg0 context.Context, arg1 *order.OrderUpdate) (*consumer.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessOrderEventForOpsSalaryVerEligibilityRefresh", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessOrderEventForOpsSalaryVerEligibilityRefresh indicates an expected call of ProcessOrderEventForOpsSalaryVerEligibilityRefresh.
func (mr *MockConsumerServerMockRecorder) ProcessOrderEventForOpsSalaryVerEligibilityRefresh(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOrderEventForOpsSalaryVerEligibilityRefresh", reflect.TypeOf((*MockConsumerServer)(nil).ProcessOrderEventForOpsSalaryVerEligibilityRefresh), arg0, arg1)
}

// ProcessSalaryDetectionEvent mocks base method.
func (m *MockConsumerServer) ProcessSalaryDetectionEvent(arg0 context.Context, arg1 *events.SalaryDetectionEvent) (*consumer.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessSalaryDetectionEvent", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSalaryDetectionEvent indicates an expected call of ProcessSalaryDetectionEvent.
func (mr *MockConsumerServerMockRecorder) ProcessSalaryDetectionEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSalaryDetectionEvent", reflect.TypeOf((*MockConsumerServer)(nil).ProcessSalaryDetectionEvent), arg0, arg1)
}

// MockUnsafeConsumerServer is a mock of UnsafeConsumerServer interface.
type MockUnsafeConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeConsumerServerMockRecorder
}

// MockUnsafeConsumerServerMockRecorder is the mock recorder for MockUnsafeConsumerServer.
type MockUnsafeConsumerServerMockRecorder struct {
	mock *MockUnsafeConsumerServer
}

// NewMockUnsafeConsumerServer creates a new mock instance.
func NewMockUnsafeConsumerServer(ctrl *gomock.Controller) *MockUnsafeConsumerServer {
	mock := &MockUnsafeConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeConsumerServer) EXPECT() *MockUnsafeConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedConsumerServer mocks base method.
func (m *MockUnsafeConsumerServer) mustEmbedUnimplementedConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedConsumerServer")
}

// mustEmbedUnimplementedConsumerServer indicates an expected call of mustEmbedUnimplementedConsumerServer.
func (mr *MockUnsafeConsumerServerMockRecorder) mustEmbedUnimplementedConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedConsumerServer", reflect.TypeOf((*MockUnsafeConsumerServer)(nil).mustEmbedUnimplementedConsumerServer))
}
