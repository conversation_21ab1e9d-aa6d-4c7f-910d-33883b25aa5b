// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/vendors/finflux/update_client.proto

package finflux

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateClientRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateClientRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateClientRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateClientRequestMultiError, or nil if none found.
func (m *UpdateClientRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateClientRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Locale

	// no validation rules for DateFormat

	if all {
		switch v := interface{}(m.GetClient()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateClientRequestValidationError{
					field:  "Client",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateClientRequestValidationError{
					field:  "Client",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClient()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateClientRequestValidationError{
				field:  "Client",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAddresses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateClientRequestValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateClientRequestValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateClientRequestValidationError{
					field:  fmt.Sprintf("Addresses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFamilyMembers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateClientRequestValidationError{
						field:  fmt.Sprintf("FamilyMembers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateClientRequestValidationError{
						field:  fmt.Sprintf("FamilyMembers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateClientRequestValidationError{
					field:  fmt.Sprintf("FamilyMembers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateClientRequestValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateClientRequestValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateClientRequestValidationError{
					field:  fmt.Sprintf("Identifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetBankAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateClientRequestValidationError{
						field:  fmt.Sprintf("BankAccounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateClientRequestValidationError{
						field:  fmt.Sprintf("BankAccounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateClientRequestValidationError{
					field:  fmt.Sprintf("BankAccounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetEmployment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateClientRequestValidationError{
					field:  "Employment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateClientRequestValidationError{
					field:  "Employment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmployment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateClientRequestValidationError{
				field:  "Employment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateClientRequestMultiError(errors)
	}

	return nil
}

// UpdateClientRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateClientRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateClientRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateClientRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateClientRequestMultiError) AllErrors() []error { return m }

// UpdateClientRequestValidationError is the validation error returned by
// UpdateClientRequest.Validate if the designated constraints aren't met.
type UpdateClientRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateClientRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateClientRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateClientRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateClientRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateClientRequestValidationError) ErrorName() string {
	return "UpdateClientRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateClientRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateClientRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateClientRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateClientRequestValidationError{}

// Validate checks the field values on UpdateClientResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateClientResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateClientResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateClientResponseMultiError, or nil if none found.
func (m *UpdateClientResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateClientResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientId

	if len(errors) > 0 {
		return UpdateClientResponseMultiError(errors)
	}

	return nil
}

// UpdateClientResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateClientResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateClientResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateClientResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateClientResponseMultiError) AllErrors() []error { return m }

// UpdateClientResponseValidationError is the validation error returned by
// UpdateClientResponse.Validate if the designated constraints aren't met.
type UpdateClientResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateClientResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateClientResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateClientResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateClientResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateClientResponseValidationError) ErrorName() string {
	return "UpdateClientResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateClientResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateClientResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateClientResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateClientResponseValidationError{}
