// Code generated by MockGen. DO NOT EDIT.
// Source: api/stockguardian/pan/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	pan "github.com/epifi/gamma/api/stockguardian/pan"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPanClient is a mock of PanClient interface.
type MockPanClient struct {
	ctrl     *gomock.Controller
	recorder *MockPanClientMockRecorder
}

// MockPanClientMockRecorder is the mock recorder for MockPanClient.
type MockPanClientMockRecorder struct {
	mock *MockPanClient
}

// NewMockPanClient creates a new mock instance.
func NewMockPanClient(ctrl *gomock.Controller) *MockPanClient {
	mock := &MockPanClient{ctrl: ctrl}
	mock.recorder = &MockPanClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPanClient) EXPECT() *MockPanClientMockRecorder {
	return m.recorder
}

// ValidatePAN mocks base method.
func (m *MockPanClient) ValidatePAN(ctx context.Context, in *pan.ValidatePANRequest, opts ...grpc.CallOption) (*pan.ValidatePANResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidatePAN", varargs...)
	ret0, _ := ret[0].(*pan.ValidatePANResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidatePAN indicates an expected call of ValidatePAN.
func (mr *MockPanClientMockRecorder) ValidatePAN(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidatePAN", reflect.TypeOf((*MockPanClient)(nil).ValidatePAN), varargs...)
}

// MockPanServer is a mock of PanServer interface.
type MockPanServer struct {
	ctrl     *gomock.Controller
	recorder *MockPanServerMockRecorder
}

// MockPanServerMockRecorder is the mock recorder for MockPanServer.
type MockPanServerMockRecorder struct {
	mock *MockPanServer
}

// NewMockPanServer creates a new mock instance.
func NewMockPanServer(ctrl *gomock.Controller) *MockPanServer {
	mock := &MockPanServer{ctrl: ctrl}
	mock.recorder = &MockPanServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPanServer) EXPECT() *MockPanServerMockRecorder {
	return m.recorder
}

// ValidatePAN mocks base method.
func (m *MockPanServer) ValidatePAN(arg0 context.Context, arg1 *pan.ValidatePANRequest) (*pan.ValidatePANResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidatePAN", arg0, arg1)
	ret0, _ := ret[0].(*pan.ValidatePANResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidatePAN indicates an expected call of ValidatePAN.
func (mr *MockPanServerMockRecorder) ValidatePAN(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidatePAN", reflect.TypeOf((*MockPanServer)(nil).ValidatePAN), arg0, arg1)
}

// MockUnsafePanServer is a mock of UnsafePanServer interface.
type MockUnsafePanServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafePanServerMockRecorder
}

// MockUnsafePanServerMockRecorder is the mock recorder for MockUnsafePanServer.
type MockUnsafePanServerMockRecorder struct {
	mock *MockUnsafePanServer
}

// NewMockUnsafePanServer creates a new mock instance.
func NewMockUnsafePanServer(ctrl *gomock.Controller) *MockUnsafePanServer {
	mock := &MockUnsafePanServer{ctrl: ctrl}
	mock.recorder = &MockUnsafePanServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafePanServer) EXPECT() *MockUnsafePanServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedPanServer mocks base method.
func (m *MockUnsafePanServer) mustEmbedUnimplementedPanServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPanServer")
}

// mustEmbedUnimplementedPanServer indicates an expected call of mustEmbedUnimplementedPanServer.
func (mr *MockUnsafePanServerMockRecorder) mustEmbedUnimplementedPanServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPanServer", reflect.TypeOf((*MockUnsafePanServer)(nil).mustEmbedUnimplementedPanServer))
}
