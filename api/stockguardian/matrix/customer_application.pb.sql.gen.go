// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gringott/api/stockguardian/matrix/customer_application.pb.go

package matrix

import (
	"database/sql/driver"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing OverrideOptions while reading from DB
func (a *OverrideOptions) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the OverrideOptions in string format in DB
func (a *OverrideOptions) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for OverrideOptions
func (a *OverrideOptions) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for OverrideOptions
func (a *OverrideOptions) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing ApplicationData while reading from DB
func (a *ApplicationData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the ApplicationData in string format in DB
func (a *ApplicationData) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ApplicationData
func (a *ApplicationData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ApplicationData
func (a *ApplicationData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing OrchestrationFlowData while reading from DB
func (a *OrchestrationFlowData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the OrchestrationFlowData in string format in DB
func (a *OrchestrationFlowData) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for OrchestrationFlowData
func (a *OrchestrationFlowData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for OrchestrationFlowData
func (a *OrchestrationFlowData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
