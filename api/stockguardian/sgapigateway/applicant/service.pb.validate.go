// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/sgapigateway/applicant/service.proto

package applicant

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetApplicantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetApplicantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApplicantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetApplicantRequestMultiError, or nil if none found.
func (m *GetApplicantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApplicantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApplicantRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApplicantRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApplicantRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetApplicantRequestMultiError(errors)
	}

	return nil
}

// GetApplicantRequestMultiError is an error wrapping multiple validation
// errors returned by GetApplicantRequest.ValidateAll() if the designated
// constraints aren't met.
type GetApplicantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApplicantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApplicantRequestMultiError) AllErrors() []error { return m }

// GetApplicantRequestValidationError is the validation error returned by
// GetApplicantRequest.Validate if the designated constraints aren't met.
type GetApplicantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApplicantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApplicantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApplicantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApplicantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApplicantRequestValidationError) ErrorName() string {
	return "GetApplicantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetApplicantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApplicantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApplicantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApplicantRequestValidationError{}

// Validate checks the field values on ApplicantIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicantIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicantIdentifierMultiError, or nil if none found.
func (m *ApplicantIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *ApplicantIdentifier_PrimaryDetails_:
		if v == nil {
			err := ApplicantIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPrimaryDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApplicantIdentifierValidationError{
						field:  "PrimaryDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApplicantIdentifierValidationError{
						field:  "PrimaryDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrimaryDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApplicantIdentifierValidationError{
					field:  "PrimaryDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ApplicantIdentifier_ApplicantId:
		if v == nil {
			err := ApplicantIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ApplicantId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ApplicantIdentifierMultiError(errors)
	}

	return nil
}

// ApplicantIdentifierMultiError is an error wrapping multiple validation
// errors returned by ApplicantIdentifier.ValidateAll() if the designated
// constraints aren't met.
type ApplicantIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantIdentifierMultiError) AllErrors() []error { return m }

// ApplicantIdentifierValidationError is the validation error returned by
// ApplicantIdentifier.Validate if the designated constraints aren't met.
type ApplicantIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantIdentifierValidationError) ErrorName() string {
	return "ApplicantIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantIdentifierValidationError{}

// Validate checks the field values on GetApplicantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetApplicantResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApplicantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetApplicantResponseMultiError, or nil if none found.
func (m *GetApplicantResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApplicantResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApplicantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApplicantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApplicantResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApplicant()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApplicantResponseValidationError{
					field:  "Applicant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApplicantResponseValidationError{
					field:  "Applicant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplicant()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApplicantResponseValidationError{
				field:  "Applicant",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetApplicantResponseMultiError(errors)
	}

	return nil
}

// GetApplicantResponseMultiError is an error wrapping multiple validation
// errors returned by GetApplicantResponse.ValidateAll() if the designated
// constraints aren't met.
type GetApplicantResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApplicantResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApplicantResponseMultiError) AllErrors() []error { return m }

// GetApplicantResponseValidationError is the validation error returned by
// GetApplicantResponse.Validate if the designated constraints aren't met.
type GetApplicantResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApplicantResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApplicantResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApplicantResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApplicantResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApplicantResponseValidationError) ErrorName() string {
	return "GetApplicantResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetApplicantResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApplicantResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApplicantResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApplicantResponseValidationError{}

// Validate checks the field values on CreateApplicantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateApplicantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateApplicantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateApplicantRequestMultiError, or nil if none found.
func (m *CreateApplicantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateApplicantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	// no validation rules for PAN

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateApplicantRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateApplicantRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateApplicantRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateApplicantRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateApplicantRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateApplicantRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if len(errors) > 0 {
		return CreateApplicantRequestMultiError(errors)
	}

	return nil
}

// CreateApplicantRequestMultiError is an error wrapping multiple validation
// errors returned by CreateApplicantRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateApplicantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateApplicantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateApplicantRequestMultiError) AllErrors() []error { return m }

// CreateApplicantRequestValidationError is the validation error returned by
// CreateApplicantRequest.Validate if the designated constraints aren't met.
type CreateApplicantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateApplicantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateApplicantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateApplicantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateApplicantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateApplicantRequestValidationError) ErrorName() string {
	return "CreateApplicantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateApplicantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateApplicantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateApplicantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateApplicantRequestValidationError{}

// Validate checks the field values on CreateApplicantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateApplicantResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateApplicantResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateApplicantResponseMultiError, or nil if none found.
func (m *CreateApplicantResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateApplicantResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateApplicantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateApplicantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateApplicantResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApplicant()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateApplicantResponseValidationError{
					field:  "Applicant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateApplicantResponseValidationError{
					field:  "Applicant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplicant()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateApplicantResponseValidationError{
				field:  "Applicant",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateApplicantResponseMultiError(errors)
	}

	return nil
}

// CreateApplicantResponseMultiError is an error wrapping multiple validation
// errors returned by CreateApplicantResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateApplicantResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateApplicantResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateApplicantResponseMultiError) AllErrors() []error { return m }

// CreateApplicantResponseValidationError is the validation error returned by
// CreateApplicantResponse.Validate if the designated constraints aren't met.
type CreateApplicantResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateApplicantResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateApplicantResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateApplicantResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateApplicantResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateApplicantResponseValidationError) ErrorName() string {
	return "CreateApplicantResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateApplicantResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateApplicantResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateApplicantResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateApplicantResponseValidationError{}

// Validate checks the field values on Applicant with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Applicant) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Applicant with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ApplicantMultiError, or nil
// if none found.
func (m *Applicant) ValidateAll() error {
	return m.validate(true)
}

func (m *Applicant) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ClientRequestId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PAN

	if len(errors) > 0 {
		return ApplicantMultiError(errors)
	}

	return nil
}

// ApplicantMultiError is an error wrapping multiple validation errors returned
// by Applicant.ValidateAll() if the designated constraints aren't met.
type ApplicantMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantMultiError) AllErrors() []error { return m }

// ApplicantValidationError is the validation error returned by
// Applicant.Validate if the designated constraints aren't met.
type ApplicantValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantValidationError) ErrorName() string { return "ApplicantValidationError" }

// Error satisfies the builtin error interface
func (e ApplicantValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicant.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantValidationError{}

// Validate checks the field values on ApplicantIdentifier_PrimaryDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ApplicantIdentifier_PrimaryDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantIdentifier_PrimaryDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ApplicantIdentifier_PrimaryDetailsMultiError, or nil if none found.
func (m *ApplicantIdentifier_PrimaryDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantIdentifier_PrimaryDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PAN

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantIdentifier_PrimaryDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantIdentifier_PrimaryDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantIdentifier_PrimaryDetailsValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplicantIdentifier_PrimaryDetailsMultiError(errors)
	}

	return nil
}

// ApplicantIdentifier_PrimaryDetailsMultiError is an error wrapping multiple
// validation errors returned by
// ApplicantIdentifier_PrimaryDetails.ValidateAll() if the designated
// constraints aren't met.
type ApplicantIdentifier_PrimaryDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantIdentifier_PrimaryDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantIdentifier_PrimaryDetailsMultiError) AllErrors() []error { return m }

// ApplicantIdentifier_PrimaryDetailsValidationError is the validation error
// returned by ApplicantIdentifier_PrimaryDetails.Validate if the designated
// constraints aren't met.
type ApplicantIdentifier_PrimaryDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantIdentifier_PrimaryDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantIdentifier_PrimaryDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantIdentifier_PrimaryDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantIdentifier_PrimaryDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantIdentifier_PrimaryDetailsValidationError) ErrorName() string {
	return "ApplicantIdentifier_PrimaryDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantIdentifier_PrimaryDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantIdentifier_PrimaryDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantIdentifier_PrimaryDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantIdentifier_PrimaryDetailsValidationError{}
