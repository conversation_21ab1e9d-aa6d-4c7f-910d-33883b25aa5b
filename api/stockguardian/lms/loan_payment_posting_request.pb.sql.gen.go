// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gringott/api/stockguardian/lms/loan_payment_posting_request.pb.go

package lms

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the LoanPaymentPostingRequestStatus in string format in DB
func (p LoanPaymentPostingRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanPaymentPostingRequestStatus while reading from DB
func (p *LoanPaymentPostingRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanPaymentPostingRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanPaymentPostingRequestStatus value: %s", val)
	}
	*p = LoanPaymentPostingRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanPaymentPostingRequestStatus
func (x LoanPaymentPostingRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanPaymentPostingRequestStatus
func (x *LoanPaymentPostingRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanPaymentPostingRequestStatus(LoanPaymentPostingRequestStatus_value[val])
	return nil
}

// Scanner interface implementation for parsing LoanPaymentPostingRequest while reading from DB
func (a *LoanPaymentPostingRequest) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the LoanPaymentPostingRequest in string format in DB
func (a *LoanPaymentPostingRequest) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for LoanPaymentPostingRequest
func (a *LoanPaymentPostingRequest) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for LoanPaymentPostingRequest
func (a *LoanPaymentPostingRequest) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
