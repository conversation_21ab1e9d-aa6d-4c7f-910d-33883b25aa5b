// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/segment/service.proto

package segment

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetUsersInSegmentOnDateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentId string     `protobuf:"bytes,1,opt,name=segment_id,json=segmentId,proto3" json:"segment_id,omitempty"`
	Date      *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	ActorId   string     `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetUsersInSegmentOnDateRequest) Reset() {
	*x = GetUsersInSegmentOnDateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersInSegmentOnDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersInSegmentOnDateRequest) ProtoMessage() {}

func (x *GetUsersInSegmentOnDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersInSegmentOnDateRequest.ProtoReflect.Descriptor instead.
func (*GetUsersInSegmentOnDateRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetUsersInSegmentOnDateRequest) GetSegmentId() string {
	if x != nil {
		return x.SegmentId
	}
	return ""
}

func (x *GetUsersInSegmentOnDateRequest) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetUsersInSegmentOnDateRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetUsersInSegmentOnDateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of actors belonging to the segment on the given date
	ActorIds []string `protobuf:"bytes,2,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
}

func (x *GetUsersInSegmentOnDateResponse) Reset() {
	*x = GetUsersInSegmentOnDateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersInSegmentOnDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersInSegmentOnDateResponse) ProtoMessage() {}

func (x *GetUsersInSegmentOnDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersInSegmentOnDateResponse.ProtoReflect.Descriptor instead.
func (*GetUsersInSegmentOnDateResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetUsersInSegmentOnDateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUsersInSegmentOnDateResponse) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

type GetSegmentEntryTimeForUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId   string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	SegmentId string `protobuf:"bytes,2,opt,name=segment_id,json=segmentId,proto3" json:"segment_id,omitempty"`
}

func (x *GetSegmentEntryTimeForUserRequest) Reset() {
	*x = GetSegmentEntryTimeForUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentEntryTimeForUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentEntryTimeForUserRequest) ProtoMessage() {}

func (x *GetSegmentEntryTimeForUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentEntryTimeForUserRequest.ProtoReflect.Descriptor instead.
func (*GetSegmentEntryTimeForUserRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetSegmentEntryTimeForUserRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetSegmentEntryTimeForUserRequest) GetSegmentId() string {
	if x != nil {
		return x.SegmentId
	}
	return ""
}

type GetSegmentEntryTimeForUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status    *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	EntryTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=entry_time,json=entryTime,proto3" json:"entry_time,omitempty"`
}

func (x *GetSegmentEntryTimeForUserResponse) Reset() {
	*x = GetSegmentEntryTimeForUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentEntryTimeForUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentEntryTimeForUserResponse) ProtoMessage() {}

func (x *GetSegmentEntryTimeForUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentEntryTimeForUserResponse.ProtoReflect.Descriptor instead.
func (*GetSegmentEntryTimeForUserResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetSegmentEntryTimeForUserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSegmentEntryTimeForUserResponse) GetEntryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EntryTime
	}
	return nil
}

type CreateOrGetSegmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the provider where segment is created
	ProviderName      SegmentProvider `protobuf:"varint,1,opt,name=provider_name,json=providerName,proto3,enum=segment.SegmentProvider" json:"provider_name,omitempty"`
	ProviderSegmentId string          `protobuf:"bytes,2,opt,name=provider_segment_id,json=providerSegmentId,proto3" json:"provider_segment_id,omitempty"`
	SegmentType       SegmentType     `protobuf:"varint,3,opt,name=segment_type,json=segmentType,proto3,enum=segment.SegmentType" json:"segment_type,omitempty"`
	ExportTill        string          `protobuf:"bytes,4,opt,name=export_till,json=exportTill,proto3" json:"export_till,omitempty"`
	// oneof field to send provider specific metadata
	ProviderSegmentMetadata *ProviderSegmentMetadata `protobuf:"bytes,5,opt,name=provider_segment_metadata,json=providerSegmentMetadata,proto3" json:"provider_segment_metadata,omitempty"`
	// Segment details like query used to generate the segment
	SegmentDetails *SegmentDetails `protobuf:"bytes,6,opt,name=segment_details,json=segmentDetails,proto3" json:"segment_details,omitempty"`
}

func (x *CreateOrGetSegmentRequest) Reset() {
	*x = CreateOrGetSegmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrGetSegmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrGetSegmentRequest) ProtoMessage() {}

func (x *CreateOrGetSegmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrGetSegmentRequest.ProtoReflect.Descriptor instead.
func (*CreateOrGetSegmentRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateOrGetSegmentRequest) GetProviderName() SegmentProvider {
	if x != nil {
		return x.ProviderName
	}
	return SegmentProvider_SEGMENT_PROVIDER_UNSPECIFIED
}

func (x *CreateOrGetSegmentRequest) GetProviderSegmentId() string {
	if x != nil {
		return x.ProviderSegmentId
	}
	return ""
}

func (x *CreateOrGetSegmentRequest) GetSegmentType() SegmentType {
	if x != nil {
		return x.SegmentType
	}
	return SegmentType_SEGMENT_TYPE_UNSPECIFIED
}

func (x *CreateOrGetSegmentRequest) GetExportTill() string {
	if x != nil {
		return x.ExportTill
	}
	return ""
}

func (x *CreateOrGetSegmentRequest) GetProviderSegmentMetadata() *ProviderSegmentMetadata {
	if x != nil {
		return x.ProviderSegmentMetadata
	}
	return nil
}

func (x *CreateOrGetSegmentRequest) GetSegmentDetails() *SegmentDetails {
	if x != nil {
		return x.SegmentDetails
	}
	return nil
}

type CreateOrGetSegmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// segment id mapped to provider segment
	SegmentId string `protobuf:"bytes,2,opt,name=segment_id,json=segmentId,proto3" json:"segment_id,omitempty"`
	//segment details present in PGDB
	Segment *Segment `protobuf:"bytes,3,opt,name=segment,proto3" json:"segment,omitempty"`
}

func (x *CreateOrGetSegmentResponse) Reset() {
	*x = CreateOrGetSegmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrGetSegmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrGetSegmentResponse) ProtoMessage() {}

func (x *CreateOrGetSegmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrGetSegmentResponse.ProtoReflect.Descriptor instead.
func (*CreateOrGetSegmentResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateOrGetSegmentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateOrGetSegmentResponse) GetSegmentId() string {
	if x != nil {
		return x.SegmentId
	}
	return ""
}

func (x *CreateOrGetSegmentResponse) GetSegment() *Segment {
	if x != nil {
		return x.Segment
	}
	return nil
}

type IsMemberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id to check membership
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// list of segments where actor membership is to be checked
	SegmentIds []string `protobuf:"bytes,2,rep,name=segment_ids,json=segmentIds,proto3" json:"segment_ids,omitempty"`
	// to check membership at latest timestamp in db before given timestamp
	// If no timestamp is passed, membership is checked before current timestamp
	LatestBy *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=latest_by,json=latestBy,proto3" json:"latest_by,omitempty"`
}

func (x *IsMemberRequest) Reset() {
	*x = IsMemberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsMemberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsMemberRequest) ProtoMessage() {}

func (x *IsMemberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsMemberRequest.ProtoReflect.Descriptor instead.
func (*IsMemberRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{6}
}

func (x *IsMemberRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *IsMemberRequest) GetSegmentIds() []string {
	if x != nil {
		return x.SegmentIds
	}
	return nil
}

func (x *IsMemberRequest) GetLatestBy() *timestamppb.Timestamp {
	if x != nil {
		return x.LatestBy
	}
	return nil
}

type IsMemberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// map of segmentId to segment membership to return actor membership
	SegmentMembershipMap map[string]*SegmentMembership `protobuf:"bytes,2,rep,name=segment_membership_map,json=segmentMembershipMap,proto3" json:"segment_membership_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *IsMemberResponse) Reset() {
	*x = IsMemberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsMemberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsMemberResponse) ProtoMessage() {}

func (x *IsMemberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsMemberResponse.ProtoReflect.Descriptor instead.
func (*IsMemberResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{7}
}

func (x *IsMemberResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsMemberResponse) GetSegmentMembershipMap() map[string]*SegmentMembership {
	if x != nil {
		return x.SegmentMembershipMap
	}
	return nil
}

type IsMemberOfExpressionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id to check membership
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// list of logical expressions of segment ids where actor membership is to be checked
	SegmentIdExpressions []string `protobuf:"bytes,2,rep,name=segment_id_expressions,json=segmentIdExpressions,proto3" json:"segment_id_expressions,omitempty"`
	// to check membership at latest timestamp in db before given timestamp
	// If no timestamp is passed, membership is checked before current timestamp
	LatestBy *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=latest_by,json=latestBy,proto3" json:"latest_by,omitempty"`
}

func (x *IsMemberOfExpressionsRequest) Reset() {
	*x = IsMemberOfExpressionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsMemberOfExpressionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsMemberOfExpressionsRequest) ProtoMessage() {}

func (x *IsMemberOfExpressionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsMemberOfExpressionsRequest.ProtoReflect.Descriptor instead.
func (*IsMemberOfExpressionsRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{8}
}

func (x *IsMemberOfExpressionsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *IsMemberOfExpressionsRequest) GetSegmentIdExpressions() []string {
	if x != nil {
		return x.SegmentIdExpressions
	}
	return nil
}

func (x *IsMemberOfExpressionsRequest) GetLatestBy() *timestamppb.Timestamp {
	if x != nil {
		return x.LatestBy
	}
	return nil
}

type IsMemberOfExpressionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// map of segmentId expressions to segment membership to return actor membership
	SegmentExpressionMembershipMap map[string]*SegmentExpressionMembership `protobuf:"bytes,2,rep,name=segment_expression_membership_map,json=segmentExpressionMembershipMap,proto3" json:"segment_expression_membership_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *IsMemberOfExpressionsResponse) Reset() {
	*x = IsMemberOfExpressionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsMemberOfExpressionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsMemberOfExpressionsResponse) ProtoMessage() {}

func (x *IsMemberOfExpressionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsMemberOfExpressionsResponse.ProtoReflect.Descriptor instead.
func (*IsMemberOfExpressionsResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{9}
}

func (x *IsMemberOfExpressionsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsMemberOfExpressionsResponse) GetSegmentExpressionMembershipMap() map[string]*SegmentExpressionMembership {
	if x != nil {
		return x.SegmentExpressionMembershipMap
	}
	return nil
}

type GetMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// segment id for which all the actors will be fetched
	SegmentId string `protobuf:"bytes,1,opt,name=segment_id,json=segmentId,proto3" json:"segment_id,omitempty"`
	// to fetch actors in segment at latest timestamp in db before given timestamp
	// If no timestamp is passed, latest by is equal to current timestamp
	LatestBy *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=latest_by,json=latestBy,proto3" json:"latest_by,omitempty"`
	// page context to help server fetch the page
	// before_token should not be specified since this is a move-forward paginated call
	// page_size is just a hint for response size and does not guarantee the elements returned
	PageContext *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetMembersRequest) Reset() {
	*x = GetMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMembersRequest) ProtoMessage() {}

func (x *GetMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMembersRequest.ProtoReflect.Descriptor instead.
func (*GetMembersRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetMembersRequest) GetSegmentId() string {
	if x != nil {
		return x.SegmentId
	}
	return ""
}

func (x *GetMembersRequest) GetLatestBy() *timestamppb.Timestamp {
	if x != nil {
		return x.LatestBy
	}
	return nil
}

func (x *GetMembersRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetMembersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// enum to represent segment status
	SegmentStatus SegmentStatus `protobuf:"varint,2,opt,name=segment_status,json=segmentStatus,proto3,enum=segment.SegmentStatus" json:"segment_status,omitempty"`
	// actual timestamp in db where actors are fetched
	SegmentTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=segment_timestamp,json=segmentTimestamp,proto3" json:"segment_timestamp,omitempty"`
	// list of actors belonging to the segment
	ActorIds []string `protobuf:"bytes,4,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
	// page context to help client fetch the next page
	PageContext *rpc.PageContextResponse `protobuf:"bytes,5,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetMembersResponse) Reset() {
	*x = GetMembersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMembersResponse) ProtoMessage() {}

func (x *GetMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMembersResponse.ProtoReflect.Descriptor instead.
func (*GetMembersResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetMembersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMembersResponse) GetSegmentStatus() SegmentStatus {
	if x != nil {
		return x.SegmentStatus
	}
	return SegmentStatus_SEGMENT_STATUS_UNSPECIFIED
}

func (x *GetMembersResponse) GetSegmentTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.SegmentTimestamp
	}
	return nil
}

func (x *GetMembersResponse) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

func (x *GetMembersResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type DeleteSegmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// segment id for which the instances will be deleted
	SegmentId string `protobuf:"bytes,1,opt,name=segment_id,json=segmentId,proto3" json:"segment_id,omitempty"`
	// optional list of segment instance ids of given segment
	// if empty, all the instances of this segment will be deleted
	SegmentInstanceIds []string `protobuf:"bytes,2,rep,name=segment_instance_ids,json=segmentInstanceIds,proto3" json:"segment_instance_ids,omitempty"`
}

func (x *DeleteSegmentRequest) Reset() {
	*x = DeleteSegmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSegmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSegmentRequest) ProtoMessage() {}

func (x *DeleteSegmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSegmentRequest.ProtoReflect.Descriptor instead.
func (*DeleteSegmentRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteSegmentRequest) GetSegmentId() string {
	if x != nil {
		return x.SegmentId
	}
	return ""
}

func (x *DeleteSegmentRequest) GetSegmentInstanceIds() []string {
	if x != nil {
		return x.SegmentInstanceIds
	}
	return nil
}

type DeleteSegmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteSegmentResponse) Reset() {
	*x = DeleteSegmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSegmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSegmentResponse) ProtoMessage() {}

func (x *DeleteSegmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSegmentResponse.ProtoReflect.Descriptor instead.
func (*DeleteSegmentResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteSegmentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type PushPinpointSegmentIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// aws pinpoint application id
	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *PushPinpointSegmentIdsRequest) Reset() {
	*x = PushPinpointSegmentIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPinpointSegmentIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPinpointSegmentIdsRequest) ProtoMessage() {}

func (x *PushPinpointSegmentIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPinpointSegmentIdsRequest.ProtoReflect.Descriptor instead.
func (*PushPinpointSegmentIdsRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{14}
}

func (x *PushPinpointSegmentIdsRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type PushPinpointSegmentIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *PushPinpointSegmentIdsResponse) Reset() {
	*x = PushPinpointSegmentIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPinpointSegmentIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPinpointSegmentIdsResponse) ProtoMessage() {}

func (x *PushPinpointSegmentIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPinpointSegmentIdsResponse.ProtoReflect.Descriptor instead.
func (*PushPinpointSegmentIdsResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{15}
}

func (x *PushPinpointSegmentIdsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetNumberOfUsersInSegmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentId string `protobuf:"bytes,1,opt,name=segment_id,json=segmentId,proto3" json:"segment_id,omitempty"`
}

func (x *GetNumberOfUsersInSegmentRequest) Reset() {
	*x = GetNumberOfUsersInSegmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNumberOfUsersInSegmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNumberOfUsersInSegmentRequest) ProtoMessage() {}

func (x *GetNumberOfUsersInSegmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNumberOfUsersInSegmentRequest.ProtoReflect.Descriptor instead.
func (*GetNumberOfUsersInSegmentRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetNumberOfUsersInSegmentRequest) GetSegmentId() string {
	if x != nil {
		return x.SegmentId
	}
	return ""
}

type GetNumberOfUsersInSegmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NumberOfUsers int64       `protobuf:"varint,2,opt,name=number_of_users,json=numberOfUsers,proto3" json:"number_of_users,omitempty"`
}

func (x *GetNumberOfUsersInSegmentResponse) Reset() {
	*x = GetNumberOfUsersInSegmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNumberOfUsersInSegmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNumberOfUsersInSegmentResponse) ProtoMessage() {}

func (x *GetNumberOfUsersInSegmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNumberOfUsersInSegmentResponse.ProtoReflect.Descriptor instead.
func (*GetNumberOfUsersInSegmentResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetNumberOfUsersInSegmentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetNumberOfUsersInSegmentResponse) GetNumberOfUsers() int64 {
	if x != nil {
		return x.NumberOfUsers
	}
	return 0
}

type GetSegmentTypesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentIds []string `protobuf:"bytes,1,rep,name=segment_ids,json=segmentIds,proto3" json:"segment_ids,omitempty"`
}

func (x *GetSegmentTypesRequest) Reset() {
	*x = GetSegmentTypesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentTypesRequest) ProtoMessage() {}

func (x *GetSegmentTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentTypesRequest.ProtoReflect.Descriptor instead.
func (*GetSegmentTypesRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetSegmentTypesRequest) GetSegmentIds() []string {
	if x != nil {
		return x.SegmentIds
	}
	return nil
}

type GetSegmentTypesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SegmentTypes map[string]SegmentType `protobuf:"bytes,2,rep,name=segment_types,json=segmentTypes,proto3" json:"segment_types,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=segment.SegmentType"`
}

func (x *GetSegmentTypesResponse) Reset() {
	*x = GetSegmentTypesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentTypesResponse) ProtoMessage() {}

func (x *GetSegmentTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentTypesResponse.ProtoReflect.Descriptor instead.
func (*GetSegmentTypesResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetSegmentTypesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSegmentTypesResponse) GetSegmentTypes() map[string]SegmentType {
	if x != nil {
		return x.SegmentTypes
	}
	return nil
}

type AreSegmentsAvailableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentIds []string `protobuf:"bytes,1,rep,name=segment_ids,json=segmentIds,proto3" json:"segment_ids,omitempty"`
}

func (x *AreSegmentsAvailableRequest) Reset() {
	*x = AreSegmentsAvailableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreSegmentsAvailableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreSegmentsAvailableRequest) ProtoMessage() {}

func (x *AreSegmentsAvailableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreSegmentsAvailableRequest.ProtoReflect.Descriptor instead.
func (*AreSegmentsAvailableRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{20}
}

func (x *AreSegmentsAvailableRequest) GetSegmentIds() []string {
	if x != nil {
		return x.SegmentIds
	}
	return nil
}

type AreSegmentsAvailableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AvailabilityMap map[string]bool `protobuf:"bytes,2,rep,name=availability_map,json=availabilityMap,proto3" json:"availability_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *AreSegmentsAvailableResponse) Reset() {
	*x = AreSegmentsAvailableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreSegmentsAvailableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreSegmentsAvailableResponse) ProtoMessage() {}

func (x *AreSegmentsAvailableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreSegmentsAvailableResponse.ProtoReflect.Descriptor instead.
func (*AreSegmentsAvailableResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{21}
}

func (x *AreSegmentsAvailableResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AreSegmentsAvailableResponse) GetAvailabilityMap() map[string]bool {
	if x != nil {
		return x.AvailabilityMap
	}
	return nil
}

type UpdateSegmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// (required) name of the provider where segment is created
	ProviderName SegmentProvider `protobuf:"varint,1,opt,name=provider_name,json=providerName,proto3,enum=segment.SegmentProvider" json:"provider_name,omitempty"`
	// (required) provider segment id of the segment which needs to be updated
	ProviderSegmentId string `protobuf:"bytes,2,opt,name=provider_segment_id,json=providerSegmentId,proto3" json:"provider_segment_id,omitempty"`
	// (required) new export till timestamp for segment
	ExportTill *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=export_till,json=exportTill,proto3" json:"export_till,omitempty"`
}

func (x *UpdateSegmentRequest) Reset() {
	*x = UpdateSegmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSegmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSegmentRequest) ProtoMessage() {}

func (x *UpdateSegmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSegmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateSegmentRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateSegmentRequest) GetProviderName() SegmentProvider {
	if x != nil {
		return x.ProviderName
	}
	return SegmentProvider_SEGMENT_PROVIDER_UNSPECIFIED
}

func (x *UpdateSegmentRequest) GetProviderSegmentId() string {
	if x != nil {
		return x.ProviderSegmentId
	}
	return ""
}

func (x *UpdateSegmentRequest) GetExportTill() *timestamppb.Timestamp {
	if x != nil {
		return x.ExportTill
	}
	return nil
}

type UpdateSegmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateSegmentResponse) Reset() {
	*x = UpdateSegmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSegmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSegmentResponse) ProtoMessage() {}

func (x *UpdateSegmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSegmentResponse.ProtoReflect.Descriptor instead.
func (*UpdateSegmentResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateSegmentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateSegmentMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentName string `protobuf:"bytes,1,opt,name=segment_name,json=segmentName,proto3" json:"segment_name,omitempty"`
	// filters on the basis of which segment is created
	// i.e., if provided query_filter is "salary >= 30000 and age <= 50 and city_tier = 2"
	// then resultant sql query is "select count(*) from sgmt_master_customer_table where salary >= 30000 and age <= 50 and city_tier = 2"
	QueryFilter string `protobuf:"bytes,2,opt,name=query_filter,json=queryFilter,proto3" json:"query_filter,omitempty"`
	// segment type - Static/Dynamic
	SegmentType SegmentType `protobuf:"varint,3,opt,name=segment_type,json=segmentType,proto3,enum=segment.SegmentType" json:"segment_type,omitempty"`
	// segment status - Active/Inactive
	Status Status `protobuf:"varint,4,opt,name=status,proto3,enum=segment.Status" json:"status,omitempty"`
	Owner  string `protobuf:"bytes,5,opt,name=owner,proto3" json:"owner,omitempty"`
	// approval status - during creation, the approval status will be false.
	ApprovalStatus common.BooleanEnum `protobuf:"varint,6,opt,name=approval_status,json=approvalStatus,proto3,enum=api.typesv2.common.BooleanEnum" json:"approval_status,omitempty"`
	// segment reviewed by
	ReviewedBy string                 `protobuf:"bytes,7,opt,name=reviewed_by,json=reviewedBy,proto3" json:"reviewed_by,omitempty"`
	ExpiresAt  *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// file path for static segments
	// i.e., s3://epifi-rewards/segmentation/static/segment_name.csv
	SegmentPath string `protobuf:"bytes,9,opt,name=segment_path,json=segmentPath,proto3" json:"segment_path,omitempty"`
}

func (x *CreateSegmentMetadataRequest) Reset() {
	*x = CreateSegmentMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSegmentMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSegmentMetadataRequest) ProtoMessage() {}

func (x *CreateSegmentMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSegmentMetadataRequest.ProtoReflect.Descriptor instead.
func (*CreateSegmentMetadataRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{24}
}

func (x *CreateSegmentMetadataRequest) GetSegmentName() string {
	if x != nil {
		return x.SegmentName
	}
	return ""
}

func (x *CreateSegmentMetadataRequest) GetQueryFilter() string {
	if x != nil {
		return x.QueryFilter
	}
	return ""
}

func (x *CreateSegmentMetadataRequest) GetSegmentType() SegmentType {
	if x != nil {
		return x.SegmentType
	}
	return SegmentType_SEGMENT_TYPE_UNSPECIFIED
}

func (x *CreateSegmentMetadataRequest) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_Status_UNSPECIFIED
}

func (x *CreateSegmentMetadataRequest) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *CreateSegmentMetadataRequest) GetApprovalStatus() common.BooleanEnum {
	if x != nil {
		return x.ApprovalStatus
	}
	return common.BooleanEnum(0)
}

func (x *CreateSegmentMetadataRequest) GetReviewedBy() string {
	if x != nil {
		return x.ReviewedBy
	}
	return ""
}

func (x *CreateSegmentMetadataRequest) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *CreateSegmentMetadataRequest) GetSegmentPath() string {
	if x != nil {
		return x.SegmentPath
	}
	return ""
}

type CreateSegmentMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// segment id mapped to provider segment
	SegmentId string `protobuf:"bytes,2,opt,name=segment_id,json=segmentId,proto3" json:"segment_id,omitempty"`
}

func (x *CreateSegmentMetadataResponse) Reset() {
	*x = CreateSegmentMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSegmentMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSegmentMetadataResponse) ProtoMessage() {}

func (x *CreateSegmentMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSegmentMetadataResponse.ProtoReflect.Descriptor instead.
func (*CreateSegmentMetadataResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{25}
}

func (x *CreateSegmentMetadataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateSegmentMetadataResponse) GetSegmentId() string {
	if x != nil {
		return x.SegmentId
	}
	return ""
}

type UpdateSegmentMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SegmentName string `protobuf:"bytes,2,opt,name=segment_name,json=segmentName,proto3" json:"segment_name,omitempty"`
	// filters on the basis of which segment is created
	QueryFilter string      `protobuf:"bytes,3,opt,name=query_filter,json=queryFilter,proto3" json:"query_filter,omitempty"`
	SegmentType SegmentType `protobuf:"varint,4,opt,name=segment_type,json=segmentType,proto3,enum=segment.SegmentType" json:"segment_type,omitempty"`
	Status      Status      `protobuf:"varint,5,opt,name=status,proto3,enum=segment.Status" json:"status,omitempty"`
	Owner       string      `protobuf:"bytes,6,opt,name=owner,proto3" json:"owner,omitempty"`
	// approval status - true/false/unspecified; only true will be used for segmentation service
	ApprovalStatus common.BooleanEnum     `protobuf:"varint,7,opt,name=approval_status,json=approvalStatus,proto3,enum=api.typesv2.common.BooleanEnum" json:"approval_status,omitempty"`
	ReviewedBy     string                 `protobuf:"bytes,8,opt,name=reviewed_by,json=reviewedBy,proto3" json:"reviewed_by,omitempty"`
	ExpiresAt      *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	SegmentPath    string                 `protobuf:"bytes,10,opt,name=segment_path,json=segmentPath,proto3" json:"segment_path,omitempty"`
}

func (x *UpdateSegmentMetadataRequest) Reset() {
	*x = UpdateSegmentMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSegmentMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSegmentMetadataRequest) ProtoMessage() {}

func (x *UpdateSegmentMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSegmentMetadataRequest.ProtoReflect.Descriptor instead.
func (*UpdateSegmentMetadataRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{26}
}

func (x *UpdateSegmentMetadataRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateSegmentMetadataRequest) GetSegmentName() string {
	if x != nil {
		return x.SegmentName
	}
	return ""
}

func (x *UpdateSegmentMetadataRequest) GetQueryFilter() string {
	if x != nil {
		return x.QueryFilter
	}
	return ""
}

func (x *UpdateSegmentMetadataRequest) GetSegmentType() SegmentType {
	if x != nil {
		return x.SegmentType
	}
	return SegmentType_SEGMENT_TYPE_UNSPECIFIED
}

func (x *UpdateSegmentMetadataRequest) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_Status_UNSPECIFIED
}

func (x *UpdateSegmentMetadataRequest) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *UpdateSegmentMetadataRequest) GetApprovalStatus() common.BooleanEnum {
	if x != nil {
		return x.ApprovalStatus
	}
	return common.BooleanEnum(0)
}

func (x *UpdateSegmentMetadataRequest) GetReviewedBy() string {
	if x != nil {
		return x.ReviewedBy
	}
	return ""
}

func (x *UpdateSegmentMetadataRequest) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *UpdateSegmentMetadataRequest) GetSegmentPath() string {
	if x != nil {
		return x.SegmentPath
	}
	return ""
}

type UpdateSegmentMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateSegmentMetadataResponse) Reset() {
	*x = UpdateSegmentMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSegmentMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSegmentMetadataResponse) ProtoMessage() {}

func (x *UpdateSegmentMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSegmentMetadataResponse.ProtoReflect.Descriptor instead.
func (*UpdateSegmentMetadataResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{27}
}

func (x *UpdateSegmentMetadataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetSegmentsBetweenUpdatedAtRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// duration is used for providing data whose updated time is greater than the specified duration
	Duration *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *GetSegmentsBetweenUpdatedAtRequest) Reset() {
	*x = GetSegmentsBetweenUpdatedAtRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentsBetweenUpdatedAtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentsBetweenUpdatedAtRequest) ProtoMessage() {}

func (x *GetSegmentsBetweenUpdatedAtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentsBetweenUpdatedAtRequest.ProtoReflect.Descriptor instead.
func (*GetSegmentsBetweenUpdatedAtRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetSegmentsBetweenUpdatedAtRequest) GetDuration() *timestamppb.Timestamp {
	if x != nil {
		return x.Duration
	}
	return nil
}

type GetSegmentsBetweenUpdatedAtResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// return array of segments between specified time duration
	SegmentMetadata []*SegmentMetadata `protobuf:"bytes,2,rep,name=segment_metadata,json=segmentMetadata,proto3" json:"segment_metadata,omitempty"`
}

func (x *GetSegmentsBetweenUpdatedAtResponse) Reset() {
	*x = GetSegmentsBetweenUpdatedAtResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentsBetweenUpdatedAtResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentsBetweenUpdatedAtResponse) ProtoMessage() {}

func (x *GetSegmentsBetweenUpdatedAtResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentsBetweenUpdatedAtResponse.ProtoReflect.Descriptor instead.
func (*GetSegmentsBetweenUpdatedAtResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetSegmentsBetweenUpdatedAtResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSegmentsBetweenUpdatedAtResponse) GetSegmentMetadata() []*SegmentMetadata {
	if x != nil {
		return x.SegmentMetadata
	}
	return nil
}

type GetNumberOfUsersInSegmentQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueryFilter string `protobuf:"bytes,1,opt,name=query_filter,json=queryFilter,proto3" json:"query_filter,omitempty"`
}

func (x *GetNumberOfUsersInSegmentQueryRequest) Reset() {
	*x = GetNumberOfUsersInSegmentQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNumberOfUsersInSegmentQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNumberOfUsersInSegmentQueryRequest) ProtoMessage() {}

func (x *GetNumberOfUsersInSegmentQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNumberOfUsersInSegmentQueryRequest.ProtoReflect.Descriptor instead.
func (*GetNumberOfUsersInSegmentQueryRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetNumberOfUsersInSegmentQueryRequest) GetQueryFilter() string {
	if x != nil {
		return x.QueryFilter
	}
	return ""
}

type GetNumberOfUsersInSegmentQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// segment base count from presto master table
	SegmentCount int64 `protobuf:"varint,2,opt,name=segment_count,json=segmentCount,proto3" json:"segment_count,omitempty"`
}

func (x *GetNumberOfUsersInSegmentQueryResponse) Reset() {
	*x = GetNumberOfUsersInSegmentQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNumberOfUsersInSegmentQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNumberOfUsersInSegmentQueryResponse) ProtoMessage() {}

func (x *GetNumberOfUsersInSegmentQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNumberOfUsersInSegmentQueryResponse.ProtoReflect.Descriptor instead.
func (*GetNumberOfUsersInSegmentQueryResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetNumberOfUsersInSegmentQueryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetNumberOfUsersInSegmentQueryResponse) GetSegmentCount() int64 {
	if x != nil {
		return x.SegmentCount
	}
	return 0
}

type GetActiveDynamicSegmentCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetActiveDynamicSegmentCountRequest) Reset() {
	*x = GetActiveDynamicSegmentCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveDynamicSegmentCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveDynamicSegmentCountRequest) ProtoMessage() {}

func (x *GetActiveDynamicSegmentCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveDynamicSegmentCountRequest.ProtoReflect.Descriptor instead.
func (*GetActiveDynamicSegmentCountRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{32}
}

type GetActiveDynamicSegmentCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// active dynamic segment count from segments_metadata table
	ActiveDynamicSegmentCount int64 `protobuf:"varint,2,opt,name=active_dynamic_segment_count,json=activeDynamicSegmentCount,proto3" json:"active_dynamic_segment_count,omitempty"`
}

func (x *GetActiveDynamicSegmentCountResponse) Reset() {
	*x = GetActiveDynamicSegmentCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveDynamicSegmentCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveDynamicSegmentCountResponse) ProtoMessage() {}

func (x *GetActiveDynamicSegmentCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveDynamicSegmentCountResponse.ProtoReflect.Descriptor instead.
func (*GetActiveDynamicSegmentCountResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetActiveDynamicSegmentCountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActiveDynamicSegmentCountResponse) GetActiveDynamicSegmentCount() int64 {
	if x != nil {
		return x.ActiveDynamicSegmentCount
	}
	return 0
}

type GetSegmentMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentId string `protobuf:"bytes,1,opt,name=segment_id,json=segmentId,proto3" json:"segment_id,omitempty"`
}

func (x *GetSegmentMetadataRequest) Reset() {
	*x = GetSegmentMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentMetadataRequest) ProtoMessage() {}

func (x *GetSegmentMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentMetadataRequest.ProtoReflect.Descriptor instead.
func (*GetSegmentMetadataRequest) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetSegmentMetadataRequest) GetSegmentId() string {
	if x != nil {
		return x.SegmentId
	}
	return ""
}

type GetSegmentMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// return segment metadata payload on basis of provided segment id
	SegmentMetadata *SegmentMetadata `protobuf:"bytes,2,opt,name=segment_metadata,json=segmentMetadata,proto3" json:"segment_metadata,omitempty"`
}

func (x *GetSegmentMetadataResponse) Reset() {
	*x = GetSegmentMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_segment_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentMetadataResponse) ProtoMessage() {}

func (x *GetSegmentMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_segment_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentMetadataResponse.ProtoReflect.Descriptor instead.
func (*GetSegmentMetadataResponse) Descriptor() ([]byte, []int) {
	return file_api_segment_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetSegmentMetadataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSegmentMetadataResponse) GetSegmentMetadata() *SegmentMetadata {
	if x != nil {
		return x.SegmentMetadata
	}
	return nil
}

var File_api_segment_service_proto protoreflect.FileDescriptor

var file_api_segment_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x67, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70,
	0x69, 0x2f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61,
	0x70, 0x69, 0x2f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f,
	0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x49, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x22, 0x63, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x22, 0x6f, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x84, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x46,
	0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa0,
	0x03, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x0d,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x11, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x37,
	0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6c,
	0x6c, 0x12, 0x5c, 0x0a, 0x19, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x40, 0x0a, 0x0f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x0e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0x8c, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x22, 0x93, 0x01, 0x0a, 0x0f, 0x49, 0x73, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x08, 0x01, 0x10, 0xc8,
	0x01, 0x52, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x37, 0x0a,
	0x09, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x74, 0x42, 0x79, 0x22, 0x87, 0x02, 0x0a, 0x10, 0x49, 0x73, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x69, 0x0a, 0x16, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x73, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x61, 0x70, 0x1a, 0x63, 0x0a, 0x19, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xb5, 0x01, 0x0a, 0x1c, 0x49, 0x73, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x45,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x16,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x92, 0x01, 0x05, 0x08, 0x01, 0x10, 0xc8, 0x01, 0x52, 0x14, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x37, 0x0a, 0x09, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x42, 0x79, 0x22, 0xd5, 0x02, 0x0a, 0x1d, 0x49, 0x73, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x95, 0x01, 0x0a, 0x21, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x73, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x1e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x4d, 0x61, 0x70, 0x1a, 0x77, 0x0a, 0x23, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xa7, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x42, 0x79, 0x12, 0x3a,
	0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x9b, 0x02, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x11, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1b,
	0x0a, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x67, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x30, 0x0a, 0x14, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x73, 0x22, 0x3c, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x46, 0x0a, 0x1d, 0x50, 0x75, 0x73, 0x68, 0x50, 0x69, 0x6e, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x1e, 0x50, 0x75, 0x73, 0x68, 0x50,
	0x69, 0x6e, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x41,
	0x0a, 0x20, 0x47, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x49, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x70, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x22, 0x39, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xee,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x57, 0x0a, 0x0d, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x55, 0x0a, 0x11, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x3e, 0x0a, 0x1b, 0x41, 0x72, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22,
	0xee, 0x01, 0x0a, 0x1c, 0x41, 0x72, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x65, 0x0a, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3a, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x72, 0x65, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x70, 0x1a, 0x42, 0x0a, 0x14,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xdf, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x0d, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x37, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0b, 0x65,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69,
	0x6c, 0x6c, 0x22, 0x3c, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xa5, 0x03, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x27, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0f, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12,
	0x48, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x42, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x41, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x22, 0x63, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xb5, 0x03,
	0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x0f,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x65, 0x64, 0x42, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73,
	0x41, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x61, 0x74, 0x68, 0x22, 0x44, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5c, 0x0a, 0x22, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65,
	0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x36, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8f, 0x01, 0x0a, 0x23, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x10, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4a, 0x0a, 0x25, 0x47,
	0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49,
	0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x72, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x25, 0x0a, 0x23, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3f, 0x0a, 0x1c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x19, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x3a, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x86, 0x01,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x43, 0x0a, 0x10, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x32, 0xcb, 0x0e, 0x0a, 0x13, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5d,
	0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a,
	0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d,
	0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a,
	0x08, 0x49, 0x73, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x18, 0x2e, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x49, 0x73, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x73,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0d,
	0xbd, 0x9e, 0xd7, 0x0a, 0xcd, 0xcc, 0xcc, 0x3d, 0xc0, 0x9e, 0xd7, 0x0a, 0x01, 0x12, 0x75, 0x0a,
	0x15, 0x49, 0x73, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x49, 0x73, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x73, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x4f, 0x66, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0d, 0xbd, 0x9e, 0xd7, 0x0a, 0xcd, 0xcc, 0xcc, 0x3d, 0xc0,
	0x9e, 0xd7, 0x0a, 0x01, 0x12, 0x45, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x12, 0x1a, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x16, 0x50,
	0x75, 0x73, 0x68, 0x50, 0x69, 0x6e, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x26, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x50, 0x69, 0x6e, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x69, 0x6e, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x29, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e,
	0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x4f, 0x66, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1f, 0x2e,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x63, 0x0a, 0x14, 0x41, 0x72, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x72, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x72, 0x65, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x25,
	0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a,
	0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x25, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x2b, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65,
	0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x81, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x2c, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2d, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x75, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2a, 0x2e,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x49, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x27, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_segment_service_proto_rawDescOnce sync.Once
	file_api_segment_service_proto_rawDescData = file_api_segment_service_proto_rawDesc
)

func file_api_segment_service_proto_rawDescGZIP() []byte {
	file_api_segment_service_proto_rawDescOnce.Do(func() {
		file_api_segment_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_segment_service_proto_rawDescData)
	})
	return file_api_segment_service_proto_rawDescData
}

var file_api_segment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_api_segment_service_proto_goTypes = []interface{}{
	(*GetUsersInSegmentOnDateRequest)(nil),         // 0: segment.GetUsersInSegmentOnDateRequest
	(*GetUsersInSegmentOnDateResponse)(nil),        // 1: segment.GetUsersInSegmentOnDateResponse
	(*GetSegmentEntryTimeForUserRequest)(nil),      // 2: segment.GetSegmentEntryTimeForUserRequest
	(*GetSegmentEntryTimeForUserResponse)(nil),     // 3: segment.GetSegmentEntryTimeForUserResponse
	(*CreateOrGetSegmentRequest)(nil),              // 4: segment.CreateOrGetSegmentRequest
	(*CreateOrGetSegmentResponse)(nil),             // 5: segment.CreateOrGetSegmentResponse
	(*IsMemberRequest)(nil),                        // 6: segment.IsMemberRequest
	(*IsMemberResponse)(nil),                       // 7: segment.IsMemberResponse
	(*IsMemberOfExpressionsRequest)(nil),           // 8: segment.IsMemberOfExpressionsRequest
	(*IsMemberOfExpressionsResponse)(nil),          // 9: segment.IsMemberOfExpressionsResponse
	(*GetMembersRequest)(nil),                      // 10: segment.GetMembersRequest
	(*GetMembersResponse)(nil),                     // 11: segment.GetMembersResponse
	(*DeleteSegmentRequest)(nil),                   // 12: segment.DeleteSegmentRequest
	(*DeleteSegmentResponse)(nil),                  // 13: segment.DeleteSegmentResponse
	(*PushPinpointSegmentIdsRequest)(nil),          // 14: segment.PushPinpointSegmentIdsRequest
	(*PushPinpointSegmentIdsResponse)(nil),         // 15: segment.PushPinpointSegmentIdsResponse
	(*GetNumberOfUsersInSegmentRequest)(nil),       // 16: segment.GetNumberOfUsersInSegmentRequest
	(*GetNumberOfUsersInSegmentResponse)(nil),      // 17: segment.GetNumberOfUsersInSegmentResponse
	(*GetSegmentTypesRequest)(nil),                 // 18: segment.GetSegmentTypesRequest
	(*GetSegmentTypesResponse)(nil),                // 19: segment.GetSegmentTypesResponse
	(*AreSegmentsAvailableRequest)(nil),            // 20: segment.AreSegmentsAvailableRequest
	(*AreSegmentsAvailableResponse)(nil),           // 21: segment.AreSegmentsAvailableResponse
	(*UpdateSegmentRequest)(nil),                   // 22: segment.UpdateSegmentRequest
	(*UpdateSegmentResponse)(nil),                  // 23: segment.UpdateSegmentResponse
	(*CreateSegmentMetadataRequest)(nil),           // 24: segment.CreateSegmentMetadataRequest
	(*CreateSegmentMetadataResponse)(nil),          // 25: segment.CreateSegmentMetadataResponse
	(*UpdateSegmentMetadataRequest)(nil),           // 26: segment.UpdateSegmentMetadataRequest
	(*UpdateSegmentMetadataResponse)(nil),          // 27: segment.UpdateSegmentMetadataResponse
	(*GetSegmentsBetweenUpdatedAtRequest)(nil),     // 28: segment.GetSegmentsBetweenUpdatedAtRequest
	(*GetSegmentsBetweenUpdatedAtResponse)(nil),    // 29: segment.GetSegmentsBetweenUpdatedAtResponse
	(*GetNumberOfUsersInSegmentQueryRequest)(nil),  // 30: segment.GetNumberOfUsersInSegmentQueryRequest
	(*GetNumberOfUsersInSegmentQueryResponse)(nil), // 31: segment.GetNumberOfUsersInSegmentQueryResponse
	(*GetActiveDynamicSegmentCountRequest)(nil),    // 32: segment.GetActiveDynamicSegmentCountRequest
	(*GetActiveDynamicSegmentCountResponse)(nil),   // 33: segment.GetActiveDynamicSegmentCountResponse
	(*GetSegmentMetadataRequest)(nil),              // 34: segment.GetSegmentMetadataRequest
	(*GetSegmentMetadataResponse)(nil),             // 35: segment.GetSegmentMetadataResponse
	nil,                                            // 36: segment.IsMemberResponse.SegmentMembershipMapEntry
	nil,                                            // 37: segment.IsMemberOfExpressionsResponse.SegmentExpressionMembershipMapEntry
	nil,                                            // 38: segment.GetSegmentTypesResponse.SegmentTypesEntry
	nil,                                            // 39: segment.AreSegmentsAvailableResponse.AvailabilityMapEntry
	(*date.Date)(nil),                              // 40: google.type.Date
	(*rpc.Status)(nil),                             // 41: rpc.Status
	(*timestamppb.Timestamp)(nil),                  // 42: google.protobuf.Timestamp
	(SegmentProvider)(0),                           // 43: segment.SegmentProvider
	(SegmentType)(0),                               // 44: segment.SegmentType
	(*ProviderSegmentMetadata)(nil),                // 45: segment.ProviderSegmentMetadata
	(*SegmentDetails)(nil),                         // 46: segment.SegmentDetails
	(*Segment)(nil),                                // 47: segment.Segment
	(*rpc.PageContextRequest)(nil),                 // 48: rpc.PageContextRequest
	(SegmentStatus)(0),                             // 49: segment.SegmentStatus
	(*rpc.PageContextResponse)(nil),                // 50: rpc.PageContextResponse
	(Status)(0),                                    // 51: segment.Status
	(common.BooleanEnum)(0),                        // 52: api.typesv2.common.BooleanEnum
	(*SegmentMetadata)(nil),                        // 53: segment.SegmentMetadata
	(*SegmentMembership)(nil),                      // 54: segment.SegmentMembership
	(*SegmentExpressionMembership)(nil),            // 55: segment.SegmentExpressionMembership
}
var file_api_segment_service_proto_depIdxs = []int32{
	40, // 0: segment.GetUsersInSegmentOnDateRequest.date:type_name -> google.type.Date
	41, // 1: segment.GetUsersInSegmentOnDateResponse.status:type_name -> rpc.Status
	41, // 2: segment.GetSegmentEntryTimeForUserResponse.status:type_name -> rpc.Status
	42, // 3: segment.GetSegmentEntryTimeForUserResponse.entry_time:type_name -> google.protobuf.Timestamp
	43, // 4: segment.CreateOrGetSegmentRequest.provider_name:type_name -> segment.SegmentProvider
	44, // 5: segment.CreateOrGetSegmentRequest.segment_type:type_name -> segment.SegmentType
	45, // 6: segment.CreateOrGetSegmentRequest.provider_segment_metadata:type_name -> segment.ProviderSegmentMetadata
	46, // 7: segment.CreateOrGetSegmentRequest.segment_details:type_name -> segment.SegmentDetails
	41, // 8: segment.CreateOrGetSegmentResponse.status:type_name -> rpc.Status
	47, // 9: segment.CreateOrGetSegmentResponse.segment:type_name -> segment.Segment
	42, // 10: segment.IsMemberRequest.latest_by:type_name -> google.protobuf.Timestamp
	41, // 11: segment.IsMemberResponse.status:type_name -> rpc.Status
	36, // 12: segment.IsMemberResponse.segment_membership_map:type_name -> segment.IsMemberResponse.SegmentMembershipMapEntry
	42, // 13: segment.IsMemberOfExpressionsRequest.latest_by:type_name -> google.protobuf.Timestamp
	41, // 14: segment.IsMemberOfExpressionsResponse.status:type_name -> rpc.Status
	37, // 15: segment.IsMemberOfExpressionsResponse.segment_expression_membership_map:type_name -> segment.IsMemberOfExpressionsResponse.SegmentExpressionMembershipMapEntry
	42, // 16: segment.GetMembersRequest.latest_by:type_name -> google.protobuf.Timestamp
	48, // 17: segment.GetMembersRequest.page_context:type_name -> rpc.PageContextRequest
	41, // 18: segment.GetMembersResponse.status:type_name -> rpc.Status
	49, // 19: segment.GetMembersResponse.segment_status:type_name -> segment.SegmentStatus
	42, // 20: segment.GetMembersResponse.segment_timestamp:type_name -> google.protobuf.Timestamp
	50, // 21: segment.GetMembersResponse.page_context:type_name -> rpc.PageContextResponse
	41, // 22: segment.DeleteSegmentResponse.status:type_name -> rpc.Status
	41, // 23: segment.PushPinpointSegmentIdsResponse.status:type_name -> rpc.Status
	41, // 24: segment.GetNumberOfUsersInSegmentResponse.status:type_name -> rpc.Status
	41, // 25: segment.GetSegmentTypesResponse.status:type_name -> rpc.Status
	38, // 26: segment.GetSegmentTypesResponse.segment_types:type_name -> segment.GetSegmentTypesResponse.SegmentTypesEntry
	41, // 27: segment.AreSegmentsAvailableResponse.status:type_name -> rpc.Status
	39, // 28: segment.AreSegmentsAvailableResponse.availability_map:type_name -> segment.AreSegmentsAvailableResponse.AvailabilityMapEntry
	43, // 29: segment.UpdateSegmentRequest.provider_name:type_name -> segment.SegmentProvider
	42, // 30: segment.UpdateSegmentRequest.export_till:type_name -> google.protobuf.Timestamp
	41, // 31: segment.UpdateSegmentResponse.status:type_name -> rpc.Status
	44, // 32: segment.CreateSegmentMetadataRequest.segment_type:type_name -> segment.SegmentType
	51, // 33: segment.CreateSegmentMetadataRequest.status:type_name -> segment.Status
	52, // 34: segment.CreateSegmentMetadataRequest.approval_status:type_name -> api.typesv2.common.BooleanEnum
	42, // 35: segment.CreateSegmentMetadataRequest.expires_at:type_name -> google.protobuf.Timestamp
	41, // 36: segment.CreateSegmentMetadataResponse.status:type_name -> rpc.Status
	44, // 37: segment.UpdateSegmentMetadataRequest.segment_type:type_name -> segment.SegmentType
	51, // 38: segment.UpdateSegmentMetadataRequest.status:type_name -> segment.Status
	52, // 39: segment.UpdateSegmentMetadataRequest.approval_status:type_name -> api.typesv2.common.BooleanEnum
	42, // 40: segment.UpdateSegmentMetadataRequest.expires_at:type_name -> google.protobuf.Timestamp
	41, // 41: segment.UpdateSegmentMetadataResponse.status:type_name -> rpc.Status
	42, // 42: segment.GetSegmentsBetweenUpdatedAtRequest.duration:type_name -> google.protobuf.Timestamp
	41, // 43: segment.GetSegmentsBetweenUpdatedAtResponse.status:type_name -> rpc.Status
	53, // 44: segment.GetSegmentsBetweenUpdatedAtResponse.segment_metadata:type_name -> segment.SegmentMetadata
	41, // 45: segment.GetNumberOfUsersInSegmentQueryResponse.status:type_name -> rpc.Status
	41, // 46: segment.GetActiveDynamicSegmentCountResponse.status:type_name -> rpc.Status
	41, // 47: segment.GetSegmentMetadataResponse.status:type_name -> rpc.Status
	53, // 48: segment.GetSegmentMetadataResponse.segment_metadata:type_name -> segment.SegmentMetadata
	54, // 49: segment.IsMemberResponse.SegmentMembershipMapEntry.value:type_name -> segment.SegmentMembership
	55, // 50: segment.IsMemberOfExpressionsResponse.SegmentExpressionMembershipMapEntry.value:type_name -> segment.SegmentExpressionMembership
	44, // 51: segment.GetSegmentTypesResponse.SegmentTypesEntry.value:type_name -> segment.SegmentType
	4,  // 52: segment.SegmentationService.CreateOrGetSegment:input_type -> segment.CreateOrGetSegmentRequest
	12, // 53: segment.SegmentationService.DeleteSegment:input_type -> segment.DeleteSegmentRequest
	6,  // 54: segment.SegmentationService.IsMember:input_type -> segment.IsMemberRequest
	8,  // 55: segment.SegmentationService.IsMemberOfExpressions:input_type -> segment.IsMemberOfExpressionsRequest
	10, // 56: segment.SegmentationService.GetMembers:input_type -> segment.GetMembersRequest
	14, // 57: segment.SegmentationService.PushPinpointSegmentIds:input_type -> segment.PushPinpointSegmentIdsRequest
	16, // 58: segment.SegmentationService.GetNumberOfUsersInSegment:input_type -> segment.GetNumberOfUsersInSegmentRequest
	18, // 59: segment.SegmentationService.GetSegmentTypes:input_type -> segment.GetSegmentTypesRequest
	20, // 60: segment.SegmentationService.AreSegmentsAvailable:input_type -> segment.AreSegmentsAvailableRequest
	22, // 61: segment.SegmentationService.UpdateSegment:input_type -> segment.UpdateSegmentRequest
	24, // 62: segment.SegmentationService.CreateSegmentMetadata:input_type -> segment.CreateSegmentMetadataRequest
	26, // 63: segment.SegmentationService.UpdateSegmentMetadata:input_type -> segment.UpdateSegmentMetadataRequest
	28, // 64: segment.SegmentationService.GetSegmentsBetweenUpdatedAt:input_type -> segment.GetSegmentsBetweenUpdatedAtRequest
	30, // 65: segment.SegmentationService.GetNumberOfUsersInSegmentQuery:input_type -> segment.GetNumberOfUsersInSegmentQueryRequest
	32, // 66: segment.SegmentationService.GetActiveDynamicSegmentCount:input_type -> segment.GetActiveDynamicSegmentCountRequest
	34, // 67: segment.SegmentationService.GetSegmentMetadata:input_type -> segment.GetSegmentMetadataRequest
	2,  // 68: segment.SegmentationService.GetSegmentEntryTimeForUser:input_type -> segment.GetSegmentEntryTimeForUserRequest
	0,  // 69: segment.SegmentationService.GetUsersInSegmentOnDate:input_type -> segment.GetUsersInSegmentOnDateRequest
	5,  // 70: segment.SegmentationService.CreateOrGetSegment:output_type -> segment.CreateOrGetSegmentResponse
	13, // 71: segment.SegmentationService.DeleteSegment:output_type -> segment.DeleteSegmentResponse
	7,  // 72: segment.SegmentationService.IsMember:output_type -> segment.IsMemberResponse
	9,  // 73: segment.SegmentationService.IsMemberOfExpressions:output_type -> segment.IsMemberOfExpressionsResponse
	11, // 74: segment.SegmentationService.GetMembers:output_type -> segment.GetMembersResponse
	15, // 75: segment.SegmentationService.PushPinpointSegmentIds:output_type -> segment.PushPinpointSegmentIdsResponse
	17, // 76: segment.SegmentationService.GetNumberOfUsersInSegment:output_type -> segment.GetNumberOfUsersInSegmentResponse
	19, // 77: segment.SegmentationService.GetSegmentTypes:output_type -> segment.GetSegmentTypesResponse
	21, // 78: segment.SegmentationService.AreSegmentsAvailable:output_type -> segment.AreSegmentsAvailableResponse
	23, // 79: segment.SegmentationService.UpdateSegment:output_type -> segment.UpdateSegmentResponse
	25, // 80: segment.SegmentationService.CreateSegmentMetadata:output_type -> segment.CreateSegmentMetadataResponse
	27, // 81: segment.SegmentationService.UpdateSegmentMetadata:output_type -> segment.UpdateSegmentMetadataResponse
	29, // 82: segment.SegmentationService.GetSegmentsBetweenUpdatedAt:output_type -> segment.GetSegmentsBetweenUpdatedAtResponse
	31, // 83: segment.SegmentationService.GetNumberOfUsersInSegmentQuery:output_type -> segment.GetNumberOfUsersInSegmentQueryResponse
	33, // 84: segment.SegmentationService.GetActiveDynamicSegmentCount:output_type -> segment.GetActiveDynamicSegmentCountResponse
	35, // 85: segment.SegmentationService.GetSegmentMetadata:output_type -> segment.GetSegmentMetadataResponse
	3,  // 86: segment.SegmentationService.GetSegmentEntryTimeForUser:output_type -> segment.GetSegmentEntryTimeForUserResponse
	1,  // 87: segment.SegmentationService.GetUsersInSegmentOnDate:output_type -> segment.GetUsersInSegmentOnDateResponse
	70, // [70:88] is the sub-list for method output_type
	52, // [52:70] is the sub-list for method input_type
	52, // [52:52] is the sub-list for extension type_name
	52, // [52:52] is the sub-list for extension extendee
	0,  // [0:52] is the sub-list for field type_name
}

func init() { file_api_segment_service_proto_init() }
func file_api_segment_service_proto_init() {
	if File_api_segment_service_proto != nil {
		return
	}
	file_api_segment_enums_proto_init()
	file_api_segment_segment_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_segment_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersInSegmentOnDateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersInSegmentOnDateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentEntryTimeForUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentEntryTimeForUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrGetSegmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrGetSegmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsMemberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsMemberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsMemberOfExpressionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsMemberOfExpressionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMembersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSegmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSegmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPinpointSegmentIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPinpointSegmentIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNumberOfUsersInSegmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNumberOfUsersInSegmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentTypesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentTypesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreSegmentsAvailableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreSegmentsAvailableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSegmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSegmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSegmentMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSegmentMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSegmentMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSegmentMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentsBetweenUpdatedAtRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentsBetweenUpdatedAtResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNumberOfUsersInSegmentQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNumberOfUsersInSegmentQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveDynamicSegmentCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveDynamicSegmentCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_segment_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_segment_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_segment_service_proto_goTypes,
		DependencyIndexes: file_api_segment_service_proto_depIdxs,
		MessageInfos:      file_api_segment_service_proto_msgTypes,
	}.Build()
	File_api_segment_service_proto = out.File
	file_api_segment_service_proto_rawDesc = nil
	file_api_segment_service_proto_goTypes = nil
	file_api_segment_service_proto_depIdxs = nil
}
