// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/accounts/balance/partner_balace_raw_response.proto

package balance

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PartnerBalanceRawResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PartnerBalanceRawResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PartnerBalanceRawResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PartnerBalanceRawResponseMultiError, or nil if none found.
func (m *PartnerBalanceRawResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PartnerBalanceRawResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for RawResponseFromPartner

	// no validation rules for Api

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PartnerBalanceRawResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PartnerBalanceRawResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PartnerBalanceRawResponseValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PartnerBalanceRawResponseValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PartnerBalanceRawResponseValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PartnerBalanceRawResponseValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PartnerBalanceRawResponseValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PartnerBalanceRawResponseValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PartnerBalanceRawResponseValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PartnerBalanceRawResponseMultiError(errors)
	}

	return nil
}

// PartnerBalanceRawResponseMultiError is an error wrapping multiple validation
// errors returned by PartnerBalanceRawResponse.ValidateAll() if the
// designated constraints aren't met.
type PartnerBalanceRawResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PartnerBalanceRawResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PartnerBalanceRawResponseMultiError) AllErrors() []error { return m }

// PartnerBalanceRawResponseValidationError is the validation error returned by
// PartnerBalanceRawResponse.Validate if the designated constraints aren't met.
type PartnerBalanceRawResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PartnerBalanceRawResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PartnerBalanceRawResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PartnerBalanceRawResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PartnerBalanceRawResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PartnerBalanceRawResponseValidationError) ErrorName() string {
	return "PartnerBalanceRawResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PartnerBalanceRawResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPartnerBalanceRawResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PartnerBalanceRawResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PartnerBalanceRawResponseValidationError{}
