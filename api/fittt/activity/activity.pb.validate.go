// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/fittt/activity/activity.proto

package activity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ExecuteBatchedFitttActionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ExecuteBatchedFitttActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteBatchedFitttActionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExecuteBatchedFitttActionRequestMultiError, or nil if none found.
func (m *ExecuteBatchedFitttActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteBatchedFitttActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteBatchedFitttActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteBatchedFitttActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteBatchedFitttActionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActionInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecuteBatchedFitttActionRequestValidationError{
						field:  fmt.Sprintf("ActionInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecuteBatchedFitttActionRequestValidationError{
						field:  fmt.Sprintf("ActionInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecuteBatchedFitttActionRequestValidationError{
					field:  fmt.Sprintf("ActionInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExecuteBatchedFitttActionRequestMultiError(errors)
	}

	return nil
}

// ExecuteBatchedFitttActionRequestMultiError is an error wrapping multiple
// validation errors returned by
// ExecuteBatchedFitttActionRequest.ValidateAll() if the designated
// constraints aren't met.
type ExecuteBatchedFitttActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteBatchedFitttActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteBatchedFitttActionRequestMultiError) AllErrors() []error { return m }

// ExecuteBatchedFitttActionRequestValidationError is the validation error
// returned by ExecuteBatchedFitttActionRequest.Validate if the designated
// constraints aren't met.
type ExecuteBatchedFitttActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteBatchedFitttActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteBatchedFitttActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteBatchedFitttActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteBatchedFitttActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteBatchedFitttActionRequestValidationError) ErrorName() string {
	return "ExecuteBatchedFitttActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteBatchedFitttActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteBatchedFitttActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteBatchedFitttActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteBatchedFitttActionRequestValidationError{}

// Validate checks the field values on ExecuteBatchedFitttActionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ExecuteBatchedFitttActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteBatchedFitttActionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ExecuteBatchedFitttActionResponseMultiError, or nil if none found.
func (m *ExecuteBatchedFitttActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteBatchedFitttActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteBatchedFitttActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteBatchedFitttActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteBatchedFitttActionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExecuteBatchedFitttActionResponseMultiError(errors)
	}

	return nil
}

// ExecuteBatchedFitttActionResponseMultiError is an error wrapping multiple
// validation errors returned by
// ExecuteBatchedFitttActionResponse.ValidateAll() if the designated
// constraints aren't met.
type ExecuteBatchedFitttActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteBatchedFitttActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteBatchedFitttActionResponseMultiError) AllErrors() []error { return m }

// ExecuteBatchedFitttActionResponseValidationError is the validation error
// returned by ExecuteBatchedFitttActionResponse.Validate if the designated
// constraints aren't met.
type ExecuteBatchedFitttActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteBatchedFitttActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteBatchedFitttActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteBatchedFitttActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteBatchedFitttActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteBatchedFitttActionResponseValidationError) ErrorName() string {
	return "ExecuteBatchedFitttActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteBatchedFitttActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteBatchedFitttActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteBatchedFitttActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteBatchedFitttActionResponseValidationError{}

// Validate checks the field values on TrackBatchedFitttActionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TrackBatchedFitttActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrackBatchedFitttActionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TrackBatchedFitttActionRequestMultiError, or nil if none found.
func (m *TrackBatchedFitttActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TrackBatchedFitttActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TrackBatchedFitttActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TrackBatchedFitttActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TrackBatchedFitttActionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActionInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TrackBatchedFitttActionRequestValidationError{
						field:  fmt.Sprintf("ActionInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TrackBatchedFitttActionRequestValidationError{
						field:  fmt.Sprintf("ActionInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TrackBatchedFitttActionRequestValidationError{
					field:  fmt.Sprintf("ActionInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TrackBatchedFitttActionRequestMultiError(errors)
	}

	return nil
}

// TrackBatchedFitttActionRequestMultiError is an error wrapping multiple
// validation errors returned by TrackBatchedFitttActionRequest.ValidateAll()
// if the designated constraints aren't met.
type TrackBatchedFitttActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrackBatchedFitttActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrackBatchedFitttActionRequestMultiError) AllErrors() []error { return m }

// TrackBatchedFitttActionRequestValidationError is the validation error
// returned by TrackBatchedFitttActionRequest.Validate if the designated
// constraints aren't met.
type TrackBatchedFitttActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrackBatchedFitttActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrackBatchedFitttActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrackBatchedFitttActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrackBatchedFitttActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrackBatchedFitttActionRequestValidationError) ErrorName() string {
	return "TrackBatchedFitttActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TrackBatchedFitttActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrackBatchedFitttActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrackBatchedFitttActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrackBatchedFitttActionRequestValidationError{}

// Validate checks the field values on TrackBatchedFitttActionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TrackBatchedFitttActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrackBatchedFitttActionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TrackBatchedFitttActionResponseMultiError, or nil if none found.
func (m *TrackBatchedFitttActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TrackBatchedFitttActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TrackBatchedFitttActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TrackBatchedFitttActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TrackBatchedFitttActionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TrackBatchedFitttActionResponseMultiError(errors)
	}

	return nil
}

// TrackBatchedFitttActionResponseMultiError is an error wrapping multiple
// validation errors returned by TrackBatchedFitttActionResponse.ValidateAll()
// if the designated constraints aren't met.
type TrackBatchedFitttActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrackBatchedFitttActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrackBatchedFitttActionResponseMultiError) AllErrors() []error { return m }

// TrackBatchedFitttActionResponseValidationError is the validation error
// returned by TrackBatchedFitttActionResponse.Validate if the designated
// constraints aren't met.
type TrackBatchedFitttActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrackBatchedFitttActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrackBatchedFitttActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrackBatchedFitttActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrackBatchedFitttActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrackBatchedFitttActionResponseValidationError) ErrorName() string {
	return "TrackBatchedFitttActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TrackBatchedFitttActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrackBatchedFitttActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrackBatchedFitttActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrackBatchedFitttActionResponseValidationError{}
