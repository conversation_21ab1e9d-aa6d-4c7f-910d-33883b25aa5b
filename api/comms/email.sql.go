package comms

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x EventType) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *EventType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := EventType_value[val]
	*x = EventType(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (o *EventMeta) Value() (driver.Value, error) {
	if o == nil {
		return nil, nil
	}
	return protojson.Marshal(o)
}

// Scanner interface implementation for parsing data while reading from DB
func (o *EventMeta) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte, got %T", input)
		return err
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, val, o)
}
