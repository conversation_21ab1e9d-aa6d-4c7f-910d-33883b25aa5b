package developer

import (
	"context"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/recurringpayment/enach/developer"
)

type ParameterFetcher interface {
	FetchParamList(ctx context.Context, entity developer.EnachEntity) ([]*cxDsPb.ParameterMeta, error)
}

type DataFetcher interface {
	FetchData(ctx context.Context, entity developer.EnachEntity, filters []*cxDsPb.Filter) (string, error)
}
