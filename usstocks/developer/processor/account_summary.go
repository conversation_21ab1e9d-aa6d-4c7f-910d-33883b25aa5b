package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"sort"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	accMgrPb "github.com/epifi/gamma/api/usstocks/account"
	devPb "github.com/epifi/gamma/api/usstocks/developer"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	accDao "github.com/epifi/gamma/usstocks/account/dao"
)

type AccountSummaryProcessor struct {
	accountDao      accDao.AccountDao
	investorDao     accDao.InvestorDao
	celestialClient celestialPb.CelestialClient
}

func NewAccountSummaryProcessor(accountDao accDao.AccountDao,
	investorDao accDao.InvestorDao,
	celestialClient celestialPb.CelestialClient) *AccountSummaryProcessor {
	return &AccountSummaryProcessor{
		accountDao:      accountDao,
		investorDao:     investorDao,
		celestialClient: celestialClient,
	}
}

func (s *AccountSummaryProcessor) FetchParamList(_ context.Context, _ devPb.USStocksEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            actorIdParamName,
			Label:           "Actor ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

// nolint:dupl
func (s *AccountSummaryProcessor) FetchData(ctx context.Context, _ devPb.USStocksEntity, filters []*db_state.Filter) (string, error) {
	var marshalledRes []byte
	marshalOptions := protojson.MarshalOptions{}
	marshalOptions.UseEnumNumbers = false

	account, err := s.getAccount(ctx, filters)
	if err != nil {
		logger.Error(ctx, "error getting account", zap.Error(err))
		marshalledRes = []byte(err.Error())
		return string(marshalledRes), nil
	}

	investor, err := s.getInvestor(ctx, filters)
	if err != nil {
		logger.Error(ctx, "error getting investor", zap.Error(err))
		marshalledRes = []byte(err.Error())
		return string(marshalledRes), nil
	}

	workflowHistory, err := s.getWorkflowHistory(ctx, account)
	if err != nil {
		logger.Error(ctx, "error getting workflow history", zap.Error(err))
		marshalledRes = []byte(err.Error())
		return string(marshalledRes), nil
	}

	workflowRequest, err := s.getWorkflowStatus(ctx, account)
	if err != nil {
		logger.Error(ctx, "error getting workflow history", zap.Error(err))
		marshalledRes = []byte(err.Error())
		return string(marshalledRes), nil
	}

	accountSummary := &devPb.AccountSummary{
		CurrentStage:    workflowRequest.GetStage().String(),
		StageStatus:     workflowRequest.GetStatus().String(),
		Account:         account,
		WorkflowHistory: workflowHistory,
		Investor:        investor,
	}

	marshalledRes, err = marshalOptions.Marshal(accountSummary)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error marshalling to json: %v", accountSummary), zap.Error(err))
		marshalledRes = []byte(fmt.Sprintf("error marshalling to json: %v, err: %s", accountSummary, err.Error()))
		return string(marshalledRes), nil
	}
	return string(marshalledRes), nil
}

func (s *AccountSummaryProcessor) getAccount(ctx context.Context, filters []*db_state.Filter) (*accMgrPb.Account, error) {
	if len(filters) == 0 {
		return nil, fmt.Errorf("no filters applied")
	}

	var actorId string

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case actorIdParamName:
			actorId = strings.TrimSpace(filter.GetStringValue())
		default:
			return nil, fmt.Errorf("unhandled filter param name: %s", filter.GetParameterName())
		}
	}

	if actorId == "" {
		return nil, fmt.Errorf("no actor id in input")
	}

	account, err := s.accountDao.GetByActorAndVendor(ctx, actorId, commonvgpb.Vendor_ALPACA, nil)
	if err != nil {
		return nil, fmt.Errorf("error getting account by actor id: %s", actorId)
	}

	return account, nil
}

func (s *AccountSummaryProcessor) getInvestor(ctx context.Context, filters []*db_state.Filter) (*accMgrPb.Investor, error) {
	if len(filters) == 0 {
		return nil, fmt.Errorf("no filters applied")
	}

	var actorId string

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case actorIdParamName:
			actorId = strings.TrimSpace(filter.GetStringValue())
		default:
			return nil, fmt.Errorf("unhandled filter param name: %s", filter.GetParameterName())
		}
	}

	if actorId == "" {
		return nil, fmt.Errorf("no actor id in input")
	}

	investor, err := s.investorDao.GetByActorId(ctx, actorId, nil)
	if err != nil {
		return nil, fmt.Errorf("error getting investor by actor id: %s", actorId)
	}

	return investor, nil
}

func (s *AccountSummaryProcessor) getWorkflowHistory(ctx context.Context, account *accMgrPb.Account) ([]*celestialPb.WorkflowHistory, error) {
	resp, err := s.celestialClient.GetWorkflowHistory(ctx, &celestialPb.GetWorkflowHistoryRequest{
		Identifier: &celestialPb.GetWorkflowHistoryRequest_ClientReqId{ClientReqId: &celestialPb.ClientReqId{
			Id:     account.GetId(),
			Client: workflow.Client_US_STOCKS,
		}},
		Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		return nil, te
	}
	workflowHistoryDetails := resp.GetWorkflowHistoryList()
	sort.Slice(workflowHistoryDetails[:], func(a, b int) bool {
		return workflowHistoryDetails[a].GetUpdatedAt().AsTime().Before(workflowHistoryDetails[b].GetUpdatedAt().AsTime())
	})
	return workflowHistoryDetails, nil
}

func (s *AccountSummaryProcessor) getWorkflowStatus(ctx context.Context, account *accMgrPb.Account) (*celestialPb.WorkflowRequest, error) {
	resp, err := s.celestialClient.GetWorkflowStatus(ctx, &celestialPb.GetWorkflowStatusRequest{
		Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{ClientRequestId: &celestialPb.ClientReqId{
			Id:     account.GetId(),
			Client: workflow.Client_US_STOCKS,
		}},
		Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		return nil, te
	}
	return resp.GetWorkflowRequest(), nil
}
