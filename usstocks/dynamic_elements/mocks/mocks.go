// Code generated by MockGen. DO NOT EDIT.
// Source: dynamic_element_getter_factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	dynamic_elements_getter "github.com/epifi/gamma/usstocks/dynamic_elements/dynamic_elements_getter"
	gomock "github.com/golang/mock/gomock"
)

// MockIDynamicElementsGetterFactory is a mock of IDynamicElementsGetterFactory interface.
type MockIDynamicElementsGetterFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIDynamicElementsGetterFactoryMockRecorder
}

// MockIDynamicElementsGetterFactoryMockRecorder is the mock recorder for MockIDynamicElementsGetterFactory.
type MockIDynamicElementsGetterFactoryMockRecorder struct {
	mock *MockIDynamicElementsGetterFactory
}

// NewMockIDynamicElementsGetterFactory creates a new mock instance.
func NewMockIDynamicElementsGetterFactory(ctrl *gomock.Controller) *MockIDynamicElementsGetterFactory {
	mock := &MockIDynamicElementsGetterFactory{ctrl: ctrl}
	mock.recorder = &MockIDynamicElementsGetterFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDynamicElementsGetterFactory) EXPECT() *MockIDynamicElementsGetterFactoryMockRecorder {
	return m.recorder
}

// GetDynamicElementsGetter mocks base method.
func (m *MockIDynamicElementsGetterFactory) GetDynamicElementsGetter(screen deeplink.Screen) (dynamic_elements_getter.IDynamicElementsGetter, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDynamicElementsGetter", screen)
	ret0, _ := ret[0].(dynamic_elements_getter.IDynamicElementsGetter)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDynamicElementsGetter indicates an expected call of GetDynamicElementsGetter.
func (mr *MockIDynamicElementsGetterFactoryMockRecorder) GetDynamicElementsGetter(screen interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDynamicElementsGetter", reflect.TypeOf((*MockIDynamicElementsGetterFactory)(nil).GetDynamicElementsGetter), screen)
}
