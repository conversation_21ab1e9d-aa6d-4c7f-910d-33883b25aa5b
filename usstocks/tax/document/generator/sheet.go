package generator

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
)

type Style int

const (
	Default Style = iota
	Bold
)

const (
	BufferColWidth = 1
	MaxColumnWidth = 80 // max width of a column including bufferColWidth
)

type Cell struct {
	Text  string
	Style Style
	// number of columns a spans to
	HorizontalSpan int
}

func NewStringCell(text string) *Cell {
	return &Cell{Text: text}
}

func NewStringCellWithStyle(style Style, text string) *Cell {
	return &Cell{Text: text, Style: style}
}

func (c *Cell) WithHorizontalSpan(span int) *Cell {
	c.HorizontalSpan = span
	return c
}

func NewStringCells(list ...string) []*Cell {
	return lo.Map(list, func(item string, _ int) *Cell {
		return NewStringCell(item)
	})
}

func NewStringCellsWitStyle(style Style, list ...string) []*Cell {
	return lo.Map(list, func(item string, _ int) *Cell {
		return NewStringCellWithStyle(style, item)
	})
}

type Section struct {
	Rows [][]*Cell
}

func NewSection() *Section {
	return &Section{}
}

func (r *Section) AppendRow(row []*Cell) *Section {
	r.Rows = append(r.Rows, row)
	return r
}

func (r *Section) AppendSection(section *Section) *Section {
	if section == nil || section.Rows == nil {
		return r
	}
	r.Rows = append(r.Rows, section.Rows...)
	return r
}

func (r *Section) AppendEmptyRows(rowCount int) *Section {
	for i := 0; i < rowCount; i++ {
		r.Rows = append(r.Rows, nil)
	}
	return r
}

// CreateExcel creates an excel from the rows. It also applies the defined style to each cell
func (r *Section) CreateExcel(ctx context.Context) ([]byte, error) {
	maxColumnWidthMap := make(map[string]int)
	excelFile := excelize.NewFile()
	defer func() {
		if err := excelFile.Close(); err != nil {
			logger.Error(ctx, "failed to close excel file", zap.Error(err))
		}
	}()

	styles, err := r.createAndGetStyles(excelFile)
	if err != nil {
		return nil, fmt.Errorf("failed to get styles for file: %w", err)
	}

	for rowIndex, row := range r.Rows {
		colIndex := 1
		for _, cellData := range row {
			cell, err2 := excelize.CoordinatesToCellName(colIndex, rowIndex+1)
			if err2 != nil {
				return nil, fmt.Errorf("failed to get cell name for coordinates")
			}
			colName, colNameErr := excelize.ColumnNumberToName(colIndex)
			if colNameErr != nil {
				return nil, fmt.Errorf("failed to get column name for col_index : %w", colNameErr)
			}
			// only take into account those cells whose data length is less than MaxColumnWidth
			if len(cellData.Text) < MaxColumnWidth {
				// TODO: len(cellData.Text) can be changed to custom impl in order to get max length if there are newlines in data
				maxColumnWidthMap[colName] = max(maxColumnWidthMap[colName], len(cellData.Text))
			}
			err2 = excelFile.SetCellValue(Sheet1, cell, cellData.Text)
			if err2 != nil {
				return nil, fmt.Errorf("failed to set sheet cell %s: %w", cell, err2)
			}

			if cellData.Style != Default {
				styleId, ok := styles[cellData.Style]
				if !ok {
					return nil, fmt.Errorf("style id not found for stype %v", cellData.Style)
				}
				err2 = excelFile.SetCellStyle(Sheet1, cell, cell, styleId)
				if err2 != nil {
					return nil, fmt.Errorf("failed to apply style %v with id %d: %w", cellData.Style, styleId, err2)
				}
			}
			if cellData.HorizontalSpan > 1 {
				colIndex = colIndex + cellData.HorizontalSpan - 1
				endCell, err2 := excelize.CoordinatesToCellName(colIndex, rowIndex+1)
				if err2 != nil {
					return nil, fmt.Errorf("failed to get cell name for end cell for horizontal merging: %w", err2)
				}
				err2 = excelFile.MergeCell(Sheet1, cell, endCell)
				if err2 != nil {
					return nil, fmt.Errorf("failed to merge cells: %w", err2)
				}
			}
			colIndex++
		}
	}
	// set width of each column as max data length in that column + buffer for better visualisation of document
	for colName, colWidth := range maxColumnWidthMap {
		setWidthErr := excelFile.SetColWidth(Sheet1, colName, colName, float64(min(MaxColumnWidth, colWidth+BufferColWidth)))
		if setWidthErr != nil {
			return nil, fmt.Errorf("failed to set column width: %w", setWidthErr)
		}
	}

	buf, err := excelFile.WriteToBuffer()
	if err != nil {
		return nil, fmt.Errorf("unable to write excel to buffer: %w", err)
	}
	return buf.Bytes(), nil
}

// createAndGetStyles creates and returns a map of well defined styles applicable to the excel file
func (r *Section) createAndGetStyles(f *excelize.File) (map[Style]int, error) {
	stylesMap := make(map[Style]int)

	boldStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create bold style")
	}
	stylesMap[Bold] = boldStyle
	return stylesMap, nil
}
