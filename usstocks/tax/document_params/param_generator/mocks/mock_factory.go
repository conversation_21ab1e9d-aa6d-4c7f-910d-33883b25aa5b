// Code generated by MockGen. DO NOT EDIT.
// Source: factory.go

// Package mock_param_generator is a generated GoMock package.
package mock_param_generator

import (
	reflect "reflect"

	tax "github.com/epifi/gamma/api/usstocks/tax"
	param_generator "github.com/epifi/gamma/usstocks/tax/document_params/param_generator"
	gomock "github.com/golang/mock/gomock"
)

// MockParamGeneratorFactory is a mock of ParamGeneratorFactory interface.
type MockParamGeneratorFactory struct {
	ctrl     *gomock.Controller
	recorder *MockParamGeneratorFactoryMockRecorder
}

// MockParamGeneratorFactoryMockRecorder is the mock recorder for MockParamGeneratorFactory.
type MockParamGeneratorFactoryMockRecorder struct {
	mock *MockParamGeneratorFactory
}

// NewMockParamGeneratorFactory creates a new mock instance.
func NewMockParamGeneratorFactory(ctrl *gomock.Controller) *MockParamGeneratorFactory {
	mock := &MockParamGeneratorFactory{ctrl: ctrl}
	mock.recorder = &MockParamGeneratorFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockParamGeneratorFactory) EXPECT() *MockParamGeneratorFactoryMockRecorder {
	return m.recorder
}

// GetParamGenerator mocks base method.
func (m *MockParamGeneratorFactory) GetParamGenerator(documentType tax.UsStockDocumentType) (param_generator.ParamsGenerator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParamGenerator", documentType)
	ret0, _ := ret[0].(param_generator.ParamsGenerator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParamGenerator indicates an expected call of GetParamGenerator.
func (mr *MockParamGeneratorFactoryMockRecorder) GetParamGenerator(documentType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParamGenerator", reflect.TypeOf((*MockParamGeneratorFactory)(nil).GetParamGenerator), documentType)
}
