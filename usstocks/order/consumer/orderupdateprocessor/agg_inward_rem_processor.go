package orderupdateprocessor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	usstocksNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
	"github.com/epifi/be-common/pkg/logger"
	orderpb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	internationalFundTransferPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/usstocks/order"
	workflowPb "github.com/epifi/gamma/api/usstocks/workflow"
	usStocksOrderDao "github.com/epifi/gamma/usstocks/order/dao"
)

type AggregatedInwardRemittanceProcessor struct {
	celestialClient      celestialPb.CelestialClient
	iftClient            internationalFundTransferPb.InternationalFundTransferClient
	aggRemTxnDao         usStocksOrderDao.AggregatedRemittanceTransactionDao
	remittanceRequestDao usStocksOrderDao.TransactionRemittanceRequestDao
}

func NewAggregatedInwardRemittanceProcessor(
	celestialClient celestialPb.CelestialClient,
	iftClient internationalFundTransferPb.InternationalFundTransferClient,
	aggRemTxnDao usStocksOrderDao.AggregatedRemittanceTransactionDao,
	remittanceRequestDao usStocksOrderDao.TransactionRemittanceRequestDao,
) *AggregatedInwardRemittanceProcessor {
	return &AggregatedInwardRemittanceProcessor{
		celestialClient:      celestialClient,
		iftClient:            iftClient,
		aggRemTxnDao:         aggRemTxnDao,
		remittanceRequestDao: remittanceRequestDao,
	}
}

func (p *AggregatedInwardRemittanceProcessor) ProcessOrderUpdate(ctx context.Context, orderWithTxns *orderpb.OrderWithTransactions) error {
	if len(orderWithTxns.GetTransactions()) == 0 {
		return fmt.Errorf("no transactions in order: %s", orderWithTxns.GetOrder().GetId())
	}
	// Pattern: USS/AGG/external-agg-rem-txn-id
	txn1Remark := orderWithTxns.GetTransactions()[0].GetRemarks()
	txn1RawNotifParticular := orderWithTxns.GetTransactions()[0].GetRawNotificationDetails()[paymentPb.AccountingEntryType_CREDIT.String()].GetParticulars()
	logger.Info(ctx, "processing aggregated remittance order update", zap.String("remark", txn1Remark), zap.String("raw_notif_particular", txn1RawNotifParticular))
	txnRemarkParts := strings.Split(txn1Remark, "/")
	if len(txnRemarkParts) < 3 {
		// Fallback to raw notification particulars if remarks are not populated
		txnRemarkParts = strings.Split(txn1RawNotifParticular, "/")
	}
	if len(txnRemarkParts) < 3 {
		return fmt.Errorf("error getting aggregated inward remittance transaction id from remark:%s", orderWithTxns.GetTransactions()[0].GetRemarks())
	}

	aggRemTxn, err := p.checkIfAllDividendTxns(ctx, txnRemarkParts[2])
	if err != nil {
		return errors.Wrap(err, "error checking all constituent txns are dividends")
	}
	if aggRemTxn == nil {
		return nil
	}
	gstRes, err := p.iftClient.GetGstCalculation(ctx, &internationalFundTransferPb.GetGstCalculationRequest{
		GrossValue: orderWithTxns.GetOrder().GetAmount(),
	})
	if err = epifigrpc.RPCError(gstRes, err); err != nil {
		return errors.Wrap(err, "error calculating GST")
	}
	processDividendReq := &workflowPb.ProcessDividendRequest{
		ActorId:           orderWithTxns.GetOrder().GetToActorId(),
		GstAmount:         gstRes.GetGst(),
		DividendAmount:    orderWithTxns.GetOrder().GetAmount(),
		OmsOrderId:        orderWithTxns.GetOrder().GetId(),
		RequestIdentifier: &workflowPb.ProcessDividendRequest_AggregatedRemittanceTxnId{AggregatedRemittanceTxnId: aggRemTxn.GetId()},
	}
	marshalledReq, err := protojson.Marshal(processDividendReq)
	if err != nil {
		return errors.Wrap(err, "error marshalling req")
	}
	res, err := p.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: orderWithTxns.GetOrder().GetToActorId(),
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(usstocksNs.ProcessDividend),
			Payload: marshalledReq,
			ClientReqId: &workflow.ClientReqId{
				Id:     orderWithTxns.GetOrder().GetId(),
				Client: workflow.Client_US_STOCKS,
			},
			Ownership:        commontypes.Ownership_US_STOCKS_ALPACA,
			QualityOfService: celestialPb.QoS_GUARANTEED,
		},
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return errors.Wrap(err, "error initiating process dividend workflow")
	}
	return nil
}

func (p *AggregatedInwardRemittanceProcessor) checkIfAllDividendTxns(ctx context.Context, aggRemTxnExtId string) (*order.AggregatedRemittanceTransaction, error) {
	aggRemTxn, err := p.aggRemTxnDao.GetByExternalId(ctx, aggRemTxnExtId)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("error getting aggregated remittance txn by external id: %s", aggRemTxnExtId))
	}
	remittanceRequests, err := p.remittanceRequestDao.GetByAggTxnId(ctx, aggRemTxn.GetId(), nil)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("error getting remittance requests by aggregated txn id: %s", aggRemTxn.GetId()))
	}

	nonDividendRequestsPresent := false
	nonDividendRequestActivityType := order.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_UNSPECIFIED
	for _, remittanceRequest := range remittanceRequests {
		if remittanceRequest.GetActivityType() != order.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND {
			nonDividendRequestsPresent = true
			nonDividendRequestActivityType = remittanceRequest.GetActivityType()
			break
		}
	}
	if nonDividendRequestsPresent {
		logger.Info(ctx, "aggregated remittance txn contains non-dividend remittance requests, skipping GST refund", zap.String(logger.ACTIVITY_TYPE, nonDividendRequestActivityType.String()))
		return nil, nil
	}
	return aggRemTxn, nil
}
