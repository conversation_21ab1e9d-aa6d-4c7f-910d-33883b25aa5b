package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/google/wire"
	"gorm.io/gorm"
	gormv2 "gorm.io/gorm"
	"gorm.io/plugin/dbresolver"

	cfgConf "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/cmd/types"
	gormctx "github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/usstocks/catalog/dao"
	"github.com/epifi/gamma/usstocks/catalog/dao/model"
)

// WatchlistStockMappingDaoCrdb implements WatchlistStockMappingDao using CRDB
// Deprecated: all new implementations for the table must be added as part of WatchlistStockMappingDaoPgdb which is PGDB
type WatchlistStockMappingDaoCrdb struct {
	db *gorm.DB
}

// NewWatchlistStockMappingDaoCrdb is factory method for creating an instance of watchlist stock mapping dao.
// This method will be used by the injector when providing the dependencies at initialization time.
// Deprecated: in favour of NewWatchlistStockMappingDaoPgdb
func NewWatchlistStockMappingDaoCrdb(db *gorm.DB) *WatchlistStockMappingDaoCrdb {
	return &WatchlistStockMappingDaoCrdb{db: db}
}

// ensure WatchlistStockMappingDaoCrdb implements WatchlistStockMappingDao at compile time
var _ dao.WatchlistStockMappingDao = &WatchlistStockMappingDaoCrdb{}
var _ dao.WatchlistStockMappingDao = &WatchlistStockMappingDaoPgdb{}

type WatchlistStockMappingDaoPgdb struct {
	*WatchlistStockMappingDaoCrdb
}

func NewWatchlistStockMappingDaoPgdb(pgdbMigrationConfig *cfgConf.PgdbConn, db types.UsstocksAlpacaPGDB) *WatchlistStockMappingDaoPgdb {
	var gormDb *gormv2.DB = db

	if pgdbMigrationConfig.PgdbConnAlias() != "" {
		gormDb = gormDb.Clauses(dbresolver.Use(pgdbMigrationConfig.PgdbConnAlias()))
	}

	return &WatchlistStockMappingDaoPgdb{WatchlistStockMappingDaoCrdb: NewWatchlistStockMappingDaoCrdb(gormDb)}
}

func WatchlistStockMappingDaoProvider(pgdbMigrationConfig *cfgConf.PgdbConn, crdbDao *WatchlistStockMappingDaoCrdb, pgdbDao *WatchlistStockMappingDaoPgdb) dao.WatchlistStockMappingDao {
	if pgdbMigrationConfig.UsePgdb() {
		return pgdbDao
	}
	return crdbDao
}

var WatchlistStockMappingWireSet = wire.NewSet(
	WatchlistStockMappingDaoProvider, NewWatchlistStockMappingDaoCrdb, NewWatchlistStockMappingDaoPgdb,
)

func (c *WatchlistStockMappingDaoCrdb) Create(
	ctx context.Context,
	watchlistStockMapping *usstocksCatalogPb.WatchlistStockMapping,
) (*usstocksCatalogPb.WatchlistStockMapping, error) {
	defer metric_util.TrackDuration("usstocks/catalog/dao/impl", "WatchlistStockMappingDaoCrdb", "Create", time.Now())
	if watchlistStockMapping.GetWatchlistId() == "" || watchlistStockMapping.GetStockId() == "" {
		return nil, fmt.Errorf("watchlist id and stock id are required")
	}
	db := gormctx.FromContextOrDefault(ctx, c.db)

	watchlistStockMappingModel, err := model.NewWatchlistStockMapping(watchlistStockMapping)
	if err != nil {
		return nil, fmt.Errorf("failed to call model.NewWatchlistStockMapping: %w", err)
	}
	if err = db.Create(watchlistStockMappingModel).Error; err != nil {
		if storageV2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, fmt.Errorf("failed to call db.Create: %w", err)
	}

	watchlistStockMappingProto, err := watchlistStockMappingModel.ToProto()
	if err != nil {
		return nil, fmt.Errorf("failed to call watchlistStockMappingModel.ToProto: %w", err)
	}
	return watchlistStockMappingProto, nil
}

// nolint: dupl
func (c *WatchlistStockMappingDaoCrdb) GetByFilters(
	ctx context.Context,
	queryOptions ...storageV2.FilterOption,
) ([]*usstocksCatalogPb.WatchlistStockMapping, error) {
	defer metric_util.TrackDuration("usstocks/catalog/dao/impl", "WatchlistStockMappingDaoCrdb", "GetByFilters", time.Now())
	if len(queryOptions) == 0 {
		return nil, fmt.Errorf("query options are required")
	}
	db := gormctx.FromContextOrDefault(ctx, c.db)
	for _, queryOption := range queryOptions {
		db = queryOption.ApplyInGorm(db)
	}
	var watchlistStockMappingModels []*model.WatchlistStockMapping
	if res := db.Find(&watchlistStockMappingModels); res.Error != nil {
		return nil, fmt.Errorf("failed to call db.Find: %w", res.Error)
	}
	watchlistStockMappingProtos := make([]*usstocksCatalogPb.WatchlistStockMapping, len(watchlistStockMappingModels))
	for i, watchlistStockMappingModel := range watchlistStockMappingModels {
		watchlistProto, err := watchlistStockMappingModel.ToProto()
		if err != nil {
			return nil, fmt.Errorf("failed to call watchlistStockMappingModel.ToProto: %w", err)
		}
		watchlistStockMappingProtos[i] = watchlistProto
	}
	return watchlistStockMappingProtos, nil
}

func (c *WatchlistStockMappingDaoCrdb) GetByActorId(
	ctx context.Context,
	actorId string,
	fieldMasks []usstocksCatalogPb.WatchlistStockMappingFieldMask,
) ([]*usstocksCatalogPb.WatchlistStockMapping, error) {
	defer metric_util.TrackDuration("usstocks/catalog/dao/impl", "WatchlistStockMappingDaoCrdb", "GetByActorId", time.Now())
	db := gormctx.FromContextOrDefault(ctx, c.db)

	db = dao.WithFieldSelector(fieldMasks).ApplyInGorm(db)
	db = db.Joins("INNER JOIN watchlists ON watchlists.id = watchlist_stock_mappings.watchlist_id").Where(
		"watchlists.actor_id = ?", actorId).Order("watchlist_stock_mappings.created_at DESC")

	var watchlistStockMappingModels []*model.WatchlistStockMapping
	if res := db.Find(&watchlistStockMappingModels); res.Error != nil {
		return nil, fmt.Errorf("failed to call db.Find: %w", res.Error)
	}
	watchlistStockMappingProtos := make([]*usstocksCatalogPb.WatchlistStockMapping, len(watchlistStockMappingModels))
	for i, watchlistStockMappingModel := range watchlistStockMappingModels {
		watchlistProto, err := watchlistStockMappingModel.ToProto()
		if err != nil {
			return nil, fmt.Errorf("failed to call watchlistStockMappingModel.ToProto: %w", err)
		}
		watchlistStockMappingProtos[i] = watchlistProto
	}
	return watchlistStockMappingProtos, nil
}

func (c *WatchlistStockMappingDaoCrdb) Delete(
	ctx context.Context,
	watchlistId string,
	stockId string,
) error {
	defer metric_util.TrackDuration("usstocks/catalog/dao/impl", "WatchlistStockMappingDaoCrdb", "Delete", time.Now())
	if watchlistId == "" || stockId == "" {
		return fmt.Errorf("watchlist id and stock id are required")
	}
	db := gormctx.FromContextOrDefault(ctx, c.db)
	res := db.Where("watchlist_id = ? AND stock_id = ?", watchlistId, stockId).
		Delete(&model.WatchlistStockMapping{})
	if res.Error != nil {
		return fmt.Errorf("failed to call db.Delete: %w", res.Error)
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}
