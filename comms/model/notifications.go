package model

import (
	"time"

	"github.com/epifi/be-common/pkg/nulltypes"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/epifi/gamma/api/comms"
)

// CommsMessage is the model of messages that is used by the dao layer for sms,email,etc
type CommsNotification struct {
	MessageId    nulltypes.NullString
	ActorId      nulltypes.NullString
	Type         pb.NotificationType
	Data         *pb.NotificationData
	Status       pb.NotificationStatus
	Priority     pb.InAppNotificationPriority
	InAppSection pb.InAppNotificationSection
	SubStatus    pb.NotificationSubStatus
	ExpireAt     nulltypes.NullTime
	CreatedAt    time.Time
	UpdatedAt    time.Time
}

type NotificationsUserLevelAction struct {
	Id              nulltypes.NullString `gorm:"type:uuid;default:gen_random_uuid()"`
	ActorId         nulltypes.NullString
	Action          pb.NotificationAction
	ActionTimestamp nulltypes.NullTime
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func (n *CommsNotification) GetMessageProto() *pb.NotificationDetails {
	return &pb.NotificationDetails{
		MessageId: n.MessageId.GetValue(),
		ActorId:   n.ActorId.GetValue(),
		Type:      n.Type,
		Data:      n.Data,
		Status:    n.Status,
		Priority:  n.Priority,
		Section:   n.InAppSection,
		SubStatus: n.SubStatus,
		ExpireAt:  timestamppb.New(n.ExpireAt.GetValue()),
		CreatedAt: timestamppb.New(n.CreatedAt),
		UpdatedAt: timestamppb.New(n.UpdatedAt),
	}
}

func NewCommsNotificationFromProto(details *pb.NotificationDetails) *CommsNotification {
	return &CommsNotification{
		MessageId:    nulltypes.NewNullString(details.GetMessageId()),
		ActorId:      nulltypes.NewNullString(details.GetActorId()),
		Type:         details.GetType(),
		Data:         details.GetData(),
		Status:       details.GetStatus(),
		Priority:     details.GetPriority(),
		InAppSection: details.GetSection(),
		SubStatus:    details.GetSubStatus(),
		ExpireAt:     nulltypes.NewNullTime(details.GetExpireAt().AsTime()),
	}
}

func (n *NotificationsUserLevelAction) GetMessageProto() *pb.NotificationsUserLevelActionDetails {
	return &pb.NotificationsUserLevelActionDetails{
		Id:              n.Id.GetValue(),
		ActorId:         n.ActorId.GetValue(),
		Action:          n.Action,
		ActionTimestamp: timestamppb.New(n.ActionTimestamp.GetValue()),
		CreatedAt:       timestamppb.New(n.CreatedAt),
		UpdatedAt:       timestamppb.New(n.UpdatedAt),
	}
}

func NewNotificationsUserLevelActionFromProto(details *pb.NotificationsUserLevelActionDetails) *NotificationsUserLevelAction {
	action := &NotificationsUserLevelAction{
		Id:      nulltypes.NewNullString(details.GetId()),
		ActorId: nulltypes.NewNullString(details.GetActorId()),
		Action:  details.GetAction(),
	}
	if details.GetActionTimestamp() == nil {
		action.ActionTimestamp = nulltypes.NewNullTime(time.Time{})
		return action
	}
	action.ActionTimestamp = nulltypes.NewNullTime(details.GetActionTimestamp().AsTime())
	return action
}
