package validators

import (
	"context"

	"github.com/pkg/errors"

	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/comms/validators/validator"
)

type ICommsSendMessageValidator interface {
	Validate(ctx context.Context, req *comms.SendMessageRequest) error
}

type CommsSendMessageValidator struct {
	factory validator.IFactory
}

func NewCommsSendMessageValidator(factory validator.IFactory) *CommsSendMessageValidator {
	return &CommsSendMessageValidator{
		factory: factory,
	}
}

var _ ICommsSendMessageValidator = &CommsSendMessageValidator{}

func (r *CommsSendMessageValidator) Validate(ctx context.Context, req *comms.SendMessageRequest) error {
	// factory will return list of all the validators for flow
	validatorProcessors, err := r.factory.GetValidators(req.GetType())
	if err != nil {
		return errors.Wrap(err, "error while fetching validator processor")
	}
	// process all validators
	for _, validatorProcessor := range validatorProcessors {
		validateErr := validatorProcessor.Validate(ctx, req)
		if validateErr != nil {
			return errors.Wrap(validateErr, "validation failed")
		}
	}
	return nil
}
