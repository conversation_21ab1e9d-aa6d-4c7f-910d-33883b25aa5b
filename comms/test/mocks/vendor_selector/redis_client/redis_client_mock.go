// Code generated by MockGen. DO NOT EDIT.
// Source: comms/vendor_selector/strategies/redis_client.go

// Package mock_strategies is a generated GoMock package.
package redis_client

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"

	comms "github.com/epifi/gamma/api/comms"
)

// MockIRedisClientService is a mock of IRedisClientService interface.
type MockIRedisClientService struct {
	ctrl     *gomock.Controller
	recorder *MockIRedisClientServiceMockRecorder
}

// MockIRedisClientServiceMockRecorder is the mock recorder for MockIRedisClientService.
type MockIRedisClientServiceMockRecorder struct {
	mock *MockIRedisClientService
}

// NewMockIRedisClientService creates a new mock instance.
func NewMockIRedisClientService(ctrl *gomock.Controller) *MockIRedisClientService {
	mock := &MockIRedisClientService{ctrl: ctrl}
	mock.recorder = &MockIRedisClientServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRedisClientService) EXPECT() *MockIRedisClientServiceMockRecorder {
	return m.recorder
}

// DeleteKey mocks base method.
func (m *MockIRedisClientService) DeleteKey(ctx context.Context, key string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteKey", ctx, key)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteKey indicates an expected call of DeleteKey.
func (mr *MockIRedisClientServiceMockRecorder) DeleteKey(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteKey", reflect.TypeOf((*MockIRedisClientService)(nil).DeleteKey), ctx, key)
}

// GetRedisKey mocks base method.
func (m *MockIRedisClientService) GetRedisKey(userIdentifier string, channel comms.Medium) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisKey", userIdentifier, channel)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetRedisKey indicates an expected call of GetRedisKey.
func (mr *MockIRedisClientServiceMockRecorder) GetRedisKey(userIdentifier, channel interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisKey", reflect.TypeOf((*MockIRedisClientService)(nil).GetRedisKey), userIdentifier, channel)
}

// GetRedisKeyValue mocks base method.
func (m *MockIRedisClientService) GetRedisKeyValue(ctx context.Context, key, vendor string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisKeyValue", ctx, key, vendor)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedisKeyValue indicates an expected call of GetRedisKeyValue.
func (mr *MockIRedisClientServiceMockRecorder) GetRedisKeyValue(ctx, key, vendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisKeyValue", reflect.TypeOf((*MockIRedisClientService)(nil).GetRedisKeyValue), ctx, key, vendor)
}

// GetRedisKeyVendor mocks base method.
func (m *MockIRedisClientService) GetRedisKeyVendor(ctx context.Context, key string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisKeyVendor", ctx, key)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedisKeyVendor indicates an expected call of GetRedisKeyVendor.
func (mr *MockIRedisClientServiceMockRecorder) GetRedisKeyVendor(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisKeyVendor", reflect.TypeOf((*MockIRedisClientService)(nil).GetRedisKeyVendor), ctx, key)
}

// IncrementValue mocks base method.
func (m *MockIRedisClientService) IncrementValue(ctx context.Context, key, vendor string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrementValue", ctx, key, vendor)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrementValue indicates an expected call of IncrementValue.
func (mr *MockIRedisClientServiceMockRecorder) IncrementValue(ctx, key, vendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrementValue", reflect.TypeOf((*MockIRedisClientService)(nil).IncrementValue), ctx, key, vendor)
}

// IsKeyExist mocks base method.
func (m *MockIRedisClientService) IsKeyExist(ctx context.Context, key string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsKeyExist", ctx, key)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsKeyExist indicates an expected call of IsKeyExist.
func (mr *MockIRedisClientServiceMockRecorder) IsKeyExist(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsKeyExist", reflect.TypeOf((*MockIRedisClientService)(nil).IsKeyExist), ctx, key)
}

// IsLimitExceeded mocks base method.
func (m *MockIRedisClientService) IsLimitExceeded(ctx context.Context, key, vendor string, limit int) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsLimitExceeded", ctx, key, vendor, limit)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsLimitExceeded indicates an expected call of IsLimitExceeded.
func (mr *MockIRedisClientServiceMockRecorder) IsLimitExceeded(ctx, key, vendor, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsLimitExceeded", reflect.TypeOf((*MockIRedisClientService)(nil).IsLimitExceeded), ctx, key, vendor, limit)
}

// SetRedisKey mocks base method.
func (m *MockIRedisClientService) SetRedisKey(ctx context.Context, key, vendor string, expirationTime time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRedisKey", ctx, key, vendor, expirationTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRedisKey indicates an expected call of SetRedisKey.
func (mr *MockIRedisClientServiceMockRecorder) SetRedisKey(ctx, key, vendor, expirationTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRedisKey", reflect.TypeOf((*MockIRedisClientService)(nil).SetRedisKey), ctx, key, vendor, expirationTime)
}

// UpdateRedisKeyVendor mocks base method.
func (m *MockIRedisClientService) UpdateRedisKeyVendor(ctx context.Context, key, prevVendor, nextVendor string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRedisKeyVendor", ctx, key, prevVendor, nextVendor)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRedisKeyVendor indicates an expected call of UpdateRedisKeyVendor.
func (mr *MockIRedisClientServiceMockRecorder) UpdateRedisKeyVendor(ctx, key, prevVendor, nextVendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRedisKeyVendor", reflect.TypeOf((*MockIRedisClientService)(nil).UpdateRedisKeyVendor), ctx, key, prevVendor, nextVendor)
}
