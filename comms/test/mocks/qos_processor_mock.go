// Code generated by MockGen. DO NOT EDIT.
// Source: comms/service/processor/processor.go

// Package mock_processor is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	comms "github.com/epifi/gamma/api/comms"
	gomock "github.com/golang/mock/gomock"
)

// MockQosProcessor is a mock of QosProcessor interface.
type MockQosProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockQosProcessorMockRecorder
}

// MockQosProcessorMockRecorder is the mock recorder for MockQosProcessor.
type MockQosProcessorMockRecorder struct {
	mock *MockQosProcessor
}

// NewMockQosProcessor creates a new mock instance.
func NewMockQosProcessor(ctrl *gomock.Controller) *MockQosProcessor {
	mock := &MockQosProcessor{ctrl: ctrl}
	mock.recorder = &MockQosProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQosProcessor) EXPECT() *MockQosProcessorMockRecorder {
	return m.recorder
}

// ProcessMessage mocks base method.
func (m *MockQosProcessor) ProcessMessage(ctx context.Context, msg *comms.SendMessageRequest) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessMessage", ctx, msg)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessMessage indicates an expected call of ProcessMessage.
func (mr *MockQosProcessorMockRecorder) ProcessMessage(ctx, msg interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessMessage", reflect.TypeOf((*MockQosProcessor)(nil).ProcessMessage), ctx, msg)
}

// ProcessMessageBatch mocks base method.
func (m *MockQosProcessor) ProcessMessageBatch(ctx context.Context, req *comms.SendMessageBatchRequest) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessMessageBatch", ctx, req)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessMessageBatch indicates an expected call of ProcessMessageBatch.
func (mr *MockQosProcessorMockRecorder) ProcessMessageBatch(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessMessageBatch", reflect.TypeOf((*MockQosProcessor)(nil).ProcessMessageBatch), ctx, req)
}

// MockMessagePublisher is a mock of MessagePublisher interface.
type MockMessagePublisher struct {
	ctrl     *gomock.Controller
	recorder *MockMessagePublisherMockRecorder
}

// MockMessagePublisherMockRecorder is the mock recorder for MockMessagePublisher.
type MockMessagePublisherMockRecorder struct {
	mock *MockMessagePublisher
}

// NewMockMessagePublisher creates a new mock instance.
func NewMockMessagePublisher(ctrl *gomock.Controller) *MockMessagePublisher {
	mock := &MockMessagePublisher{ctrl: ctrl}
	mock.recorder = &MockMessagePublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMessagePublisher) EXPECT() *MockMessagePublisherMockRecorder {
	return m.recorder
}

// PublishMessage mocks base method.
func (m *MockMessagePublisher) PublishMessage(ctx context.Context, req *comms.SendMessageRequest, commsId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishMessage", ctx, req, commsId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishMessage indicates an expected call of PublishMessage.
func (mr *MockMessagePublisherMockRecorder) PublishMessage(ctx, req, commsId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishMessage", reflect.TypeOf((*MockMessagePublisher)(nil).PublishMessage), ctx, req, commsId)
}

// MockMessageSender is a mock of MessageSender interface.
type MockMessageSender struct {
	ctrl     *gomock.Controller
	recorder *MockMessageSenderMockRecorder
}

// MockMessageSenderMockRecorder is the mock recorder for MockMessageSender.
type MockMessageSenderMockRecorder struct {
	mock *MockMessageSender
}

// NewMockMessageSender creates a new mock instance.
func NewMockMessageSender(ctrl *gomock.Controller) *MockMessageSender {
	mock := &MockMessageSender{ctrl: ctrl}
	mock.recorder = &MockMessageSenderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMessageSender) EXPECT() *MockMessageSenderMockRecorder {
	return m.recorder
}

// SendMessage mocks base method.
func (m *MockMessageSender) SendMessage(ctx context.Context, req *comms.SendMessageRequest) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", ctx, req)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockMessageSenderMockRecorder) SendMessage(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockMessageSender)(nil).SendMessage), ctx, req)
}
