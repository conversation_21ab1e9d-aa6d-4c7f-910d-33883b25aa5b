package consumer

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cmd/types"

	"github.com/epifi/be-common/pkg/epificontext"
	queuepkg "github.com/epifi/be-common/pkg/queue"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	consumerPb "github.com/epifi/be-common/api/celestial/consumer"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/gamma/celestial/dao"
	"github.com/epifi/gamma/celestial/internal"
	"github.com/epifi/gamma/celestial/internal/adaptor"
	logHelper "github.com/epifi/gamma/celestial/internal/log"
	"github.com/epifi/be-common/pkg/epifitemporal"
)

type Service struct {
	// UnimplementedConsumerServer is embedded to have forward compatible implementations
	consumerPb.UnimplementedConsumerServer
	workflowRequestDao dao.WorkflowRequestDao
	clientFactory      epifitemporal.ClientFactory
	temporalProc       internal.TemporalProcessor
	codecAesKey        types.CelestialCodecAesKey
}

func NewService(workflowRequestDao dao.WorkflowRequestDao, clientFactory epifitemporal.ClientFactory, temporalProc internal.TemporalProcessor, codecAesKey types.CelestialCodecAesKey) *Service {
	return &Service{
		workflowRequestDao: workflowRequestDao,
		clientFactory:      clientFactory,
		temporalProc:       temporalProc,
		codecAesKey:        codecAesKey,
	}
}

func (s *Service) SignalWorkflow(ctx context.Context, req *consumerPb.SignalWorkflowRequest) (*consumerPb.SignalWorkflowResponse, error) {
	var (
		res             = &consumerPb.SignalWorkflowResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{}}
		workflowRequest *celestialPb.WorkflowRequest
		err             error
		workflowReqList []*celestialPb.WorkflowRequest
	)

	switch req.GetIdentifier().(type) {
	// fetching workflow requests using client request ids
	case *consumerPb.SignalWorkflowRequest_ClientReqId:
		workflowReqList, err = s.workflowRequestDao.BatchGetByClientReqId(ctx, []*workflowPb.ClientReqId{adaptor.GetWorkflowClientReqId(req.GetClientReqId())}, req.GetOwnership(), req.GetUseCase())
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "workflow request not found for given client req id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId().GetId()))
			res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
			return res, nil
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			logger.Error(ctx, "unable to get workflow request", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId().GetId()), zap.Error(err))
			res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
			return res, nil
		case err != nil:
			logger.Error(ctx, "error in fetching workflow request", logHelper.ClientReqID(req.GetClientReqId(), zap.Error(err))...)
			res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
			return res, nil
		default:
			logger.Debug(ctx, "workflow request fetched successfully", logHelper.ClientReqID(req.GetClientReqId())...)
			workflowRequest = workflowReqList[0]
		}
	// fetching workflow requests using client request ids
	case *consumerPb.SignalWorkflowRequest_WorkflowReqId:
		workflowRequest, err = s.workflowRequestDao.GetByID(ctx, req.GetWorkflowReqId(), req.GetOwnership(), req.GetUseCase())
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "workflow request not found for given id", zap.String(logger.CLIENT_REQUEST_ID, req.GetWorkflowReqId()))
			res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
			return res, nil
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			logger.Error(ctx, "unable to get workflow request", zap.String(logger.WORKFLOW_REQ_ID, req.GetWorkflowReqId()), zap.Error(err))
			res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
			return res, nil
		case err != nil:
			logger.Error(ctx, "error in fetching workflow request", zap.String(logger.WORKFLOW_REQ_ID, req.GetWorkflowReqId()), zap.Error(err))
			res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
			return res, nil
		default:
			logger.Debug(ctx, "workflow request fetched successfully", zap.String(logger.WORKFLOW_REQ_ID, req.GetWorkflowReqId()), zap.Error(err))
		}
	default:
		logger.Error(ctx, fmt.Sprintf("unsupported identifier: %T", req.GetIdentifier()))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}

	err = s.temporalProc.SignalWorkflow(ctx, workflowRequest, epifitemporal.Signal(req.GetSignalId()), req.GetPayload())
	if err != nil {
		logger.Error(ctx, "error in signaling workflow", logHelper.ClientReqID(req.GetClientReqId(), zap.Error(err))...)
		res.ResponseHeader.Status = queuepkg.GetStatusFrom(err)
		return res, nil
	}

	res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

// nolint:funlen
func (s *Service) InitiateWorkflow(ctx context.Context, req *consumerPb.InitiateWorkflowRequest) (*consumerPb.InitiateWorkflowResponse, error) {
	var (
		res = &consumerPb.InitiateWorkflowResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{}}
		err error
	)

	ctx = epificontext.WithOwnership(ctx, req.GetOwnership())
	workflowResponse, err := s.workflowRequestDao.GetByID(ctx, req.GetWorkflowReqId(), req.GetOwnership(), req.GetUseCase())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching workflow request", zap.Error(err))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	default:
		logger.Debug(ctx, "workflow request fetched successfully")

	}

	err = s.temporalProc.InitiateWorkflow(ctx, workflowResponse, req.GetUseCase())
	if err != nil {
		logger.Error(ctx, "Failed to initiated workflow",
			logHelper.ClientReqID(workflowResponse.GetClientReqId(),
				zap.Error(err), zap.String(logger.WORKFLOW_REQ_ID, workflowResponse.GetId()))...)
		res.ResponseHeader.Status = queuepkg.GetStatusFrom(err)
		return res, nil
	}

	res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS

	return res, nil
}
