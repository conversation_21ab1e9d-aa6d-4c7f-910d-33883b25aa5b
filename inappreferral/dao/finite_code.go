package dao

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"github.com/golang/protobuf/ptypes"
	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/nulltypes"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/inappreferral/dao/model"
)

const (
	// TODO(harish): confirm length
	finiteCodeLength                        = 10
	alphaNumericCharset                     = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
	phoneNumberReferralCodeUniqueConstraint = "finite_code_phone_number_referral_code_idx"
)

// FiniteCodeDaoCrdb implements FiniteCodeDao using CRDB
type FiniteCodeDaoCrdb struct {
	db    *gormv2.DB
	idGen idgen.IdGenerator
}

// Ensure FiniteCodeDaoCrdb implements FiniteCodeDao at compile time
var _ FiniteCodeDao = &FiniteCodeDaoCrdb{}

var rng = rand.New(idgen.NewCryptoSeededSource()) //nolint:gosec

// Factory method for creating an instance of finite code dao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewFiniteCodeDaoCrdb(db pkgTypes.EpifiCRDB, idGen idgen.IdGenerator) *FiniteCodeDaoCrdb {
	return &FiniteCodeDaoCrdb{db: db, idGen: idGen}
}

// Create creates a new record entry in the FiniteCode table.
func (fcd *FiniteCodeDaoCrdb) Create(ctx context.Context, finiteCode *inAppReferralPb.FiniteCode) (*inAppReferralPb.FiniteCode, error) {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)

	id, err := fcd.idGen.Get(idgen.FiniteCode)
	if err != nil {
		return nil, fmt.Errorf("id generation failed: %w", err)
	}

	finiteCodeModel := convertToFiniteCodeModel(finiteCode)
	// By default during finiteCode creation ensure IsActive is true and claimedCount is 0
	finiteCodeModel.IsActive = nulltypes.NewNullBool(true)
	finiteCodeModel.ClaimedCount = 0
	finiteCodeModel.Id = id

	// set the new finite code
	if finiteCodeModel.Code, err = fcd.newFiniteCode(ctx, db); err != nil {
		return nil, fmt.Errorf("failed to generate new finite code, %w", err)
	}

	if err := db.Create(finiteCodeModel).Error; err != nil {
		if storagev2.IsViolatingUniqueConstraint(err, "") {
			return nil, fmt.Errorf("failed to create finite code: %w", epifierrors.ErrUniqueConstraintViolation)
		}
		return nil, fmt.Errorf("failed to create finite code: %w", err)
	}

	return convertToFiniteCodeProto(finiteCodeModel)
}

// IncrementClaimedCount increments the claimed_count by 1 for a finite code.
// This method is to be used whenever a finite code is claimed by a referee.
// If the incremented claimed_count is greater than the claim_limit, the method returns an error.
func (fcd *FiniteCodeDaoCrdb) IncrementClaimedCount(ctx context.Context, finiteCode *inAppReferralPb.FiniteCode) (*inAppReferralPb.FiniteCode, error) {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "IncrementClaimedCount", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	if finiteCode.ClaimedCount == finiteCode.ClaimLimit {
		return nil, fmt.Errorf("claim limit exhausted, can't increment claimed count")
	}
	finiteCodeModel := convertToFiniteCodeModel(finiteCode)
	finiteCodeModel.ClaimedCount += 1

	if res := db.Model(&model.FiniteCode{}).Where("id = ?", finiteCode.Id).Update("claimed_count", finiteCodeModel.ClaimedCount); res.Error != nil || res.RowsAffected == 0 {
		if res.RowsAffected == 0 {
			return nil, fmt.Errorf("failed to increment claimed count: id: %s, %w", finiteCode.Id, epifierrors.ErrRecordNotFound)
		}
		return nil, fmt.Errorf("failed to increment claimed count: id: %s: %w", finiteCode.Id, res.Error)
	}
	return convertToFiniteCodeProto(finiteCodeModel)
}

// StoreReferralInviteLinkInfoForActor stores/replaces the referrals-invite link info for the actor
func (fcd *FiniteCodeDaoCrdb) StoreReferralInviteLinkInfoForActor(ctx context.Context, actorId string, referralInviteLinkInfo *inAppReferralPb.ReferralInviteLinkInfo) error {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "StoreReferralInviteLinkInfoForActor", time.Now())
	if actorId == "" || referralInviteLinkInfo == nil {
		return fmt.Errorf("mandatory params to update referrals invite link info missing")
	}

	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)

	res := db.Model(&model.FiniteCode{}).Where("actor_id = ?", actorId).Update("referral_invite_link_info", referralInviteLinkInfo)
	if res.Error != nil {
		return fmt.Errorf("error updating referral-invite-link-info for actor: %s, err: %w", actorId, res.Error)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("failed to update referral-invite-link-info for actor: %s, err: %w", actorId, epifierrors.ErrRecordNotFound)
	}

	return nil
}

// GetFiniteCodesForActor returns a list of finite codes for an actor ID.
func (fcd *FiniteCodeDaoCrdb) GetFiniteCodesForActor(ctx context.Context, actorId string) ([]*inAppReferralPb.FiniteCode, error) {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "GetFiniteCodesForActor", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	finiteCodes := make([]*model.FiniteCode, 0)
	if err := db.Where("actor_id = ?", actorId).
		Order("created_at DESC").Find(&finiteCodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get finite codes by actorId %s, %w", actorId, err)
	}
	if len(finiteCodes) == 0 {
		return nil, fmt.Errorf("failed to get finite codes by actorID %s, %w", actorId, epifierrors.ErrRecordNotFound)
	}

	return convertToFiniteCodeProtos(finiteCodes)
}

// GetFiniteCodesOfChannelAndTypeForActor returns a list of finite codes for an actor ID for a list of channels and type
// channels and type are optional params.
func (fcd *FiniteCodeDaoCrdb) GetFiniteCodesOfChannelAndTypeForActor(ctx context.Context, actorId string, channels []inAppReferralEnumPb.FiniteCodeChannel,
	types []inAppReferralEnumPb.FiniteCodeType) ([]*inAppReferralPb.FiniteCode, error) {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "GetFiniteCodesOfChannelAndTypeForActor", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	finiteCodes := make([]*model.FiniteCode, 0)

	query := db.Where("actor_id = ?", actorId)

	if len(channels) > 0 {
		query = query.Where("channel IN (?)", channels)
	}

	if len(types) > 0 {
		query = query.Where("type IN (?)", types)
	}
	query = query.Order("created_at DESC")

	if err := query.Find(&finiteCodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get finite codes by actorId %s, %w", actorId, err)
	}
	if len(finiteCodes) == 0 {
		return nil, fmt.Errorf("failed to get finite codes by actorID %s, %w", actorId, epifierrors.ErrRecordNotFound)
	}

	return convertToFiniteCodeProtos(finiteCodes)
}

// GetByFiniteCode fetches a finite code object by it's unique code
func (fcd *FiniteCodeDaoCrdb) GetByFiniteCode(ctx context.Context, finiteCode string) (*inAppReferralPb.FiniteCode, error) {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "GetByFiniteCode", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	var fetchedFiniteCode model.FiniteCode
	if err := db.Where("code = ?", finiteCode).First(&fetchedFiniteCode).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to get finite code by code: %s, error: %v: %w", finiteCode, err, epifierrors.ErrRecordNotFound)
		}
		return nil, fmt.Errorf("failed to get finite code by code: %s: %w", finiteCode, err)
	}

	return convertToFiniteCodeProto(&fetchedFiniteCode)
}

// GetById fetches a finite code object by id
func (fcd *FiniteCodeDaoCrdb) GetById(ctx context.Context, id string) (*inAppReferralPb.FiniteCode, error) {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "GetById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	var fetchedFiniteCode model.FiniteCode
	if err := db.Where("id = ?", id).First(&fetchedFiniteCode).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to get finite code by id: %s, error: %v: %w", id, err, epifierrors.ErrRecordNotFound)
		}
		return nil, fmt.Errorf("failed to get finite code by id: %s: %w", id, err)
	}

	return convertToFiniteCodeProto(&fetchedFiniteCode)
}

// GetFiniteCodesByIds gets a list of finite codes for a list of finite code Ids
// Note: if for a finite code id, no corresponding record is found, the method will just skip fetching for that id
func (fcd *FiniteCodeDaoCrdb) GetFiniteCodesByIds(ctx context.Context, finiteCodeIds []string) ([]*inAppReferralPb.FiniteCode, error) {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "GetFiniteCodesByIds", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	finiteCodes := make([]*model.FiniteCode, 0)

	if err := db.Where("id IN (?)", finiteCodeIds).
		Order("created_at DESC").Find(&finiteCodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get finite codes by Ids, %w", err)
	}
	if len(finiteCodes) == 0 {
		return nil, fmt.Errorf("failed to get finite codes by Ids, %w", epifierrors.ErrRecordNotFound)
	}

	return convertToFiniteCodeProtos(finiteCodes)
}

// DisableFiniteCode disables a finite code by setting the is_active flag to false
func (fcd *FiniteCodeDaoCrdb) DisableFiniteCode(ctx context.Context, finiteCode *inAppReferralPb.FiniteCode) error {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "DisableFiniteCode", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	finiteCodeModel := convertToFiniteCodeModel(finiteCode)
	finiteCodeModel.IsActive = nulltypes.NewNullBool(false)

	if res := db.Model(&model.FiniteCode{}).Where("id = ?", finiteCodeModel.Id).
		Update("is_active", finiteCodeModel.IsActive); res.Error != nil || res.RowsAffected == 0 {
		if res.RowsAffected == 0 {
			return fmt.Errorf("failed to disable finite code id: %s, %w", finiteCode.Id, epifierrors.ErrRecordNotFound)
		}
		return fmt.Errorf("failed to disable finite code id: %s, %w", finiteCode.Id, res.Error)
	}
	return nil
}

// nolint:dupl
// GetByPhoneNumber fetches a finite code object by its phone number
func (fcd *FiniteCodeDaoCrdb) GetByPhoneNumber(ctx context.Context, phoneNumber string) (*inAppReferralPb.FiniteCode, error) {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "GetByPhoneNumber", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	var fetchedFiniteCode model.FiniteCode
	if err := db.Where("phone_number_referral_code = ?", phoneNumber).Take(&fetchedFiniteCode).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to get finite code by phone number: %s, error: %v: %w", phoneNumber, err, epifierrors.ErrRecordNotFound)
		}
		return nil, fmt.Errorf("failed to get finite code by phone number: %s: %w", phoneNumber, err)
	}

	return convertToFiniteCodeProto(&fetchedFiniteCode)
}

func (fcd *FiniteCodeDaoCrdb) UpdatePhoneNumberReferralCodeById(ctx context.Context, id string, phoneNumber string) error {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "UpdatePhoneNumberReferralCodeById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	finiteCodeModel := convertToFiniteCodeModel(&inAppReferralPb.FiniteCode{
		Id:                      id,
		PhoneNumberReferralCode: phoneNumber,
	})

	res := db.Model(&model.FiniteCode{}).Where("id = ?", finiteCodeModel.Id).
		Update("phone_number_referral_code", finiteCodeModel.PhoneNumberReferralCode)
	if res.Error != nil {
		if storagev2.IsViolatingUniqueConstraint(res.Error, phoneNumberReferralCodeUniqueConstraint) {
			return fmt.Errorf("failed to update finite code by id: %s, %w", id, epifierrors.ErrUniqueConstraintViolation)
		}
		return fmt.Errorf("failed to update finite code by id: %s, %w", id, res.Error)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("failed to update finite code by id: %s, %w", id, epifierrors.ErrRecordNotFound)
	}

	return nil
}

func (fcd *FiniteCodeDaoCrdb) MarkPhoneNumberAsNullById(ctx context.Context, id string) error {
	defer metric_util.TrackDuration("inappreferral/dao", "FiniteCodeDaoCrdb", "MarkPhoneNumberAsNullById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, fcd.db)
	finiteCodeModel := convertToFiniteCodeModel(&inAppReferralPb.FiniteCode{
		Id: id,
	})

	res := db.Model(&model.FiniteCode{}).Select("phone_number_referral_code").Where("id = ?", finiteCodeModel.Id).
		Update("phone_number_referral_code", gormv2.Expr("NULL"))
	if res.Error != nil {
		return fmt.Errorf("failed to mark phone number referral code as null for finite code by id: %s, %w", id, res.Error)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("failed to mark phone number referral code as null for finite code by id: %s, %w", id, epifierrors.ErrRecordNotFound)
	}

	return nil
}

func convertToFiniteCodeProtos(finiteCodeModels []*model.FiniteCode) ([]*inAppReferralPb.FiniteCode, error) {
	var finiteCodes = make([]*inAppReferralPb.FiniteCode, 0)

	for _, finiteCodeModel := range finiteCodeModels {
		finiteCode, err := convertToFiniteCodeProto(finiteCodeModel)
		if err != nil {
			return nil, err
		}
		finiteCodes = append(finiteCodes, finiteCode)
	}
	return finiteCodes, nil
}

func convertToFiniteCodeModel(finiteCode *inAppReferralPb.FiniteCode) *model.FiniteCode {
	return &model.FiniteCode{
		Id:                           finiteCode.Id,
		Code:                         finiteCode.Code,
		ActorId:                      nulltypes.NewNullString(finiteCode.ActorId),
		Channel:                      finiteCode.Channel,
		Type:                         finiteCode.Type,
		ClaimLimit:                   finiteCode.ClaimLimit,
		ClaimedCount:                 finiteCode.ClaimedCount,
		IsActive:                     nulltypes.NewNullBool(finiteCode.IsActive),
		SkipEmploymentCheckPrivilege: finiteCode.SkipEmploymentCheckPrivilege,
		ReferralInviteLinkInfo:       finiteCode.ReferralInviteLinkInfo,
		PhoneNumberReferralCode:      nulltypes.NewNullString(finiteCode.PhoneNumberReferralCode),
	}
}

func convertToFiniteCodeProto(finiteCodeModel *model.FiniteCode) (*inAppReferralPb.FiniteCode, error) {
	var err error
	finiteCodeProto := &inAppReferralPb.FiniteCode{
		Id:                           finiteCodeModel.Id,
		Code:                         finiteCodeModel.Code,
		ActorId:                      finiteCodeModel.ActorId.GetValue(),
		Channel:                      finiteCodeModel.Channel,
		Type:                         finiteCodeModel.Type,
		ClaimLimit:                   finiteCodeModel.ClaimLimit,
		ClaimedCount:                 finiteCodeModel.ClaimedCount,
		IsActive:                     finiteCodeModel.IsActive.GetValue(),
		SkipEmploymentCheckPrivilege: finiteCodeModel.SkipEmploymentCheckPrivilege,
		ReferralInviteLinkInfo:       finiteCodeModel.ReferralInviteLinkInfo,
		PhoneNumberReferralCode:      finiteCodeModel.PhoneNumberReferralCode.GetValue(),
	}
	if finiteCodeProto.CreatedAt, err = ptypes.TimestampProto(finiteCodeModel.CreatedAt); err != nil {
		return nil, fmt.Errorf("failed to convert created at from model to proto: %w", err)
	}
	if finiteCodeProto.UpdatedAt, err = ptypes.TimestampProto(finiteCodeModel.UpdatedAt); err != nil {
		return nil, fmt.Errorf("failed to convert updated at from model to proto: %w", err)
	}
	if finiteCodeModel.DeletedAt.Valid {
		if finiteCodeProto.DeletedAt, err = ptypes.TimestampProto(finiteCodeModel.DeletedAt.Time); err != nil {
			return nil, fmt.Errorf("failed to convert deleted at from model to proto: %w", err)
		}
	}
	return finiteCodeProto, nil
}

func (fcd *FiniteCodeDaoCrdb) newFiniteCode(ctx context.Context, db *gormv2.DB) (string, error) {
	// TODO(harish): add prefix to finite code to avoid collisions with existing codes
	const maxTries = 10
	for i := 0; i < maxTries; i++ {
		fc := finiteCode(finiteCodeLength, alphaNumericCharset)
		_, err := fcd.GetByFiniteCode(ctx, fc)
		if err == nil {
			logger.Debug(ctx, "Finite code repeated", zap.String("finite code", fc))
			continue
		}

		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return fc, nil
		} else {
			// DB Error - Sleep and retry.
			time.Sleep(100 * time.Millisecond)
		}
	}
	return "", errors.New("error to generate unique finite code")
}

// nolint: gosec
func finiteCode(length int, charSet string) string {
	// TODO(harish): Follow all best practices in https://www.voucherify.io/blog/how-to-generate-unique-coupon-codes.
	rc := ""
	for i := 0; i < length; i++ {
		idx := rng.Intn(len(charSet))
		rc += string(charSet[idx])
	}
	return rc
}
