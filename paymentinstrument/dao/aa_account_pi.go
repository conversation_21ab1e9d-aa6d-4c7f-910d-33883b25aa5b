package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	gormv2 "gorm.io/gorm"

	pkgCmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	accountsPb "github.com/epifi/gamma/api/accounts"
	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/paymentinstrument/dao/model"
)

// AAAccountPiDaoPgdb implements AAAccountPiDao using CRDB
type AAAccountPiDaoPgdb struct {
	db    *gormv2.DB
	idGen idgen.IdGenerator
}

// Ensure AAAccountPiDaoPgdb implements AccountPiDao at compile time
var _ AAAccountPiDao = &AAAccountPiDaoPgdb{}

// Factory method for creating an instance of aaAccountPI dao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewAAAccountPiDaoPgdb(db pkgCmdTypes.PaymentInstrumentPGDB, idGen idgen.IdGenerator) *AAAccountPiDaoPgdb {
	return &AAAccountPiDaoPgdb{db: db, idGen: idGen}
}

// Create creates an relationship between aa-account <> PI
func (ap *AAAccountPiDaoPgdb) Create(ctx context.Context, aaAccountPI *accountPIPb.AAAccountPI) (*accountPIPb.AAAccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AAAccountPiDaoPgdb", "Create", time.Now())
	id, err := ap.idGen.Get(idgen.AccountPI)
	if err != nil {
		return nil, fmt.Errorf("id generation failed: %w", err)
	}

	aaAccountPIModel := model.NewAaAccountPiModel(aaAccountPI)
	aaAccountPIModel.Id = id

	db := gormctxv2.FromContextOrDefault(ctx, ap.db)
	if err = db.Create(&aaAccountPIModel).Error; err != nil {
		return nil, fmt.Errorf("failed to insert aaAccountPI: %w", err)
	}

	return aaAccountPIModel.ToProto(), nil
}

func (ap *AAAccountPiDaoPgdb) GetByPiId(ctx context.Context, piId string) (*accountPIPb.AAAccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AAAccountPiDaoPgdb", "GetByPiId", time.Now())
	var aaAccountPiModel model.AAAccountPI
	db := gormctxv2.FromContextOrDefault(ctx, ap.db)
	if err := db.Where("pi_id = ?", piId).First(&aaAccountPiModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get aaAccountPI in DB, pi_id: %s: %w", piId, err)
	}

	return aaAccountPiModel.ToProto(), nil
}

// GetByAccountId fetches PIs belonging to a given aa-accountId and accountType/accountTableName
func (ap *AAAccountPiDaoPgdb) GetByAccountId(ctx context.Context, aaAccountId string, accountType accountsPb.Type) ([]*accountPIPb.AAAccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AAAccountPiDaoPgdb", "GetByAccountId", time.Now())
	var aaAccountPIModels []*model.AAAccountPI
	db := gormctxv2.FromContextOrDefault(ctx, ap.db)
	if err := db.Where("account_id = ? AND account_type = ?", aaAccountId, accountType).Find(&aaAccountPIModels).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch accountPIs, accountId: %s, accountType:%s, %w", aaAccountId, accountType, err)
	}
	if len(aaAccountPIModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return model.NewAaAccountPiProtoList(aaAccountPIModels), nil
}

// DeleteByAccountId deletes the account pi relation for given account id
func (ap *AAAccountPiDaoPgdb) DeleteByAccountId(ctx context.Context, accountId string) error {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AAAccountPiDaoPgdb", "DeleteByAccountId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, ap.db)
	err := db.Unscoped().Where("account_id = ?", accountId).Delete([]*model.AAAccountPI{}).Error
	if err != nil {
		return fmt.Errorf("error deleting aa account pi relation: %w", err)
	}
	return nil
}

// GetById fetches AaAccountPi belonging to given Id.
func (ap *AAAccountPiDaoPgdb) GetById(ctx context.Context, id string) (*accountPIPb.AAAccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AAAccountPiDaoPgdb", "GetById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, ap.db)
	var aaAccountPiModel model.AAAccountPI
	if err := db.Where("id = ?", id).First(&aaAccountPiModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get aaAccountPI in DB, id: %s: %w", id, err)
	}

	return aaAccountPiModel.ToProto(), nil
}

// GetByActorId fetches list of AaAccountPis for the given actor Id.
func (ap *AAAccountPiDaoPgdb) GetByActorId(ctx context.Context, actorId string) ([]*accountPIPb.AAAccountPI, error) {
	defer metric_util.TrackDuration("paymentinstrument/dao", "AAAccountPiDaoPgdb", "GetByActorId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, ap.db)

	if actorId == "" {
		return nil, fmt.Errorf("actor Id can't be empty: %w", epifierrors.ErrInvalidArgument)
	}

	var aaAccountPiModels []*model.AAAccountPI

	if err := db.Where("actor_id = ?", actorId).Find(&aaAccountPiModels).Error; err != nil {
		return nil, fmt.Errorf("failed to get aaAccountPI in DB, actor_id: %s: %w", actorId, err)
	}

	if len(aaAccountPiModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return model.NewAaAccountPiProtoList(aaAccountPiModels), nil
}
