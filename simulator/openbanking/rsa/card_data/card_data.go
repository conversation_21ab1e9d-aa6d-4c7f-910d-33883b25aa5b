package card

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	vendorsFederal "github.com/epifi/gamma/api/vendors/federal"

	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/logger"
	"go.uber.org/zap"
)

func GetEncryptedCardData(rsaCryptor crypto.Cryptor, data *vendorsFederal.CardData) (string, error) {
	dataToEncode, _ := json.Marshal(data)
	encryptedData, err := rsaCryptor.Encrypt(context.Background(), dataToEncode, "")
	if err != nil {
		return "", err
	}
	logger.InfoNoCtx("debug ", zap.String("debug carddata", string(encryptedData)))
	return string(encryptedData), nil
}

func RandSeqDigits(n int) string {
	var digits = []rune("0123456789")

	s := rand.NewSource(time.Now().UnixNano())
	r := rand.New(s)

	b := make([]rune, n)
	for i := range b {
		b[i] = digits[r.Intn(len(digits))]
	}
	return string(b)
}

func ExpiryDateGenerator() string {
	s := rand.NewSource(time.Now().UnixNano())
	r := rand.New(s)
	month := "" + fmt.Sprintf("%02v", r.Intn(12)+1)
	year := "" + fmt.Sprintf("%v", r.Intn(6)+time.Now().Year()%100)
	return month + year
}

func GenerateEncryptedCardDataWithoutCVV(rsaCryptor crypto.Cryptor) (string, error) {
	card := &vendorsFederal.CardData{
		CardNumber: RandSeqDigits(16),
		Expiry:     ExpiryDateGenerator(),
	}
	return GetEncryptedCardData(rsaCryptor, card)
}

func GenerateEncryptedCardDataWithCVV(rsaCryptor crypto.Cryptor) (string, error) {
	card := &vendorsFederal.CardData{
		CardNumber: RandSeqDigits(16),
		Expiry:     ExpiryDateGenerator(),
		Cvv:        RandSeqDigits(3),
	}
	return GetEncryptedCardData(rsaCryptor, card)
}
