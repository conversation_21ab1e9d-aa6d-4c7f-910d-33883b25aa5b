package consumer

import (
	"bytes"
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"

	queuePb "github.com/epifi/be-common/api/queue"
	pb "github.com/epifi/gamma/api/simulator/openbanking/shipping_preference/federal"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
)

type ShippingAddressUpdateCallBackService struct {
	h *http.Client
}

func NewShippingAddressUpdateCallBackService(h *http.Client) *ShippingAddressUpdateCallBackService {
	return &ShippingAddressUpdateCallBackService{h: h}
}

// ProcessShippingAddressUpdateCallBack initiates a call back event to the callbackURL specified in the request
// This method is invoked by queue subscriber to consume call back notifications from simulator's consumer
func (s *ShippingAddressUpdateCallBackService) ProcessShippingAddressUpdateCallBack(ctx context.Context,
	req *pb.ProcessShippingAddressUpdateCallBackRequest) (*pb.ProcessShippingAddressUpdateCallBackResponse, error) {
	res := &pb.ProcessShippingAddressUpdateCallBackResponse{}

	res.ResponseHeader = &queuePb.ConsumerResponseHeader{}

	callbackUrl := req.GetCallbackUrl()

	// TODO(pruthvi): TLS is terminated at load balancer for VNGw.
	// Load balancers are not present in dev localstack and hence VNGw will run on Http in dev & test envs
	// Enforce this check on other envs
	// if !strings.HasPrefix(callbackUrl, "https://") {
	// 	logger.ErrorNoCtx("URL should have https protocol", zap.String("callbackUrl", callbackUrl))
	// 	res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
	// 	return res, nil
	// }
	uri, err := url.Parse(callbackUrl)
	if err != nil {
		logger.ErrorNoCtx("URL could not be parsed", zap.String("callbackUrl", callbackUrl))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}
	var requestBody []byte

	requestBody, err = protojson.Marshal(req.GetEvent())

	logger.DebugNoCtx(fmt.Sprintf("Sending http request %s on callbackUrl %s", requestBody, uri.String()),
		zap.String("vendorName", "federal"))

	// #nosec G107 To prevent lint error of using url from a variable
	resp, err := s.h.Post(callbackUrl, "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		logger.ErrorNoCtx("error encountered calling http endpoint", zap.Error(err))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	}
	defer func() {
		_ = resp.Body.Close()
	}()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusAccepted {
		logger.DebugNoCtx(fmt.Sprintf("HTTP status not OK. Received %v", resp.StatusCode))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.DebugNoCtx("Error reading HTTP response body")
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	}
	logger.DebugNoCtx(fmt.Sprintf("Got http response %s", body), zap.String("vendorName", "federal"))
	res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS

	return res, nil
}
