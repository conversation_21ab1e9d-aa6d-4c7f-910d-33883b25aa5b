package leadsquared

import (
	"encoding/json"
	"net/http"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	leadsquaredPb "github.com/epifi/gamma/api/vendors/leadsquared"
)

// LeadSquaredService is the simulator implementation for LeadSquared service.
type LeadSquaredService struct{}

func NewLeadSquaredService() *LeadSquaredService {
	return &LeadSquaredService{}
}

func (l LeadSquaredService) CreateOrUpdateLead(w http.ResponseWriter, req *http.Request) {
	createOrUpdateLeadResponse := &leadsquaredPb.CreateOrUpdateLeadResponse{
		Status: "Success",
		Message: &leadsquaredPb.CreateOrUpdateLeadResponse_ResponseMessage{
			Id:           "id",
			AffectedRows: 1,
		},
	}

	marshalledRes, err := json.Marshal(createOrUpdateLeadResponse)
	if err != nil {
		logger.ErrorNoCtx("error in marshalling response", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	_, err = w.Write(marshalledRes)
	if err != nil {
		logger.ErrorNoCtx("error in writing res to ResponseWriter", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)

	return

}
