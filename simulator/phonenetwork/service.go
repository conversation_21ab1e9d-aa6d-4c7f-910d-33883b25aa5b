package phonenetwork

import (
	"context"

	"github.com/epifi/gamma/api/vendors/bureau"
)

type Service struct {
}

func NewService() *Service {
	return &Service{}
}

func (s *Service) GetBillingTypeForPhoneNumber(_ context.Context, req *bureau.GetNetworkDetailsRequest) (*bureau.GetNetworkDetailsResponse, error) {
	billingType := "prepaid"
	// Setting this to postpaid for specific numbers only
	if req.GetPhoneNumber() == "919999999999" {
		billingType = "postpaid"
	}

	return &bureau.GetNetworkDetailsResponse{
		NumberBillingType: billingType,
		StatusCode:        200,
	}, nil
}
