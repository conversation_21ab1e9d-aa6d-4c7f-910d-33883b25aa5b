package model

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"time"

	vendorPb "github.com/epifi/gamma/api/vendors/m2p/lending/creditcard"
)

type CreditCardTransaction struct {
	Id                      string
	ReferenceId             string
	TransactionType         string
	TransactionTransferType string
	TransactionTime         *time.Time
	TransactionStatus       string
	BillingStatus           string
	AuthorizationStatus     string
	CreatedAt               *time.Time
	UpdatedAt               *time.Time
	TransactionDetails      *vendorPb.Transaction
	Vendor                  commonvgpb.Vendor
}
