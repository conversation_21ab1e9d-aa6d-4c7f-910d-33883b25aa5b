// nolint
package dao

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	gormctxV2 "github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/simulator/lending/creditcard/dao/model"
)

type CrdbCreditCardTransactionsDao struct {
	db *gorm.DB
}

func NewCrdbCreditCardTransactionsDao(db *gorm.DB) *CrdbCreditCardTransactionsDao {
	return &CrdbCreditCardTransactionsDao{
		db: db,
	}
}

func (c *CrdbCreditCardTransactionsDao) Create(ctx context.Context, creditCardTransaction *model.CreditCardTransaction) (*model.CreditCardTransaction, error) {
	defer metric_util.TrackDuration("simulator/lending/creditcard/dao", "CrdbCreditCardTransactionsDao", "Create", time.Now())
	handle := gormctxV2.FromContextOrDefault(ctx, c.db)
	res := handle.Create(creditCardTransaction)
	if res.Error != nil {
		return nil, res.Error
	}
	return creditCardTransaction, nil
}

func (c *CrdbCreditCardTransactionsDao) GetBetweenDatesWithCriterion(ctx context.Context, vendor commonvgpb.Vendor, fromDate *time.Time, toDate *time.Time, referenceId string, criterionMap *map[string][]string) ([]*model.CreditCardTransaction, error) {
	defer metric_util.TrackDuration("simulator/lending/creditcard/dao", "CrdbCreditCardTransactionsDao", "GetBetweenDatesWithCriterion", time.Now())
	var creditCardTransactionResponse []*model.CreditCardTransaction
	d := gormctxV2.FromContextOrDefault(ctx, c.db)
	d = d.Where("vendor = ?", vendor).Where("reference_id = ?", referenceId).Where("transaction_time >= ?", fromDate).Where("transaction_time <= ?", toDate)
	d = addWhereClauseFromCriterionMap(d, criterionMap)
	if err := d.Find(&creditCardTransactionResponse).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return creditCardTransactionResponse, nil
}

func (c *CrdbCreditCardTransactionsDao) GetById(ctx context.Context, vendor commonvgpb.Vendor, id string) (*model.CreditCardTransaction, error) {
	defer metric_util.TrackDuration("simulator/lending/creditcard/dao", "CrdbCreditCardTransactionsDao", "GetById", time.Now())
	var creditCardTransactionResponse model.CreditCardTransaction
	d := gormctxV2.FromContextOrDefault(ctx, c.db)
	d = d.Where("vendor = ?", vendor).Where("id = ?", id)
	if err := d.Find(&creditCardTransactionResponse).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	if d.RowsAffected == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return &creditCardTransactionResponse, nil
}

func addWhereClauseFromCriterionMap(d *gorm.DB, criterionMap *map[string][]string) *gorm.DB {
	if criterionMap == nil || len(*criterionMap) == 0 {
		return d
	}

	for key, valueSlice := range *criterionMap {
		d = d.Where(key+" IN (?)", valueSlice)
	}
	return d
}
func (c *CrdbCreditCardTransactionsDao) Update(ctx context.Context, transaction *model.CreditCardTransaction) error {
	defer metric_util.TrackDuration("simulator/lending/creditcard/dao", "CrdbCreditCardTransactionsDao", "Update", time.Now())

	if transaction.Id == "" {
		return fmt.Errorf("id can't be empty for an update operation")
	}

	handle := gormctxV2.FromContextOrDefault(ctx, c.db)
	whereClause := &model.CreditCardLoanAccounts{
		Id: transaction.Id,
	}
	if err := handle.Model(&model.CreditCardTransaction{}).Where(whereClause).Updates(transaction).Error; err != nil {
		return err
	}
	return nil
}
