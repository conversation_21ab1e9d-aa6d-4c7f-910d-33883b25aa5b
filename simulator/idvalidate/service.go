package idvalidate

import (
	"context"

	"github.com/epifi/gamma/api/vendors/karza"
)

type IdValidateService struct {
}

func NewIdValidateService() *IdValidateService {
	return &IdValidateService{}
}

func (s *IdValidateService) ValidateVoterId(ctx context.Context, request *karza.ValidateVoterIdRequest) (*karza.ValidateVoterIdResponse, error) {
	return &karza.ValidateVoterIdResponse{
		Result:     &karza.ValidateVoterIdResult{},
		RequestId:  "1",
		StatusCode: 101,
	}, nil
}
