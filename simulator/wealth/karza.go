package wealth

import (
	"io/ioutil"
	"net/http"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	karzaVendorPb "github.com/epifi/gamma/api/vendors/wealth/karza"
	"github.com/epifi/be-common/pkg/logger"
)

type KarzaService struct {
}

func NewKarzaService() *KarzaService {
	return &KarzaService{}
}

func (k *KarzaService) GetDocOcr(w http.ResponseWriter, request *http.Request) {
	reqData, err := ioutil.ReadAll(request.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		k.sendResponse(w, http.StatusInternalServerError, "failed to read request body")
		return
	}
	header := request.Header
	req := &karzaVendorPb.GetDocOcrRequest{}
	err = protojson.UnmarshalOptions{AllowPartial: true}.Unmarshal(reqData, req)
	if err != nil {
		logger.ErrorNoCtx("failed to unmarshall request body", zap.Error(err))
		k.sendResponse(w, http.StatusInternalServerError, "failed to unmarshall request body")
		return
	}
	if req.GetFileBase64() == "" || header.Get("x-karza-key") == "" {
		k.sendResponse(w, http.StatusBadRequest, "no file base64 present in request")
		return
	}
	resp := &karzaVendorPb.GetDocOcrResponse{
		RequestId: uuid.New().String(),
		Results: []*karzaVendorPb.Result{
			{
				Details: &karzaVendorPb.Details{
					ImageUrl: &karzaVendorPb.ImageUrl{
						Value: "https://fi.money/assets/images/494ce78be63cc009da402b6c7e9f18a2.webp",
					},
				},
				Type: "Aadhaar Front Bottoom",
			},
		},
		StatusCode: 101,
	}
	resByte, err := protojson.Marshal(resp)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal response body", zap.Error(err))
		k.sendResponse(w, http.StatusInternalServerError, "failed to marshal response body")
		return
	}
	k.sendResponse(w, http.StatusOK, string(resByte))
}

func (k *KarzaService) sendResponse(w http.ResponseWriter, statusCode int, resp string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	_, _ = w.Write([]byte(resp))
}
