package auth

import (
	"context"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/afu"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	authDl "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/auth"
	sConst "github.com/epifi/gamma/frontend/account/signup/constants"
	"github.com/epifi/gamma/pkg/deeplink"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

func (s *Service) checkAFUWaitScreenOptions(ctx context.Context, afuID string) *anyPb.Any {
	afuResp, err := s.GetAuthFactorUpdateRecord(ctx, &auth.GetAuthFactorUpdateRecordRequest{
		AuthFactorUpdateId: afuID,
	})
	if err = epifigrpc.RPCError(afuResp, err); err != nil {
		logger.Error(ctx, "unable to fetch afu record", zap.Error(err))
		return deeplinkV3.GetScreenOptionV2WithoutError(sConst.GenericWaitSO(afuID))
	}
	afR := afuResp.GetRecord()
	af := afR.GetContext().GetAuthFactors()
	if lo.Contains(af, afu.AuthFactor_PHONE_NUM) || lo.Contains(af, afu.AuthFactor_EMAIL) {
		return deeplinkV3.GetScreenOptionV2WithoutError(sConst.LongWaitSO(afuID))
	}
	if time.Since(afR.GetVendorContext().GetVendorRequestStartedAt().AsTime()) > 20*time.Second {
		return deeplinkV3.GetScreenOptionV2WithoutError(sConst.GenericWaitSO(afuID))
	}
	return deeplinkV3.GetScreenOptionV2WithoutError(sConst.ShortWaitSO(afuID))

}

func (s *Service) pollNextAction(ctx context.Context, afuId string) *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen:          dlPb.Screen_AFU_CHECK_UPDATE_STATUS,
		ScreenOptionsV2: s.checkAFUWaitScreenOptions(ctx, afuId),
	}
}

func (s *Service) afuDeviceRegistrationDeeplink() *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_REGISTER_DEVICE,
		ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&authDl.RegisterDeviceScreenOptions{
			HeaderBar: deeplink.HeaderBarForFederalOwnedScreen(),
			Source:    auth.DeviceRegistrationSource_DEVICE_REGISTRATION_SOURCE_AFU.String(),
		}),
	}
}
