package main

import (
	"context"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"

	"github.com/epifi/be-common/mcp/opensearch/tools"
	"github.com/epifi/be-common/mcp/pkg/logger"
	"github.com/epifi/be-common/mcp/pkg/mcpserver"
)

func main() {
	// Set up logging to file
	safeLogger, err := logger.New("opensearch-mcp")
	if err != nil {
		fmt.Printf("Error setting up logging: %v\n", err)
		return
	}
	defer func() {
		if err := safeLogger.Close(); err != nil {
			fmt.Printf("Error closing log file: %v\n", err)
		}
	}()

	// Set up panic recovery
	defer mcpserver.HandlePanic(safeLogger)
	mcpserver.SetupSignalHandler(safeLogger)
	safeLogger.Println("Starting OpenSearch MCP tools...")

	// Create MCP server
	s := server.NewMCPServer(
		"OpenSearch Tools",
		"1.0.0",
		server.WithResourceCapabilities(true, true),
		server.WithLogging(),
	)

	// Add search_logs tool
	searchLogsTool := tools.SearchLogsTool(safeLogger)
	s.AddTool(searchLogsTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// Wrap the handler with panic recovery
		defer mcpserver.HandlePanic(safeLogger)
		return tools.SearchLogsHandler(ctx, request, safeLogger)
	})

	safeLogger.Println("Starting stdio server...")
	// Start the stdio server
	if err := server.ServeStdio(s); err != nil {
		safeLogger.Printf("Server error: %v\n", err)
	}
}
