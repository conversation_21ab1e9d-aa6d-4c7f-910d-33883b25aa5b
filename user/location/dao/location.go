package dao

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm/clause"

	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/user/location/dao/model"

	gormv2 "gorm.io/gorm"
)

//go:generate mockgen -source=./location.go -destination=./mocks/location.go -package=mocks
type LocationDao interface {
	GetAddress(ctx context.Context, idType types.IdentifierType, idValue string) (*types.AddressForIdentifier, error)
	GetOrCreate(ctx context.Context, row *types.AddressForIdentifier) (*types.AddressForIdentifier, error)
	CreatePinCodeSeed(ctx context.Context, locationToken string, latitude, longitude float64, pincode string) error
}

type LocationDaoCrdb struct {
	db dbTypes.EpifiCRDB
}

var _ LocationDao = &LocationDaoCrdb{}

func NewLocationDaoCrdb(dbv2 dbTypes.EpifiCRDB) *LocationDaoCrdb {
	return &LocationDaoCrdb{db: dbv2}
}

func (crdb *LocationDaoCrdb) GetAddress(ctx context.Context, idType types.IdentifierType, idValue string) (*types.AddressForIdentifier, error) {
	defer metric_util.TrackDuration("user/location/dao", "LocationDaoCrdb", "GetAddress", time.Now())
	if idType == types.IdentifierType_IDENTIFIER_TYPE_UNSPECIFIED {
		return nil, epifierrors.ErrInvalidArgument
	}

	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	var mod model.AddressForIdentifier

	err := db.Where("identifier_type = ? AND identifier_value = ? AND deleted_at_unix = 0", idType, idValue).Take(&mod).Error
	if err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			err = epifierrors.ErrRecordNotFound
		}
		return nil, err
	}

	return mod.GetProto(), nil
}

func (crdb *LocationDaoCrdb) GetOrCreate(ctx context.Context, row *types.AddressForIdentifier) (*types.AddressForIdentifier, error) {
	defer metric_util.TrackDuration("user/location/dao", "LocationDaoCrdb", "GetOrCreate", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	resp, err := crdb.GetAddress(ctx, row.GetIdentifierType(), row.GetIdentifierValue())

	// Value already exists and postal code is not empty - return as is
	if resp != nil && err == nil && resp.GetAddress().GetPostalCode() != "" {
		return resp, nil
	}

	// Value doesn't exist, first insert and then fetch and return
	mod := model.NewAddressForIdentifierModel(row)
	res := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "identifier_type"},
			{Name: "identifier_value"},
			{Name: "deleted_at_unix"},
		},
		UpdateAll: true,
	}).Create(mod)
	if res.Error != nil {
		return nil, err
	}

	return res.Statement.Model.(*model.AddressForIdentifier).GetProto(), nil
}

func (crdb *LocationDaoCrdb) CreatePinCodeSeed(ctx context.Context, locationToken string, latitude, longitude float64, pincode string) error {
	defer metric_util.TrackDuration("user/location/dao", "LocationDaoCrdb", "CreatePinCodeSeed", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	if locationToken == "" || pincode == "" || latitude == 0 || longitude == 0 {
		return epifierrors.ErrInvalidArgument
	}
	if err := db.Create(&model.PinCodeSeed{
		LocationToken: locationToken,
		Pincode:       pincode,
		Latitude:      latitude,
		Longitude:     longitude,
	}).Error; err != nil {
		if storageV2.IsDuplicateRowError(err) {
			return epifierrors.ErrDuplicateEntry
		}
		return err
	}
	return nil
}
