package vkyc

import (
	"context"
	"errors"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

type Proc struct {
	vkycClient vkycPb.VKYCClient
}

func NewProc(vkycClient vkycPb.VKYCClient) *Proc {
	return &Proc{
		vkycClient: vkycClient,
	}
}

var (
	InvalidUserError      = errors.New("Invalid user type for register user vkyc")
	EkycNameMisMatchError = errors.New("Ekyc name mismatch error")
)

func (p *Proc) RegisterUserVKYC(ctx context.Context, actorId string) error {
	vkycResp, err := p.vkycClient.RegisterUser(ctx, &vkycPb.RegisterUserRequest{
		ActorId: actorId,
	})
	if vkycResp.GetStatus().GetCode() == uint32(kycPb.GetKYCRecordResponse_NAME_MISMATCH) {
		logger.Info(ctx, "Name mismatch in ekyc record not registering user")
		return EkycNameMisMatchError
	}
	if vkycResp.GetStatus().IsRecordNotFound() {
		return InvalidUserError
	}
	if te := epifigrpc.RPCError(vkycResp, err); te != nil {
		if rpc.StatusFromError(te).IsFailedPrecondition() {
			return InvalidUserError
		}
		logger.Error(ctx, "error registering customer in vkyc backend service", zap.Error(te))
		return errors.New("error registering customer in vkyc backend service")
	}
	logger.Info(ctx, "user successfully registered for vkyc")
	if lo.Contains(vkycPkg.ReAttemptVKYC, actorId) {
		logger.Info(ctx, "user successfully registered for re attempt vkyc")
	}
	return nil
}
