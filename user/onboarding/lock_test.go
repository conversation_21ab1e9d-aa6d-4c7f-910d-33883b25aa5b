package onboarding

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/jonboulle/clockwork"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/pkg/cfg"
	idgenMocks "github.com/epifi/be-common/pkg/idgen/mocks"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/storage"
)

func getRedisClient(options *cfg.RedisOptions) *redis.Client {
	if val, ok := os.LookupEnv("REDIS_HOST"); ok {
		options.Addr = val
	}
	return storage.NewRedisClientFromConfig(options, false)
}

func TestService_integration_acquireLockOnOrchestrator(t *testing.T) {
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	t.Parallel()
	const (
		actIdA = "actor-1"
		actIdB = "actor-2"
		actIdC = "actor-3"
	)

	tests := []struct {
		name  string
		logic func(t *testing.T, s *Service)
	}{
		{
			name: "lock acquired",
			logic: func(t *testing.T, s *Service) {
				var (
					ctxWithNoTO         = context.Background()
					ctxWithLargeTO, cfn = context.WithTimeout(ctxWithNoTO, 1*time.Minute)
				)
				defer cfn()
				releaseFn, err := s.acquireLockOnOrchestrator(ctxWithLargeTO, actIdA)
				assert.Nil(t, err)
				assert.NotNil(t, releaseFn)
				if releaseFn != nil {
					releaseFn()
				}
			},
		},
		{
			name: "wait for lock",
			logic: func(t *testing.T, s *Service) {
				var (
					lock1AcqTime        time.Time
					lock2AcqTime        time.Time
					ctxWithNoTO         = context.Background()
					ctxWithLargeTO, cfn = context.WithTimeout(ctxWithNoTO, 1*time.Minute)
				)

				defer cfn()
				// actor B tries to acquire lock -> done immediately
				fn1, err1 := s.acquireLockOnOrchestrator(ctxWithLargeTO, actIdB)
				lock1AcqTime = time.Now()
				assert.Nil(t, err1)
				assert.NotNil(t, fn1)

				// actor C tries to acquire lock -> done immediately
				fnB, errB := s.acquireLockOnOrchestrator(ctxWithLargeTO, actIdC)
				assert.Nil(t, errB)
				assert.NotNil(t, fnB)

				// buffer time to avoid race conditions
				// time.Sleep(600 * time.Millisecond)

				// actor B tries to acquire lock again -> waits for earlier process to complete
				fn2, err2 := s.acquireLockOnOrchestrator(ctxWithLargeTO, actIdB)
				lock2AcqTime = time.Now()
				assert.Nil(t, err2)
				assert.NotNil(t, fn2)

				// ensure that actor B has to wait till lock is expired
				lock2AcqDelay := lock2AcqTime.Sub(lock1AcqTime)
				assert.Greater(t, lock2AcqDelay, ts.Conf.Onboarding.OrchestratorLockConfig.Timeout)
				assert.Less(t, lock2AcqDelay, ts.Conf.Onboarding.OrchestratorLockConfig.Timeout+2*time.Second)
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			fakeClock := clockwork.NewFakeClockAt(time.Now())
			redisClient := getRedisClient(ts.Conf.RedisOptions)
			uuidGen := idgenMocks.NewMockIUuidGenerator(ctr)
			lockMgr := lock.NewRedisLockManager(redisClient, fakeClock, uuidGen)
			uuidGen.EXPECT().GenerateUuid().Return("lock_token").AnyTimes()
			s := &Service{
				dynConf:     ts.DynConf,
				lockManager: lockMgr,
			}
			tt.logic(t, s)
		})
	}
}

func Test_minTimeout(t *testing.T) {
	t.Parallel()
	var (
		cfgTimeout           = 30 * time.Second
		smallTO              = 10 * time.Second
		largeTO              = 60 * time.Second
		ctxWithNoTO          = context.Background()
		ctxWithLargeTO, cfn1 = context.WithTimeout(ctxWithNoTO, largeTO)
		ctxWithSmallTO, cfn2 = context.WithTimeout(ctxWithNoTO, smallTO)
	)

	defer cfn1()
	defer cfn2()
	type args struct {
		ctx        context.Context
		cfgTimeout time.Duration
	}
	type approxDuration struct {
		min time.Duration
		max time.Duration
	}
	tests := []struct {
		name      string
		args      args
		want      time.Duration
		wantRange *approxDuration
	}{
		{
			name: "no timeout",
			args: args{
				ctx:        ctxWithNoTO,
				cfgTimeout: cfgTimeout,
			},
			want: cfgTimeout,
		},
		{
			name: "large timeout",
			args: args{
				ctx:        ctxWithLargeTO,
				cfgTimeout: cfgTimeout,
			},
			want: cfgTimeout,
		},
		{
			name: "small timeout",
			args: args{
				ctx:        ctxWithSmallTO,
				cfgTimeout: cfgTimeout,
			},
			want: cfgTimeout,
			wantRange: &approxDuration{
				min: smallTO - 3*time.Second,
				max: smallTO,
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := minTimeout(tt.args.ctx, tt.args.cfgTimeout)
			if tt.wantRange != nil {
				assert.Greater(t, got, tt.wantRange.min)
				assert.Less(t, got, tt.wantRange.max)
				return
			}
			assert.Equalf(t, tt.want, got, "minTimeout(%v, %v)", tt.args.ctx, tt.args.cfgTimeout)
		})
	}
}
