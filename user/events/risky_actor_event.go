package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type RiskyActor struct {
	EventId    string
	ProspectId string
	ActorId    string
	SessionId  string
	EventType  string
	Timestamp  time.Time
}

func NewRiskyActor(actorId string) *RiskyActor {
	return &RiskyActor{
		ProspectId: "",
		ActorId:    actorId,
		SessionId:  "",
		Timestamp:  time.Now(),
		EventId:    uuid.New().String(),
		EventType:  events.EventTrack,
	}
}

func (p *RiskyActor) GetEventType() string {
	return events.EventTrack
}

func (p *RiskyActor) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(p, properties)
	return properties
}

func (p *RiskyActor) GetEventTraits() map[string]interface{} {
	return nil
}

func (p *RiskyActor) GetEventId() string {
	return p.EventId
}

func (p *RiskyActor) GetUserId() string {
	return p.ActorId
}

func (p *RiskyActor) GetProspectId() string {
	return p.ProspectId
}

func (p *RiskyActor) GetEventName() string {
	return EventRiskyActor
}
