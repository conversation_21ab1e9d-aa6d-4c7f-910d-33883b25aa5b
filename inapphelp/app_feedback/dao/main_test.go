package dao

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/inapphelp/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, db, teardown := test.InitTestServer(true)
	fadts = FeedbackAttemptDAOTestSuite{
		db:                 db,
		conf:               conf,
		feedbackAttemptDao: NewFeedbackAttemptDao(db),
	}
	userFeedbackDaoTS = UserFeedbackDAOTestSuite{
		db:              db,
		conf:            conf,
		userFeedbackDao: NewUserFeedbackDao(db),
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
