package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/inapphelp/app_feedback"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/mocks"
	"github.com/epifi/gamma/inapphelp/config"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

type AllTxnTestSuite struct {
	conf *config.Config
}

var (
	atts				AllTxnTestSuite
	errOrderResponse		= &orderPb.GetOrdersForActorResponse{Status: rpcPb.StatusInternal()}
	failedOrdersPresentResponse	= &orderPb.GetOrdersForActorResponse{
		Status:	rpcPb.StatusOk(),
		Orders: []*orderPb.Order{
			{
				Id:		"123",
				FromActorId:	"test-actor-1",
			},
		},
	}
	noFailedOrdersPresentResponse	= &orderPb.GetOrdersForActorResponse{
		Status:	rpcPb.StatusOk(),
		Orders:	[]*orderPb.Order{},
	}
	DebitOrderWithAmount101	= &orderPb.Order{
		Id:		"123",
		FromActorId:	"test-actor-1",
		Amount:		moneyPb.AmountINR(101).GetPb(),
	}
	DebitOrderWithAmount10	= &orderPb.Order{
		Id:		"123",
		FromActorId:	"test-actor-1",
		Amount:		moneyPb.AmountINR(10).GetPb(),
	}
	CreditOrder	= &orderPb.Order{
		Id:		"123",
		FromActorId:	"test-actor-2",
		ToActorId:	"test-actor-1",
		Amount:		moneyPb.AmountINR(10).GetPb(),
	}
	successfulOrdersResponse1	= &orderPb.GetOrdersForActorResponse{
		Status:	rpcPb.StatusOk(),
		Orders:	[]*orderPb.Order{CreditOrder},
	}
	successfulOrdersResponse2	= &orderPb.GetOrdersForActorResponse{
		Status:	rpcPb.StatusOk(),
		Orders:	[]*orderPb.Order{CreditOrder, CreditOrder},
	}
	successfulOrdersResponse3	= &orderPb.GetOrdersForActorResponse{
		Status:	rpcPb.StatusOk(),
		Orders:	[]*orderPb.Order{CreditOrder, CreditOrder, DebitOrderWithAmount10},
	}
	successfulOrdersResponse4	= &orderPb.GetOrdersForActorResponse{
		Status:	rpcPb.StatusOk(),
		Orders:	[]*orderPb.Order{CreditOrder, CreditOrder, DebitOrderWithAmount10, DebitOrderWithAmount101},
	}
	successfulOrdersResponse5	= &orderPb.GetOrdersForActorResponse{
		Status:	rpcPb.StatusOk(),
		Orders:	[]*orderPb.Order{CreditOrder, CreditOrder, DebitOrderWithAmount10, DebitOrderWithAmount10, DebitOrderWithAmount10},
	}
)

func TestAllTxnRuleProcessor_IsUserEligible(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)

	mockOrderClient := mocks.NewMockOrderServiceClient(ctr)

	type args struct {
		mocks		[]interface{}
		ctx		context.Context
		actorId		string
		platform	commontypes.Platform
	}
	tests := []struct {
		name			string
		args			args
		want			commontypes.BooleanEnum
		wantErr			bool
		wantIneligibilityReason	app_feedback.AppRatingNudgeIneligibilityReason
	}{
		{
			name:	"error while fetching failed txns",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(errOrderResponse, nil),
				},
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"failed txn above threshold",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(failedOrdersPresentResponse, nil),
				},
			},
			want:				commontypes.BooleanEnum_FALSE,
			wantErr:			false,
			wantIneligibilityReason:	app_feedback.AppRatingNudgeIneligibilityReason_APP_RATING_NUDGE_INELIGIBILITY_REASON_ALL_TXN_RULE_FAILED_TRANSACTION_COUNT_ABOVE_THRESHOLD,
		},
		{
			name:	"error while fetching successful txns",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(noFailedOrdersPresentResponse, nil),
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(errOrderResponse, nil),
				},
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"error while fetching successful txns",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(noFailedOrdersPresentResponse, nil),
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(errOrderResponse, nil),
				},
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"successful txn count less than threshold",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(noFailedOrdersPresentResponse, nil),
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(successfulOrdersResponse1, nil),
				},
			},
			want:				commontypes.BooleanEnum_FALSE,
			wantErr:			false,
			wantIneligibilityReason:	app_feedback.AppRatingNudgeIneligibilityReason_APP_RATING_NUDGE_INELIGIBILITY_REASON_ALL_TXN_RULE_SUCCESSFUL_ORDER_COUNT_LESS_THAN_THRESHOLD_OR_SUCCESSFUL_DEBIT_ORDER_COUNT_LESS_THAN_THRESHOLD,
		},
		{
			name:	"successful debit txn count less than threshold",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(noFailedOrdersPresentResponse, nil),
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(successfulOrdersResponse2, nil),
				},
			},
			want:				commontypes.BooleanEnum_FALSE,
			wantErr:			false,
			wantIneligibilityReason:	app_feedback.AppRatingNudgeIneligibilityReason_APP_RATING_NUDGE_INELIGIBILITY_REASON_ALL_TXN_RULE_SUCCESSFUL_ORDER_COUNT_LESS_THAN_THRESHOLD_OR_SUCCESSFUL_DEBIT_ORDER_COUNT_LESS_THAN_THRESHOLD,
		},
		{
			name:	"successful debit txn greater than threshold count less than threshold",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(noFailedOrdersPresentResponse, nil),
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(successfulOrdersResponse3, nil),
				},
			},
			want:				commontypes.BooleanEnum_FALSE,
			wantErr:			false,
			wantIneligibilityReason:	app_feedback.AppRatingNudgeIneligibilityReason_APP_RATING_NUDGE_INELIGIBILITY_REASON_ALL_TXN_RULE_SUCCESSFUL_ORDER_COUNT_LESS_THAN_THRESHOLD_OR_SUCCESSFUL_DEBIT_ORDER_COUNT_LESS_THAN_THRESHOLD,
		},
		{
			name:	"debit txn found in first page",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(noFailedOrdersPresentResponse, nil),
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(successfulOrdersResponse4, nil),
				},
			},
			want:		commontypes.BooleanEnum_TRUE,
			wantErr:	false,
		},
		{
			name:	"debit txn found on second page",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(noFailedOrdersPresentResponse, nil),
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(successfulOrdersResponse5, nil),
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).
						Return(successfulOrdersResponse4, nil),
				},
			},
			want:		commontypes.BooleanEnum_TRUE,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := NewAllTxnRuleProcessor(mockOrderClient)
			got, gotIneligibilityReason, err := a.IsUserEligible(tt.args.ctx, tt.args.actorId, tt.args.platform,
				atts.conf.FeedbackRuleEngineConfigMap[strings.ToLower(tt.args.platform.String())])
			if (err != nil) != tt.wantErr {
				t.Errorf("IsUserEligible() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsUserEligible() got = %v, want %v", got, tt.want)
			}
			if gotIneligibilityReason != tt.wantIneligibilityReason {
				t.Errorf("IsUserEligible() got = %v, want %v", gotIneligibilityReason, tt.wantIneligibilityReason)
			}
		})
	}
}
