package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"strings"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"golang.org/x/net/context"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/inapphelp/app_feedback"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	"github.com/epifi/gamma/inapphelp/config"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

type DepositAddFundsTestSuite struct {
	conf *config.Config
}

var (
	dafts DepositAddFundsTestSuite
)

func TestDepositAddFundsRuleProcessor_IsUserEligible(t *testing.T) {
	t.<PERSON>l()
	ctr := gomock.NewController(t)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)

	type args struct {
		mocks		[]interface{}
		ctx		context.Context
		actorId		string
		platform	commontypes.Platform
	}
	tests := []struct {
		name			string
		args			args
		want			commontypes.BooleanEnum
		wantErr			bool
		wantIneligibilityReason	app_feedback.AppRatingNudgeIneligibilityReason
	}{
		{
			name:	"error while fetching order details for actor",
			args: args{
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).Return(&orderPb.GetOrdersForActorResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act-101",
				platform:	commontypes.Platform_ANDROID,
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"no orders found for given actor",
			args: args{
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).Return(&orderPb.GetOrdersForActorResponse{
						Status:	rpcPb.StatusOk(),
						Orders:	nil,
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act-101",
				platform:	commontypes.Platform_ANDROID,
			},
			want:				commontypes.BooleanEnum_FALSE,
			wantErr:			false,
			wantIneligibilityReason:	app_feedback.AppRatingNudgeIneligibilityReason_APP_RATING_NUDGE_INELIGIBILITY_REASON_DEPOSIT_ADD_FUNDS_RULE_NO_ORDERS_FOUND_IN_SET_DURATION,
		},
		{
			name:	"actor is NOT eligible as per deposit add funds rule due to amount threshold",
			args: args{
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).Return(&orderPb.GetOrdersForActorResponse{
						Status:	rpcPb.StatusOk(),
						Orders: []*orderPb.Order{
							{
								Amount:		moneyPb.AmountINR(400).GetPb(),
								UpdatedAt:	timestampPb.New(time.Now().Add(-1 * time.Hour)),
								Status:		orderPb.OrderStatus_PAID,
							},
						},
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act-101",
				platform:	commontypes.Platform_ANDROID,
			},
			want:				commontypes.BooleanEnum_FALSE,
			wantErr:			false,
			wantIneligibilityReason:	app_feedback.AppRatingNudgeIneligibilityReason_APP_RATING_NUDGE_INELIGIBILITY_REASON_DEPOSIT_ADD_FUNDS_RULE_NO_ELIGIBLE_ORDERS_WITH_FUNDS_ADDED_ABOVE_THRESHOLD,
		},
		{
			name:	"actor is NOT eligible as per deposit add funds rule due to duration threshold",
			args: args{
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).Return(&orderPb.GetOrdersForActorResponse{
						Status:	rpcPb.StatusOk(),
						Orders: []*orderPb.Order{
							{
								Amount:		moneyPb.AmountINR(400).GetPb(),
								UpdatedAt:	timestampPb.New(time.Now().Add(-3 * time.Hour)),
								Status:		orderPb.OrderStatus_PAID,
							},
						},
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act-101",
				platform:	commontypes.Platform_ANDROID,
			},
			want:				commontypes.BooleanEnum_FALSE,
			wantErr:			false,
			wantIneligibilityReason:	app_feedback.AppRatingNudgeIneligibilityReason_APP_RATING_NUDGE_INELIGIBILITY_REASON_DEPOSIT_ADD_FUNDS_RULE_NO_ELIGIBLE_ORDERS_WITH_FUNDS_ADDED_ABOVE_THRESHOLD,
		},
		{
			name:	"actor is eligible as per deposit add funds rule",
			args: args{
				mocks: []interface{}{
					mockOrderClient.EXPECT().GetOrdersForActor(context.Background(), gomock.Any()).Return(&orderPb.GetOrdersForActorResponse{
						Status:	rpcPb.StatusOk(),
						Orders: []*orderPb.Order{
							{
								Amount:		moneyPb.AmountINR(600).GetPb(),
								UpdatedAt:	timestampPb.New(time.Now().Add(-1 * time.Hour)),
								Status:		orderPb.OrderStatus_PAID,
							},
						},
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act-101",
				platform:	commontypes.Platform_ANDROID,
			},
			want:		commontypes.BooleanEnum_TRUE,
			wantErr:	false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := NewDepositAddFundsRuleProcessor(mockOrderClient)
			got, gotIneligibilityReason, err := a.IsUserEligible(tt.args.ctx, tt.args.actorId, tt.args.platform,
				dafts.conf.FeedbackRuleEngineConfigMap[strings.ToLower(tt.args.platform.String())])
			if (err != nil) != tt.wantErr {
				t.Errorf("IsUserEligible() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsUserEligible() got = %v, want %v", got, tt.want)
			}
			if gotIneligibilityReason != tt.wantIneligibilityReason {
				t.Errorf("IsUserEligible() got = %v, want %v", gotIneligibilityReason, tt.wantIneligibilityReason)
			}
		})
	}
}
