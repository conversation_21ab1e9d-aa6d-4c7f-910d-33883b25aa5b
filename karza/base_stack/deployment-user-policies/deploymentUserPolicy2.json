{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["apigateway:GET", "apigateway:POST", "apigateway:PUT", "apigateway:DELETE", "apigateway:PATCH"], "Resource": ["arn:aws:apigateway:*::/restapis*"]}, {"Effect": "Allow", "Action": ["iam:PassRole"], "Resource": ["arn:aws:iam::*:role/*"]}, {"Effect": "Allow", "Action": "kinesis:*", "Resource": ["arn:aws:kinesis:*:*:stream/face-match*"]}, {"Effect": "Allow", "Action": ["iam:GetRole", "iam:CreateRole", "iam:PutRolePolicy", "iam:DeleteRolePolicy", "iam:DeleteRole", "iam:ListAttachedRolePolicies", "iam:ListRolePolicies"], "Resource": ["arn:aws:iam::*:role/face-match*"]}, {"Effect": "Allow", "Action": ["iam:GetPolicyVersion", "iam:GetPolicy"], "Resource": ["*"]}, {"Effect": "Allow", "Action": "sqs:*", "Resource": ["arn:aws:sqs:*:*:face-match*"]}]}