package employment

import (
	"context"
	"strings"

	"go.uber.org/zap"

	employmentPb "github.com/epifi/gamma/api/employment"
	vgEmploymentPb "github.com/epifi/gamma/api/vendorgateway/employment"
	"github.com/epifi/gamma/employment/events"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	storage "github.com/epifi/be-common/pkg/storage/v2"
)

func (s *CheckHandlerImpl) DomainNameVerificationCheck(ctx context.Context, empData *employmentPb.EmploymentData,
	process *employmentPb.EmploymentVerificationProcess) (*employmentPb.EmploymentVerificationCheck, error) {
	// if domain name check already done for the user, return the result from db
	checkEntry, err := s.EmplVerificationCheckDao.GetByProcessIdAndName(ctx, process.GetId(), employmentPb.CheckName_CHECK_NAME_DOMAIN_NAME_VERIFICATION)
	switch {
	case storage.IsRecordNotFoundError(err):
		break
	case err != nil:
		logger.Error(ctx, "error while fetching domain check entry", zap.String("processId", process.GetId()), zap.Error(err))
		return nil, err
	default:
		return checkEntry, nil
	}
	checkEntry = &employmentPb.EmploymentVerificationCheck{
		ActorId:               empData.ActorId,
		VerificationProcessId: process.Id,
		Name:                  employmentPb.CheckName_CHECK_NAME_DOMAIN_NAME_VERIFICATION,
	}
	// check if domain name is whitelisted
	domainName, allowAccess := s.AllowAccessToNonSalariedUser(ctx, empData.GetEmploymentInfo().GetPersonalProfileInfo())
	if allowAccess {
		s.triggerEmplVerificationServerRespEvent(ctx, empData.ActorId, "", "", "", events.Success, "", events.ValidationTypeDomainCheck, domainName, nil, events.VerificationStatusAccepted, empData.EmploymentType.String())
		checkEntry.Result = employmentPb.CheckResult_CHECK_RESULT_ACCEPTED
		checkEntry.Reason = employmentPb.CheckResultReason_CHECK_RESULT_REASON_DOMAIN_NAME_WHITELISTED
	} else {
		s.triggerEmplVerificationServerRespEvent(ctx, empData.ActorId, "", "", "", events.Failure, events.ReasonDomainNameNotWhitelisted, events.ValidationTypeDomainCheck, domainName, nil, events.VerificationStatusHold, empData.EmploymentType.String())
		checkEntry.Result = employmentPb.CheckResult_CHECK_RESULT_REJECTED
		checkEntry.Reason = employmentPb.CheckResultReason_CHECK_RESULT_REASON_DOMAIN_NAME_NOT_WHITELISTED
	}
	checkEntry, err = s.EmplVerificationCheckDao.Create(ctx, checkEntry)
	if err != nil {
		logger.Error(ctx, "error creating domain check entry", zap.Error(err))
		return nil, err
	}
	return checkEntry, nil
}

func (s *CheckHandlerImpl) AllowAccessToNonSalariedUser(ctx context.Context, profileInfo string) (string, bool) {
	// if profile info is an email-id, return false
	isEmail := s.EmailIdRegex.MatchString(profileInfo)
	if isEmail {
		return "", false
	}
	// if profile info is not a valid url, return false
	isUrl := s.UrlValidationRegex.MatchString(profileInfo)
	if !isUrl {
		return "", false
	}
	profileInfo = strings.ToLower(profileInfo)
	// check if profileInfo contains any of the accepted domain names
	for _, domainName := range s.Config.AllowedDomainsForPersonalProfile() {
		if strings.Contains(profileInfo, domainName) {
			return domainName, true
		}
	}
	url := s.UrlValidationRegex.FindString(profileInfo)
	domain := s.DomainNameRegex.FindString(url)
	return domain, false
}

func (s *CheckHandlerImpl) triggerEmplVerificationServerRespEvent(ctx context.Context, actorId string, vendor, reqStatus,
	reqFailedReason, validationStatus, validationFailureReason, validationType, domainName string,
	orgPfData *vgEmploymentPb.EmployeeNameSearchResponse, verificationStatus, emplType string) {
	go s.EventsBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
		events.NewEmploymentVerificationResponseServer(actorId, vendor, reqStatus, reqFailedReason, validationStatus,
			validationFailureReason, validationType, domainName, orgPfData.GetIsNameUnique(), orgPfData.GetIsEmployed(),
			orgPfData.GetIsNameExact(), orgPfData.GetOrganizationName(), orgPfData.GetVendorStatus().String(), verificationStatus, emplType))
}
