package tagger

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/api/actor"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/risk/tagging"
	"github.com/epifi/gamma/risk/dao/mocks"
	mock_txn_taggers "github.com/epifi/gamma/risk/tagger/mocks"
	"github.com/epifi/gamma/risk/tagger/txn_taggers"
)

var (
	input = &txn_taggers.TxnInput{
		ActorId: "actor123",
		Order: &orderPb.Order{
			FromActorId: "actor123",
			ToActorId:   "actor456",
		},
		EntityDetailsMap: map[string]*actor.GetEntityDetailsResponse_EntityDetail{
			"actor456": {
				Name: &commontypes.Name{FirstName: "<PERSON>", LastName: "Doe"},
			},
		},
	}
)

func TestTaggerImpl_TagTransaction(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockTxnTaggersFactory := mock_txn_taggers.NewMockTxnTaggerFactory(ctrl)
	mockTxnTagMappingDao := mocks.NewMockTxnTagMappingDao(ctrl)

	taggerImpl := NewTagger(mockTxnTaggersFactory, mockTxnTagMappingDao)

	tests := []struct {
		name       string
		input      *txn_taggers.TxnInput
		setupMocks func()
		wantTags   []tagging.TransactionTag
		wantErr    bool
	}{
		{
			name:  "fails with some random error while assigning tags",
			input: input,
			setupMocks: func() {
				mockTagger := mock_txn_taggers.NewMockTxnTagger(ctrl)
				mockTxnTaggersFactory.EXPECT().GetTaggers(gomock.Any()).Return([]txn_taggers.TxnTagger{mockTagger}, nil)
				mockTagger.EXPECT().GetTags(gomock.Any(), input).Return(nil, fmt.Errorf("some random error"))
			},
			wantTags: nil,
			wantErr:  true,
		},
		{
			name:  "successful tagging with family transfer tag",
			input: input,
			setupMocks: func() {
				mockTagger := mock_txn_taggers.NewMockTxnTagger(ctrl)
				mockTxnTaggersFactory.EXPECT().GetTaggers(gomock.Any()).Return([]txn_taggers.TxnTagger{mockTagger}, nil)
				mockTagger.EXPECT().GetTags(gomock.Any(), input).Return([]tagging.TransactionTag{tagging.TransactionTag_TRANSACTION_TAG_FAMILY_TRANSFER}, nil)
				mockTxnTagMappingDao.EXPECT().BulkInsert(gomock.Any(), gomock.Any()).Return(nil)
			},
			wantTags: []tagging.TransactionTag{tagging.TransactionTag_TRANSACTION_TAG_FAMILY_TRANSFER},
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			gotTags, err := taggerImpl.TagTransaction(context.Background(), tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("TaggerImpl.TagTransaction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotTags, tt.wantTags) {
				t.Errorf("TaggerImpl.TagTransaction() = %v, want %v", gotTags, tt.wantTags)
			}
		})
	}
}
