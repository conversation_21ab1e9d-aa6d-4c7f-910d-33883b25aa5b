// Code generated by MockGen. DO NOT EDIT.
// Source: risk/case_management/consumer/processor_factory.go

// Package mock_consumer is a generated GoMock package.
package mock_consumer

import (
	context "context"
	reflect "reflect"

	case_management "github.com/epifi/gamma/api/risk/case_management"
	processors "github.com/epifi/gamma/risk/case_management/consumer/processors"
	gomock "github.com/golang/mock/gomock"
)

// MockIProcessorFactory is a mock of IProcessorFactory interface.
type MockIProcessorFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIProcessorFactoryMockRecorder
}

// MockIProcessorFactoryMockRecorder is the mock recorder for MockIProcessorFactory.
type MockIProcessorFactoryMockRecorder struct {
	mock *MockIProcessorFactory
}

// NewMockIProcessorFactory creates a new mock instance.
func NewMockIProcessorFactory(ctrl *gomock.Controller) *MockIProcessorFactory {
	mock := &MockIProcessorFactory{ctrl: ctrl}
	mock.recorder = &MockIProcessorFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIProcessorFactory) EXPECT() *MockIProcessorFactoryMockRecorder {
	return m.recorder
}

// GetBatchProcessor mocks base method.
func (m *MockIProcessorFactory) GetBatchProcessor(ctx context.Context, payloadType case_management.PayloadType) (processors.PayloadBatchProcessor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBatchProcessor", ctx, payloadType)
	ret0, _ := ret[0].(processors.PayloadBatchProcessor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBatchProcessor indicates an expected call of GetBatchProcessor.
func (mr *MockIProcessorFactoryMockRecorder) GetBatchProcessor(ctx, payloadType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchProcessor", reflect.TypeOf((*MockIProcessorFactory)(nil).GetBatchProcessor), ctx, payloadType)
}

// GetProcessor mocks base method.
func (m *MockIProcessorFactory) GetProcessor(ctx context.Context, payloadType case_management.PayloadType) (processors.PayloadProcessor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProcessor", ctx, payloadType)
	ret0, _ := ret[0].(processors.PayloadProcessor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProcessor indicates an expected call of GetProcessor.
func (mr *MockIProcessorFactoryMockRecorder) GetProcessor(ctx, payloadType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProcessor", reflect.TypeOf((*MockIProcessorFactory)(nil).GetProcessor), ctx, payloadType)
}
