package dao

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/risk"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/risk/config"
)

type DisputeDaoTestSuite struct {
	db   *gorm.DB
	Dao  DisputeDao
	conf *config.Config
}

var (
	dts             DisputeDaoTestSuite
	testInitiatedAt = timestampPb.Now()
	testResolvedAt  = timestampPb.New(time.Now().AddDate(0, 0, 5))
	testDispute     = &risk.Dispute{
		ActorId:        "actorId",
		AccountId:      "accountId",
		AccountType:    accountsPb.Type_SAVINGS,
		TransactionId:  "transactionId",
		State:          riskEnumsPb.DisputeState_DISPUTE_STATE_INITIATED,
		Verdict:        riskEnumsPb.DisputeVerdict_DISPUTE_VERDICT_UNSPECIFIED,
		VerdictDetails: nil,
		DisputedAmount: &typesPb.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		AdditionalDetails: &risk.DisputeAdditionalDetails{},
		FederalCaseId:     "federalCaseId",
		InitiatedAt:       testInitiatedAt,
		ResolvedAt:        nil,
		BankUpdatedAt:     testInitiatedAt,
		CreatedAt:         nil,
		UpdatedAt:         nil,
		DeletedAt:         nil,
	}
	testUpdateDispute = &risk.Dispute{
		ActorId:       "actorId",
		AccountId:     "accountId",
		AccountType:   accountsPb.Type_EQUITY_ACCOUNT,
		TransactionId: "txnId",
		State:         riskEnumsPb.DisputeState_DISPUTE_STATE_CLOSED,
		Verdict:       riskEnumsPb.DisputeVerdict_DISPUTE_VERDICT_REJECTED,
		VerdictDetails: &risk.DisputeVerdictDetails{
			Details: &risk.DisputeVerdictDetails_RejectionDetails{
				RejectionDetails: &risk.DisputeRejectionDetails{
					RejectionReason: "reason",
				},
			},
		},
		DisputedAmount: &typesPb.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		AdditionalDetails: &risk.DisputeAdditionalDetails{},
		FederalCaseId:     "federalCaseId",
		InitiatedAt:       testInitiatedAt,
		ResolvedAt:        testResolvedAt,
		BankUpdatedAt:     testResolvedAt,
		CreatedAt:         nil,
		UpdatedAt:         nil,
		DeletedAt:         nil,
	}
)

func TestDisputePGDB_Create(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, scts.db, scts.conf.FRMPgdb.GetName(), []string{"disputes"})
	tests := []struct {
		name        string
		dispute     *risk.Dispute
		wantErr     bool
		assertError func(error) bool
	}{
		{
			name:    "fails for nil dispute",
			wantErr: true,
			assertError: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name:    "fails with invalid dispute, empty actor id",
			dispute: &risk.Dispute{},
			wantErr: true,
			assertError: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name:    "success",
			dispute: testDispute,
		},
		{
			name:    "successfully updated",
			dispute: testUpdateDispute,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := DisputePGDB{
				db: scts.db,
			}
			err := l.Upsert(context.Background(), tt.dispute)
			if (err != nil) != tt.wantErr {
				t.Errorf("Upsert() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.assertError != nil {
				assert.True(t, tt.assertError(err))
			}
		})
	}
}
