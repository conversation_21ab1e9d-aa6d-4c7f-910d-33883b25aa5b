package priotizer

import (
	"context"
	"fmt"
	"math"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/risk/case_management/prioritization"
	"github.com/epifi/gamma/api/vendorgateway/risk"
	"github.com/epifi/gamma/risk/case_management/prioritisation/integrator"
)

type PrecisionBasedPrioritization struct {
	ModelParameterIntegrator integrator.ParameterIntegrator
}

var _ IPrecisionBasedPrioritization = &PrecisionBasedPrioritization{}

func NewPrecisionBasedPrioritization(modelParameterIntegrator integrator.ParameterIntegrator) *PrecisionBasedPrioritization {
	return &PrecisionBasedPrioritization{
		ModelParameterIntegrator: modelParameterIntegrator,
	}
}

func (c *PrecisionBasedPrioritization) GetConfidenceScore(ctx context.Context,
	params *prioritization.InputParameter) ([]*risk.ModelResponseInfo, float32, error) {

	if params == nil {
		return nil, 0, fmt.Errorf("invalid parameter %w", epifierrors.ErrInvalidArgument)
	}

	integratedParameter, integratedParameterErr := c.ModelParameterIntegrator.Get(ctx, params)

	if integratedParameterErr != nil {
		return nil, 0, fmt.Errorf("could not fetch the prioritization parameters, %w", integratedParameterErr)
	}

	precisionProduct := float32(1.0)
	for key, parameter := range integratedParameter {
		precision := parameter.GetPrecision()
		if precision > 1 || precision < 0 {
			logger.Error(ctx, "invalid precision value for the key", zap.String(logger.KEY_ID, key))
			continue // only consider the precision value [0-1] to not have the adverse effect on the formula
		}
		precisionProduct *= getMultipleAlertOccurrencePrecision(precision, parameter.GetOccurrence())
	}
	finalPrecision := 1 - precisionProduct
	logger.Info(ctx, fmt.Sprintf("prioritization parameters %s", integratedParameter))
	logger.Info(ctx, fmt.Sprintf("calculated precision %f", finalPrecision))
	// returning empty array to avoid error, PrecisionBasedPrioritization is deprecated.
	return []*risk.ModelResponseInfo{}, finalPrecision, nil
}

func getMultipleAlertOccurrencePrecision(precision float32, occurrence int32) float32 {
	if occurrence < 1 { // return 1 if there is no occurrence for this
		return 1
	}
	combinedPrecision := math.Pow(1-float64(precision), float64(getHarmonicSum(occurrence)))
	return float32(combinedPrecision)
}

func getHarmonicSum(n int32) float32 {
	if n < 1 {
		return 0
	}
	var (
		sum, i float32
	)
	for i = 1; i <= float32(n); i++ {
		sum += 1.0 / i
	}
	return sum
}
