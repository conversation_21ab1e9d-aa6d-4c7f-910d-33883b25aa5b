package activity

import (
	"context"

	"github.com/pkg/errors"

	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func (p *Processor) UpdateAlert(ctx context.Context, req *activityPb.UpdateAlertRequest) (*activityPb.UpdateAlertResponse, error) {
	_, err := p.alertDao.Update(ctx, req.Get<PERSON>t(), req.GetFieldMask())
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
	case err != nil:
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	default:
		return &activityPb.UpdateAlertResponse{}, nil
	}
}
