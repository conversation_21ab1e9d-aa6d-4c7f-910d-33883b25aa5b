package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"
	"github.com/epifi/be-common/pkg/logger"
	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	cmActivityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	cmWorkflowPb "github.com/epifi/gamma/api/risk/case_management/workflow"
)

func ExecuteAutoActions(ctx workflow.Context, req *cmWorkflowPb.ExecuteAutoActionsRequest) (*cmWorkflowPb.
	ExecuteAutoActionsResponse, error) {
	var workflowStageStatus stagePb.Status
	lg := workflow.GetLogger(ctx)

	// --------------- ADD DELAY FOR ACCUMULATING ALL THE ALERTS DUE FOR AUTO ACTIONS ----------------------------
	workflowStageStatus, err := delayExecution(ctx)
	switch {
	case err != nil:
		lg.Error("error in sleep workflow stage", zap.Error(err))
		return &cmWorkflowPb.ExecuteAutoActionsResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "sleep workflow failed")
	case workflowStageStatus != stagePb.Status_SUCCESSFUL:
		return &cmWorkflowPb.ExecuteAutoActionsResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// ---------------- Get the Auto Actions ---------------------------
	dedupedActionResp, workflowStageStatus, err := getFinalActionsForCaseId(ctx, req.GetCaseId())
	switch {
	case err != nil:
		lg.Error("failed to get the final actions to execute", zap.Error(err))
		return &cmWorkflowPb.ExecuteAutoActionsResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "failed to get the final actions to execute")
	case workflowStageStatus != stagePb.Status_SUCCESSFUL:
		return &cmWorkflowPb.ExecuteAutoActionsResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// ------------------ Log Auto Action in case -------------------------
	workflowStageStatus, err = AddDedupeCommentsInCase(ctx, dedupedActionResp)
	switch {
	case err != nil:
		lg.Error("error in adding comment to a case", zap.Error(err))
		return &cmWorkflowPb.ExecuteAutoActionsResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "failed to add comment in the case")
	case workflowStageStatus != stagePb.Status_SUCCESSFUL:
		return &cmWorkflowPb.ExecuteAutoActionsResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	for _, actionResp := range dedupedActionResp.GetDeduplicatedReviewActions() {
		if actionResp.GetShouldExclude() {
			workflowStageStatus, err = updateCaseStatusToCreated(ctx, req.GetCaseId())
			switch {
			case err != nil:
				lg.Error("error in update cases to manual review", zap.Error(err))
				return &cmWorkflowPb.ExecuteAutoActionsResponse{
					ResponseHeader: &workflowPb.ResponseHeader{
						Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
					},
				}, errors.Wrap(err, "error in update cases to manual review")
			case workflowStageStatus != stagePb.Status_SUCCESSFUL:
				return &cmWorkflowPb.ExecuteAutoActionsResponse{
					ResponseHeader: &workflowPb.ResponseHeader{
						Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
					},
				}, nil
			}

			// return success if auto action is not required
			return &cmWorkflowPb.ExecuteAutoActionsResponse{
				ResponseHeader: &workflowPb.ResponseHeader{
					Status: workflowPb.StatusOk(),
				},
			}, nil
		}
	}

	// ------------------ Execute Action ----------------------------------
	workflowStageStatus, err = executeFinalActions(ctx, dedupedActionResp)
	switch {
	case err != nil:
		lg.Error("error in executing the final actions", zap.Error(err))
		return &cmWorkflowPb.ExecuteAutoActionsResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "failed to execute the final actions")
	case workflowStageStatus != stagePb.Status_SUCCESSFUL:
		return &cmWorkflowPb.ExecuteAutoActionsResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// return success Auto Action is executed
	return &cmWorkflowPb.ExecuteAutoActionsResponse{
		ResponseHeader: &workflowPb.ResponseHeader{
			Status: workflowPb.StatusOk(),
		},
	}, nil
}

func delayExecution(ctx workflow.Context) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	// =========== Step 1: initiate stage for the workflow ============
	err := celestialPkg.InitiateWorkflowStage(ctx, riskNs.ExecuteAutoActionsSleep, stagePb.Status_INITIATED)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", riskNs.ExecuteAutoActionsSleep, err)
	}

	// =========== Step 2: Sleep Workflow ============
	sleepDuration := 10 * time.Minute
	err = workflow.Sleep(ctx, sleepDuration)
	if err != nil {
		lg.Error("workflow sleep failed", zap.Error(err))
	}

	finalStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)

	// =========== Step 3: update final stage for the workflow ============
	err = celestialPkg.UpdateWorkflowStage(ctx, riskNs.ExecuteAutoActionsSleep, finalStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return 0, fmt.Errorf("failed to update workflow stage: %s: %w", riskNs.ExecuteAutoActionsSleep, err)
	}

	return finalStageStatus, nil
}

func updateCaseStatusToCreated(ctx workflow.Context, caseId string) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	// =========== Step 1: initiate stage for the workflow ============
	err := celestialPkg.InitiateWorkflowStage(ctx, riskNs.ExecuteAutoActionsMoveToManualReview, stagePb.Status_INITIATED)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", riskNs.ExecuteAutoActionsMoveToManualReview,
			err)
	}

	// =========== Step 2: move case status to created ============
	updateCaseResponse := &cmActivityPb.UpdateCaseResponse{}
	// Updates case status to created
	err = activityPkg.Execute(ctx, riskNs.UpdateCase, updateCaseResponse, &cmActivityPb.UpdateCaseRequest{
		Case: &reviewPb.Case{
			Id:     caseId,
			Status: reviewPb.Status_STATUS_CREATED,
		},
		FieldMasks: []reviewPb.CaseFieldMask{
			reviewPb.CaseFieldMask_CASE_FIELD_STATUS,
		},
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.UpdateCase)), zap.Error(err))
	}

	finalStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)

	// =========== Step 3: update final stage for the workflow ============
	err = celestialPkg.UpdateWorkflowStage(ctx, riskNs.ExecuteAutoActionsMoveToManualReview, finalStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return 0, fmt.Errorf("failed to update workflow stage: %s: %w", riskNs.ExecuteAutoActionsMoveToManualReview, err)
	}

	return finalStageStatus, nil
}

func getFinalActionsForCaseId(ctx workflow.Context, caseId string) (*cmActivityPb.GetAutoActionsResponse,
	stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	// =========== Step 1: initiate stage for the workflow ============
	err := celestialPkg.InitiateWorkflowStage(ctx, riskNs.ExecuteAutoActionsGetFinalActions, stagePb.Status_INITIATED)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return nil, 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", riskNs.ExecuteAutoActionsGetFinalActions,
			err)
	}

	// =========== Step 2: Get Final Auto Actions ============
	getAutoActionsResponse := &cmActivityPb.GetAutoActionsResponse{}
	err = activityPkg.Execute(ctx, riskNs.GetAutoActions, getAutoActionsResponse, &cmActivityPb.GetAutoActionsRequest{
		CaseId: caseId,
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.GetAutoActions)),
			zap.Error(err))
	}

	finalStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)

	// =========== Step 3: update final stage for the workflow ============
	err = celestialPkg.UpdateWorkflowStage(ctx, riskNs.ExecuteAutoActionsGetFinalActions, finalStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return nil, 0, fmt.Errorf("failed to update workflow stage: %s: %w", riskNs.ExecuteAutoActionsGetFinalActions, err)
	}

	return getAutoActionsResponse, finalStageStatus, nil
}

func AddDedupeCommentsInCase(ctx workflow.Context, autoActionsResp *cmActivityPb.GetAutoActionsResponse) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	var finalStageStatus stagePb.Status
	// =========== Step 1: initiate stage for the workflow ============
	err := celestialPkg.InitiateWorkflowStage(ctx, riskNs.ExecuteAutoActionsAddComments, stagePb.Status_INITIATED)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", riskNs.ExecuteAutoActionsAddComments,
			err)
	}

	// =========== Step 2: Add Comments to case ============
	var comments []*reviewPb.Comment
	for _, dedupeAction := range autoActionsResp.GetDeduplicatedReviewActions() {
		if dedupeAction.GetDedupeComment() != nil {
			comments = append(comments, dedupeAction.GetDedupeComment())
		}
		if dedupeAction.GetExclusionComment() != nil {
			comments = append(comments, dedupeAction.GetExclusionComment())
		}
	}
	for _, comment := range comments {
		addCommentResponse := &cmActivityPb.AddCommentResponse{}
		err = activityPkg.Execute(ctx, riskNs.AddComment, addCommentResponse,
			&cmActivityPb.AddCommentRequest{
				Comment: comment,
			})
		if err != nil {
			lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.AddComment)), zap.Error(err))
		}

		finalStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
		if finalStageStatus != stagePb.Status_SUCCESSFUL {
			break // if stage fails break the execution and return
		}
	}

	// =========== Step 3: update final stage for the workflow ============
	err = celestialPkg.UpdateWorkflowStage(ctx, riskNs.ExecuteAutoActionsAddComments, finalStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return 0, fmt.Errorf("failed to update workflow stage: %s: %w", riskNs.ExecuteAutoActionsAddComments, err)
	}

	return finalStageStatus, nil
}

func executeFinalActions(ctx workflow.Context, autoActionsResp *cmActivityPb.GetAutoActionsResponse) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	var finalStageStatus stagePb.Status
	// =========== Step 1: initiate stage for the workflow ============
	err := celestialPkg.InitiateWorkflowStage(ctx, riskNs.ExecuteAutoActionsExecute, stagePb.Status_INITIATED)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", riskNs.ExecuteAutoActionsExecute,
			err)
	}

	// =========== Step 2: Execute Final Actions  ============
	for _, dedupeReviewAction := range autoActionsResp.GetDeduplicatedReviewActions() {
		err = triggerProcessReviewActionWorkflow(ctx, dedupeReviewAction.GetPerformReviewActionRequest())
		if err != nil {
			lg.Error("failed to execute trigger review action workflow", zap.Error(err))
		}
		finalStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
		if finalStageStatus != stagePb.Status_SUCCESSFUL {
			err = celestialPkg.UpdateWorkflowStage(ctx, riskNs.ExecuteAutoActionsExecute, finalStageStatus)
			if err != nil {
				// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
				return 0, fmt.Errorf("failed to update workflow stage: %s: %w", riskNs.ExecuteAutoActionsExecute, err)
			}
			break // if stage fails break the execution and return
		}
	}
	// =========== Step 3: update final stage for the workflow ============
	err = celestialPkg.UpdateWorkflowStage(ctx, riskNs.ExecuteAutoActionsExecute, finalStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return 0, fmt.Errorf("failed to update workflow stage: %s: %w", riskNs.ExecuteAutoActionsExecute, err)
	}

	return finalStageStatus, nil
}

func sendCommunications(ctx workflow.Context, autoActionsResp *cmActivityPb.GetAutoActionsResponse) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	var finalStageStatus = stagePb.Status_SUCCESSFUL
	// =========== Step 1: initiate stage for the workflow ============
	err := celestialPkg.InitiateWorkflowStage(ctx, riskNs.ExecuteAutoActionsSendCommunication, stagePb.Status_INITIATED)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return 0, fmt.Errorf("failed to initiate workflow stage: %s: %w", riskNs.ExecuteAutoActionsSendCommunication,
			err)
	}

	// =========== Step 2: Send communication to user ============
	for _, dedupeAction := range autoActionsResp.GetDeduplicatedReviewActions() {
		if dedupeAction.GetNotifications() == nil || len(dedupeAction.GetNotifications().GetCommunicationList()) == 0 {
			continue
		}
		// sends multiple notifications to the user simultaneously
		sendNotificationErr := activityPkg.Execute(ctx, epifitemporal.SendNotification,
			&notificationPb.SendNotificationResponse{}, &notificationPb.SendNotificationRequest{
				RequestHeader: &activityPb.RequestHeader{
					Ownership: commontypes.Ownership_EPIFI_TECH,
				},
				Notifications: dedupeAction.GetNotifications(),
			})

		if sendNotificationErr != nil {
			lg.Error("error in activity execution: ", zap.String(logger.ACTIVITY,
				string(riskNs.ExecuteAutoActionsSendCommunication)),
				zap.Error(sendNotificationErr))
		}
		finalStageStatus = celestialPkg.GetWorkflowStageStatusForErr(sendNotificationErr)
		if finalStageStatus != stagePb.Status_SUCCESSFUL {
			break // if stage fails break the execution and return
		}
	}
	// =========== Step 3: update final stage for the workflow ============
	err = celestialPkg.UpdateWorkflowStage(ctx, riskNs.ExecuteAutoActionsSendCommunication, finalStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return 0, fmt.Errorf("failed to update workflow stage: %s: %w", riskNs.ExecuteAutoActionsSendCommunication, err)
	}

	return finalStageStatus, nil
}
