// Code generated by MockGen. DO NOT EDIT.
// Source: evaluator.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	review "github.com/epifi/gamma/api/risk/case_management/review"
	gomock "github.com/golang/mock/gomock"
)

// MockEvaluator is a mock of Evaluator interface.
type MockEvaluator struct {
	ctrl     *gomock.Controller
	recorder *MockEvaluatorMockRecorder
}

// MockEvaluatorMockRecorder is the mock recorder for MockEvaluator.
type MockEvaluatorMockRecorder struct {
	mock *MockEvaluator
}

// NewMockEvaluator creates a new mock instance.
func NewMockEvaluator(ctrl *gomock.Controller) *MockEvaluator {
	mock := &MockEvaluator{ctrl: ctrl}
	mock.recorder = &MockEvaluatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEvaluator) EXPECT() *MockEvaluatorMockRecorder {
	return m.recorder
}

// EvaluateProcessingType mocks base method.
func (m *MockEvaluator) EvaluateProcessingType(ctx context.Context, action *review.Action) (*review.ActionProcessingParams, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EvaluateProcessingType", ctx, action)
	ret0, _ := ret[0].(*review.ActionProcessingParams)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EvaluateProcessingType indicates an expected call of EvaluateProcessingType.
func (mr *MockEvaluatorMockRecorder) EvaluateProcessingType(ctx, action interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EvaluateProcessingType", reflect.TypeOf((*MockEvaluator)(nil).EvaluateProcessingType), ctx, action)
}
