// nolint: errcheck
package dao

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"google.golang.org/protobuf/testing/protocmp"
	"gorm.io/gorm"

	pb "github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/risk/case_management/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/risk/config"
)

type AlertDaoTestSuite struct {
	db       *gorm.DB
	alertDao AlertDao
	conf     *config.Config
}

var (
	alertTestTables = []string{"alerts"}
	adts            AlertDaoTestSuite

	// Create Dao
	createDaoa1 = &pb.Alert{
		CaseId:      "1",
		AccountType: enums.AccountType_ACCOUNT_TYPE_SAVING_BANK,
		AccountId:   "accountId",
		ActorId:     "acctorId",
		EntityType:  enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:    "someTxnId",
		Verdict:     enums.Verdict_VERDICT_PASS,
		RuleId:      "someRuleId",
		BatchName:   "klsdfj",
	}
	createDaoa2 = &pb.Alert{
		CaseId:        "1",
		AccountType:   enums.AccountType_ACCOUNT_TYPE_SAVING_BANK,
		AccountId:     "accountId",
		ActorId:       "acctorId",
		EntityType:    enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:      "someTxnId",
		Verdict:       enums.Verdict_VERDICT_PASS,
		RuleId:        "someRuleId",
		BatchName:     "klsdfj",
		RulePrecision: 0.8,
	}
	withNullRuleId = &pb.Alert{
		CaseId:     "1",
		AccountId:  "accountId",
		ActorId:    "acctorId",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "someTxnId",
		Verdict:    enums.Verdict_VERDICT_PASS,
		BatchName:  "klsdfj",
	}
	withInvalidEntityType = &pb.Alert{
		CaseId:     "1",
		AccountId:  "accountId",
		ActorId:    "acctorId",
		EntityType: enums.EntityType_ENTITY_TYPE_UNSPECIFIED,
		EntityId:   "someTxnId",
		RuleId:     "someRuleId",
		Verdict:    enums.Verdict_VERDICT_PASS,
		BatchName:  "klsdfj",
	}
	withNullEntityId = &pb.Alert{
		CaseId:     "1",
		AccountId:  "accountId",
		ActorId:    "acctorId",
		RuleId:     "someRuleId",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		Verdict:    enums.Verdict_VERDICT_PASS,
		BatchName:  "klsdfj",
	}

	// Update Dao
	alertId  = uuid.NewString()
	updateA1 = &pb.Alert{
		Id:         alertId,
		CaseId:     "1",
		AccountId:  "accountId",
		ActorId:    "acctorId",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "someTxnId",
		Verdict:    enums.Verdict_VERDICT_UNSPECIFIED,
		RuleId:     "someRuleId",
		BatchName:  "klsdfj",
	}
	updatedA1 = &pb.Alert{
		Id:           alertId,
		CaseId:       "1",
		AccountId:    "accountId",
		ActorId:      "acctorId",
		EntityType:   enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:     "someTxnId",
		Verdict:      enums.Verdict_VERDICT_PASS,
		RuleId:       "someRuleId",
		BatchName:    "klsdfj",
		HandlingType: enums.AlertHandlingType_ALERT_HANDLING_TYPE_SEND_FOR_REVIEW,
		HandlingReasons: []enums.AlertHandlingReason{
			enums.AlertHandlingReason_ALERT_HANDLING_REASON_RULE_INACTIVE,
			enums.AlertHandlingReason_ALERT_HANDLING_REASON_LOW_CONFIDENCE_RULE,
		},
	}
	updateWithNullRuleId = &pb.Alert{
		CaseId:     "1",
		AccountId:  "accountId",
		ActorId:    "acctorId",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "someTxnId",
		Verdict:    enums.Verdict_VERDICT_PASS,
		BatchName:  "klsdfj",
	}

	// Get by Actor id Dao
	actorId  = uuid.NewString()
	actorId2 = uuid.NewString()

	getByActorIdA1 = &pb.Alert{
		CaseId:     "1",
		AccountId:  "accountId",
		ActorId:    actorId,
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "someTxnId",
		Verdict:    enums.Verdict_VERDICT_UNSPECIFIED,
		RuleId:     "someRuleId",
		BatchName:  "klsdfj",
	}

	getCase2ByActor1 = &pb.Alert{
		CaseId:     "2",
		AccountId:  "accountId",
		ActorId:    actorId,
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "someTxnId",
		Verdict:    enums.Verdict_VERDICT_UNSPECIFIED,
		RuleId:     "someRuleId",
		BatchName:  "klsdfj",
	}

	getByActorId2 = &pb.Alert{
		CaseId:     "1",
		AccountId:  "accountId",
		ActorId:    actorId2,
		EntityType: enums.EntityType_ENTITY_TYPE_SCREENER,
		EntityId:   "someTxnId",
		Verdict:    enums.Verdict_VERDICT_UNSPECIFIED,
		RuleId:     "someRuleId",
		BatchName:  "klsdfj",
	}

	getByActorId2WithCaseIDAndActorId = &pb.Alert{
		CaseId:  "1",
		ActorId: actorId2,
	}

	// Get by Case id Dao
	caseId        = "10"
	getByCaseIdA1 = &pb.Alert{
		CaseId:     "10",
		AccountId:  "accountId",
		ActorId:    "actorId",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "someTxnId",
		Verdict:    enums.Verdict_VERDICT_UNSPECIFIED,
		RuleId:     "someRuleId",
		BatchName:  "klsdfj",
	}

	// Get by Account id Dao
	accountId        = uuid.NewString()
	getByAccountIdA1 = &pb.Alert{
		CaseId:     "1",
		AccountId:  accountId,
		ActorId:    "actorId",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "someTxnId",
		Verdict:    enums.Verdict_VERDICT_UNSPECIFIED,
		RuleId:     "someRuleId",
		BatchName:  "klsdfj",
	}

	// Get by entity dao
	entityId      = uuid.NewString()
	getByEntityA1 = &pb.Alert{
		CaseId:     "1",
		AccountId:  "accountId",
		ActorId:    "actorId",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   entityId,
		Verdict:    enums.Verdict_VERDICT_UNSPECIFIED,
		RuleId:     "someRuleId",
		BatchName:  "klsdfj",
	}
)

func TestAlertDaoCRDB_Create(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, adts.conf.FRMDb.GetName(), adts.db, alertTestTables)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx   context.Context
		alert *pb.Alert
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.Alert
		wantErr bool
	}{
		{
			name: "Successfully insert value with rule precision",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:   context.Background(),
				alert: createDaoa2,
			},
			want:    createDaoa2,
			wantErr: false,
		},
		{
			name: "Successfully insert value",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:   context.Background(),
				alert: createDaoa1,
			},
			want:    createDaoa1,
			wantErr: false,
		},
		{
			name: "give error for null rule id",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:   context.Background(),
				alert: withNullRuleId,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "give error for invalid entity type",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:   context.Background(),
				alert: withInvalidEntityType,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "give error for null entity id",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:   context.Background(),
				alert: withNullEntityId,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AlertDaoCRDB{
				db: tt.fields.db,
			}
			got, err := a.Create(tt.args.ctx, tt.args.alert)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.Alert{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Create() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}

func TestAlertDaoCRDB_Update(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, adts.conf.FRMDb.GetName(), adts.db, alertTestTables)
	adts.alertDao.Create(context.Background(), updateA1)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx        context.Context
		alert      *pb.Alert
		updateMask []pb.AlertFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.Alert
		wantErr bool
	}{
		{
			name: "Successfully update value",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:   context.Background(),
				alert: updatedA1,
				updateMask: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_VERDICT,
					pb.AlertFieldMask_ALERT_FIELD_MASK_HANDLING_TYPE,
					pb.AlertFieldMask_ALERT_FIELD_MASK_HANDLING_REASON,
				},
			},
			want:    updatedA1,
			wantErr: false,
		},
		{
			name: "give error for null update mask",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:   context.Background(),
				alert: updateWithNullRuleId,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "give error for invalid update mask",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				alert:      updatedA1,
				updateMask: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_ALL},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AlertDaoCRDB{
				db: tt.fields.db,
			}
			got, err := a.Update(tt.args.ctx, tt.args.alert, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.Alert{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Update() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}

func TestAlertDaoCRDB_GetByActorId(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, adts.conf.FRMDb.GetName(), adts.db, alertTestTables)
	adts.alertDao.Create(context.Background(), getByActorIdA1)
	adts.alertDao.Create(context.Background(), getByActorId2)
	adts.alertDao.Create(context.Background(), getCase2ByActor1)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx        context.Context
		actorId    string
		fieldMasks []pb.AlertFieldMask
		limit      int
		options    []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.Alert
		wantErr bool
	}{
		{
			name: "error because field mask is empty",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:     context.Background(),
				actorId: actorId,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "getting the error for nil actor id",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    "",
				fieldMasks: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_ALL},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error because limit passed if above max limit",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    actorId,
				fieldMasks: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_ALL},
				limit:      250,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Successfully get the value by actor id with field mask all",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    actorId,
				fieldMasks: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_ALL},
			},
			want:    []*pb.Alert{getCase2ByActor1, getByActorIdA1},
			wantErr: false,
		},
		{
			name: "Successfully get the value by actor id, with field mask alert id and case id",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    actorId2,
				fieldMasks: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_CASE_ID, pb.AlertFieldMask_ALERT_FIELD_MASK_ACTOR_ID},
			},
			want:    []*pb.Alert{getByActorId2WithCaseIDAndActorId},
			wantErr: false,
		},
		{
			name: "Successfully get the value by actor id without specific case Id ",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    actorId,
				fieldMasks: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_ALL},
				options:    []storagev2.FilterOption{ExcludeCaseId(getCase2ByActor1.CaseId)},
			},
			want:    []*pb.Alert{getByActorIdA1},
			wantErr: false,
		},
		{
			name: "getting error when no alert is present",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    "some random actor id",
				fieldMasks: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_ALL},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "empty time in WithCreatedAtAfter filter option with field masks verdict, account id",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    actorId,
				fieldMasks: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_VERDICT, pb.AlertFieldMask_ALERT_FIELD_MASK_ACCOUNT_ID},
				limit:      100,
				options:    []storagev2.FilterOption{WithCreatedAtAfter(time.Time{})},
			},
			want: []*pb.Alert{
				{
					Verdict:   getByActorIdA1.GetVerdict(),
					AccountId: getByActorIdA1.GetAccountId(),
				},
				{
					Verdict:   getCase2ByActor1.GetVerdict(),
					AccountId: getCase2ByActor1.GetAccountId(),
				},
			},
			wantErr: false,
		},
		{
			name: "record not found for empty time in WithCreatedAtAfter filter option with field masks verdict, account id",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				actorId:    actorId,
				fieldMasks: []pb.AlertFieldMask{pb.AlertFieldMask_ALERT_FIELD_MASK_VERDICT, pb.AlertFieldMask_ALERT_FIELD_MASK_ACCOUNT_ID},
				limit:      100,
				options:    []storagev2.FilterOption{WithCreatedAtAfter(time.Now())},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AlertDaoCRDB{
				db: tt.fields.db,
			}
			got, err := a.GetByActorId(tt.args.ctx, tt.args.actorId, tt.args.fieldMasks, tt.args.limit, tt.args.options...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.Alert{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByActorId() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}

func TestAlertDaoCRDB_GetByCaseId(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, adts.conf.FRMDb.GetName(), adts.db, alertTestTables)
	adts.alertDao.Create(context.Background(), getByCaseIdA1)
	adts.alertDao.Create(context.Background(), getByActorIdA1)
	adts.alertDao.Create(context.Background(), getByActorId2)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx     context.Context
		caseId  string
		options []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.Alert
		wantErr bool
	}{
		{
			name: "Successfully get the value by case id",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:    context.Background(),
				caseId: caseId,
			},
			want:    []*pb.Alert{getByCaseIdA1},
			wantErr: false,
		},
		{
			name: "Successfully get the value by case id with entity filter",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:    context.Background(),
				caseId: getByActorId2.CaseId,
				options: []storagev2.FilterOption{
					WithEntityTypes([]enums.EntityType{enums.EntityType_ENTITY_TYPE_SCREENER}),
				},
			},
			want: []*pb.Alert{getByActorId2},

			wantErr: false,
		},
		{
			name: "getting error when no alert is present",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:    context.Background(),
				caseId: "100000",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "getting error when case id is invalid",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:    context.Background(),
				caseId: "",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AlertDaoCRDB{
				db: tt.fields.db,
			}
			got, err := a.GetByCaseId(tt.args.ctx, tt.args.caseId, tt.args.options...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByCaseId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.Alert{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByCaseId() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}

func TestAlertDaoCRDB_GetByAccountId(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, adts.conf.FRMDb.GetName(), adts.db, alertTestTables)
	adts.alertDao.Create(context.Background(), getByAccountIdA1)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx       context.Context
		accountId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.Alert
		wantErr bool
	}{
		{
			name: "Successfully get the value by actor id",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:       context.Background(),
				accountId: accountId,
			},
			want:    []*pb.Alert{getByAccountIdA1},
			wantErr: false,
		},
		{
			name: "getting error when no alert is present",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:       context.Background(),
				accountId: "some random account id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "getting error when account id is invalid",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:       context.Background(),
				accountId: "",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AlertDaoCRDB{
				db: tt.fields.db,
			}
			got, err := a.GetByAccountId(tt.args.ctx, tt.args.accountId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.Alert{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByAccountId() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}

func TestAlertDaoCRDB_GetByEntity(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, adts.conf.FRMDb.GetName(), adts.db, alertTestTables)
	adts.alertDao.Create(context.Background(), getByEntityA1)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx        context.Context
		entityType enums.EntityType
		entityId   string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pb.Alert
		wantErr bool
	}{
		{
			name: "Successfully get the value by actor id",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				entityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
				entityId:   entityId,
			},
			want:    []*pb.Alert{getByEntityA1},
			wantErr: false,
		},
		{
			name: "getting error when entity type is not defined",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:        context.Background(),
				entityType: enums.EntityType_ENTITY_TYPE_UNSPECIFIED,
				entityId:   "some random account id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "getting error when no alert is present",
			fields: fields{
				db: rdts.db,
			},
			args: args{
				ctx:      context.Background(),
				entityId: "some random account id",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AlertDaoCRDB{
				db: tt.fields.db,
			}
			got, err := a.GetByEntity(tt.args.ctx, tt.args.entityType, tt.args.entityId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByEntity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.Alert{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByAccountId() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}

func TestAlertDaoCRDB_AggregateForActor(t *testing.T) {
	type fields struct {
		db *gorm.DB
	}
	pkgTest.PrepareScopedDatabase(t, adts.conf.FRMDb.GetName(), adts.db, alertTestTables)
	_, _ = adts.alertDao.Create(context.Background(), getByEntityA1)
	_, _ = adts.alertDao.Create(context.Background(), getByEntityA1)
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name      string
		fields    fields
		args      args
		want      []*pb.AlertAggregateForActor
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "failed with invalid argument for empty actor id",
			args: args{actorId: "", ctx: context.Background()},
			fields: fields{
				db: rdts.db,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "failed with record not found",
			args: args{actorId: uuid.NewString(), ctx: context.Background()},
			fields: fields{
				db: rdts.db,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, epifierrors.ErrRecordNotFound)
			},
		},
		{
			name: "Success",
			args: args{actorId: getByEntityA1.GetActorId(), ctx: context.Background()},
			fields: fields{
				db: rdts.db,
			},
			want: []*pb.AlertAggregateForActor{
				{
					CaseId: getByEntityA1.GetCaseId(),
					RuleId: getByEntityA1.GetRuleId(),
					Count:  2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AlertDaoCRDB{
				db: tt.fields.db,
			}
			got, err := a.AggregateForActor(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("AggregateForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.AlertAggregateForActor{}, "created_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("AggregateForActor() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}
