function f_resolvedconf {
  echo "[$SCRIPT_COUNT] Systemd/resolved.conf"

  local dnsarray
  local dnslist

  mapfile -t dnsarray < <( grep ^nameserver /etc/resolv.conf | sed 's/^nameserver\s//g' )
  dnslist=${dnsarray[*]}

  if [ ${#dnsarray[@]} -lt 2 ]; then
    dnslist="$dnslist *******"
  fi

  sed -i '/^nameserver/d' /etc/resolv.conf

  for n in $dnslist; do
    echo "nameserver $n" >> /etc/resolv.conf
  done

  sed -i "s/^#DNS=.*/DNS=$dnslist/" "$RESOLVEDCONF"
  sed -i "s/^#FallbackDNS=.*/FallbackDNS=*******/" "$RESOLVEDCONF"
  sed -i "s/^#DNSSEC=.*/DNSSEC=allow-downgrade/" "$RESOLVEDCONF"
  sed -i "s/^#DNSOverTLS=.*/DNSOverTLS=opportunistic/" "$RESOLVEDCONF"

  sed -i '/^hosts:/ s/files dns/files resolve dns/' /etc/nsswitch.conf

  systemctl daemon-reload

  if [[ $VERBOSE == "Y" ]]; then
    journalctl -r -n10 -u systemd-resolved --no-pager
    echo
  fi

  ((SCRIPT_COUNT++))
}
