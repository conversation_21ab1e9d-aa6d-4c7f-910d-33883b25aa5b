package cohort

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/acquisition/crossattach"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/user/onboarding"
)

func isWBConnectedUser(netWorthAssetsValueList *crossattach.UserAttributeValue_NetWorthAssetValueList) bool {
	for _, assetValue := range netWorthAssetsValueList.GetNetWorthAssetValues() {
		if assetValue.GetComputationStatus() == networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS {
			return true
		}
	}
	return false
}

func getAssetValue(ctx context.Context, assetType networthPb.AssetType, netWorthAssetsValueList []*networthPb.AssetValue) *networthPb.AssetValue {
	for _, assetValue := range netWorthAssetsValueList {
		if assetValue.GetNetWorthAttribute() == assetType {
			return assetValue
		}
	}
	logger.Info(ctx, "Asset value not found", zap.String(logger.REQUEST_TYPE, assetType.String()))
	return &networthPb.AssetValue{
		ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND,
	}
}

func isWealthBuilderOnboardedUser(intentSelectionMetadata *onboarding.IntentSelectionMetadata, featureInfo *onboarding.FeatureDetails) bool {
	return intentSelectionMetadata.GetSelection().IsWealthBuilderUserIntent() &&
		featureInfo.GetFeatureInfo()[onboarding.Feature_FEATURE_WEALTH_ANALYSER.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE
}
