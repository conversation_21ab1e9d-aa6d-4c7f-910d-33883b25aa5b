package cohort

import (
	"context"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	"github.com/epifi/gamma/api/user/onboarding"
)

type CohortSAExpired struct {
	userAttributesHelper userattributes.UserAttributesHelper
}

func NewCohortSAExpired(userAttributesHelper userattributes.UserAttributesHelper) *CohortSAExpired {
	return &CohortSAExpired{
		userAttributesHelper: userAttributesHelper,
	}
}

func (s *CohortSAExpired) IsMember(ctx context.Context, req *IsMemberRequest) (*IsMemberResponse, error) {
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_ONBOARDING_FI_LITE_DETAILS,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	if getUserAttributeRes.GetUserAttributeValue().GetOnbFiLiteDetails().GetIsEnabled() == commontypes.BooleanEnum_TRUE &&
		getUserAttributeRes.GetUserAttributeValue().GetOnbFiLiteDetails().GetFiLiteSource() == onboarding.FiLiteSource_FI_LITE_SOURCE_SA_ONBOARDING_EXPIRED {
		logger.Info(ctx, "SA onboarding expired drop off")
		return &IsMemberResponse{
			IsMember: true,
		}, nil
	}

	return &IsMemberResponse{
		IsMember: false,
	}, nil
}

func (s *CohortSAExpired) CanUpsell(ctx context.Context, req *CanUpsellRequest) (*CanUpsellResponse, error) {
	// can upsell after 0 days
	return &CanUpsellResponse{
		CanUpsell:   true,
		UpsellAfter: 0,
	}, nil
}
