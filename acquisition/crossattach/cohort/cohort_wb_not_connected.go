package cohort

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
)

type CohortWbNotConnected struct {
	userAttributesHelper userattributes.UserAttributesHelper
}

func NewCohortWbNotConnected(userAttributesHelper userattributes.UserAttributesHelper) *CohortWbNotConnected {
	return &CohortWbNotConnected{
		userAttributesHelper: userAttributesHelper,
	}
}

func (s *CohortWbNotConnected) IsMember(ctx context.Context, req *IsMemberRequest) (*IsMemberResponse, error) {
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_ONBOARDING_FEATURE_DETAILS,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	intentMetadataRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_ONBOARDING_INTENT_SELECTION_METADATA,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	if !isWealthBuilderOnboardedUser(intentMetadataRes.GetUserAttributeValue().GetOnbIntentSelectionMetadata(), getUserAttributeRes.GetUserAttributeValue().GetOnbFeatureDetails()) {
		logger.Info(ctx, "User is not a WB user")
		return &IsMemberResponse{
			IsMember: false,
		}, nil
	}

	getUserAttributeRes, err = s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	if !isWBConnectedUser(getUserAttributeRes.GetUserAttributeValue().GetNetWorthAssetValueList()) {
		logger.Info(ctx, "User is not a WB connected user")
		return &IsMemberResponse{
			IsMember: true,
		}, nil
	}
	return &IsMemberResponse{
		IsMember: false,
	}, nil
}

func (s *CohortWbNotConnected) CanUpsell(ctx context.Context, req *CanUpsellRequest) (*CanUpsellResponse, error) {
	// can upsell after 3 days from home landed timestamp
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	homeLandedTimestamp := getUserAttributeRes.GetUserAttributeValue().GetHomeLandedTimestamp()
	if homeLandedTimestamp == nil {
		logger.Error(ctx, "empty home landed timestamp")
		return nil, fmt.Errorf("empty home landed timestamp")
	}
	upsellAfter := 3 * 24 * time.Hour
	if time.Since(homeLandedTimestamp.AsTime()) < upsellAfter {
		logger.Info(ctx, "cannot upsell as landed on home within 3 days")
		return &CanUpsellResponse{
			CanUpsell:   false,
			UpsellAfter: homeLandedTimestamp.AsTime().Add(upsellAfter).Sub(time.Now()),
		}, nil
	}
	return &CanUpsellResponse{
		CanUpsell:   true,
		UpsellAfter: 0,
	}, nil
}
