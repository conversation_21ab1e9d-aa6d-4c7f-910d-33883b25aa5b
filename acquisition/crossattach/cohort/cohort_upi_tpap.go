package cohort

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/user/onboarding"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
)

type CohortUpiTpap struct {
	userAttributesHelper userattributes.UserAttributesHelper
}

func NewCohortUpiTpap(userAttributesHelper userattributes.UserAttributesHelper) *CohortUpiTpap {
	return &CohortUpiTpap{
		userAttributesHelper: userAttributesHelper,
	}
}

func (s *CohortUpiTpap) IsMember(ctx context.Context, req *IsMemberRequest) (*IsMemberResponse, error) {
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_ONBOARDING_FEATURE_DETAILS,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	if getUserAttributeRes.GetUserAttributeValue().GetOnbFeatureDetails().GetFeatureInfo()[onboarding.Feature_FEATURE_UPI_TPAP.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE {
		logger.Info(ctx, "User is a UPI TPAP user")
		return &IsMemberResponse{
			IsMember: true,
		}, nil
	}

	return &IsMemberResponse{
		IsMember: false,
	}, nil
}

func (s *CohortUpiTpap) CanUpsell(ctx context.Context, req *CanUpsellRequest) (*CanUpsellResponse, error) {
	// can upsell after 0 days
	return &CanUpsellResponse{
		CanUpsell:   true,
		UpsellAfter: 0,
	}, nil
}
