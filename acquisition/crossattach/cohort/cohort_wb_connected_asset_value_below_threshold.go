package cohort

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/acquisition/config/genconf"
	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	networthPb "github.com/epifi/gamma/api/insights/networth"
)

type CohortWbConnectedAssetValueBelowThreshold struct {
	userAttributesHelper userattributes.UserAttributesHelper
	gconf                *genconf.CrossAttachConfig
}

func NewCohortWbConnectedAssetValueBelowThreshold(userAttributesHelper userattributes.UserAttributesHelper, gconf *genconf.CrossAttachConfig) *CohortWbConnectedAssetValueBelowThreshold {
	return &CohortWbConnectedAssetValueBelowThreshold{
		userAttributesHelper: userAttributesHelper,
		gconf:                gconf,
	}
}

func (s *CohortWbConnectedAssetValueBelowThreshold) IsMember(ctx context.Context, req *IsMemberRequest) (*IsMemberResponse, error) {
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_ONBOARDING_FEATURE_DETAILS,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	intentMetadataRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_ONBOARDING_INTENT_SELECTION_METADATA,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	if !isWealthBuilderOnboardedUser(intentMetadataRes.GetUserAttributeValue().GetOnbIntentSelectionMetadata(), getUserAttributeRes.GetUserAttributeValue().GetOnbFeatureDetails()) {
		logger.Info(ctx, "User is not a WB user")
		return &IsMemberResponse{
			IsMember: false,
		}, nil
	}

	getUserAttributeRes, err = s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	if !isWBConnectedUser(getUserAttributeRes.GetUserAttributeValue().GetNetWorthAssetValueList()) {
		logger.Info(ctx, "User is not a WB connected user")
		return &IsMemberResponse{
			IsMember: false,
		}, nil
	}
	netWorthAssetValueList := getUserAttributeRes.GetUserAttributeValue().GetNetWorthAssetValueList()
	mfAssetValue := getAssetValue(ctx, networthPb.AssetType_ASSET_TYPE_MUTUAL_FUND, netWorthAssetValueList.GetNetWorthAssetValues())
	caAssetValue := getAssetValue(ctx, networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS, netWorthAssetValueList.GetNetWorthAssetValues())

	// todo(saiteja): check currency units
	if mfAssetValue.GetComputationStatus() == networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS && mfAssetValue.GetValue().GetUnits() < s.gconf.MutualFundValueThresholdInINR() {
		logger.Info(ctx, "User has mutual fund asset value below threshold")
		return &IsMemberResponse{
			IsMember: true,
		}, nil
	}

	if caAssetValue.GetComputationStatus() == networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS && caAssetValue.GetValue().GetUnits() < s.gconf.MutualFundValueThresholdInINR() {
		logger.Info(ctx, "User has connected accounts asset value below threshold")
		return &IsMemberResponse{
			IsMember: true,
		}, nil
	}

	return &IsMemberResponse{
		IsMember: false,
	}, nil
}

func (s *CohortWbConnectedAssetValueBelowThreshold) CanUpsell(ctx context.Context, req *CanUpsellRequest) (*CanUpsellResponse, error) {
	// can upsell after 24 hrs from home landed date
	getUserAttributeRes, err := s.userAttributesHelper.GetUserAttribute(ctx, &userattributes.GetUserAttributeRequest{
		ActorId:       req.GetActorId(),
		UserAttribute: crossAttachPb.UserAttribute_USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP,
	})
	if err != nil {
		logger.Error(ctx, "failed to get user attribute", zap.Error(err))
		return nil, err
	}
	homeLandedTimestamp := getUserAttributeRes.GetUserAttributeValue().GetHomeLandedTimestamp()
	if homeLandedTimestamp == nil {
		logger.Error(ctx, "empty home landed timestamp")
		return nil, fmt.Errorf("empty home landed timestamp")
	}
	upsellAfter := 24 * time.Hour
	if time.Since(homeLandedTimestamp.AsTime()) < upsellAfter {
		logger.Info(ctx, "cannot upsell as landed on home within 24 hrs")
		return &CanUpsellResponse{
			CanUpsell:   false,
			UpsellAfter: homeLandedTimestamp.AsTime().Add(upsellAfter).Sub(time.Now()),
		}, nil
	}

	return &CanUpsellResponse{
		CanUpsell: true,
	}, nil
}
