package crossattach

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/pkg/events"

	typesPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/acquisition/crossattach/cohort"
	crossAttachEvents "github.com/epifi/gamma/acquisition/crossattach/events"
	"github.com/epifi/gamma/acquisition/crossattach/userattributes/dao"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	productPb "github.com/epifi/gamma/api/product"
)

type Service struct {
	eventBroker                 events.Broker
	userAttributesDao           dao.UserAttributesDao
	userAttributesHelper        userattributes.UserAttributesHelper
	cohortTypeToCohortHelperMap map[string]cohort.CohortHelper
}

func NewService(userAttributesHelper userattributes.UserAttributesHelper,
	userAttributesDao dao.UserAttributesDao,
	eventBroker events.Broker,
	cohortWbNotConnected *cohort.CohortWbNotConnected,
	cohortWbConnected *cohort.CohortWbConnected,
	cohortWbConnectedAssetValueBelowThreshold *cohort.CohortWbConnectedAssetValueBelowThreshold,
	cohortUpiTpap *cohort.CohortUpiTpap,
	cohortPLDropOff *cohort.CohortPLDropOff,
	cohortSAExpired *cohort.CohortSAExpired,
	cohortSAForcedDropOff *cohort.CohortSAForcedDropOff) *Service {
	return &Service{
		userAttributesHelper: userAttributesHelper,
		userAttributesDao:    userAttributesDao,
		eventBroker:          eventBroker,
		cohortTypeToCohortHelperMap: map[string]cohort.CohortHelper{
			COHORT_TYPE_WB_NOT_CONNECTED:                         cohortWbNotConnected,
			COHORT_TYPE_WB_CONNECTED_ASSET_VALUE_BELOW_THRESHOLD: cohortWbConnectedAssetValueBelowThreshold,
			COHORT_TYPE_WB_CONNECTED:                             cohortWbConnected,
			COHORT_TYPE_UPI_TPAP:                                 cohortUpiTpap,
			COHORT_TYPE_SA_FORCED_DROP_OFF:                       cohortSAForcedDropOff,
			COHORT_TYPE_SA_EXPIRED:                               cohortSAExpired,
			COHORT_TYPE_PL_DROP_OFF:                              cohortPLDropOff,
		},
	}
}

func (s *Service) GetCrossSellInfo(ctx context.Context, req *crossAttachPb.GetCrossSellInfoRequest) (*crossAttachPb.GetCrossSellInfoResponse, error) {
	var (
		errStatus = func(err error) (*crossAttachPb.GetCrossSellInfoResponse, error) {
			return &crossAttachPb.GetCrossSellInfoResponse{
				Status:       rpc.StatusInternalWithDebugMsg(err.Error()),
				CanCrossSell: typesPb.BooleanEnum_FALSE,
			}, nil
		}
	)
	if req.GetActorId() == "" {
		return &crossAttachPb.GetCrossSellInfoResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	for _, cohortType := range cohortTypeListByPriority {
		cohortHelper, ok := s.cohortTypeToCohortHelperMap[cohortType]
		if !ok {
			logger.Error(ctx, "cohortHelper not found for cohort", zap.String(logger.FEATURE, cohortType))
			return errStatus(fmt.Errorf("cohortHelper not found for cohort: %s", cohortType))
		}
		isMemberRes, err := cohortHelper.IsMember(ctx, &cohort.IsMemberRequest{
			ActorId: req.GetActorId(),
		})
		if err != nil {
			logger.Error(ctx, "error in checking if user is member of cohort", zap.String(logger.FEATURE, cohortType), zap.Error(err))
			return errStatus(err)
		}
		if !isMemberRes.GetIsMember() {
			continue
		}
		logger.Info(ctx, "user is part of cohort", zap.String(logger.FEATURE, cohortType))
		canUpsellRes, err := cohortHelper.CanUpsell(ctx, &cohort.CanUpsellRequest{
			ActorId: req.GetActorId(),
		})
		if err != nil {
			logger.Error(ctx, "error in checking if user can be upsold, returning default products to pitch order", zap.String(logger.FEATURE, cohortType), zap.Error(err))
			return &crossAttachPb.GetCrossSellInfoResponse{
				Status:                     rpc.StatusOk(),
				PrioritizedProductsToPitch: defaultProductsToPitchOrder,
				CanCrossSell:               typesPb.BooleanEnum_FALSE,
			}, nil
		}
		productsToPitch, err := s.getProductsToPitch(ctx, req.GetActorId(), canUpsellRes.GetUpsellAfter(), cohortType)
		if err != nil {
			logger.Error(ctx, "error in getting prioritized products to pitch", zap.Error(err))
			return errStatus(err)
		}
		// todo: remove this log once feature is stable
		logger.Info(ctx, "cross sell info",
			zap.String(logger.FEATURE, cohortType),
			zap.Any("prioritizedProductsToPitch", productsToPitch),
			zap.Bool("canUpsell", canUpsellRes.GetCanUpsell()),
			zap.Duration("upsellAfter", canUpsellRes.GetUpsellAfter()))
		return &crossAttachPb.GetCrossSellInfoResponse{
			Status:                     rpc.StatusOk(),
			PrioritizedProductsToPitch: productsToPitch,
			CanCrossSell:               typesPb.BoolToBooleanEnum(canUpsellRes.GetCanUpsell()),
		}, nil
	}

	logger.Info(ctx, "user is not part of any cohort, returning default products to pitch order")
	return &crossAttachPb.GetCrossSellInfoResponse{
		Status:                     rpc.StatusOk(),
		PrioritizedProductsToPitch: defaultProductsToPitchOrder,
		CanCrossSell:               typesPb.BooleanEnum_FALSE,
	}, nil
}

// getProductsToPitch returns the prioritized products to pitch to the user
// This method does fresh evaluation when there is no entry available in cache and generates a rudder event for every fresh evaluation
func (s *Service) getProductsToPitch(ctx context.Context, actorId string, upsellAfter time.Duration, cohort string) ([]productPb.ProductType, error) {
	products, err := s.userAttributesDao.GetPrioritizedProductsToPitch(ctx, actorId)
	if err == nil {
		logger.Info(ctx, "using cached products to pitch")
		return products, nil
	}
	if !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error fetching prioritized products to pitch", zap.Error(err))
		return nil, err
	}
	res, evalErr := s.evaluateProductsToPitch(ctx, actorId)
	if evalErr != nil {
		logger.Error(ctx, "error evaluating products to pitch", zap.Error(evalErr))
		return nil, evalErr
	}
	// This event needs to be triggered only when we are evaluating products to pitch
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		userAttributesMap := map[string]interface{}{
			"pd_category":            res.GetPdCategory().String(),
			"loan_affinity_category": res.GetLoanAffinityCategory().String(),
			"screener_check_result":  fmt.Sprint(res.GetScreenerCheckResult()),
		}
		// product eligibility status as defined in PRD: https://docs.google.com/document/d/1Trr0rfbubPi-0S3v1IDP5YRWhXuotB8Etjx_sm5WTjQ/edit?tab=t.0
		loanEligibilityStatus := (res.GetPdCategory() == lendabilityPb.PDCategory_PD_CATEGORY_LOW) ||
			(res.GetPdCategory() == lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM && res.GetLoanAffinityCategory() == lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_HIGH)
		productToEligibilityStatusMap := map[string]interface{}{
			productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT.String(): res.GetScreenerCheckResult(),
			productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS.String():  loanEligibilityStatus,
		}
		productsToPitchStrings := make([]string, 0)
		for _, product := range res.GetProductsToPitch() {
			productsToPitchStrings = append(productsToPitchStrings, product.String())
		}
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), crossAttachEvents.NewProductsToPitchEvent(actorId, userAttributesMap, productToEligibilityStatusMap, productsToPitchStrings, upsellAfter, cohort))
	})

	if err = s.userAttributesDao.SetPrioritizedProductsToPitch(ctx, actorId, res.GetProductsToPitch()); err != nil {
		logger.Error(ctx, "error setting prioritized products to pitch", zap.Error(err))
		return nil, err
	}

	return res.GetProductsToPitch(), nil
}
