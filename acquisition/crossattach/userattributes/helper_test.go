package userattributes_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes"
	"github.com/epifi/gamma/api/acquisition/crossattach"
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
)

func TestGetUserAttribute(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *userattributes.GetUserAttributeRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMocks     func(args args, mc *mockClients)
		expectedResult *userattributes.GetUserAttributeResponse
		expectedError  error
	}{
		{
			name: "invalid arguments",
			args: args{
				ctx: context.Background(),
				req: &userattributes.GetUserAttributeRequest{
					ActorId:       "",
					UserAttribute: crossattach.UserAttribute_USER_ATTRIBUTE_UNSPECIFIED,
				},
			},
			setupMocks:     func(args args, mc *mockClients) {},
			expectedResult: nil,
			expectedError:  errors.New("invalid user attribute"),
		},
		{
			name: "fetch from actual source due to error on getting user attribute from cache",
			args: args{
				ctx: context.Background(),
				req: &userattributes.GetUserAttributeRequest{
					ActorId:       "test-actor",
					UserAttribute: crossattach.UserAttribute_USER_ATTRIBUTE_PD_CATEGORY,
				},
			},
			setupMocks: func(args args, mc *mockClients) {
				mc.userAttributesDao.EXPECT().GetUserAttribute(gomock.Any(), args.req.GetActorId(), args.req.GetUserAttribute()).Return(nil, epifierrors.ErrRecordNotFound)
				attributeGetter := mc.attributeGetterMap[args.req.GetUserAttribute()]
				attributeGetter.EXPECT().GetUserAttributesFromSource(gomock.Any(), &userattributes.GetUserAttributesFromSourceRequest{
					ActorId:       args.req.GetActorId(),
					UserAttribute: args.req.GetUserAttribute(),
				}).Return(&userattributes.GetUserAttributesFromSourceResponse{
					UserAttributeValueMap: map[crossattach.UserAttribute]*crossattach.UserAttributeValue{
						crossattach.UserAttribute_USER_ATTRIBUTE_PD_CATEGORY: {
							Value: &crossattach.UserAttributeValue_PdCategory{
								PdCategory: lendabilityPb.PDCategory_PD_CATEGORY_HIGH,
							},
						},
					},
				}, nil)
				mc.userAttributesDao.EXPECT().SetUserAttributes(gomock.Any(), args.req.GetActorId(), map[crossattach.UserAttribute]*crossattach.UserAttributeValue{
					crossattach.UserAttribute_USER_ATTRIBUTE_PD_CATEGORY: {
						Value: &crossattach.UserAttributeValue_PdCategory{
							PdCategory: lendabilityPb.PDCategory_PD_CATEGORY_HIGH,
						},
					},
				}).Return(nil)
			},
			expectedResult: &userattributes.GetUserAttributeResponse{
				UserAttributeValue: &crossattach.UserAttributeValue{
					Value: &crossattach.UserAttributeValue_PdCategory{
						PdCategory: lendabilityPb.PDCategory_PD_CATEGORY_HIGH,
					},
				},
			},
			expectedError: nil,
		},
		{
			name: "successful get user attribute from cache",
			args: args{
				ctx: context.Background(),
				req: &userattributes.GetUserAttributeRequest{
					ActorId:       "test-actor",
					UserAttribute: crossattach.UserAttribute_USER_ATTRIBUTE_PD_CATEGORY,
				},
			},
			setupMocks: func(args args, mc *mockClients) {
				mc.userAttributesDao.EXPECT().GetUserAttribute(gomock.Any(), args.req.GetActorId(), args.req.GetUserAttribute()).Return(&crossattach.UserAttributeValue{
					Value: &crossattach.UserAttributeValue_PdCategory{
						PdCategory: lendabilityPb.PDCategory_PD_CATEGORY_HIGH,
					},
				}, nil)
			},
			expectedResult: &userattributes.GetUserAttributeResponse{
				UserAttributeValue: &crossattach.UserAttributeValue{
					Value: &crossattach.UserAttributeValue_PdCategory{
						PdCategory: lendabilityPb.PDCategory_PD_CATEGORY_HIGH,
					},
				},
			},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			u, mocks := getUserAttributesHelperImpl(t)
			tt.setupMocks(tt.args, mocks)
			result, err := u.GetUserAttribute(tt.args.ctx, tt.args.req)
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}
