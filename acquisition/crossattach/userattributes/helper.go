package userattributes

import (
	"context"

	"github.com/google/wire"

	"github.com/epifi/gamma/acquisition/crossattach/userattributes/dao"
	"github.com/epifi/gamma/api/acquisition/crossattach"
)

var UserAttributesHelperWireSet = wire.NewSet(dao.UserAttributesWireSet,
	NewUserAttributesHelperImpl, wire.Bind(new(UserAttributesHelper), new(*UserAttributesHelperImpl)),
	NewLendabilityDetailsGetter,
	NewNetWorthAssetValuesGetter,
	NewOnboardingDetailsGetter,
	NewScreenerCheckResultGetter,
	NewLatestAssetConnectedTimestampGetter)

//go:generate mockgen -source=helper.go -destination=./mocks/helper_mocks.go -package=mocks
type UserAttributesHelper interface {
	GetUserAttribute(context.Context, *GetUserAttributeRequest) (*GetUserAttributeResponse, error)
}

type GetUserAttributeRequest struct {
	ActorId       string
	UserAttribute crossattach.UserAttribute
	ForceRefresh  bool
}

type GetUserAttributeResponse struct {
	UserAttributeValue *crossattach.UserAttributeValue
}

func (r *GetUserAttributeRequest) GetActorId() string {
	if r != nil {
		return r.ActorId
	}
	return ""
}

func (r *GetUserAttributeRequest) GetForceRefresh() bool {
	if r != nil {
		return r.ForceRefresh
	}
	return false
}

func (r *GetUserAttributeRequest) GetUserAttribute() crossattach.UserAttribute {
	if r != nil {
		return r.UserAttribute
	}
	return crossattach.UserAttribute_USER_ATTRIBUTE_UNSPECIFIED
}

func (r *GetUserAttributeResponse) GetUserAttributeValue() *crossattach.UserAttributeValue {
	if r != nil {
		return r.UserAttributeValue
	}
	return nil
}
