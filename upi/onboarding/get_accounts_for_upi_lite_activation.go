package onboarding

import (
	"context"
	"errors"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	accountsPb "github.com/epifi/gamma/api/accounts"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	upiPkg "github.com/epifi/gamma/pkg/upi"
	"github.com/epifi/gamma/upi/dao"
)

// GetAccountsForUpiLiteActivation - fetches the lis of all the bank accounts that are eligible for upi lite activation
// Eligibility of the bank account is determined by the response received from list account provider RPC
// nolint:dupl
func (s *Service) GetAccountsForUpiLiteActivation(ctx context.Context, req *upiOnbPb.GetAccountsForUpiLiteActivationRequest) (*upiOnbPb.GetAccountsForUpiLiteActivationResponse, error) {
	var (
		res              = &upiOnbPb.GetAccountsForUpiLiteActivationResponse{}
		eligibleAccounts []*upiOnbPb.UpiAccount
	)

	upiAccounts, err := s.upiAccountDao.GetByActorId(ctx, req.GetActorId(), []storagev2.FilterOption{
		dao.WithUpiAccountStatusFilter([]upiOnbEnumsPb.UpiAccountStatus{upiOnbEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE}),
	}...)
	switch {
	case err != nil && errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no upi accounts present for given ActorId and given filter option", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpcPb.StatusRecordNotFound()
		return res, nil
	case err != nil && errors.Is(err, epifierrors.ErrInvalidArgument):
		logger.Error(ctx, "invalidArgument passed", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpcPb.StatusInvalidArgument()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching upi accounts", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	listAccPvdRes, err := s.ListAccountProviders(ctx, &upiOnbPb.ListAccountProvidersRequest{
		ActorId:        req.GetActorId(),
		UpiAccountType: upiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_UPI_LITE,
	})
	if err = epifigrpc.RPCError(listAccPvdRes, err); err != nil {
		logger.Error(ctx, "failed to get list account providers response for account type upi lite",
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpcPb.StatusFromError(err)
		return nil, err
	}

	for _, upiAcc := range upiAccounts {
		if upiAcc.GetAccountType() == accountsPb.Type_UPI_LITE {
			logger.Error(ctx, "active upi lite account already exists for actor",
				zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			res.Status = rpcPb.StatusInvalidArgument()
			return nil, err
		}

		isUpiLiteEnabled, procErr := upiPkg.IsUpiLiteEnabledForBank(upiAcc.GetIfscCode(), listAccPvdRes.GetBankInfos())
		if procErr != nil {
			logger.Error(ctx, "error checking upi lite enabled for given bank or not",
				zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.IFSC_CODE, upiAcc.GetIfscCode()), zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return nil, err
		}
		if isUpiLiteEnabled {
			eligibleAccounts = append(eligibleAccounts, upiAcc)
		}
	}

	res.Accounts = eligibleAccounts
	res.Status = rpcPb.StatusOk()
	return res, nil
}
