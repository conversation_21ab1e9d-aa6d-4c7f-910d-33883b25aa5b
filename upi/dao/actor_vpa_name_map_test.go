package dao_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/proto"

	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	pkgTest2 "github.com/epifi/be-common/pkg/test/v2"
)

func TestActorVpaNameMapDaoCrdb_Create(t *testing.T) {
	tests := []struct {
		name    string
		req     *upiOnboardingPb.ActorVpaNameMap
		want    *upiOnboardingPb.ActorVpaNameMap
		wantErr bool
	}{
		{
			name: "created entry successfully",
			req: &upiOnboardingPb.ActorVpaNameMap{
				ActorId: "actor-3",
				VpaName: "vpaName",
			},
			want: &upiOnboardingPb.ActorVpaNameMap{
				ActorId: "actor-3",
				VpaName: "vpaName",
			},
			wantErr: false,
		},
		{
			name: "error creating actor vpa name map due to unique constraint failure on vpa",
			req: &upiOnboardingPb.ActorVpaNameMap{
				ActorId: "actor-3",
				VpaName: "abcd",
			},
			wantErr: true,
		},
		{
			name: "error creating actor vpa name map due to unique constraint failure on actor id",
			req: &upiOnboardingPb.ActorVpaNameMap{
				ActorId: "actor-1",
				VpaName: "random",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			// Clean database, run migrations and load fixtures
			pkgTest2.PrepareRandomScopedCrdbDatabase(t, uts.conf.EpifiDb, uts.dbName, uts.db, upiAffectedTestTables, &initialiseSameDbOnce)

			err := uts.actorVpaNameMapDao.Create(context.Background(), tt.req)
			if err != nil != tt.wantErr {
				t.Errorf("Create() gotErr: %v wantErr: %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				got, _ := uts.actorVpaNameMapDao.GetByVpaName(context.Background(), tt.req.GetVpaName())
				if !assertActorVpaNameMap(got, tt.want) {
					t.Errorf("Create got :%v want :%v", got, tt.want)
				}
			}
		})
	}
}

func TestActorVpaNameMapDaoCrdb_GetByActorId(t *testing.T) {
	tests := []struct {
		name    string
		req     string
		want    *upiOnboardingPb.ActorVpaNameMap
		wantErr bool
	}{
		{
			name: "got actor vpa name successfully",
			req:  "actor-1",
			want: &upiOnboardingPb.ActorVpaNameMap{
				ActorId: "actor-1",
				VpaName: "abcd",
			},
		},
		{
			name:    "record not found for the given actor id",
			req:     "actor-random",
			wantErr: true,
		},
		{
			name:    "empty actor id passed",
			req:     "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			// Clean database, run migrations and load fixtures
			pkgTest2.PrepareRandomScopedCrdbDatabase(t, uts.conf.EpifiDb, uts.dbName, uts.db, upiAffectedTestTables, &initialiseSameDbOnce)

			got, err := uts.actorVpaNameMapDao.GetByActorId(context.Background(), tt.req)
			if err != nil != tt.wantErr {
				t.Errorf("GetByActorId() gotErr: %v wantErr: %v", err, tt.wantErr)
				return
			}
			if !assertActorVpaNameMap(got, tt.want) {
				t.Errorf("GetByActorId got :%v want :%v", got, tt.want)
			}
		})
	}
}

func TestActorVpaNameMapDaoCrdb_GetByVpaName(t *testing.T) {
	tests := []struct {
		name    string
		req     string
		want    *upiOnboardingPb.ActorVpaNameMap
		wantErr bool
	}{
		{
			name: "got actor vpa name successfully",
			req:  "abcd",
			want: &upiOnboardingPb.ActorVpaNameMap{
				ActorId: "actor-1",
				VpaName: "abcd",
			},
		},
		{
			name:    "record not found for the given vpa name",
			req:     "random",
			wantErr: true,
		},
		{
			name:    "empty vpa name passed",
			req:     "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			// Clean database, run migrations and load fixtures
			pkgTest2.PrepareRandomScopedCrdbDatabase(t, uts.conf.EpifiDb, uts.dbName, uts.db, upiAffectedTestTables, &initialiseSameDbOnce)

			got, err := uts.actorVpaNameMapDao.GetByVpaName(context.Background(), tt.req)
			if err != nil != tt.wantErr {
				t.Errorf("GetByVpaName() gotErr: %v wantErr: %v", err, tt.wantErr)
				return
			}
			if !assertActorVpaNameMap(got, tt.want) {
				t.Errorf("GetByVpaName got :%v want :%v", got, tt.want)
			}
		})
	}
}

func TestActorVpaNameMapDaoCrdb_UpdateVpaNameForActor(t *testing.T) {
	tests := []struct {
		name    string
		vpaName string
		actorId string
		wantErr bool
	}{
		{
			name:    "update vpa name successfully",
			actorId: "actor-1",
			vpaName: "vpa_name",
		},
		{
			name:    "actor not present",
			actorId: "actor-random",
			vpaName: "vpa_name",
			wantErr: true,
		},
		{
			name:    "vpa name already in use for other actor",
			actorId: "actor-1",
			vpaName: "xyz",
			wantErr: true,
		},
		{
			name:    "vpa name cannot be empty",
			actorId: "actor-1",
			wantErr: true,
		},
		{
			name:    "actor name cannot be emoty",
			vpaName: "abcd",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			// Clean database, run migrations and load fixtures
			pkgTest2.PrepareRandomScopedCrdbDatabase(t, uts.conf.EpifiDb, uts.dbName, uts.db, upiAffectedTestTables, &initialiseSameDbOnce)

			err := uts.actorVpaNameMapDao.UpdateVpaNameForActor(context.Background(), tt.vpaName, tt.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateVpaNameForActor() gotErr :%v, wnatErr: %v", err, tt.wantErr)
				return
			}
			if err == nil {
				res, err := uts.actorVpaNameMapDao.GetByActorId(context.Background(), tt.actorId)
				if err != nil {
					t.Errorf("GetByActorId() returned error expecting nil :%v", err)
					return
				}
				if res.GetVpaName() != tt.vpaName {
					t.Errorf("UpdateVpaNameForActor() vpa name not updated, got: %s want: %s", res.GetVpaName(), tt.vpaName)
					return
				}
			}
		})
	}
}

func assertActorVpaNameMap(got, want *upiOnboardingPb.ActorVpaNameMap) bool {
	if (got != nil) != (want != nil) {
		return false
	}
	if got == nil {
		return true
	}
	want.CreatedAt = got.CreatedAt
	want.UpdatedAt = got.UpdatedAt
	want.DeletedAt = got.DeletedAt
	want.Id = got.Id
	return proto.Equal(got, want)
}
