package upi_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/idgen/mocks"
	"github.com/epifi/gamma/upi/helper/upi"
)

func TestProcessor_GetSuggestedVpaNameForActorName(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockNumIdGen := mocks.NewMockNumberIDGenerator(ctr)
	helperSvc := upi.NewProcessor(nil, nil, nil, conf, mockNumIdGen)

	type mockGenId struct {
		suffixLength int
		generatedNum int64
	}

	tests := []struct {
		name        string
		displayName *commontypes.Name
		mockGenId   []mockGenId
		vpaNames    []string
		wantErr     error
	}{
		{
			name: "got vpa names successfully for full name length less then vpa min name length",
			displayName: &commontypes.Name{
				FirstName:  "a",
				MiddleName: "b",
				LastName:   "c",
			},
			mockGenId: []mockGenId{
				{
					suffixLength: 3,
					generatedNum: 123,
				},
				{
					suffixLength: 3,
					generatedNum: 234,
				},
				{
					suffixLength: 3,
					generatedNum: 345,
				},
				{
					suffixLength: 3,
					generatedNum: 456,
				},
				{
					suffixLength: 3,
					generatedNum: 456,
				},
				{
					suffixLength: 3,
					generatedNum: 567,
				},
			},
			vpaNames: []string{
				"abc123",
				"abc234",
				"abc345",
				"abc456",
				"abc567",
			},
		},
		{
			name: "first name + last name length less then vpa min name length",
			displayName: &commontypes.Name{
				FirstName:  "a",
				MiddleName: "bcd",
				LastName:   "e",
			},
			mockGenId: []mockGenId{
				{
					suffixLength: 2,
					generatedNum: 12,
				},
				{
					suffixLength: 2,
					generatedNum: 23,
				},
				{
					suffixLength: 2,
					generatedNum: 34,
				},
				{
					suffixLength: 2,
					generatedNum: 45,
				},
				{
					suffixLength: 2,
					generatedNum: 56,
				},
				{
					suffixLength: 3,
					generatedNum: 123,
				},
				{
					suffixLength: 3,
					generatedNum: 234,
				},
				{
					suffixLength: 3,
					generatedNum: 345,
				},
				{
					suffixLength: 3,
					generatedNum: 456,
				},
				{
					suffixLength: 3,
					generatedNum: 567,
				},
			},
			vpaNames: []string{
				"abcde",
				"eabcd",
				"abcde12",
				"abcde23",
				"abcde34",
				"abcde45",
				"abcde56",
				"abcde123",
				"abcde234",
				"abcde345",
				"abcde456",
				"abcde567",
			},
		},
		{
			name: "last name is empty",
			displayName: &commontypes.Name{
				FirstName:  "a",
				MiddleName: "bcd",
			},
			mockGenId: []mockGenId{
				{
					suffixLength: 2,
					generatedNum: 12,
				},
				{
					suffixLength: 2,
					generatedNum: 23,
				},
				{
					suffixLength: 2,
					generatedNum: 34,
				},
				{
					suffixLength: 2,
					generatedNum: 45,
				},
				{
					suffixLength: 2,
					generatedNum: 56,
				},
				{
					suffixLength: 3,
					generatedNum: 123,
				},
				{
					suffixLength: 3,
					generatedNum: 234,
				},
				{
					suffixLength: 3,
					generatedNum: 345,
				},
				{
					suffixLength: 3,
					generatedNum: 456,
				},
				{
					suffixLength: 3,
					generatedNum: 567,
				},
			},
			vpaNames: []string{
				"abcd",
				"abcd12",
				"abcd23",
				"abcd34",
				"abcd45",
				"abcd56",
				"abcd123",
				"abcd234",
				"abcd345",
				"abcd456",
				"abcd567",
			},
		},
		{
			name: "first name + last name length is not less then vpa min length",
			displayName: &commontypes.Name{
				FirstName:  "ab",
				MiddleName: "ef",
				LastName:   "cd",
			},
			mockGenId: []mockGenId{
				{
					suffixLength: 2,
					generatedNum: 12,
				},
				{
					suffixLength: 2,
					generatedNum: 23,
				},
				{
					suffixLength: 2,
					generatedNum: 34,
				},
				{
					suffixLength: 2,
					generatedNum: 45,
				},
				{
					suffixLength: 2,
					generatedNum: 56,
				},
				{
					suffixLength: 3,
					generatedNum: 123,
				},
				{
					suffixLength: 3,
					generatedNum: 234,
				},
				{
					suffixLength: 3,
					generatedNum: 345,
				},
				{
					suffixLength: 3,
					generatedNum: 456,
				},
				{
					suffixLength: 3,
					generatedNum: 567,
				},
			},
			vpaNames: []string{
				"abcd",
				"cdab",
				"abcd12",
				"abcd23",
				"abcd34",
				"abcd45",
				"abcd56",
				"abcd123",
				"abcd234",
				"abcd345",
				"abcd456",
				"abcd567",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for _, mock := range tt.mockGenId {
				mockNumIdGen.EXPECT().GenID(mock.suffixLength).Return(mock.generatedNum)
			}

			got := helperSvc.GetSuggestedVpaNameForActorName(context.Background(), tt.displayName)
			if !reflect.DeepEqual(got, tt.vpaNames) {
				t.Errorf("GetSuggestedVpaNameForActorName() got :%v want :%v", got, tt.vpaNames)
			}
		})
	}
}
