//nolint:dupl
package incidentprocessor

import (
	"context"
	"errors"
	"fmt"

	errors2 "github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/actor"
	commsPb "github.com/epifi/gamma/api/comms"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	p2pIncidentManagerPb "github.com/epifi/gamma/api/p2pinvestment/incidentmanager"
	p2pServerConfig "github.com/epifi/gamma/p2pinvestment/config/genconf"
	"github.com/epifi/gamma/p2pinvestment/dao"
	"github.com/epifi/gamma/p2pinvestment/watsonincidentmanager/incidentmanager"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/p2pinvestment"
)

type InvestmentIncidentProcessor struct {
	config                *p2pServerConfig.Config
	watsonClient          watsonPb.WatsonClient
	baseIncidentProcessor *BaseIncidentProcessor
	actorClient           actor.ActorClient
	schemeDao             dao.SchemeDao
}

func NewInvestmentIncidentProcessor(
	config *p2pServerConfig.Config,
	watsonClient watsonPb.WatsonClient,
	baseIncidentProcessor *BaseIncidentProcessor,
	actorClient actor.ActorClient,
	schemeDao dao.SchemeDao,
) *InvestmentIncidentProcessor {
	return &InvestmentIncidentProcessor{
		config:                config,
		watsonClient:          watsonClient,
		baseIncidentProcessor: baseIncidentProcessor,
		actorClient:           actorClient,
		schemeDao:             schemeDao,
	}
}

// GetClientReqId helps to get the client req id
func (p *InvestmentIncidentProcessor) GetClientReqId(ctx context.Context, transactionId string, incidentType p2pIncidentManagerPb.IncidentType) string {
	return p.baseIncidentProcessor.getClientReqId(transactionId, incidentType)
}

// GetCommsDetails is used to fetch custom comms to be sent to the user at various stages of ticket processing
func (p *InvestmentIncidentProcessor) GetCommsDetails(ctx context.Context, req *incidentmanager.GetCommsDetailsReq) (*incidentmanager.GetCommsDetailsRes, error) {
	notificationComms, err := p.getNotificationComms(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("unable to fetch notification comms for %s, err %w", req.CommsType.String(), err)
	}
	res := &incidentmanager.GetCommsDetailsRes{
		CommsDetails: notificationComms,
	}
	return res, nil
}

// GetTicketDetails helps in getting the ticket details of the incident
func (p *InvestmentIncidentProcessor) GetTicketDetails(ctx context.Context, req *incidentmanager.GetTicketDetailsReq) (*incidentmanager.GetTicketDetailsRes, error) {
	txn, err := p.baseIncidentProcessor.getTransaction(ctx, req.ClientReqId)
	if err != nil {
		logger.Error(ctx, "unable to fetch transaction", zap.Error(err))
		return nil, err
	}

	return &incidentmanager.GetTicketDetailsRes{
		TicketDetails: &watsonPb.TicketDetails{
			CustomTicketFields: &ticketPb.CustomFields{
				Utr:             txn.GetPaymentRefId(),
				TransactionDate: txn.GetCreatedAt(),
			},
			Group:    ticketPb.Group_GROUP_L1_SUPPORT,
			Priority: ticketPb.Priority_PRIORITY_MEDIUM,
		},
	}, nil
}

// IsIncidentValid used to check that the incident is still valid or not
func (p *InvestmentIncidentProcessor) IsIncidentValid(ctx context.Context, req *incidentmanager.IsIncidentValidReq) (bool, error) {
	return true, nil
}

// getNotificationComms is used to fetch custom notification comms to be sent to the user at various stages of ticket processing
func (p *InvestmentIncidentProcessor) getNotificationComms(ctx context.Context, req *incidentmanager.GetCommsDetailsReq) ([]*watsonPb.CommsDetail, error) {
	// fetch transaction
	transaction, getTransactionErr := p.baseIncidentProcessor.getTransaction(ctx, req.Incident.GetClientRequestId())
	if getTransactionErr != nil {
		return nil, errors2.Wrap(getTransactionErr, "unable to get the transaction")
	}

	// fetching the investor name, but not sending back if investor name is not there
	investorName, getInvestorErr := p.baseIncidentProcessor.getInvestorName(ctx, transaction.GetInvestorId())
	if getInvestorErr != nil {
		logger.Error(ctx, "unable to get the investor name", zap.Error(getInvestorErr))
	}

	// fetch scheme corresponding to the transaction
	scheme, getSchemeErr := p.schemeDao.GetById(ctx, transaction.GetSchemeId())
	if getSchemeErr != nil {
		return nil, errors2.Wrap(getSchemeErr, "unable to get the scheme")
	}

	// fetch the correct display name
	displayScheme := p2pinvestment.BeSchemeToDisplayScheme[scheme.GetName()]
	schemeString := p2pinvestment.GetSchemeNameString(displayScheme, true)

	var emailOption *commsPb.EmailOption

	switch req.CommsType {
	case watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION:
		emailOption = p.getDelayedEmailOption(req, investorName, schemeString, transaction.GetComputedTransactionAmount())
	case watsonPb.CommsType_COMMS_TYPE_INCIDENT_RESOLUTION:
		if transaction.GetStatus() == p2pPb.InvestmentTransactionStatus_FAILED {
			emailOption = p.getFailedEmailOption(req, investorName, schemeString, transaction.GetComputedTransactionAmount())
		} else if transaction.GetStatus() == p2pPb.InvestmentTransactionStatus_SUCCESS {
			emailOption = p.getSuccessEmailOption(req, investorName, schemeString, transaction.GetComputedTransactionAmount())
		}
	default:
		return nil, errors.New("unknown commsType")
	}

	var res []*watsonPb.CommsDetail
	res = append(res, &watsonPb.CommsDetail{
		Detail: &watsonPb.CommsDetail_Email{
			Email: &commsPb.EmailMessage{
				EmailOption: emailOption,
			},
		},
	})

	return res, nil
}

// getDelayedEmailOption is used to fetch email option for investment delayed
func (p *InvestmentIncidentProcessor) getDelayedEmailOption(req *incidentmanager.GetCommsDetailsReq, investorName string, planName string, amount int64) *commsPb.EmailOption {
	emailOption := &commsPb.EmailOption{
		Option: &commsPb.EmailOption_JumpInvestmentDelayedEmailOption{
			JumpInvestmentDelayedEmailOption: &commsPb.JumpInvestmentDelayedEmailOption{
				EmailType: commsPb.EmailType_JUMP_INVESTMENT_DELAYED,
				Option: &commsPb.JumpInvestmentDelayedEmailOption_JumpInvestmentDelayedEmailV1{
					JumpInvestmentDelayedEmailV1: &commsPb.JumpInvestmentDelayedEmailV1{
						TickedId:        fmt.Sprintf("%d", req.CommsMetadata.GetTicketId()),
						InvestorName:    investorName,
						PlanName:        planName,
						TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
						Amount:          fmt.Sprintf("%d", amount),
					},
				},
			},
		},
	}
	return emailOption
}

// getSuccessEmailOption is used to fetch email option for investment success
func (p *InvestmentIncidentProcessor) getSuccessEmailOption(req *incidentmanager.GetCommsDetailsReq, investorName string, planName string, amount int64) *commsPb.EmailOption {
	emailOption := &commsPb.EmailOption{
		Option: &commsPb.EmailOption_JumpInvestmentSuccessEmailOption{
			JumpInvestmentSuccessEmailOption: &commsPb.JumpInvestmentSuccessEmailOption{
				EmailType: commsPb.EmailType_JUMP_INVESTMENT_SUCCESS,
				Option: &commsPb.JumpInvestmentSuccessEmailOption_JumpInvestmentSuccessEmailV1{
					JumpInvestmentSuccessEmailV1: &commsPb.JumpInvestmentSuccessEmailV1{
						TickedId:        fmt.Sprintf("%d", req.CommsMetadata.GetTicketId()),
						InvestorName:    investorName,
						PlanName:        planName,
						TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
						Amount:          fmt.Sprintf("%d", amount),
					},
				},
			},
		},
	}
	return emailOption
}

// getFailedEmailOption is used to fetch email option for investment failed
func (p *InvestmentIncidentProcessor) getFailedEmailOption(req *incidentmanager.GetCommsDetailsReq, investorName string, planName string, amount int64) *commsPb.EmailOption {
	emailOption := &commsPb.EmailOption{
		Option: &commsPb.EmailOption_JumpInvestmentFailedEmailOption{
			JumpInvestmentFailedEmailOption: &commsPb.JumpInvestmentFailedEmailOption{
				EmailType: commsPb.EmailType_JUMP_INVESTMENT_FAILED,
				Option: &commsPb.JumpInvestmentFailedEmailOption_JumpInvestmentFailedEmailV1{
					JumpInvestmentFailedEmailV1: &commsPb.JumpInvestmentFailedEmailV1{
						TickedId:        fmt.Sprintf("%d", req.CommsMetadata.GetTicketId()),
						InvestorName:    investorName,
						PlanName:        planName,
						TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
						Amount:          fmt.Sprintf("%d", amount),
					},
				},
			},
		},
	}
	return emailOption
}
