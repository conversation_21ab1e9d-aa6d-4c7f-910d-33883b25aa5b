//nolint:depguard
package dao

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/go-cmp/cmp"
	"github.com/jinzhu/copier"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"gorm.io/gorm"

	accountsPb "github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/deposit/config"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

type DepositTestSuite struct {
	db                *gorm.DB
	conf              *config.Config
	depositAccountDao DepositAccountDao
}

func NewDepositTestSuite(db *gorm.DB, conf *config.Config, depositAccountDao DepositAccountDao) *DepositTestSuite {
	return &DepositTestSuite{
		db:                db,
		conf:              conf,
		depositAccountDao: depositAccountDao,
	}
}

var (
	dpts *DepositTestSuite

	fixtureMoney = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        100,
	}
	FixtureDepositAccountSD1 = &depositPb.DepositAccount{
		Id:              "deposit-account-sd-1",
		ActorId:         fixtureDepositRequestCreateSD1.DepositInfo.ActorId,
		AccountNumber:   "**********",
		Name:            fixtureDepositRequestCreateSD1.DepositInfo.Name,
		Type:            fixtureDepositRequestCreateSD1.DepositInfo.Type,
		SchemeCode:      depositPb.DepositScheme_SMART_DEPOSIT,
		State:           depositPb.DepositState_CREATED,
		PrincipalAmount: fixtureDepositRequestCreateSD1.DepositInfo.Amount,
		RunningBalance:  fixtureMoney,
		MaturityAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        110,
		},
		Term: fixtureDepositRequestCreateSD1.DepositInfo.Term,
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate:           "10",
		RenewInfo:              fixtureDepositRequestCreateSD1.DepositInfo.RenewInfo,
		OperativeAccountNumber: fixtureDepositRequestCreateSD1.DepositInfo.OperativeAccountNumber,
		RepayAccountNumber:     fixtureDepositRequestCreateSD1.DepositInfo.RepayAccountNumber,
		PartnerBank:            fixtureDepositRequestCreateSD1.DepositInfo.Vendor,
		IfscCode:               "FDRL0001001",
		NomineeDetails:         &depositPb.DepositNomineeDetails{},
		IsAddFundsAllowed:      true,
		ClosureInfo:            &depositPb.ClosureInfo{},
		Provenance:             depositPb.DepositAccountProvenance_USER_APP,
	}

	fixtureDepositAccountSD2 = &depositPb.DepositAccount{
		Id:              "deposit-account-sd-2",
		ActorId:         FixtureDepositRequestCreateSD2.DepositInfo.ActorId,
		AccountNumber:   "**********",
		Name:            FixtureDepositRequestCreateSD2.DepositInfo.Name,
		Type:            FixtureDepositRequestCreateSD2.DepositInfo.Type,
		SchemeCode:      depositPb.DepositScheme_SMART_DEPOSIT,
		State:           depositPb.DepositState_PRECLOSED,
		PrincipalAmount: FixtureDepositRequestCreateSD2.DepositInfo.Amount,
		RunningBalance:  fixtureMoney,
		MaturityAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        110,
		},
		Term: FixtureDepositRequestCreateSD2.DepositInfo.Term,
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate:           "10",
		RenewInfo:              FixtureDepositRequestCreateSD2.DepositInfo.RenewInfo,
		OperativeAccountNumber: FixtureDepositRequestCreateSD2.DepositInfo.OperativeAccountNumber,
		RepayAccountNumber:     FixtureDepositRequestCreateSD2.DepositInfo.RepayAccountNumber,
		PartnerBank:            FixtureDepositRequestCreateSD2.DepositInfo.Vendor,
		IfscCode:               "FDRL0001001",
		NomineeDetails:         &depositPb.DepositNomineeDetails{},
		IsAddFundsAllowed:      true,
		ClosureInfo:            &depositPb.ClosureInfo{},
		Provenance:             depositPb.DepositAccountProvenance_USER_APP,
	}

	fixtureDepositAccountSD3 = &depositPb.DepositAccount{
		Id:              "deposit-account-sd-3",
		ActorId:         FixtureDepositRequestCreateSD3.DepositInfo.ActorId,
		AccountNumber:   "**********",
		Name:            FixtureDepositRequestCreateSD3.DepositInfo.Name,
		Type:            FixtureDepositRequestCreateSD3.DepositInfo.Type,
		SchemeCode:      depositPb.DepositScheme_SMART_DEPOSIT,
		State:           depositPb.DepositState_CREATED,
		PrincipalAmount: FixtureDepositRequestCreateSD3.DepositInfo.Amount,
		RunningBalance:  fixtureMoney,
		MaturityAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        110,
		},
		Term: FixtureDepositRequestCreateSD3.DepositInfo.Term,
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate:           "10",
		RenewInfo:              FixtureDepositRequestCreateSD3.DepositInfo.RenewInfo,
		OperativeAccountNumber: FixtureDepositRequestCreateSD3.DepositInfo.OperativeAccountNumber,
		RepayAccountNumber:     FixtureDepositRequestCreateSD3.DepositInfo.RepayAccountNumber,
		PartnerBank:            FixtureDepositRequestCreateSD3.DepositInfo.Vendor,
		IfscCode:               "FDRL0001001",
		NomineeDetails:         &depositPb.DepositNomineeDetails{},
		IsAddFundsAllowed:      true,
		ClosureInfo:            &depositPb.ClosureInfo{},
		Provenance:             depositPb.DepositAccountProvenance_REWARDS_APP,
	}

	fixtureDepositAccountFD1 = &depositPb.DepositAccount{
		Id:              "deposit-account-fd-1",
		ActorId:         FixtureDepositRequestCreateFD1.DepositInfo.ActorId,
		AccountNumber:   "**********",
		Name:            FixtureDepositRequestCreateFD1.DepositInfo.Name,
		Type:            FixtureDepositRequestCreateFD1.DepositInfo.Type,
		SchemeCode:      depositPb.DepositScheme_FD_CASH_CERTIFICATE,
		State:           depositPb.DepositState_CREATED,
		PrincipalAmount: FixtureDepositRequestCreateFD1.DepositInfo.Amount,
		RunningBalance:  fixtureMoney,
		MaturityAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        107,
		},
		Term: FixtureDepositRequestCreateFD1.DepositInfo.Term,
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate:           "7",
		RenewInfo:              FixtureDepositRequestCreateFD1.DepositInfo.RenewInfo,
		OperativeAccountNumber: FixtureDepositRequestCreateFD1.DepositInfo.OperativeAccountNumber,
		RepayAccountNumber:     FixtureDepositRequestCreateFD1.DepositInfo.RepayAccountNumber,
		PartnerBank:            FixtureDepositRequestCreateFD1.DepositInfo.Vendor,
		IfscCode:               "FDRL0001001",
		NomineeDetails:         &depositPb.DepositNomineeDetails{},
		IsAddFundsAllowed:      true,
		ClosureInfo:            &depositPb.ClosureInfo{},
		Provenance:             depositPb.DepositAccountProvenance_USER_APP,
	}
)

func TestDepositAccountDaoCrdb_Create(t *testing.T) {
	testCreateDeposit := &depositPb.DepositAccount{
		ActorId:         FixtureDepositAccountSD1.ActorId,
		AccountNumber:   "**********",
		Name:            "Car",
		Type:            accountsPb.Type_FIXED_DEPOSIT,
		SchemeCode:      depositPb.DepositScheme_FD_CASH_CERTIFICATE,
		State:           depositPb.DepositState_CREATED,
		PrincipalAmount: fixtureMoney,
		RunningBalance:  fixtureMoney,
		MaturityAmount:  fixtureMoney,
		Term: &types.DepositTerm{
			Months: 12,
			Days:   21,
		},
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate: "7",
		RenewInfo: &depositPb.RenewInfo{
			IsAutoRenewable: true,
			Option:          depositPb.RenewOption_MATURITY_ONLY,
		},
		OperativeAccountNumber: "*********0",
		RepayAccountNumber:     "*********0",
		PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
		IfscCode:               FixtureDepositAccountSD1.IfscCode,
		Provenance:             depositPb.DepositAccountProvenance_USER_APP,
	}
	type args struct {
		ctx     context.Context
		account *depositPb.DepositAccount
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.DepositAccount
		wantErr bool
	}{
		{
			name: "Should successfully create a deposit account",
			args: args{
				ctx:     context.Background(),
				account: testCreateDeposit,
			},
			want:    testCreateDeposit,
			wantErr: false,
		},
		{
			name: "Should successfully return an already existing account and not create a new one",
			args: args{
				ctx:     context.Background(),
				account: FixtureDepositAccountSD1,
			},
			want:    FixtureDepositAccountSD1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

			got, err := dpts.depositAccountDao.Create(tt.args.ctx, tt.args.account)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				tt.want.Id = got.Id
			}
			if diff := isDepositAccountDeepEqual(got, tt.want); diff != "" {
				t.Errorf("Create() error = %v, wantErr %v, diff %v", err, tt.wantErr, diff)
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetByAccountNumberAndIfsc(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

	type args struct {
		ctx           context.Context
		accountNumber string
		ifscCode      string
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.DepositAccount
		wantErr bool
		errType error
	}{
		{
			name: "Should successfully fetch deposit account",
			args: args{
				ctx:           context.Background(),
				accountNumber: FixtureDepositAccountSD1.AccountNumber,
				ifscCode:      FixtureDepositAccountSD1.IfscCode,
			},
			want:    FixtureDepositAccountSD1,
			wantErr: false,
			errType: nil,
		},
		{
			name: "Should fail to fetch deposit account as no such account exists",
			args: args{
				ctx:           context.Background(),
				accountNumber: "*********",
				ifscCode:      "FED00000022",
			},
			want:    nil,
			wantErr: true,
			errType: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dpts.depositAccountDao.GetByAccountNumberAndIfsc(tt.args.ctx, tt.args.accountNumber, tt.args.ifscCode)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountNumberAndIfsc() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err != nil && !errors.Is(err, tt.errType) {
				t.Errorf("got: %v, want: %v", err, tt.errType)
			}

			if diff := isDepositAccountDeepEqual(got, tt.want); diff != "" {
				t.Errorf("GetByAccountNumberAndIfsc() error = %v, wantErr %v, diff %v", err, tt.wantErr, diff)
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetById(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

	type args struct {
		ctx              context.Context
		depositAccountId string
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.DepositAccount
		wantErr bool
		errType error
	}{
		{
			name: "Should successfully fetch deposit account",
			args: args{
				ctx:              context.Background(),
				depositAccountId: FixtureDepositAccountSD1.Id,
			},
			want:    FixtureDepositAccountSD1,
			wantErr: false,
			errType: nil,
		},
		{
			name: "Should fail to fetch deposit account as no such account exists",
			args: args{
				ctx:              context.Background(),
				depositAccountId: "random-account-id",
			},
			want:    nil,
			wantErr: true,
			errType: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dpts.depositAccountDao.GetById(tt.args.ctx, tt.args.depositAccountId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err != nil && !errors.Is(err, tt.errType) {
				t.Errorf("got: %v, want: %v", err, tt.errType)
			}

			if diff := isDepositAccountDeepEqual(got, tt.want); diff != "" {
				t.Errorf("GetById() error = %v, wantErr %v, diff %v", err, tt.wantErr, diff)
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetByIds(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

	type args struct {
		ctx        context.Context
		accountIds []string
		selectMask []depositPb.DepositAccountFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    []*depositPb.DepositAccount
		wantErr bool
	}{
		{
			name: "Should successfully fetch deposit account for 0 accounts",
			args: args{
				ctx:        context.Background(),
				accountIds: []string{},
			},
			want:    []*depositPb.DepositAccount{},
			wantErr: false,
		},
		{
			name: "Should successfully fetch deposit account for 1 account",
			args: args{
				ctx:        context.Background(),
				accountIds: []string{FixtureDepositAccountSD1.Id},
			},
			want:    []*depositPb.DepositAccount{FixtureDepositAccountSD1},
			wantErr: false,
		},
		{
			name: "Should successfully fetch deposit account for 4 accounts",
			args: args{
				ctx: context.Background(),
				accountIds: []string{fixtureDepositAccountFD1.Id, FixtureDepositAccountSD1.Id,
					fixtureDepositAccountSD2.Id, fixtureDepositAccountSD3.Id},
			},
			want: []*depositPb.DepositAccount{fixtureDepositAccountFD1, FixtureDepositAccountSD1,
				fixtureDepositAccountSD2, fixtureDepositAccountSD3},
			wantErr: false,
		},
		{
			name: "Should successfully fetch deposit account with id filter",
			args: args{
				ctx:        context.Background(),
				accountIds: []string{FixtureDepositAccountSD1.Id},
				selectMask: []depositPb.DepositAccountFieldMask{depositPb.DepositAccountFieldMask_ACCOUNT_ID},
			},
			want:    []*depositPb.DepositAccount{{Id: FixtureDepositAccountSD1.Id}},
			wantErr: false,
		},
		{
			name: "Should successfully fetch deposit accounts with id, name, interest_rate filter",
			args: args{
				ctx: context.Background(),
				accountIds: []string{fixtureDepositAccountFD1.Id, FixtureDepositAccountSD1.Id,
					fixtureDepositAccountSD2.Id},
				selectMask: []depositPb.DepositAccountFieldMask{depositPb.DepositAccountFieldMask_ACCOUNT_ID,
					depositPb.DepositAccountFieldMask_NAME, depositPb.DepositAccountFieldMask_INTEREST_RATE},
			},
			want: []*depositPb.DepositAccount{
				{
					Id:           fixtureDepositAccountFD1.Id,
					Name:         fixtureDepositAccountFD1.Name,
					InterestRate: fixtureDepositAccountFD1.InterestRate,
				},
				{
					Id:           FixtureDepositAccountSD1.Id,
					Name:         FixtureDepositAccountSD1.Name,
					InterestRate: FixtureDepositAccountSD1.InterestRate,
				},
				{
					Id:           fixtureDepositAccountSD2.Id,
					Name:         fixtureDepositAccountSD2.Name,
					InterestRate: fixtureDepositAccountSD2.InterestRate,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dpts.depositAccountDao.GetByIds(tt.args.ctx, tt.args.accountIds, tt.args.selectMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if len(got) != len(tt.want) {
				t.Errorf("GetByIds() error in length, got: %d, want: %d", len(got), len(tt.want))
				return
			}

			for i := range tt.want {
				if diff := isDepositAccountDeepEqual(got[i], tt.want[i]); diff != "" {
					t.Errorf("GetByIds() got = %v, want %v, diff %s", got[i], tt.want[i], diff)
				}
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetByActorIdAndTemplateIDs(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

	type args struct {
		ctx         context.Context
		actorId     string
		templateIds []string
	}

	tests := []struct {
		name    string
		args    *args
		want    []*depositPb.DepositAccount
		wantErr bool
	}{
		{
			name: "should successfully get all deposit accounts if templateIDs is empty array",
			args: &args{
				ctx:         context.Background(),
				actorId:     FixtureDepositAccountSD1.ActorId,
				templateIds: []string{},
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountFD1, fixtureDepositAccountSD3, fixtureDepositAccountSD2, FixtureDepositAccountSD1},
			wantErr: false,
		},
		{
			name: "should successfully get all deposit accounts if templateIDs is nil",
			args: &args{
				ctx:         context.Background(),
				actorId:     FixtureDepositAccountSD1.ActorId,
				templateIds: nil,
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountFD1, fixtureDepositAccountSD3, fixtureDepositAccountSD2, FixtureDepositAccountSD1},
			wantErr: false,
		},
		{
			name: "should successfully get empty array if there's no deposit account with the given template id",
			args: &args{
				ctx:         context.Background(),
				actorId:     FixtureDepositAccountSD1.ActorId,
				templateIds: []string{"SD3"},
			},
			want:    []*depositPb.DepositAccount{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dpts.depositAccountDao.GetByActorIdAndTemplateIDs(tt.args.ctx, tt.args.actorId, tt.args.templateIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdAndTemplateIDs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				return
			}

			if len(got) != len(tt.want) {
				t.Errorf("GetByActorIdAndTemplateIDs() error in length, got: %d, want: %d", len(got), len(tt.want))
				return
			}

			for i := range tt.want {
				if diff := isDepositAccountDeepEqual(got[i], tt.want[i]); diff != "" {
					t.Errorf("GetByActorIdAndTemplateIDs() got = %v, want %v, diff %s", got[i], tt.want[i], diff)
				}
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetDepositAccountIdsByActorId(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

	type args struct {
		ctx     context.Context
		actorId string
	}

	tests := []struct {
		name    string
		args    *args
		want    []string
		wantErr bool
	}{
		{
			name: "should successfully get all deposit account ids for given actor",
			args: &args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
			},
			want:    []string{"deposit-account-fd-1", "deposit-account-sd-3", "deposit-account-sd-2", "deposit-account-sd-1"},
			wantErr: false,
		},
		{
			name: "should successfully get empty array if there's no deposit account with the given actor",
			args: &args{
				ctx:     context.Background(),
				actorId: "actor-user-2",
			},
			want:    []string{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dpts.depositAccountDao.GetDepositAccountIdsByActorId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDepositAccountIdsByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				return
			}

			if len(got) != len(tt.want) {
				t.Errorf("GetDepositAccountIdsByActorId() error in length, got: %d, want: %d", len(got), len(tt.want))
				return
			}

			for i := range tt.want {
				if got[i] != tt.want[i] {
					t.Errorf("GetDepositAccountIdsByActorId() got = %v, want %v", got[i], tt.want[i])
				}
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetByActorIdAndStatesAndAccountTypes(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

	type args struct {
		ctx     context.Context
		actorId string
		states  []depositPb.DepositState
		types   []accountsPb.Type
	}

	tests := []struct {
		name    string
		args    *args
		want    []*depositPb.DepositAccount
		wantErr bool
	}{
		{
			name: "Should successfully get all deposit accounts",
			args: &args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
					depositPb.DepositState_PRECLOSED,
					depositPb.DepositState_CLOSED,
				},
				types: []accountsPb.Type{
					accountsPb.Type_SMART_DEPOSIT,
					accountsPb.Type_FIXED_DEPOSIT,
					accountsPb.Type_RECURRING_DEPOSIT,
				},
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountFD1, fixtureDepositAccountSD3, fixtureDepositAccountSD2, FixtureDepositAccountSD1},
			wantErr: false,
		},
		{
			name: "Should successfully get deposit accounts filtered by type",
			args: &args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
					depositPb.DepositState_PRECLOSED,
					depositPb.DepositState_CLOSED,
				},
				types: []accountsPb.Type{
					accountsPb.Type_SMART_DEPOSIT,
				},
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountSD3, fixtureDepositAccountSD2, FixtureDepositAccountSD1},
			wantErr: false,
		},
		{
			name: "Should successfully get deposit accounts filtered by states",
			args: &args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
				},
				types: []accountsPb.Type{
					accountsPb.Type_SMART_DEPOSIT,
					accountsPb.Type_FIXED_DEPOSIT,
					accountsPb.Type_RECURRING_DEPOSIT,
				},
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountFD1, fixtureDepositAccountSD3, FixtureDepositAccountSD1},
			wantErr: false,
		},
		{
			name: "Should successfully get deposit accounts filtered by both types and states",
			args: &args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
				},
				types: []accountsPb.Type{
					accountsPb.Type_FIXED_DEPOSIT,
				},
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountFD1},
			wantErr: false,
		},
		{
			name: "Should not fetch any deposit account for the actor",
			args: &args{
				ctx:     context.Background(),
				actorId: "random-actor-id",
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
				},
				types: []accountsPb.Type{
					accountsPb.Type_FIXED_DEPOSIT,
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dpts.depositAccountDao.GetByActorIdAndStatesAndAccountTypes(tt.args.ctx, tt.args.actorId, tt.args.states, tt.args.types)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdAndStatesAndAccountTypes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				return
			}

			if len(got) != len(tt.want) {
				t.Errorf("GetByActorIdAndStatesAndAccountTypes() error in length, got: %d, want: %d", len(got), len(tt.want))
				return
			}

			for i := range tt.want {
				if diff := isDepositAccountDeepEqual(got[i], tt.want[i]); diff != "" {
					t.Errorf("GetByActorIdAndStatesAndAccountTypes() got = %v, want %v, diff %s", got[i], tt.want[i], diff)
				}
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetByActorIdStatesAccountTypesWithinRange(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

	type args struct {
		ctx          context.Context
		actorId      string
		states       []depositPb.DepositState
		accountTypes []accountsPb.Type
		fromTime     *timestamp.Timestamp
		toTime       *timestamp.Timestamp
		limit        int32
	}
	tests := []struct {
		name    string
		args    args
		want    []*depositPb.DepositAccount
		wantErr bool
	}{
		{
			name: "Should successfully get all deposit accounts",
			args: args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
					depositPb.DepositState_PRECLOSED,
					depositPb.DepositState_CLOSED,
				},
				accountTypes: []accountsPb.Type{
					accountsPb.Type_SMART_DEPOSIT,
					accountsPb.Type_FIXED_DEPOSIT,
					accountsPb.Type_RECURRING_DEPOSIT,
				},
				limit:    4,
				fromTime: nil,
				toTime:   nil,
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountFD1, fixtureDepositAccountSD3, fixtureDepositAccountSD2, FixtureDepositAccountSD1},
			wantErr: false,
		},
		{
			name: "Should successfully get all deposit accounts after 27th August 2020",
			args: args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
					depositPb.DepositState_PRECLOSED,
					depositPb.DepositState_CLOSED,
				},
				accountTypes: []accountsPb.Type{
					accountsPb.Type_SMART_DEPOSIT,
					accountsPb.Type_FIXED_DEPOSIT,
					accountsPb.Type_RECURRING_DEPOSIT,
				},
				limit: 4,
				fromTime: &timestamp.Timestamp{
					Seconds: **********,
					Nanos:   0,
				},
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountFD1, fixtureDepositAccountSD3},
			wantErr: false,
		},
		{
			name: "Should not fetch any deposit account for the actor",
			args: args{
				ctx:     context.Background(),
				actorId: "random-actor-id",
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
				},
				accountTypes: []accountsPb.Type{
					accountsPb.Type_FIXED_DEPOSIT,
				},
				limit: 4,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dpts.depositAccountDao.GetByActorIdStatesAccountTypesWithinRange(tt.args.ctx, tt.args.actorId, tt.args.states, tt.args.accountTypes, tt.args.fromTime, tt.args.toTime, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdStatesAccountTypesWithinRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				return
			}

			if len(got) != len(tt.want) {
				t.Errorf("GetByActorIdStatesAccountTypesWithinRange() error in length, got: %d, want: %d", len(got), len(tt.want))
				return
			}

			for i := range tt.want {
				if diff := isDepositAccountDeepEqual(got[i], tt.want[i]); diff != "" {
					t.Errorf("GetByActorIdStatesAccountTypesWithinRange() got = %v, want %v, diff %s", got[i], tt.want[i], diff)
				}
			}
		})
	}
}

func TestDepositAccountDaoCrdb_UpdateBalanceByAccountNumber(t *testing.T) {
	type args struct {
		accountNumber    string
		ifscCode         string
		principalBalance *moneyPb.Money
	}
	tests := []struct {
		name    string
		args    *args
		wantErr bool
		errType error
	}{
		{
			name: "Should successfully update principal balance",
			args: &args{
				accountNumber: FixtureDepositAccountSD1.AccountNumber,
				ifscCode:      FixtureDepositAccountSD1.IfscCode,
				principalBalance: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        1000,
				},
			},
			wantErr: false,
			errType: nil,
		},
		{
			name: "Should fail to update principal balance as no deposit account exists",
			args: &args{
				accountNumber: "*********",
				ifscCode:      "FDRL0001001",
				principalBalance: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        1000,
				},
			},
			wantErr: true,
			errType: epifierrors.ErrRecordNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

			err := dpts.depositAccountDao.UpdateBalanceByAccountNumber(context.Background(), tt.args.accountNumber,
				tt.args.ifscCode, tt.args.principalBalance)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateBalanceByAccountNumber() err: %v, wantErr: %v", err, tt.wantErr)
			}

			if err != nil && !errors.Is(err, tt.errType) {
				t.Errorf("got: %v, want: %v", err, tt.errType)
			}
		})
	}
}

func TestDepositAccountDaoCrdb_UpdateById(t *testing.T) {
	type args struct {
		ctx            context.Context
		depositAccount *depositPb.DepositAccount
		updateMask     []depositPb.DepositAccountFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		errType error
	}{
		{
			name: "Should successfully update multiple fields",
			args: args{
				ctx: context.Background(),
				depositAccount: &depositPb.DepositAccount{
					Id:             FixtureDepositAccountSD1.Id,
					MaturityAmount: fixtureMoney,
					Name:           fixtureDepositAccountSD2.Name,
				},
				updateMask: []depositPb.DepositAccountFieldMask{
					depositPb.DepositAccountFieldMask_MATURITY_AMOUNT,
					depositPb.DepositAccountFieldMask_NAME,
				},
			},
			wantErr: false,
			errType: nil,
		},
		{
			name: "Should fail as no record is found",
			args: args{
				ctx: context.Background(),
				depositAccount: &depositPb.DepositAccount{
					Id:   "random-deposit-account-id",
					Name: FixtureDepositAccountSD1.Name,
				},
				updateMask: []depositPb.DepositAccountFieldMask{
					depositPb.DepositAccountFieldMask_NAME,
				},
			},
			wantErr: true,
			errType: epifierrors.ErrRecordNotFound,
		},
		{
			name: "Should successfully update goal id field",
			args: args{
				ctx: context.Background(),
				depositAccount: &depositPb.DepositAccount{
					Id:     FixtureDepositAccountSD1.Id,
					GoalId: "goal-id-4",
				},
				updateMask: []depositPb.DepositAccountFieldMask{
					depositPb.DepositAccountFieldMask_DEPOSIT_ACCOUNT_MASK_GOAL_ID,
				},
			},
			wantErr: false,
			errType: nil,
		},
		{
			name: "Should successfully empty goal id field",
			args: args{
				ctx: context.Background(),
				depositAccount: &depositPb.DepositAccount{
					Id:     FixtureDepositAccountSD1.Id,
					GoalId: "",
				},
				updateMask: []depositPb.DepositAccountFieldMask{
					depositPb.DepositAccountFieldMask_DEPOSIT_ACCOUNT_MASK_GOAL_ID,
				},
			},
			wantErr: false,
			errType: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

			err := dpts.depositAccountDao.UpdateById(tt.args.ctx, tt.args.depositAccount, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateById() error = %v, wantErr %v", err, tt.wantErr)
			}
			if err != nil && !errors.Is(err, tt.errType) {
				t.Errorf("UpdateById() got: %v, want: %v", err, tt.errType)
			}
		})
	}
}

func TestDepositAccountDaoCrdb_UpdateIsAddFundsAllowed(t *testing.T) {
	type args struct {
		ctx            context.Context
		depositAccount *depositPb.DepositAccount
	}

	tests := []struct {
		name                  string
		args                  args
		wantErr               bool
		errType               error
		wantIsAddFundsAllowed bool
	}{
		{
			name: "Should successfully update is_add_funds_allowed",
			args: args{
				ctx: context.Background(),
				depositAccount: &depositPb.DepositAccount{
					Id:                FixtureDepositAccountSD1.Id,
					MaturityAmount:    fixtureMoney,
					Name:              FixtureDepositAccountSD1.Name,
					IsAddFundsAllowed: FixtureDepositAccountSD1.IsAddFundsAllowed,
				},
			},
			wantErr:               false,
			errType:               nil,
			wantIsAddFundsAllowed: false,
		},
		{
			name: "Should fail as no record is found",
			args: args{
				ctx: context.Background(),
				depositAccount: &depositPb.DepositAccount{
					Id:                "random-deposit-account-id",
					Name:              FixtureDepositAccountSD1.Name,
					IsAddFundsAllowed: FixtureDepositAccountSD1.IsAddFundsAllowed,
				},
			},
			wantErr:               true,
			errType:               epifierrors.ErrRecordNotFound,
			wantIsAddFundsAllowed: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

			err := dpts.depositAccountDao.UpdateIsAddFundsAllowed(tt.args.ctx, tt.args.depositAccount.Id, false)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateIsAddFundsAllowed() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			depositAccount, _ := dpts.depositAccountDao.GetById(tt.args.ctx, tt.args.depositAccount.Id)
			if depositAccount != nil && depositAccount.IsAddFundsAllowed != tt.wantIsAddFundsAllowed {
				t.Errorf("deposit account isAddFundsAllowed got= %v want= %v", depositAccount.IsAddFundsAllowed, tt.wantIsAddFundsAllowed)
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetCountByActorIdStatesAccountTypesWithinRange(t *testing.T) {

	type args struct {
		ctx             context.Context
		actorId         string
		states          []depositPb.DepositState
		accountTypes    []accountsPb.Type
		fromCreatedTime *timestamp.Timestamp
		toCreatedTime   *timestamp.Timestamp
		fromUpdatedTime *timestamp.Timestamp
	}
	tests := []struct {
		name    string
		args    args
		want    int32
		wantErr error
	}{
		{
			name: "Should successfully count all deposit accounts",
			args: args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
					depositPb.DepositState_PRECLOSED,
					depositPb.DepositState_CLOSED,
				},
				accountTypes: []accountsPb.Type{
					accountsPb.Type_SMART_DEPOSIT,
					accountsPb.Type_FIXED_DEPOSIT,
					accountsPb.Type_RECURRING_DEPOSIT,
				},

				fromCreatedTime: nil,
				toCreatedTime:   nil,
				fromUpdatedTime: nil,
			},
			want:    4,
			wantErr: nil,
		},
		{
			name: "Should successfully count all deposit accounts after 27th August 2020",
			args: args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
					depositPb.DepositState_PRECLOSED,
					depositPb.DepositState_CLOSED,
				},
				accountTypes: []accountsPb.Type{
					accountsPb.Type_SMART_DEPOSIT,
					accountsPb.Type_FIXED_DEPOSIT,
					accountsPb.Type_RECURRING_DEPOSIT,
				},

				fromCreatedTime: &timestamp.Timestamp{
					Seconds: **********,
					Nanos:   0,
				},
			},
			want:    2,
			wantErr: nil,
		},
		{
			name: "Should successfully count all deposit accounts created after 27th August 2020 and updated after" +
				" 27th August 2020 19:54",
			args: args{
				ctx:     context.Background(),
				actorId: FixtureDepositAccountSD1.ActorId,
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
					depositPb.DepositState_PRECLOSED,
					depositPb.DepositState_CLOSED,
				},
				accountTypes: []accountsPb.Type{
					accountsPb.Type_SMART_DEPOSIT,
					accountsPb.Type_FIXED_DEPOSIT,
					accountsPb.Type_RECURRING_DEPOSIT,
				},
				fromCreatedTime: &timestamp.Timestamp{
					Seconds: **********,
					Nanos:   0,
				},
				fromUpdatedTime: &timestamp.Timestamp{
					Seconds: **********, // August 27, 2020 7:53:19 PM
					Nanos:   0,
				},
			},
			want:    1,
			wantErr: nil,
		},
		{
			name: "Should count zero for the actor",
			args: args{
				ctx:     context.Background(),
				actorId: "random-actor-id",
				states: []depositPb.DepositState{
					depositPb.DepositState_CREATED,
				},
				accountTypes: []accountsPb.Type{
					accountsPb.Type_FIXED_DEPOSIT,
				},
			},
			want:    0,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

			got, err := dpts.depositAccountDao.GetCountByActorIdStatesAccountTypesWithinRange(tt.args.ctx,
				tt.args.actorId,
				tt.args.states, tt.args.accountTypes, tt.args.fromCreatedTime, tt.args.toCreatedTime, tt.args.fromUpdatedTime)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetCountByActorIdStatesAccountTypesWithinRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetCountByActorIdStatesAccountTypesWithinRange() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetByStatesAccountTypesWithinRange(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)

	type args struct {
		ctx              context.Context
		states           []depositPb.DepositState
		accountTypes     []accountsPb.Type
		fromMaturityTime *timestamp.Timestamp
		toMaturityTime   *timestamp.Timestamp
		limit            int32
		selectMask       []depositPb.DepositAccountFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    []*depositPb.DepositAccount
		wantErr bool
	}{
		{
			name: "should get empty deposit accounts by passing empty state",
			args: args{
				ctx:    context.Background(),
				states: []depositPb.DepositState{},
			},
			want:    []*depositPb.DepositAccount{},
			wantErr: false,
		},
		{
			name: "should get empty deposit accounts by passing empty accounts type",
			args: args{
				ctx:          context.Background(),
				accountTypes: []accountsPb.Type{},
			},
			want:    []*depositPb.DepositAccount{},
			wantErr: false,
		},
		{
			name: "should get empty deposit accounts as no accounts are there after the given from maturity date",
			args: args{
				ctx:          context.Background(),
				states:       []depositPb.DepositState{depositPb.DepositState_CREATED},
				accountTypes: []accountsPb.Type{accountsPb.Type_SMART_DEPOSIT},
				fromMaturityTime: &timestamp.Timestamp{
					Seconds: **********, // Mon May 12 2025 19:36:13 GMT+0530
					Nanos:   0,
				},
			},
			want:    []*depositPb.DepositAccount{},
			wantErr: false,
		},
		{
			name: "should get empty deposit accounts as no accounts are there before the given to maturity date",
			args: args{
				ctx:          context.Background(),
				states:       []depositPb.DepositState{depositPb.DepositState_CREATED},
				accountTypes: []accountsPb.Type{accountsPb.Type_SMART_DEPOSIT},
				toMaturityTime: &timestamp.Timestamp{
					Seconds: **********, // Monday, May 5, 2025 7:36:13 PM GMT+05:30
					Nanos:   0,
				},
			},
			want:    []*depositPb.DepositAccount{},
			wantErr: false,
		},
		{
			name: "should get deposit accounts after the given from maturity date",
			args: args{
				ctx:          context.Background(),
				states:       []depositPb.DepositState{depositPb.DepositState_CREATED},
				accountTypes: []accountsPb.Type{accountsPb.Type_SMART_DEPOSIT},
				fromMaturityTime: &timestamp.Timestamp{
					Seconds: **********, // Mon May 12 2025 15:36:13 GMT+0530
					Nanos:   0,
				},
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountSD3, FixtureDepositAccountSD1},
			wantErr: false,
		},
		{
			name: "should get deposit accounts between given maturity date range",
			args: args{
				ctx:          context.Background(),
				states:       []depositPb.DepositState{depositPb.DepositState_CREATED},
				accountTypes: []accountsPb.Type{accountsPb.Type_FIXED_DEPOSIT},
				fromMaturityTime: &timestamp.Timestamp{
					Seconds: **********, // Sunday, May 10, 2020 1:06:13 PM GMT+05:30
					Nanos:   0,
				},
				toMaturityTime: &timestamp.Timestamp{
					Seconds: **********, // Wednesday, May 20, 2020 1:06:13 PM GMT+05:30
					Nanos:   0,
				},
			},
			want:    []*depositPb.DepositAccount{fixtureDepositAccountFD1},
			wantErr: false,
		},
		{
			name: "should get empty deposit accounts between given maturity date range",
			args: args{
				ctx:          context.Background(),
				states:       []depositPb.DepositState{depositPb.DepositState_CREATED},
				accountTypes: []accountsPb.Type{accountsPb.Type_SMART_DEPOSIT},
				fromMaturityTime: &timestamp.Timestamp{
					Seconds: **********, // Mon May 12 2025 15:36:13 GMT+0530
					Nanos:   0,
				},
				toMaturityTime: &timestamp.Timestamp{
					Seconds: **********, // Mon May 12 2025 15:36:13 GMT+0530
					Nanos:   0,
				},
			},
			want:    []*depositPb.DepositAccount{},
			wantErr: false,
		},
		{
			name: "should get deposit accounts with given selected fields",
			args: args{
				ctx:          context.Background(),
				states:       []depositPb.DepositState{depositPb.DepositState_CREATED},
				accountTypes: []accountsPb.Type{accountsPb.Type_FIXED_DEPOSIT},
				fromMaturityTime: &timestamp.Timestamp{
					Seconds: **********, // Sunday, May 10, 2020 1:06:13 PM GMT+05:30
					Nanos:   0,
				},
				toMaturityTime: &timestamp.Timestamp{
					Seconds: **********, // Wednesday, May 20, 2020 1:06:13 PM GMT+05:30
					Nanos:   0,
				},
				selectMask: []depositPb.DepositAccountFieldMask{
					depositPb.DepositAccountFieldMask_ACCOUNT_ID,
					depositPb.DepositAccountFieldMask_NAME,
					depositPb.DepositAccountFieldMask_PRINCIPAL_AMOUNT,
				},
			},
			want: []*depositPb.DepositAccount{
				{
					Id:              fixtureDepositAccountFD1.Id,
					Name:            fixtureDepositAccountFD1.Name,
					PrincipalAmount: fixtureDepositAccountFD1.PrincipalAmount,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dpts.depositAccountDao.GetByStatesAccountTypesWithinRange(tt.args.ctx, tt.args.states,
				tt.args.accountTypes, tt.args.fromMaturityTime, tt.args.toMaturityTime, tt.args.limit, tt.args.selectMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByStatesAccountTypesWithinRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if len(got) != len(tt.want) {
				t.Errorf("GetByStatesAccountTypesWithinRange() error in length, got: %d, want: %d", len(got), len(tt.want))
				return
			}

			for i := range tt.want {
				if diff := isDepositAccountDeepEqual(got[i], tt.want[i]); diff != "" {
					t.Errorf("GetByStatesAccountTypesWithinRange() got = %v, want %v, diff %s", got[i], tt.want[i], diff)
				}
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetCountByActorIdGroupByTemplateId(t *testing.T) {
	fixtureDepositAccountSD := &depositPb.DepositAccount{
		Name:            FixtureDepositRequestCreateSD2.DepositInfo.Name,
		Type:            FixtureDepositRequestCreateSD2.DepositInfo.Type,
		ActorId:         "actor-user-1",
		SchemeCode:      depositPb.DepositScheme_SMART_DEPOSIT,
		State:           depositPb.DepositState_PRECLOSED,
		PrincipalAmount: FixtureDepositRequestCreateSD2.DepositInfo.Amount,
		RunningBalance:  fixtureMoney,
		MaturityAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        110,
		},
		Term: FixtureDepositRequestCreateSD2.DepositInfo.Term,
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate:           "10",
		RenewInfo:              FixtureDepositRequestCreateSD2.DepositInfo.RenewInfo,
		OperativeAccountNumber: FixtureDepositRequestCreateSD2.DepositInfo.OperativeAccountNumber,
		RepayAccountNumber:     FixtureDepositRequestCreateSD2.DepositInfo.RepayAccountNumber,
		PartnerBank:            FixtureDepositRequestCreateSD2.DepositInfo.Vendor,
		IfscCode:               "FDRL0001001",
		NomineeDetails:         &depositPb.DepositNomineeDetails{},
		IsAddFundsAllowed:      true,
		ClosureInfo:            &depositPb.ClosureInfo{},
		Provenance:             depositPb.DepositAccountProvenance_USER_APP,
	}
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]int32
		wantErr bool
		prepare func(*args, *DepositTestSuite)
	}{
		{
			name: "should get counts for no deposit templates",
			args: args{
				ctx: context.Background(),
			},
			want:    map[string]int32{},
			wantErr: false,
		},
		{
			name: "should get counts for deposit templates SD1 - 2, SD2 - 1",
			args: args{
				ctx: context.Background(),
			},
			want: map[string]int32{
				"SD1": 2,
				"SD2": 1,
			},
			wantErr: false,
			prepare: func(a *args, dpts *DepositTestSuite) {
				fixtureDepositAccountSD.Id = "deposit-account-id-11"
				fixtureDepositAccountSD.DepositTemplateId = "SD1"
				fixtureDepositAccountSD.AccountNumber = "**********"
				_, err := dpts.depositAccountDao.Create(a.ctx, fixtureDepositAccountSD)
				if err != nil {
					t.Errorf("error in creating deposit account: %v", err)
				}

				fixtureDepositAccountSD.Id = "deposit-account-id-12"
				fixtureDepositAccountSD.DepositTemplateId = "SD1"
				fixtureDepositAccountSD.AccountNumber = "**********"
				_, err = dpts.depositAccountDao.Create(a.ctx, fixtureDepositAccountSD)
				if err != nil {
					t.Errorf("error in creating deposit account: %v", err)
				}

				fixtureDepositAccountSD.Id = "deposit-account-id-13"
				fixtureDepositAccountSD.DepositTemplateId = "SD2"
				fixtureDepositAccountSD.AccountNumber = "**********"
				_, err = dpts.depositAccountDao.Create(a.ctx, fixtureDepositAccountSD)
				if err != nil {
					t.Errorf("error in creating deposit account: %v", err)
				}
			},
		},
		{
			name: "should get counts for deposit templates SD2 - 1, SD3 - 1 for actor-2",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
			},
			want: map[string]int32{
				"SD3": 1,
				"SD4": 1,
			},
			wantErr: false,
			prepare: func(a *args, dpts *DepositTestSuite) {
				fixtureDepositAccountSD.Id = "deposit-account-id-21"
				fixtureDepositAccountSD.DepositTemplateId = "SD3"
				fixtureDepositAccountSD.AccountNumber = "**********"
				fixtureDepositAccountSD.ActorId = "actor-2"
				_, err := dpts.depositAccountDao.Create(a.ctx, fixtureDepositAccountSD)
				if err != nil {
					t.Errorf("error in creating deposit account: %v", err)
				}

				fixtureDepositAccountSD.Id = "deposit-account-id-22"
				fixtureDepositAccountSD.DepositTemplateId = "SD3"
				fixtureDepositAccountSD.AccountNumber = "**********"
				fixtureDepositAccountSD.ActorId = "actor-1"
				_, err = dpts.depositAccountDao.Create(a.ctx, fixtureDepositAccountSD)
				if err != nil {
					t.Errorf("error in creating deposit account: %v", err)
				}

				fixtureDepositAccountSD.Id = "deposit-account-id-23"
				fixtureDepositAccountSD.DepositTemplateId = "SD4"
				fixtureDepositAccountSD.AccountNumber = "**********"
				fixtureDepositAccountSD.ActorId = "actor-2"
				_, err = dpts.depositAccountDao.Create(a.ctx, fixtureDepositAccountSD)
				if err != nil {
					t.Errorf("error in creating deposit account: %v", err)
				}
			},
		},
	}
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.prepare != nil {
				tt.prepare(&tt.args, dpts)
			}
			got, err := dpts.depositAccountDao.GetCountByActorIdGroupByTemplateId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCountByActorIdGroupByTemplateId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCountByActorIdGroupByTemplateId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDepositAccountDaoCrdb_GetAccountsByFilters(t *testing.T) {
	depositAccount := &depositPb.DepositAccount{}
	_ = copier.Copy(depositAccount, fixtureDepositAccountFD1)
	// set the correct mutual fund id to satisfy the FK constraint
	depositAccount.Id = fixtureDepositAccountFD1.GetId()

	type args struct {
		ctx       context.Context
		filters   []storagev2.FilterOption
		pageSize  uint32
		pageToken *pagination.PageToken
	}
	tests := []struct {
		name          string
		args          args
		wantErr       bool
		wantLen       int
		wantHasBefore bool
		wantHasAfter  bool
	}{
		{
			name: "get when page size is less than total elements",
			args: args{
				ctx:       context.Background(),
				filters:   []storagev2.FilterOption{WithMaturityDateLessThan(time.Date(2026, 9, 12, 0, 0, 0, 0, datetime.IST))},
				pageSize:  2,
				pageToken: nil,
			},
			wantErr:       false,
			wantLen:       2,
			wantHasBefore: false,
			wantHasAfter:  true,
		},
		{
			name: "get when page size is greater than total elements",
			args: args{
				ctx:       context.Background(),
				filters:   []storagev2.FilterOption{WithMaturityDateLessThan(time.Date(2026, 9, 12, 0, 0, 0, 0, datetime.IST))},
				pageSize:  5,
				pageToken: nil,
			},
			wantErr:       false,
			wantLen:       4,
			wantHasBefore: false,
			wantHasAfter:  false,
		},
		{
			name: "get when page size is equal to total elements",
			args: args{
				ctx:       context.Background(),
				filters:   []storagev2.FilterOption{WithMaturityDateLessThan(time.Date(2026, 9, 12, 0, 0, 0, 0, datetime.IST))},
				pageSize:  4,
				pageToken: nil,
			},
			wantErr:       false,
			wantLen:       4,
			wantHasAfter:  false,
			wantHasBefore: false,
		},
		{
			name: "get with page token with only has before",
			args: args{
				ctx:       context.Background(),
				filters:   []storagev2.FilterOption{WithMaturityDateLessThan(time.Date(2026, 9, 12, 0, 0, 0, 0, datetime.IST))},
				pageSize:  2,
				pageToken: &pagination.PageToken{Offset: 3},
			},
			wantErr:       false,
			wantLen:       1,
			wantHasAfter:  false,
			wantHasBefore: true,
		},
		{
			name: "get with page token having both has and before after",
			args: args{
				ctx:       context.Background(),
				filters:   []storagev2.FilterOption{WithMaturityDateLessThan(time.Date(2026, 9, 12, 0, 0, 0, 0, datetime.IST))},
				pageSize:  1,
				pageToken: &pagination.PageToken{Offset: 1},
			},
			wantErr:       false,
			wantLen:       1,
			wantHasAfter:  true,
			wantHasBefore: true,
		},
	}
	pkgTest.PrepareScopedDatabase(t, dpts.conf.EpifiDb.GetName(), dpts.db, depositAffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, pgctx, err := dpts.depositAccountDao.GetAccountsByFilters(tt.args.ctx, tt.args.pageToken, tt.args.pageSize, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrdersByFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if len(got) != tt.wantLen {
				t.Errorf("GetOrdersByFilters() got = %v,\n want %v", len(got), tt.wantLen)
			}

			if pgctx.HasAfter != tt.wantHasAfter {
				t.Errorf("GetOrdersByFilters() gotHasAfter = %v,\n want %v", pgctx.HasAfter, tt.wantHasAfter)
			}

			if pgctx.HasBefore != tt.wantHasBefore {
				t.Errorf("GetOrdersByFilters() gotHasBefore = %v,\n want %v", pgctx.HasBefore, tt.wantHasBefore)
			}

		})
	}
}

func isDepositAccountDeepEqual(actual, expected *depositPb.DepositAccount) string {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
		expected.DeletedAt = actual.DeletedAt
	}
	return cmp.Diff(actual, expected, protocmp.Transform())
}
