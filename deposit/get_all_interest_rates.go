package deposit

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) GetAllInterestRates(ctx context.Context, _ *depositPb.GetAllInterestRatesRequest) (*depositPb.
	GetAllInterestRatesResponse, error) {
	res := &depositPb.GetAllInterestRatesResponse{}

	interestRates, err := s.depositInterestRateDao.GetAll(ctx)
	if err != nil {
		logger.Error(ctx, "failed to get all interest rates",
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.InterestRates = interestRates
	res.Status = rpc.StatusOk()
	return res, nil
}
