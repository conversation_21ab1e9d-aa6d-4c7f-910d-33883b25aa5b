package dao

import (
	"errors"
	"fmt"
	"time"

	"golang.org/x/net/context"
	gormV2 "gorm.io/gorm"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/salaryprogram/dao/model"
)

type SalaryEstimationsDao struct {
	db *gormV2.DB
}

func NewSalaryEstimationsDao(db pkgTypes.SalaryprogramPGDB) *SalaryEstimationsDao {
	return &SalaryEstimationsDao{db: db}
}

// compile time check to make sure SalaryEstimationsDao implements ISalaryEstimationsDao
var _ ISalaryEstimationsDao = &SalaryEstimationsDao{}

func (se *SalaryEstimationsDao) Create(ctx context.Context, req *salaryprogramPb.SalaryEstimation) (*salaryprogramPb.SalaryEstimation, error) {
	defer metric_util.TrackDuration("salaryprogram/dao", "SalaryEstimationsDao", "Create", time.Now())
	err := se.validateSalaryEstimationCreation(req)
	if err != nil {
		return nil, fmt.Errorf("error validating salary eatimation request before creating entry in db, err : %w", err)
	}
	db := gormctxv2.FromContextOrDefault(ctx, se.db)
	salaryEstimationDbModel := model.NewSalaryEstimation(req)

	if err := db.Create(salaryEstimationDbModel).Error; err != nil {
		return nil, fmt.Errorf("error creating salary eatimation request entry in db, err : %w", err)
	}

	return salaryEstimationDbModel.GetProto(), nil
}

func (se *SalaryEstimationsDao) GetByActorIdAndSourceTypeAndStatus(ctx context.Context, actorId string, accountSourceType salaryprogramPb.SalaryAccountSourceType, status salaryprogramPb.SalaryEstimationStalenessStatusType) (*salaryprogramPb.SalaryEstimation, error) {
	defer metric_util.TrackDuration("salaryprogram/dao", "SalaryEstimationsDao", "GetByActorIdAndSourceTypeAndStatus", time.Now())
	if actorId == "" || accountSourceType == salaryprogramPb.SalaryAccountSourceType_SALARY_ACCOUNT_SOURCE_TYPE_UNSPECIFIED {
		return nil, errors.New("actor Id or account source type can not be empty/nil")
	}
	db := gormctxv2.FromContextOrDefault(ctx, se.db)

	salaryEstimationModel := &model.SalaryEstimation{}
	res := db.Take(salaryEstimationModel, "actor_id = ? AND salary_account_source_type = ? and status = ?", actorId, accountSourceType, status)
	if errors.Is(res.Error, gormV2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch salary estimation by actor_id and salary_account_source_type, err : %w", res.Error)
	}
	return salaryEstimationModel.GetProto(), nil
}

func (se *SalaryEstimationsDao) GetLatestForActor(ctx context.Context, actorId string) (*salaryprogramPb.SalaryEstimation, error) {
	defer metric_util.TrackDuration("salaryprogram/dao", "SalaryEstimationsDao", "GetLatestForActor", time.Now())
	if actorId == "" {
		return nil, errors.New("actor Id can not be empty/nil")
	}
	db := gormctxv2.FromContextOrDefault(ctx, se.db)

	salaryEstimationModel := &model.SalaryEstimation{}
	res := db.Take(salaryEstimationModel, "actor_id = ? AND status = ?", actorId, salaryprogramPb.SalaryEstimationStalenessStatusType_SALARY_ESTIMATION_STALENESS_STATUS_TYPE_LATEST)
	if errors.Is(res.Error, gormV2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch salary estimation by actor_id and salary_account_source_type, err : %w", res.Error)
	}
	return salaryEstimationModel.GetProto(), nil
}

//nolint:dupl
func (se *SalaryEstimationsDao) GetById(ctx context.Context, id string) (*salaryprogramPb.SalaryEstimation, error) {
	defer metric_util.TrackDuration("salaryprogram/dao", "SalaryEstimationsDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, se.db)

	salaryEstimationModel := &model.SalaryEstimation{}
	res := db.Take(salaryEstimationModel, "id = ?", id)
	if errors.Is(res.Error, gormV2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch salary estimation by id, err : %w", res.Error)
	}
	return salaryEstimationModel.GetProto(), nil
}

func (se *SalaryEstimationsDao) validateSalaryEstimationCreation(req *salaryprogramPb.SalaryEstimation) error {
	switch {
	case req.GetActorId() == "":
		return errors.New("actor id can not be empty")
	case req.GetSalaryAccountSourceType() == salaryprogramPb.SalaryAccountSourceType_SALARY_ACCOUNT_SOURCE_TYPE_AA_ACCOUNT && req.GetSalaryAccountSourceRefId() == "":
		return errors.New("source ref id can not be empty if source is from aa account")
	case req.GetLastEstimatedAt() == nil:
		return errors.New("verified salary amount can not be nil")
		// todo: uncomment after adding provenance for aa salary in-app flow
		// case req.GetProvenance() == salaryprogramPb.SalaryEstimationProvenance_SALARY_ESTIMATION_PROVENANCE_UNSPECIFIED:
		// 	return errors.New("salary verification provenance can not be unspecified")
		// case req.GetEstimatedBy() == salaryprogramPb.SalaryEstimatedBy_SALARY_ESTIMATED_BY_UNSPECIFIED:
		// 	return errors.New("salary estimation verified by can not be unspecified")
	}
	return nil
}

func (se *SalaryEstimationsDao) MarkStaleAllEstimationsForActorId(ctx context.Context, actorId string) error {
	defer metric_util.TrackDuration("salaryprogram/dao", "SalaryEstimationsDao", "MarkStaleAllEstimationsForActorId", time.Now())
	if actorId == "" {
		return errors.New("actorId cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, se.db)

	salaryEstimationModel := &model.SalaryEstimation{
		Status: salaryprogramPb.SalaryEstimationStalenessStatusType_SALARY_ESTIMATION_STALENESS_STATUS_TYPE_STALE,
	}
	res := db.Model(salaryEstimationModel).Where("actor_id = ?", actorId).Select([]string{"status"}).Updates(salaryEstimationModel)
	if res.Error != nil {
		return fmt.Errorf("error marking salary estimations as stale for actor, err : %w", res.Error)
	}
	return nil
}
