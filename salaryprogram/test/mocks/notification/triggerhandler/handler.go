// Code generated by MockGen. DO NOT EDIT.
// Source: salaryprogram/notification/triggerhandler/handler.go

// Package mock_triggerhandler is a generated GoMock package.
package mock_triggerhandler

import (
	context "context"
	reflect "reflect"

	notification "github.com/epifi/gamma/api/salaryprogram/notification"
	triggerhandler "github.com/epifi/gamma/salaryprogram/notification/triggerhandler"
	gomock "github.com/golang/mock/gomock"
)

// MockITriggerHandler is a mock of ITriggerHandler interface.
type MockITriggerHandler struct {
	ctrl     *gomock.Controller
	recorder *MockITriggerHandlerMockRecorder
}

// MockITriggerHandlerMockRecorder is the mock recorder for MockITriggerHandler.
type MockITriggerHandlerMockRecorder struct {
	mock *MockITriggerHandler
}

// NewMockITriggerHandler creates a new mock instance.
func NewMockITriggerHandler(ctrl *gomock.Controller) *MockITriggerHandler {
	mock := &MockITriggerHandler{ctrl: ctrl}
	mock.recorder = &MockITriggerHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITriggerHandler) EXPECT() *MockITriggerHandlerMockRecorder {
	return m.recorder
}

// GetNotificationMessage mocks base method.
func (m *MockITriggerHandler) GetNotificationMessage(ctx context.Context, actorId string, medium notification.NotificationMedium, triggerMetadata *notification.TriggerMetadata) (*triggerhandler.NotificationMediumMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNotificationMessage", ctx, actorId, medium, triggerMetadata)
	ret0, _ := ret[0].(*triggerhandler.NotificationMediumMessage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNotificationMessage indicates an expected call of GetNotificationMessage.
func (mr *MockITriggerHandlerMockRecorder) GetNotificationMessage(ctx, actorId, medium, triggerMetadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotificationMessage", reflect.TypeOf((*MockITriggerHandler)(nil).GetNotificationMessage), ctx, actorId, medium, triggerMetadata)
}

// IsTriggerValid mocks base method.
func (m *MockITriggerHandler) IsTriggerValid(ctx context.Context, actorId string, triggerMetadata *notification.TriggerMetadata) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTriggerValid", ctx, actorId, triggerMetadata)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTriggerValid indicates an expected call of IsTriggerValid.
func (mr *MockITriggerHandlerMockRecorder) IsTriggerValid(ctx, actorId, triggerMetadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTriggerValid", reflect.TypeOf((*MockITriggerHandler)(nil).IsTriggerValid), ctx, actorId, triggerMetadata)
}
