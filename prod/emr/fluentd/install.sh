#!/bin/bash

curl -L https://toolbelt.treasuredata.com/sh/install-amazon2-td-agent4.sh | sh
sudo rm /etc/td-agent/td-agent.conf
sudo /usr/sbin/td-agent-gem install fluent-plugin-secure-forward
sudo /usr/sbin/td-agent-gem install fluent-plugin-elasticsearch
sudo aws s3 cp s3://epifi-dp-resources/data-platform/data-configs/emr/fluentd/td-agent.conf /etc/td-agent/td-agent.conf
sudo systemctl stop td-agent.service
sudo systemctl start td-agent.service