package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	yodapb "github.com/epifi/gamma/api/yoda"
)

type Chat struct {
	Id          string `gorm:"type:uuid;default:gen_random_uuid();primary_key"`
	UserId      string `gorm:"not null"`
	TextContent string
	CreatedAt   time.Time `gorm:"not null"`
	UpdatedAt   time.Time `gorm:"not null"`
}

func NewChat(r *yodapb.Chat) *Chat {
	return &Chat{
		Id:          r.GetId(),
		UserId:      r.GetUserId(),
		TextContent: r.GetTextContent(),
		CreatedAt:   r.GetCreatedAt().AsTime(),
		UpdatedAt:   r.GetUpdatedAt().AsTime(),
	}
}

func (r *Chat) ToProto() *yodapb.Chat {
	return &yodapb.Chat{
		Id:          r.Id,
		UserId:      r.User<PERSON>d,
		TextContent: r.TextContent,
		CreatedAt:   timestamppb.New(r.CreatedAt),
		UpdatedAt:   timestamppb.New(r.UpdatedAt),
	}
}
