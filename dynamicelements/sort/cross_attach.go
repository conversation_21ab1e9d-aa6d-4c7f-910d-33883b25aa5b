package sort

import (
	"context"

	"math/rand"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/hash"
	"github.com/epifi/be-common/pkg/logger"
	productPb "github.com/epifi/gamma/api/product"
	types "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/be-common/pkg/epifigrpc"

	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/dynamicelements/config/genconf"
)

// CrossAttachSortingStrategy sorts dynamic elements according to the cross-sell information
type CrossAttachSortingStrategy struct {
	crossAttachClient crossAttachPb.CrossAttachClient
	dynConf           *genconf.Config
}

func NewCrossAttachSortingStrategy(crossAttachClient crossAttachPb.CrossAttachClient, dynConf *genconf.Config) *CrossAttachSortingStrategy {
	return &CrossAttachSortingStrategy{
		crossAttachClient: crossAttachClient,
		dynConf:           dynConf,
	}
}

var productToServiceMap = map[productPb.ProductType]types.ServiceName{
	productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT: types.ServiceName_SAVINGS_SERVICE,
	productPb.ProductType_PRODUCT_TYPE_USSTOCKS:        types.ServiceName_US_STOCKS_SERVICE,
	productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS:  types.ServiceName_PRE_APPROVED_LOAN_SERVICE,
}

func (c *CrossAttachSortingStrategy) Sort(ctx context.Context, actorId string, screenName deeplinkPb.Screen, sessionId string, dynamicElements []*dePb.DynamicElement) ([]*dePb.DynamicElement, error) {
	resp, err := c.crossAttachClient.GetCrossSellInfo(ctx, &crossAttachPb.GetCrossSellInfoRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		return nil, errors.Wrap(grpcErr, "GetCrossSellInfo rpc failed")
	}

	logger.InfoForActor(ctx, c.dynConf.ActorsWhitelistedForLogs(), "dynamic elements based on cross attach sorting order", zap.String(logger.ACTOR_ID, actorId), zap.Any("Products", getProductsToPitch(resp)), zap.String("CanCrossSell", resp.GetCanCrossSell().String()))
	return getOrderedDynamicElements(dynamicElements, getProductsToPitch(resp), resp.GetCanCrossSell(), sessionId)
}

// getOrderedDynamicElements returns the dynamic elements in the order of the products to pitch
func getOrderedDynamicElements(dynamicElements []*dePb.DynamicElement, productsToPitch []productPb.ProductType, canCrossSell commontypes.BooleanEnum, sessionId string) ([]*dePb.DynamicElement, error) {
	orderedDynamicElements := make([]*dePb.DynamicElement, 0)
	for _, product := range productsToPitch {
		for _, dynamicElement := range dynamicElements {
			if dynamicElement.GetOwnerService() == productToServiceMap[product] {
				orderedDynamicElements = append(orderedDynamicElements, dynamicElement)
			}
		}
	}

	// If cross-sell priotised products are not available, then randomly shuffling the ordered dynamic elements
	if canCrossSell == commontypes.BooleanEnum_FALSE {
		// nolint -- Not used for security/cryptography
		rnd := rand.New(rand.NewSource(int64(hash.Hash(sessionId, 0))))
		rnd.Shuffle(len(orderedDynamicElements), func(i, j int) {
			orderedDynamicElements[i], orderedDynamicElements[j] = orderedDynamicElements[j], orderedDynamicElements[i]
		})
	}

	return orderedDynamicElements, nil
}

// getProductsToPitch returns the products to pitch based on the cross-sell information
func getProductsToPitch(resp *crossAttachPb.GetCrossSellInfoResponse) []productPb.ProductType {
	// If cross-sell is not allowed, return the default products to pitch
	if resp.GetCanCrossSell() == commontypes.BooleanEnum_FALSE {
		return []productPb.ProductType{
			productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
			productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
			productPb.ProductType_PRODUCT_TYPE_USSTOCKS,
		}
	}
	// If cross-sell is allowed, return the prioritized products to pitch from the response
	return resp.GetPrioritizedProductsToPitch()
}
