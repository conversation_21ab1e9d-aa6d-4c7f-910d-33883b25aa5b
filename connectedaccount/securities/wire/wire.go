//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"

	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/securities"
	"github.com/epifi/be-common/pkg/cmd/types"
)

func InitializeConnectedAccountSecuritiesService(pgdb types.ConnectedAccountPGDB) *securities.Service {
	wire.Build(
		securities.NewService,
		dao.AaEquityFiDaoWireSet,
		dao.AaTransactionDaoWireSet,
		dao.AaEtfFiDaoWireSet,
		dao.AaReitFiDaoWireSet,
		dao.AaInvitFiDaoWireSet,
	)
	return &securities.Service{}
}
