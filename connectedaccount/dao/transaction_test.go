package dao

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	pagePb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/connectedaccount/config"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
)

type AaTransactionDaoTestSuite struct {
	db               *gormv2.DB
	aaTransactionDao AaTransactionDao
	conf             *config.Config
	dbName           string
	gconf            *genconf.Config
}

var (
	atdts       AaTransactionDaoTestSuite
	transaction = &caPb.AaTransaction{
		AccountId:    "9ef656fc-3e12-4214-838b-f62485655e40",
		TxnId:        "txn_test_1",
		ActorId:      "actor_test_1",
		DerivedTxnId: "test-derived-txn-id-11",
	}
	AaTransactionResp1 = &caPb.AaTransaction{
		Id:                "9031c12f-1ac3-47f1-9208-3fde2000cda4",
		AccountId:         "8568c12f-1ac3-47f1-9208-3fde2000cda4",
		TxnId:             "OneMoney_TXN_1",
		ActorId:           "actor_test_1",
		TransactionDate:   timestamppb.New(time.Date(2017, 6, 5, 1, 36, 31, 0, datetime.UTC)),
		TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
		DerivedTxnId:      "test-derived-txn-id-1",
		TxnIdSource:       caEnumPb.TxnIdSource_TXN_ID_SOURCE_FIP,
	}

	AaTransactionResp2 = &caPb.AaTransaction{
		Id:                "67ebf15b-6a3b-4fa2-92cd-d6abf209c38a",
		AccountId:         "8568c12f-1ac3-47f1-9208-3fde2000cda4",
		TxnId:             "OneMoney_TXN_2",
		ActorId:           "actor_test_1",
		TransactionDate:   timestamppb.New(time.Date(2017, 6, 6, 1, 36, 31, 0, datetime.UTC)),
		TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_DEPOSIT,
		DerivedTxnId:      "test-derived-txn-id-2",
		TxnIdSource:       caEnumPb.TxnIdSource_TXN_ID_SOURCE_GENERATED,
	}

	AaTransactionResp3 = &caPb.AaTransaction{
		Id:                "920320bf-60db-412a-88df-144b84d405d3",
		AccountId:         "8568c12f-1ac3-47f1-9208-3fde2000cda4",
		TxnId:             "Finvu_TXN_3",
		ActorId:           "actor_test_1",
		TransactionDate:   timestamppb.New(time.Date(2017, 6, 6, 1, 37, 31, 0, datetime.UTC)),
		TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_EQUITIES,
		DerivedTxnId:      "test-derived-txn-id-3",
		TxnIdSource:       caEnumPb.TxnIdSource_TXN_ID_SOURCE_FIP,
	}

	AaTransactionResp4 = &caPb.AaTransaction{
		Id:                "920320bf-60db-412a-88df-144b84d405d4",
		AccountId:         "8568c12f-1ac3-47f1-9208-3fde2000cda4",
		TxnId:             "Finvu_TXN_4",
		ActorId:           "actor_test_1",
		TransactionDate:   timestamppb.New(time.Date(2017, 6, 6, 1, 38, 31, 0, datetime.UTC)),
		TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_EQUITIES,
		DerivedTxnId:      "test-derived-txn-id-4",
		TxnIdSource:       caEnumPb.TxnIdSource_TXN_ID_SOURCE_FIP,
	}

	AaTransactionResp5 = &caPb.AaTransaction{
		Id:                "920320bf-60db-412a-88df-144b84d405d5",
		AccountId:         "8568c12f-1ac3-47f1-9208-3fde2000cda4",
		TxnId:             "Finvu_TXN_5",
		ActorId:           "actor_test_1",
		TransactionDate:   timestamppb.New(time.Date(2017, 6, 6, 1, 39, 31, 0, datetime.UTC)),
		TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_EQUITIES,
		DerivedTxnId:      "test-derived-txn-id-5",
		TxnIdSource:       caEnumPb.TxnIdSource_TXN_ID_SOURCE_FIP,
	}

	AaTransactionResp6 = &caPb.AaTransaction{
		Id:                "920320bf-60db-412a-88df-144b84d405d6",
		AccountId:         "8568c12f-1ac3-47f1-9208-3fde2000cda4",
		TxnId:             "Finvu_TXN_6",
		ActorId:           "actor_test_1",
		TransactionDate:   timestamppb.New(time.Date(2017, 6, 6, 1, 40, 31, 0, datetime.UTC)),
		TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_EQUITIES,
		DerivedTxnId:      "test-derived-txn-id-6",
		TxnIdSource:       caEnumPb.TxnIdSource_TXN_ID_SOURCE_FIP,
	}

	AaTransactionResp7 = &caPb.AaTransaction{
		Id:                "920320bf-60db-412a-88df-144b84d405d7",
		AccountId:         "8568c12f-1ac3-47f1-9208-3fde2000cda4",
		TxnId:             "Finvu_TXN_7",
		ActorId:           "actor_test_1",
		TransactionDate:   timestamppb.New(time.Date(2017, 6, 6, 1, 41, 31, 0, datetime.UTC)),
		TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_EQUITIES,
		DerivedTxnId:      "test-derived-txn-id-7",
		TxnIdSource:       caEnumPb.TxnIdSource_TXN_ID_SOURCE_FIP,
	}

	noNextPage = &pagePb.PageContextResponse{
		BeforeToken: "",
		HasBefore:   false,
		AfterToken:  "",
		HasAfter:    false,
	}
)

func TestTransactionDaoCrdb_Create(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx           context.Context
		aaTransaction *caPb.AaTransaction
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successfully create record",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:           context.Background(),
				aaTransaction: transaction,
			},
			wantErr: false,
		}, {
			name: "failure create record violates foreign key constraint",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:           context.Background(),
				aaTransaction: &caPb.AaTransaction{AccountId: "16d87e8b-cc66-42bd-8d93-4ab8cd68f094"},
			},
			wantErr: true,
		}, {
			name: "failure create record invalid txn id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:           context.Background(),
				aaTransaction: &caPb.AaTransaction{AccountId: "9ef656fc-3e12-4214-838b-f62485655e40", TxnId: "invalid_txn_id"},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			c := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := c.Create(tt.args.ctx, tt.args.aaTransaction)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestTransactionDaoCrdb_GetByAccountAndTxnId(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx           context.Context
		transactionId string
		accountRefId  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successfully fetch transaction by id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:           context.Background(),
				transactionId: "OneMoney_TXN_1",
				accountRefId:  "8568c12f-1ac3-47f1-9208-3fde2000cda4",
			},
			wantErr: false,
		}, {
			name: "failure fetch transaction by id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:           context.Background(),
				transactionId: "random_OneMoney_TXN_1",
				accountRefId:  "8568c12f-1ac3-47f1-9208-3fde2000cda4",
			},
			wantErr: true,
		}, {
			name: "failure fetch transaction by empty id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:           context.Background(),
				transactionId: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			c := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := c.GetByAccountAndTxnId(tt.args.ctx, tt.args.accountRefId, tt.args.transactionId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountAndTxnId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestTransactionDaoCrdb_GetByActorId(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successfully fetch transaction by id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_test_1",
			},
			wantErr: false,
		},
		{
			name: "failure fetch transaction by non existent actor id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_test_23",
			},
			wantErr: true,
		}, {
			name: "failure fetch transaction by empty actor id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			c := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := c.GetByActorId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestTransactionDaoCrdb_GetByAccountId(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx       context.Context
		accountId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successfully fetch transaction by id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				accountId: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
			},
			wantErr: false,
		},
		{
			name: "failure in fetch transaction by id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				accountId: "9568c12f-1ac3-47f1-9208-3fde2000cda3",
			},
			wantErr: true,
		},
		{
			name: "failure in fetch transaction by empty id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				accountId: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			c := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := c.GetByAccountId(tt.args.ctx, tt.args.accountId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountId() error = %v, wantErr %v got %v", err, tt.wantErr, got)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaTransactionDaoCrdb_CreateOrGet(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx           context.Context
		aaTransaction *caPb.AaTransaction
	}
	tests := []struct {
		name      string
		fields    fields
		args      args
		wantErr   bool
		isCreated bool
		want      *caPb.AaTransaction
	}{
		{
			name: "successfully create record",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:           context.Background(),
				aaTransaction: transaction,
			},
			wantErr:   false,
			want:      &caPb.AaTransaction{Id: "9031c12f-1ac3-47f1-9208-3fde2000cda4"},
			isCreated: true,
		},
		{
			name: "successfully getting record",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
				aaTransaction: &caPb.AaTransaction{
					TxnId:        "OneMoney_TXN_1",
					AccountId:    "8568c12f-1ac3-47f1-9208-3fde2000cda4",
					DerivedTxnId: "test-derived-txn-id-1",
				},
			},
			wantErr:   false,
			want:      &caPb.AaTransaction{Id: "9031c12f-1ac3-47f1-9208-3fde2000cda4"},
			isCreated: false,
		},
		{
			name: "failure getting record empty id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:           context.Background(),
				aaTransaction: &caPb.AaTransaction{},
			},
			wantErr:   true,
			want:      &caPb.AaTransaction{},
			isCreated: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			c := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := c.CreateOrGet(tt.args.ctx, tt.args.aaTransaction)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrGet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				if tt.isCreated {
					// a new txn should be created
					assert.NotEqual(t, got.Id, tt.want.Id)
				} else {
					// an existing txn should be returned
					assert.Equal(t, got.Id, tt.want.Id)
				}
			}
		})
	}
}

func TestAaTransactionDaoCrdb_SoftDeleteByAccountReference(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx          context.Context
		accountRefId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "delete failed due to empty id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "delete failed on non existent id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: "67ebf15b-6a3b-4fa2-92cd-d6abf209c38a",
			},
			wantErr: false,
		},
		{
			name: "delete successful for account id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			err := a.SoftDeleteByAccountReference(tt.args.ctx, tt.args.accountRefId)
			if (err != nil) != tt.wantErr {
				t.Errorf("SoftDeleteByAccountReference() error = %v, wantErr %v", err, tt.wantErr)
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("SoftDelete() error = %v, wantErr %v", err, tt.wantErr)
			}
			if err == nil {
				got, err := a.GetByAccountId(tt.args.ctx, tt.args.accountRefId)
				assert.Equal(t, err, epifierrors.ErrRecordNotFound)
				assert.Nil(t, got)
			}
		})
	}
}

func TestAaTransactionDaoCrdb_GetByAccountAndDerivedTxnId(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx          context.Context
		accountRefId string
		derivedTxnId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.AaTransaction
		wantErr bool
	}{
		{
			name: "successfully fetch transaction by id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				derivedTxnId: "test-derived-txn-id-1",
				accountRefId: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
			},
			wantErr: false,
		}, {
			name: "failure fetch transaction by id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				derivedTxnId: "random_OneMoney_TXN_1",
				accountRefId: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
			},
			wantErr: true,
		}, {
			name: "failure fetch transaction by empty id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				derivedTxnId: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetByAccountAndDerivedTxnId(tt.args.ctx, tt.args.accountRefId, tt.args.derivedTxnId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountAndDerivedTxnId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaTransactionDaoCrdb_GetByActorIdAndDateRange(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx     context.Context
		actorId string
		after   *timestamppb.Timestamp
		before  *timestamppb.Timestamp
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.AaTransaction
		wantErr bool
	}{
		{
			name: "successful - date 1",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_test_1",
				after:   timestamppb.New(newTimestamp("05-Jun-2017")),
				before:  timestamppb.New(newTimestamp("05-Oct-2017")),
			},
			wantErr: false,
		},
		{
			name: "successful - date 2",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_test_1",
				after:   timestamppb.New(newTimestamp("05-Jun-2017")),
				before:  timestamppb.New(newTimestamp("06-Jun-2017")),
			},
			wantErr: false,
		},
		{
			name: "fail - actorId missing",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "fail - invalid date",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_test_1",
				after:   timestamppb.New(newTimestamp("05-Jun-2017")),
				before:  timestamppb.New(newTimestamp("04-Jun-2017")),
			},
			wantErr: true,
		},
		{
			name: "fail - invalid date 2",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_test_1",
				after:   timestamppb.New(newTimestamp("05-Jul-2017")),
				before:  timestamppb.New(newTimestamp("06-Jul-2017")),
			},
			wantErr: true,
		},
		{
			name: "fail - invalid actor_id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "random_actor",
				after:   timestamppb.New(newTimestamp("05-Jun-2017")),
				before:  timestamppb.New(newTimestamp("06-Jun-2017")),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetByActorIdAndDateRange(tt.args.ctx, tt.args.actorId, tt.args.after, tt.args.before)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdAndDateRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func newTimestamp(t string) time.Time {
	ts, _ := time.Parse("02-Jan-2006", t)
	return ts
}

func TestAaTransactionDaoCrdb_GetByAccountIdAndDateRange(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx          context.Context
		accountRefId string
		after        *timestamppb.Timestamp
		before       *timestamppb.Timestamp
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.AaTransaction
		wantErr bool
	}{
		{
			name: "successful - date 1",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
				after:        timestamppb.New(newTimestamp("05-Jun-2017")),
				before:       timestamppb.New(newTimestamp("05-Oct-2017")),
			},
			wantErr: false,
		},
		{
			name: "successful - date 2",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
				after:        timestamppb.New(newTimestamp("05-Jun-2017")),
				before:       timestamppb.New(newTimestamp("06-Jun-2017")),
			},
			wantErr: false,
		},
		{
			name: "fail - accountRefId missing",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "fail - invalid date",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
				after:        timestamppb.New(newTimestamp("05-Jun-2017")),
				before:       timestamppb.New(newTimestamp("04-Jun-2017")),
			},
			wantErr: true,
		},
		{
			name: "fail - invalid date 2",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
				after:        timestamppb.New(newTimestamp("05-Jul-2017")),
				before:       timestamppb.New(newTimestamp("06-Jul-2017")),
			},
			wantErr: true,
		},
		{
			name: "fail - invalid accountRefId",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: "75a724c6-8eeb-43a0-83da-e04f638e433c", // invalid id
				after:        timestamppb.New(newTimestamp("05-Jun-2017")),
				before:       timestamppb.New(newTimestamp("06-Jun-2017")),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetByAccountIdAndDateRange(tt.args.ctx, tt.args.accountRefId, tt.args.after, tt.args.before)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountIdAndDateRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaTransactionDaoCrdb_GetById(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.AaTransaction
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
				id:  "9031c12f-1ac3-47f1-9208-3fde2000cda4",
			},
			wantErr: false,
		},
		{
			name: "fail - wrong id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
				id:  "472fcec1-cdd7-4bf5-85fb-276465644f0f", // random id
			},
			wantErr: true,
		},
		{
			name: "fail - missing id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaTransactionDaoCrdb_GetBulkByIds(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx context.Context
		ids []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.AaTransaction
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
				ids: []string{"9031c12f-1ac3-47f1-9208-3fde2000cda4", "67ebf15b-6a3b-4fa2-92cd-d6abf209c38a"},
			},
			wantErr: false,
		},
		{
			name: "fail - wrong id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
				ids: []string{"472fcec1-cdd7-4bf5-85fb-276465644f0f"},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetBulkByIds(tt.args.ctx, tt.args.ids)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBulkByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaTransactionDaoCrdb_GetCountForAccount(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx          context.Context
		accountRefId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int32
		wantErr bool
	}{
		{
			name: "empty account id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: "",
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "get count by account id non zero",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: accountFix1.GetId(),
			},
			want:    7,
			wantErr: false,
		},
		{
			name: "get count by account id zero",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				accountRefId: "9ef656fc-3e12-4214-838b-f62485655e40",
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetCountForAccount(tt.args.ctx, tt.args.accountRefId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCountForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.Equal(t, got, tt.want)
			}
		})
	}
}

func TestAaTransactionDaoCrdb_Update(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx        context.Context
		id         string
		txn        *caPb.AaTransaction
		updateMask []caPb.AaTransactionFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.AaTransaction
		wantErr bool
	}{
		{
			name: "#1 empty id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
				id:  "",
				txn: &caPb.AaTransaction{},
				updateMask: []caPb.AaTransactionFieldMask{
					caPb.AaTransactionFieldMask_AA_TRANSACTION_FIELD_MASK_TXN_ID,
					caPb.AaTransactionFieldMask_AA_TRANSACTION_FIELD_MASK_DERIVED_TXN_ID,
					caPb.AaTransactionFieldMask_AA_TRANSACTION_FIELD_MASK_TXN_ID_SOURCE,
				},
			},
			wantErr: true,
		},
		{
			name: "#2 empty update mask",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				id:         "9031c12f-1ac3-47f1-9208-3fde2000cda4",
				txn:        &caPb.AaTransaction{Id: "9031c12f-1ac3-47f1-9208-3fde2000cda4"},
				updateMask: []caPb.AaTransactionFieldMask{},
			},
			wantErr: true,
		},
		{
			name: "#3 successful update",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
				id:  "67ebf15b-6a3b-4fa2-92cd-d6abf209c38a",
				txn: &caPb.AaTransaction{Id: "67ebf15b-6a3b-4fa2-92cd-d6abf209c38a", TxnId: "updated-txn-id", DerivedTxnId: "updated-derived-txn-id", TxnIdSource: caEnumPb.TxnIdSource_TXN_ID_SOURCE_FIP},
				updateMask: []caPb.AaTransactionFieldMask{
					caPb.AaTransactionFieldMask_AA_TRANSACTION_FIELD_MASK_TXN_ID,
					caPb.AaTransactionFieldMask_AA_TRANSACTION_FIELD_MASK_DERIVED_TXN_ID,
					caPb.AaTransactionFieldMask_AA_TRANSACTION_FIELD_MASK_TXN_ID_SOURCE,
				},
			},
			wantErr: false,
		},
		{
			name: "#4 id not found",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx: context.Background(),
				id:  randomUuid,
				txn: &caPb.AaTransaction{Id: randomUuid, TxnId: "updated-txn-id-4", DerivedTxnId: "updated-derived-txn-id-4", TxnIdSource: caEnumPb.TxnIdSource_TXN_ID_SOURCE_FIP},
				updateMask: []caPb.AaTransactionFieldMask{
					caPb.AaTransactionFieldMask_AA_TRANSACTION_FIELD_MASK_TXN_ID,
					caPb.AaTransactionFieldMask_AA_TRANSACTION_FIELD_MASK_DERIVED_TXN_ID,
					caPb.AaTransactionFieldMask_AA_TRANSACTION_FIELD_MASK_TXN_ID_SOURCE,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := a.Update(tt.args.ctx, tt.args.id, tt.args.txn, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestAaTransactionDaoCrdb_GetByAccReferenceId(t *testing.T) {
	t.Parallel()
	nextPageResForFirstPage := &pagePb.PageContextResponse{
		AfterToken: "****************************************************************************************",
		HasAfter:   true,
	}
	nextPageTokenForSecondPageQuery := &pagination.PageToken{}
	err := nextPageTokenForSecondPageQuery.Unmarshal(nextPageResForFirstPage.GetAfterToken())
	if err != nil {
		t.Errorf("error getting sample next page token")
	}
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx                context.Context
		accountReferenceID string
		startTs            *timestamppb.Timestamp
		endTs              *timestamppb.Timestamp
		pageToken          *pagination.PageToken
		pageSize           uint32
	}
	tests := []struct {
		name            string
		fields          fields
		args            args
		want            []*caPb.AaTransaction
		wantErr         bool
		wantPageContext *pagePb.PageContextResponse
	}{
		{
			name: "return first page of txns with next page token when num of txns > page size",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:                context.Background(),
				accountReferenceID: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
				pageToken:          nil,
				pageSize:           2,
			},
			wantErr: false,
			want: []*caPb.AaTransaction{
				AaTransactionResp1,
				AaTransactionResp2,
			},
			wantPageContext: &pagePb.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  "****************************************************************************************",
				HasAfter:    true,
			},
		},
		{
			name: "return all txns with no next page token when num of txns < page size",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:                context.Background(),
				accountReferenceID: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
				pageToken:          nil,
				pageSize:           8,
			},
			wantErr: false,
			want: []*caPb.AaTransaction{
				AaTransactionResp1,
				AaTransactionResp2,
				AaTransactionResp3,
				AaTransactionResp4,
				AaTransactionResp5,
				AaTransactionResp6,
				AaTransactionResp7,
			},
			wantPageContext: noNextPage,
		},
		{
			name: "return 1st page of txns within start time and end time",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:                context.Background(),
				accountReferenceID: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
				startTs:            timestamppb.New(time.Date(2017, 6, 1, 0, 0, 0, 0, datetime.UTC)),
				endTs:              timestamppb.New(time.Date(2017, 6, 6, 1, 37, 0, 0, datetime.UTC)),
				pageToken:          nil,
				pageSize:           1,
			},
			want: []*caPb.AaTransaction{
				AaTransactionResp1,
			},
			wantPageContext: &pagePb.PageContextResponse{
				AfterToken: "****************************************************************************************",
				HasAfter:   true,
			},
		},
		{
			name: "return 2nd page of txns within start time and end time",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:                context.Background(),
				accountReferenceID: "8568c12f-1ac3-47f1-9208-3fde2000cda4",
				startTs:            timestamppb.New(time.Date(2017, 6, 1, 0, 0, 0, 0, datetime.UTC)),
				endTs:              timestamppb.New(time.Date(2017, 6, 6, 1, 37, 0, 0, datetime.UTC)),
				pageToken:          nextPageTokenForSecondPageQuery,
				pageSize:           1,
			},
			want: []*caPb.AaTransaction{
				AaTransactionResp2,
			},
			wantPageContext: &pagePb.PageContextResponse{
				BeforeToken: "****************************************************************************************",
				HasBefore:   true,
			},
		},
		{
			name: "invalid account id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:                context.Background(),
				accountReferenceID: "9568c12f-1ac3-47f1-9208-3fde2000cda4",
				pageToken:          nil,
				pageSize:           0,
			},
			wantErr:         false,
			want:            nil,
			wantPageContext: noNextPage,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, pageContext, err := a.GetByAccReferenceId(tt.args.ctx,
				tt.args.accountReferenceID, tt.args.startTs, tt.args.endTs, tt.args.pageToken, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccReferenceId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(pageContext, tt.wantPageContext, protocmp.Transform()); diff != "" {
				t.Errorf("unexpected page context:\n%v", diff)
			}
			if err == nil {
				if len(got) != len(tt.want) {
					t.Errorf("GetByAccReferenceId() want = %v, got %v", tt.want, got)
				}
				for idx := 0; idx < len(got); idx++ {
					got[idx].CreatedAt = tt.want[idx].CreatedAt
					got[idx].UpdatedAt = tt.want[idx].UpdatedAt
					if diff := cmp.Diff(got[idx], tt.want[idx], protocmp.Transform()); diff != "" {
						t.Errorf("unexpected difference:\n%v", diff)
					}
				}
			}
		})
	}
}

func TestAaTransactionDaoCrdb_GetTotalTxnByActorIdAndAccRefId(t *testing.T) {
	t.Parallel()
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx          context.Context
		actorId      string
		accountRefId []string
		startTime    *timestamppb.Timestamp
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.AccountIdAndTotalTransactions
		wantErr bool
	}{
		{
			name: "empty actor id",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				actorId:      "",
				accountRefId: []string{},
			},
			want:    []*caPb.AccountIdAndTotalTransactions{},
			wantErr: true,
		},
		{
			name: "get count by actorId, account ids for timestamp-1",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				actorId:      accountFix1.GetActorId(),
				accountRefId: []string{accountFix1.GetId()},
				startTime: &timestamppb.Timestamp{
					Seconds: **********,
					Nanos:   0,
				},
			},
			want: []*caPb.AccountIdAndTotalTransactions{
				{
					AccountReferenceId: accountFix1.GetId(),
					TotalTransactions:  7,
				},
			},
			wantErr: false,
		},
		{
			name: "get count by actorId, account id for timestamp-2",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				actorId:      accountFix1.GetActorId(),
				accountRefId: []string{accountFix1.GetId()},
				startTime: &timestamppb.Timestamp{
					Seconds: **********,
					Nanos:   0,
				},
			},
			want: []*caPb.AccountIdAndTotalTransactions{
				{
					AccountReferenceId: accountFix1.GetId(),
					TotalTransactions:  6,
				},
			},
			wantErr: false,
		},
		{
			name: "get count by actorId, account id for timestamp-3",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				actorId:      accountFix1.GetActorId(),
				accountRefId: []string{accountFix1.GetId()},
				startTime: &timestamppb.Timestamp{
					Seconds: **********,
					Nanos:   0,
				},
			},
			want:    []*caPb.AccountIdAndTotalTransactions{},
			wantErr: false,
		},
		{
			name: "get count by random actorId",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				actorId:      "random_actor_id",
				accountRefId: []string{accountFix1.GetId()},
			},
			want:    []*caPb.AccountIdAndTotalTransactions{},
			wantErr: false,
		},
		{
			name: "when start time is nil",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				actorId:      accountFix1.GetActorId(),
				accountRefId: []string{accountFix1.GetId()},
				startTime:    nil,
			},
			want: []*caPb.AccountIdAndTotalTransactions{
				{
					AccountReferenceId: accountFix1.GetId(),
					TotalTransactions:  7,
				},
			},
			wantErr: false,
		},
		{
			name: "when no account id is provided",
			fields: fields{
				DB:    atdts.db,
				gconf: atdts.gconf,
			},
			args: args{
				ctx:          context.Background(),
				actorId:      accountFix1.GetActorId(),
				accountRefId: []string{},
				startTime:    nil,
			},
			want: []*caPb.AccountIdAndTotalTransactions{
				{
					AccountReferenceId: accountFix1.GetId(),
					TotalTransactions:  7,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tx := atdts.db.Begin()
			defer tx.Rollback()
			pkgTest.TruncateAndPopulateRdsFixtures(t, tx, atdts.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			a := &AaTransactionDaoImpl{
				Pgdb: tx,
			}
			got, err := a.GetTotalTxnByActorIdAndAccRefId(tt.args.ctx, tt.args.actorId, tt.args.accountRefId, tt.args.startTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCountForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
					t.Errorf("unexpected difference:\n%v", diff)
				}
			}
		})
	}
}
