package model

import (
	"time"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/nulltypes"
	"github.com/epifi/gamma/api/connected_account/analytics"
)

type AnalysisRequest struct {
	Id          string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	OrchId      string
	ActorID     string
	Details     *analytics.Details `gorm:"type:jsonb;default:'null';column:details"`
	Status      analytics.AttemptStatus
	CompletedAt nulltypes.NullTime
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at"`
}

func (m *AnalysisRequest) TableName() string {
	return "aa_analysis_requests"
}

func NewAnalysisRequest(proto *analytics.AnalysisRequest) *AnalysisRequest {
	model := &AnalysisRequest{
		Id:      proto.GetId(),
		OrchId:  proto.GetOrchId(),
		ActorID: proto.GetActorId(),
		Details: proto.GetDetails(),
		Status:  proto.GetStatus(),
	}
	if proto.GetCompletedAt().IsValid() {
		model.CompletedAt = nulltypes.NewNullTime(proto.GetCompletedAt().AsTime())
	}
	if proto.GetDeletedAt().IsValid() {
		model.DeletedAt = gorm.DeletedAt{
			Time:  proto.GetDeletedAt().AsTime(),
			Valid: true,
		}
	}
	return model
}

func (m *AnalysisRequest) GetProto() *analytics.AnalysisRequest {
	if m == nil {
		return nil
	}
	proto := &analytics.AnalysisRequest{
		Id:        m.Id,
		OrchId:    m.OrchId,
		ActorId:   m.ActorID,
		Details:   m.Details,
		Status:    m.Status,
		CreatedAt: timestamp.New(m.CreatedAt),
		UpdatedAt: timestamp.New(m.UpdatedAt),
	}
	if m.CompletedAt.Valid {
		proto.CompletedAt = timestamp.New(m.CompletedAt.GetValue())
	}
	if m.DeletedAt.Valid {
		proto.DeletedAt = timestamp.New(m.DeletedAt.Time)
	}
	return proto
}
