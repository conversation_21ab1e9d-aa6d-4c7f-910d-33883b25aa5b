//nolint:dupl
package processor

import (
	"context"
	"errors"

	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/developer"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/connectedaccount/dao"
)

type DevCaDataAttemptEntity struct {
	fetchAttemptDao   dao.DataFetchAttemptDao
	processAttemptDao dao.DataProcessAttemptDao
	batchProcessDao   dao.AaBatchProcessDao
}

func NewDevDataAttemptEntity(fetchAttemptDao dao.DataFetchAttemptDao, processAttemptDao dao.DataProcessAttemptDao, batchProcessDao dao.AaBatchProcessDao) *DevCaDataAttemptEntity {
	return &DevCaDataAttemptEntity{
		fetchAttemptDao:   fetchAttemptDao,
		processAttemptDao: processAttemptDao,
		batchProcessDao:   batchProcessDao,
	}
}

func (d *DevCaDataAttemptEntity) FetchParamList(ctx context.Context, entity developer.CaEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            FetchAttemptId,
			Label:           FetchAttemptIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ProcessAttemptId,
			Label:           ProcessAttemptIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            BatchId,
			Label:           BatchIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            SessionId,
			Label:           SessionIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ConsentReferenceId,
			Label:           ConsentReferenceIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ActorId,
			Label:           ActorIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            XAttempts,
			Label:           XAttemptsLabel,
			Type:            db_state.ParameterDataType_INTEGER,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevCaDataAttemptEntity) FetchData(ctx context.Context, entity developer.CaEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var fetchAttemptId, processAttemptId, batchId, sessionId, actorId, consentRefId string
	xAttempts := 100
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case FetchAttemptId:
			fetchAttemptId = filter.GetStringValue()
		case ProcessAttemptId:
			processAttemptId = filter.GetStringValue()
		case BatchId:
			batchId = filter.GetStringValue()
		case SessionId:
			sessionId = filter.GetStringValue()
		case ConsentReferenceId:
			consentRefId = filter.GetStringValue()
		case ActorId:
			actorId = filter.GetStringValue()
		case XAttempts:
			xAttempts = (int)(filter.GetIntegerValue())
		}
	}

	if fetchAttemptId != "" {
		return d.getDataEntityByFetchId(ctx, fetchAttemptId)
	}

	if processAttemptId != "" {
		return d.getDataEntityByProcessId(ctx, processAttemptId)
	}

	if batchId != "" {
		return d.getDataEntityByBatchId(ctx, batchId)
	}
	if sessionId != "" {
		return d.getDataEntityBySessionId(ctx, sessionId)
	}

	if consentRefId != "" && xAttempts > 0 {
		return d.getDataEntityByConsentReferenceId(ctx, consentRefId, xAttempts)
	}

	if actorId != "" && xAttempts > 0 {
		return d.getLastXAttemptsDataEntityByActorId(ctx, actorId, xAttempts)
	}

	return "", nil
}

func (d *DevCaDataAttemptEntity) getDataEntityByConsentReferenceId(ctx context.Context, consentRefId string, limit int) (string, error) {
	resl := &developer.DataEntityResponseList{}

	fetchAttempts, _, fetchAttemptsErr := d.fetchAttemptDao.GetDataFetchAttemptsPaginatedAndPurpose(ctx, consentRefId, nil, uint32(limit),
		[]caEnumPb.DataFetchAttemptPurpose{caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
			caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
			caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_BACKFILL})
	if fetchAttemptsErr != nil {
		logger.Error(ctx, "error while querying data using consent_reference_id", zap.Error(fetchAttemptsErr), zap.String("consent_reference_id", consentRefId))
		return fetchDataErr, nil
	}

	for _, fetchAttempt := range fetchAttempts {
		resp := &developer.DataEntityResponse{}
		resp.DataFetchAttempt = fetchAttempt

		processAttempt, processAttemptErr := d.processAttemptDao.GetByFetchAttemptId(ctx, fetchAttempt.GetId())
		if processAttemptErr != nil {
			logger.Error(ctx, "error while querying process attempt using attempt_id", zap.Error(processAttemptErr), zap.String("fetch_attempt_id", fetchAttempt.GetId()))
		}
		resp.DataProcessAttemptList = processAttempt
		for _, dpa := range resp.DataProcessAttemptList {
			batches, batchesErr := d.batchProcessDao.GetByFetchAndProcessAttempt(ctx, fetchAttempt.GetId(), dpa.GetId())
			if batchesErr != nil {
				logger.Error(ctx, "error while fetching batches using process_attempt_id", zap.Error(batchesErr), zap.String("process_attempt_id", dpa.GetId()), zap.String("fetch_attempt_id", fetchAttempt.GetId()))
				continue
			}
			resp.BatchList = append(resp.BatchList, batches...)
		}

		resl.DataEntityResponseList = append(resl.GetDataEntityResponseList(), resp)
	}

	res, err := protojson.Marshal(resl)
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
		return marshalErr, nil
	}

	return string(res), nil
}

func (d *DevCaDataAttemptEntity) getLastXAttemptsDataEntityByActorId(ctx context.Context, actorId string, xAttempts int) (string, error) {
	resl := &developer.DataEntityResponseList{}

	fetchAttempts, fetchAttemptsErr := d.fetchAttemptDao.GetByActorIdAndPurpose(ctx, actorId, xAttempts,
		[]caEnumPb.DataFetchAttemptPurpose{caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
			caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED,
			caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_BACKFILL,
		})
	if fetchAttemptsErr != nil {
		logger.Error(ctx, "error while querying data using actor_id", zap.Error(fetchAttemptsErr), zap.String("actor_id", ActorId))
		return fetchDataErr, nil
	}

	for _, fetchAttempt := range fetchAttempts {
		resp := &developer.DataEntityResponse{}
		resp.DataFetchAttempt = fetchAttempt

		processAttempt, processAttemptErr := d.processAttemptDao.GetByFetchAttemptId(ctx, fetchAttempt.GetId())
		if processAttemptErr != nil {
			logger.Error(ctx, "error while querying process attempt using attempt_id", zap.Error(processAttemptErr), zap.String("fetch_attempt_id", fetchAttempt.GetId()))
		}
		resp.DataProcessAttemptList = processAttempt
		for _, dpa := range resp.DataProcessAttemptList {
			batches, batchesErr := d.batchProcessDao.GetByFetchAndProcessAttempt(ctx, fetchAttempt.GetId(), dpa.GetId())
			if batchesErr != nil {
				logger.Error(ctx, "error while fetching batches using process_attempt_id", zap.Error(batchesErr), zap.String("process_attempt_id", dpa.GetId()), zap.String("fetch_attempt_id", fetchAttempt.GetId()))
				continue
			}
			resp.BatchList = append(resp.BatchList, batches...)
		}

		resl.DataEntityResponseList = append(resl.GetDataEntityResponseList(), resp)
	}

	res, err := protojson.Marshal(resl)
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
		return marshalErr, nil
	}

	return string(res), nil
}

func (d *DevCaDataAttemptEntity) getDataEntityByFetchId(ctx context.Context, fetchAttemptId string) (string, error) {
	resp := &developer.DataEntityResponse{}
	fetchAttempt, fetchAttemptErr := d.fetchAttemptDao.GetByAttemptId(ctx, fetchAttemptId)
	if fetchAttemptErr != nil {
		logger.Error(ctx, "error while querying data using fetch attempt", zap.Error(fetchAttemptErr), zap.String("fetch_attempt_id", fetchAttemptId))
		return fetchDataErr, nil
	}
	resp.DataFetchAttempt = fetchAttempt
	processAttempt, processAttemptErr := d.processAttemptDao.GetByFetchAttemptId(ctx, fetchAttempt.GetId())
	if processAttemptErr != nil {
		logger.Error(ctx, "error while querying process attempt using attempt_id", zap.Error(processAttemptErr), zap.String("fetch_attempt_id", fetchAttemptId))
	}
	resp.DataProcessAttemptList = processAttempt
	for _, dpa := range resp.DataProcessAttemptList {
		batches, batchesErr := d.batchProcessDao.GetByFetchAndProcessAttempt(ctx, fetchAttemptId, dpa.GetId())
		if batchesErr != nil {
			logger.Error(ctx, "error while fetching batches using process_attempt_id", zap.Error(batchesErr), zap.String("process_attempt_id", dpa.GetId()), zap.String("fetch_attempt_id", fetchAttemptId))
			continue
		}
		resp.BatchList = append(resp.BatchList, batches...)
	}
	res, err := protojson.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
		return marshalErr, nil
	}
	return string(res), nil
}

func (d *DevCaDataAttemptEntity) getDataEntityByProcessId(ctx context.Context, processAttemptId string) (string, error) {
	resp := &developer.DataEntityResponse{}
	processAttempt, processAttemptErr := d.processAttemptDao.GetById(ctx, processAttemptId)
	if processAttemptErr != nil {
		logger.Error(ctx, "error while querying process attempt using attempt_id", zap.Error(processAttemptErr), zap.String("process_attempt_id", processAttemptId))
		return fetchDataErr, processAttemptErr
	}
	fetchAttempt, fetchAttemptErr := d.fetchAttemptDao.GetByAttemptId(ctx, processAttempt.GetAttemptId())
	if fetchAttemptErr != nil {
		logger.Error(ctx, "error while querying data using fetch attempt", zap.Error(fetchAttemptErr), zap.String("fetch_attempt_id", processAttempt.GetAttemptId()))
		return fetchDataErr, fetchAttemptErr
	}
	resp.DataFetchAttempt = fetchAttempt
	resp.DataProcessAttemptList = []*caPb.DataProcessAttempt{processAttempt}
	for _, dpa := range resp.DataProcessAttemptList {
		batches, batchesErr := d.batchProcessDao.GetByFetchAndProcessAttempt(ctx, fetchAttempt.GetId(), dpa.GetId())
		if batchesErr != nil {
			logger.Error(ctx, "error while fetching batches using process_attempt_id", zap.Error(batchesErr), zap.String("process_attempt_id", dpa.GetId()), zap.String("fetch_attempt_id", processAttempt.GetAttemptId()))
			continue
		}
		resp.BatchList = append(resp.BatchList, batches...)
	}
	res, err := protojson.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
		return marshalErr, err
	}
	return string(res), nil
}

func (d *DevCaDataAttemptEntity) getDataEntityByBatchId(ctx context.Context, batchId string) (string, error) {
	resp := &developer.DataEntityResponse{}
	batchProcessTxn, batchProcessTxnErr := d.batchProcessDao.Get(ctx, batchId)
	if batchProcessTxnErr != nil {
		logger.Error(ctx, "error while fetching batches using process_attempt_id", zap.Error(batchProcessTxnErr), zap.String(logger.BATCH_ID, batchId))
		return fetchDataErr, batchProcessTxnErr
	}
	fetchAttemptId := batchProcessTxn.GetFetchAttemptId()
	fetchAttempt, fetchAttemptErr := d.fetchAttemptDao.GetByAttemptId(ctx, fetchAttemptId)
	if fetchAttemptErr != nil {
		logger.Error(ctx, "error while querying data using fetch attempt", zap.Error(fetchAttemptErr), zap.String("fetch_attempt_id", fetchAttemptId))
		return fetchDataErr, fetchAttemptErr
	}

	resp.DataFetchAttempt = fetchAttempt
	processAttempt, processAttemptErr := d.processAttemptDao.GetByFetchAttemptId(ctx, fetchAttemptId)
	if processAttemptErr != nil {
		logger.Error(ctx, "error while querying process attempt using fetch_attempt_id", zap.Error(processAttemptErr), zap.String("fetch_attempt_id", fetchAttemptId))
		return fetchDataErr, processAttemptErr
	}

	resp.DataProcessAttemptList = processAttempt
	resp.BatchList = []*caPb.BatchProcessTransaction{batchProcessTxn}
	res, err := protojson.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
		return marshalErr, nil
	}
	return string(res), nil
}

func (d *DevCaDataAttemptEntity) getDataEntityBySessionId(ctx context.Context, sessionId string) (string, error) {
	resp := &developer.DataEntityResponse{}
	fetchAttempt, fetchAttemptErr := d.fetchAttemptDao.GetBySessionId(ctx, sessionId, nil)
	if fetchAttemptErr != nil {
		logger.Error(ctx, "error while querying data using fetch attempt", zap.Error(fetchAttemptErr),
			zap.String(logger.ATTEMPT_ID, fetchAttempt.GetId()))
		return fetchDataErr, nil
	}
	resp.DataFetchAttempt = fetchAttempt
	processAttempt, processAttemptErr := d.processAttemptDao.GetByFetchAttemptId(ctx, fetchAttempt.GetId())
	if processAttemptErr != nil {
		logger.Error(ctx, "error while querying process attempt using attempt_id", zap.Error(processAttemptErr),
			zap.String(logger.ATTEMPT_ID, fetchAttempt.GetId()))
	}
	resp.DataProcessAttemptList = processAttempt
	for _, dpa := range resp.DataProcessAttemptList {
		batches, batchesErr := d.batchProcessDao.GetByFetchAndProcessAttempt(ctx, fetchAttempt.GetId(), dpa.GetId())
		if batchesErr != nil {
			logger.Error(ctx, "error while fetching batches using process_attempt_id", zap.Error(batchesErr),
				zap.String(logger.ATTEMPT_ID, fetchAttempt.GetId()))
			continue
		}
		resp.BatchList = append(resp.BatchList, batches...)
	}
	res, err := protojson.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
		return marshalErr, nil
	}
	return string(res), nil
}
