package analytics

import (
	"context"
	"fmt"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/workflow"
	ignosisVgPb "github.com/epifi/gamma/api/vendorgateway/aa/analytics/ignosis"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	config "github.com/epifi/gamma/connectedaccount/config/worker"
	caDao "github.com/epifi/gamma/connectedaccount/dao"
)

type Processor struct {
	conf            *config.Config
	arDao           caDao.AnalysisRequestsDao
	auDao           caDao.AnalysedUserDao
	ignosisVgClient ignosisVgPb.IgnosisAaAnalyticsServiceClient
	vmClient        vmPb.VendorMappingServiceClient
	caClient        caPb.ConnectedAccountClient
	s3Client        s3.S3Client
}

func NewProcessor(
	conf *config.Config,
	arDao caDao.AnalysisRequestsDao,
	auDao caDao.AnalysedUserDao,
	ignosisVgClient ignosisVgPb.IgnosisAaAnalyticsServiceClient,
	vmClient vmPb.VendorMappingServiceClient,
	caClient caPb.ConnectedAccountClient,
	s3Client s3.S3Client,
) *Processor {
	return &Processor{
		conf:            conf,
		arDao:           arDao,
		auDao:           auDao,
		ignosisVgClient: ignosisVgClient,
		vmClient:        vmClient,
		caClient:        caClient,
		s3Client:        s3Client,
	}
}

func (p *Processor) UpdateAnalysisRequest(ctx context.Context, req *workflow.UpdateAnalysisRequestRequest) (*workflow.UpdateAnalysisRequestResponse, error) {
	lg := activity.GetLogger(ctx)
	ctx = epificontext.CtxWithActorId(ctx, req.GetAnalysisRequest().GetActorId())
	err := p.arDao.Update(ctx, req.GetAnalysisRequest(), req.GetUpdateFieldMasks())
	if err != nil {
		lg.Error("error updating analysis request", zap.Error(err), zap.String(logger.ATTEMPT_ID, req.GetAnalysisRequest().GetId()))
		return nil, epifitemporal.NewTransientError(fmt.Errorf("error updating analysis request with id, err: %v", err))
	}
	return &workflow.UpdateAnalysisRequestResponse{}, nil
}
