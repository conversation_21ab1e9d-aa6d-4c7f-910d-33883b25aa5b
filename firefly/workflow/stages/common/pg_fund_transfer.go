// nolint
package common

import (
	"go.temporal.io/sdk/workflow"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	ffActPb "github.com/epifi/gamma/api/firefly/activity"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/firefly/workflow/stages"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"
)

type PaymentGatewayFundTransfer struct {
	*PostProcessStageCommon
	*PreProcessStageCommon
	*DeeplinkCommon
}

func NewPaymentGatewayFundTransfer() *PaymentGatewayFundTransfer {
	return &PaymentGatewayFundTransfer{}
}

var _ stages.Stage = &PaymentGatewayFundTransfer{}

func (t *PaymentGatewayFundTransfer) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		CardRequest:      req.CardRequest,
		CardRequestStage: req.CardRequestStage,
	}
	actErr := activityPkg.Execute(ctx, ffNs.PerformPaymentGatewayFundTransfer, &ffActPb.PerformPennyDropResponse{}, &ffActPb.PerformPennyDropRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
		},
	})
	if actErr != nil {
		res.ActivityError = actErr
		return res, nil
	}
	return res, nil
}

func (t *PaymentGatewayFundTransfer) GetStageName() ffEnumsPb.CardRequestStageName {
	return ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PAYMENT_GATEWAY_FUND_TRANSFER
}

func (t *PaymentGatewayFundTransfer) GetCelestialStageName() epifitemporal.Stage {
	return ffNs.PennyDrop
}
