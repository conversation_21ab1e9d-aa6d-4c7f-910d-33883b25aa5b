package common_test

import (
	"os"
	"testing"

	"github.com/google/uuid"

	fireflyPb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/firefly/test"
	workflow2 "github.com/epifi/gamma/firefly/workflow"
	"github.com/epifi/gamma/firefly/workflow/stages/common"
	epifitemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"
)

var wts epifitemporalTest.WorkflowTestSuite

// nolint: dogsled
func TestMain(m *testing.M) {
	_, _, _, _, _, _, _, _, teardown := test.InitTestServer()
	wts.SetLogger(epifitemporalLogging.NewZapAdapter(logger.Log))
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

const (
	ownership         = workflow2.OWNERSHIP
	defaultCardReqId  = "default-card-req-id"
	defaultWorkflowID = "default-test-workflow-id"
)

var (
	clientReqId = uuid.New().String()
	cardReq     = &fireflyPb.CardRequest{
		Id:     defaultCardReqId,
		CardId: "default-card-id",
	}
	cardReqStage = &fireflyPb.CardRequestStage{
		Id:              "sample-card-req-id",
		CardRequestId:   defaultCardReqId,
		OrchestrationId: clientReqId,
	}
	authStage             = &common.Auth{}
	preprocessStageCommon = &common.PreProcessStageCommon{}
)
