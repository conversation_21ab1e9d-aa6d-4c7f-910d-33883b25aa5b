//nolint:dupl
package impl

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"gorm.io/plugin/dbresolver"

	ccFireFlyPb "github.com/epifi/gamma/api/firefly"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
)

var (
	sampleCardRequestStage1 = &ccFireFlyPb.CardRequestStage{
		CardRequestId:         "a5004f18-5d52-4991-82a9-xa1e5010e991",
		OrchestrationId:       "orch_id_1",
		Stage:                 ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UNSPECIFIED,
		ExternalRequestId:     "vendor_request_id_1",
		StageExecutionDetails: &ccFireFlyPb.CardRequestStageExecutionDetails{},
		Status:                ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_UNSPECIFIED,
		SubStatus:             ccEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED,
	}
	sampleCardRequestStageWithId = &ccFireFlyPb.CardRequestStage{
		Id:                    "a5004f18-5d52-4991-82a9-xa1e5010e993",
		CardRequestId:         "a5004f18-5d52-4991-82a9-xa1e5010e991",
		OrchestrationId:       "orch_id_1",
		Stage:                 ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UNSPECIFIED,
		StageExecutionDetails: &ccFireFlyPb.CardRequestStageExecutionDetails{},
		Status:                ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_UNSPECIFIED,
		SubStatus:             ccEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED,
	}
	sampleCardRequestStageWithIdAndOrchId = &ccFireFlyPb.CardRequestStage{
		Id:              "a5004f18-5d52-4991-82a9-xa1e5010e993",
		OrchestrationId: "orch_id_1",
		CardRequestId:   "a5004f18-5d52-4991-82a9-xa1e5010e991",
	}
)

func TestCardRequestStageDao_Create(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *ccFireFlyPb.CardRequestStage
	}
	tests := []struct {
		name    string
		args    args
		want    *ccFireFlyPb.CardRequestStage
		wantErr bool
		err     error
	}{
		{
			name: "successful creation flow",
			args: args{
				ctx:     context.Background(),
				request: sampleCardRequestStage1,
			},
			want: sampleCardRequestStage1,
		},
	}
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := NewCrdbCardRequestStage(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"card_request_stages"})
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.Create(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.err)
				}
			}
			tt.want.Id = got.Id
			tt.want.CreatedAt = got.CreatedAt
			tt.want.UpdatedAt = got.UpdatedAt
			tt.want.StaledAt = got.StaledAt
			tt.want.CompletedAt = got.CompletedAt
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCardRequestStageDao_GetByCardRequestIdAndStage(t *testing.T) {
	type args struct {
		ctx           context.Context
		cardRequestId string
		stage         ccEnumsPb.CardRequestStageName
		fields        []ccEnumsPb.CardRequestStageFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *ccFireFlyPb.CardRequestStage
		wantErr bool
		err     error
	}{
		{
			name: "successful fetch flow",
			args: args{
				ctx:           context.Background(),
				cardRequestId: "a5004f18-5d52-4991-82a9-xa1e5010e991",
				stage:         ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UNSPECIFIED,
				fields: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			want: sampleCardRequestStageWithIdAndOrchId,
		},
		{
			name: "invalid card request id flow",
			args: args{
				ctx:           context.Background(),
				cardRequestId: "invalid_card_request_id",
				stage:         ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UNSPECIFIED,
				fields: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "empty card request id",
			args: args{
				ctx:           context.Background(),
				cardRequestId: "",
				stage:         0,
				fields: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			wantErr: true,
		},
	}
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := NewCrdbCardRequestStage(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"card_request_stages"})
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByCardRequestIdAndStage(tt.args.ctx, tt.args.cardRequestId, tt.args.stage, tt.args.fields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByCardRequestIdAndStage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByCardRequestIdAndStage() error = %v, wantErr %v", err, tt.err)
				}
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByCardRequestIdAndStage() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCardRequestStageDao_GetById(t *testing.T) {
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := NewCrdbCardRequestStage(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"card_request_stages"})

	type args struct {
		ctx    context.Context
		id     string
		fields []ccEnumsPb.CardRequestStageFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *ccFireFlyPb.CardRequestStage
		wantErr bool
		err     error
	}{
		{
			name: "Successful record fetch flow",
			args: args{
				ctx: context.Background(),
				id:  "a5004f18-5d52-4991-82a9-xa1e5010e993",
				fields: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			wantErr: false,
			want:    sampleCardRequestStageWithIdAndOrchId,
		},
		{
			name: "invalid id flow",
			args: args{
				ctx: context.Background(),
				id:  "invalid_id",
				fields: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "empty id flow",
			args: args{
				ctx: context.Background(),
				id:  "",
				fields: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetById(tt.args.ctx, tt.args.id, tt.args.fields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.err)
				}
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCardRequestStageDao_GetByOrchestrationId(t *testing.T) {
	type args struct {
		ctx             context.Context
		orchestrationId string
		fields          []ccEnumsPb.CardRequestStageFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *ccFireFlyPb.CardRequestStage
		wantErr bool
		err     error
	}{
		{
			name: "successful fetch",
			args: args{
				ctx:             context.Background(),
				orchestrationId: "orch_id_1",
				fields: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			want: sampleCardRequestStageWithIdAndOrchId,
		},
		{
			name: "invalid orch id flow",
			args: args{
				ctx:             context.Background(),
				orchestrationId: "invalid_orch_id",
				fields: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "empty orch id flow",
			args: args{
				ctx:             context.Background(),
				orchestrationId: "",
				fields: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			wantErr: true,
		},
	}
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := NewCrdbCardRequestStage(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"card_request_stages"})
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByOrchestrationId(tt.args.ctx, tt.args.orchestrationId, tt.args.fields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByOrchestrationId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByOrchestrationId() error = %v, wantErr %v", err, tt.err)
				}
			}
			if got != tt.want && (got.GetOrchestrationId() != tt.want.GetOrchestrationId() || got.GetId() != tt.want.GetId()) {
				t.Errorf("GetByOrchestrationId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCardRequestStageDao_Update(t *testing.T) {
	type args struct {
		ctx         context.Context
		request     *ccFireFlyPb.CardRequestStage
		updateMasks []ccEnumsPb.CardRequestStageFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		err     error
	}{
		{
			name: "successful update flow",
			args: args{
				ctx:     context.Background(),
				request: sampleCardRequestStageWithId,
				updateMasks: []ccEnumsPb.CardRequestStageFieldMask{
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
					ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ORCH_ID,
				},
			},
			wantErr: false,
		},
		{
			name: "empty field mask flow",
			args: args{
				ctx:         context.Background(),
				request:     sampleCardRequestStageWithId,
				updateMasks: []ccEnumsPb.CardRequestStageFieldMask{},
			},
			wantErr: true,
		},
	}
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := NewCrdbCardRequestStage(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"card_request_stages"})
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := c.Update(tt.args.ctx, tt.args.request, tt.args.updateMasks); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCardRequestStageDao_GetByCardRequestId(t *testing.T) {
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := NewCrdbCardRequestStage(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"card_request_stages"})
	var (
		err error
	)
	cardRequestStage1 := &ccFireFlyPb.CardRequestStage{
		CardRequestId: "cc-req-1",
		Stage:         ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UPDATE_CARD_DELIVERY_ADDRESS,
	}
	cardRequestStage1, err = c.Create(context.Background(), cardRequestStage1)
	if err != nil {
		t.Errorf("error in creating card request stage 1 %v", err)
	}
	cardRequestStage2 := &ccFireFlyPb.CardRequestStage{
		CardRequestId: "cc-req-1",
		Stage:         ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_BLOCK_CARD,
	}
	cardRequestStage2, err = c.Create(context.Background(), cardRequestStage2)
	if err != nil {
		t.Errorf("error in creating card request stage 1 %v", err)
	}
	cardRequestStage2.StageExecutionDetails = nil
	cardRequestStage1.StageExecutionDetails = nil

	tests := []struct {
		name           string
		cardRequestId  string
		selectedFields []ccEnumsPb.CardRequestStageFieldMask
		want           []*ccFireFlyPb.CardRequestStage
		wantErr        bool
	}{
		{
			name:          "successfully fetched all card request stages",
			cardRequestId: "cc-req-1",
			selectedFields: []ccEnumsPb.CardRequestStageFieldMask{ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STAGE,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CARD_REQUEST_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CREATED_AT,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_UPDATED_AT},
			want:    []*ccFireFlyPb.CardRequestStage{cardRequestStage2, cardRequestStage1},
			wantErr: false,
		},
		{
			name:          "failed to fetch card request stages",
			cardRequestId: "cc-req-2",
			selectedFields: []ccEnumsPb.CardRequestStageFieldMask{ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STAGE,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CARD_REQUEST_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CREATED_AT,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_UPDATED_AT},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByCardRequestId(context.Background(), tt.cardRequestId, tt.selectedFields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByCardRequestId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByCardRequestId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCardRequestStageDao_GetLatestStageByCardRequestId(t *testing.T) {
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := NewCrdbCardRequestStage(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"card_request_stages"})
	var (
		err error
	)
	cardRequestStage1 := &ccFireFlyPb.CardRequestStage{
		CardRequestId: "cc-req-1",
		Stage:         ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UPDATE_CARD_DELIVERY_ADDRESS,
	}
	cardRequestStage1, err = c.Create(context.Background(), cardRequestStage1)
	if err != nil {
		t.Errorf("error in creating card request stage 1 %v", err)
	}
	cardRequestStage2 := &ccFireFlyPb.CardRequestStage{
		CardRequestId: "cc-req-1",
		Stage:         ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_BLOCK_CARD,
	}
	cardRequestStage2, err = c.Create(context.Background(), cardRequestStage2)
	if err != nil {
		t.Errorf("error in creating card request stage 1 %v", err)
	}
	cardRequestStage2.StageExecutionDetails = nil
	cardRequestStage1.StageExecutionDetails = nil
	tests := []struct {
		name           string
		cardRequestId  string
		selectedFields []ccEnumsPb.CardRequestStageFieldMask
		want           *ccFireFlyPb.CardRequestStage
		wantErr        bool
	}{
		{
			name:          "successfully fetched latest card req stage",
			cardRequestId: "cc-req-1",
			selectedFields: []ccEnumsPb.CardRequestStageFieldMask{ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STAGE,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CARD_REQUEST_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CREATED_AT,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_UPDATED_AT},
			want:    cardRequestStage2,
			wantErr: false,
		},
		{
			name:          "failed to fetch card request stage",
			cardRequestId: "cc-req-2",
			selectedFields: []ccEnumsPb.CardRequestStageFieldMask{ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STAGE,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CARD_REQUEST_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CREATED_AT,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_UPDATED_AT},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetLatestStageByCardRequestId(context.Background(), tt.cardRequestId, tt.selectedFields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLatestStageByCardRequestId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLatestStageByCardRequestId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCardRequestStageDao_GetByExternalRequestIdAndStage(t *testing.T) {
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := NewCrdbCardRequestStage(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"card_request_stages"})
	var (
		err error
	)
	cardRequestStage1 := &ccFireFlyPb.CardRequestStage{
		CardRequestId:     "cc-req-1",
		ExternalRequestId: "ext-req-1",
		Stage:             ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UPDATE_CARD_DELIVERY_ADDRESS,
	}
	cardRequestStage1, err = c.Create(context.Background(), cardRequestStage1)
	if err != nil {
		t.Errorf("error in creating card request stage 1 %v", err)
	}
	cardRequestStage1.StageExecutionDetails = nil

	tests := []struct {
		name           string
		externalReqId  string
		stage          ccEnumsPb.CardRequestStageName
		selectedFields []ccEnumsPb.CardRequestStageFieldMask
		want           *ccFireFlyPb.CardRequestStage
		wantErr        bool
	}{
		{
			name:          "successfully fetched card req stage",
			externalReqId: "ext-req-1",
			stage:         ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UPDATE_CARD_DELIVERY_ADDRESS,
			selectedFields: []ccEnumsPb.CardRequestStageFieldMask{ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STAGE,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CARD_REQUEST_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CREATED_AT,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_UPDATED_AT,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_EXTERNAL_REQUEST_ID},
			want:    cardRequestStage1,
			wantErr: false,
		},
		{
			name:          "failed to fetch card request stage",
			externalReqId: "ext-req-1",
			stage:         ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_BILLING_DAYS_CAPTURE,
			selectedFields: []ccEnumsPb.CardRequestStageFieldMask{ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STAGE,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CARD_REQUEST_ID,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_CREATED_AT,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_UPDATED_AT,
				ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_EXTERNAL_REQUEST_ID},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByExternalRequestIdAndStage(context.Background(), tt.externalReqId, tt.stage, tt.selectedFields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByExternalRequestIdAndStage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByExternalRequestIdAndStage() got = %v, want %v", got, tt.want)
			}
		})
	}
}
