// nolint
package comms

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	commsPb "github.com/epifi/gamma/api/comms"
	accountingPb "github.com/epifi/gamma/api/firefly/accounting"
	accEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
	"github.com/epifi/gamma/firefly/helper"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	ffPkgHelper "github.com/epifi/gamma/pkg/firefly"
)

type CreditUtilisationReachingThresholdRule struct {
	commsDataHelper *ffHelper.CommsDataHelper
}

func NewCreditUtilisationReachingThresholdRule(commsDataHelper *ffHelper.CommsDataHelper) *CreditUtilisationReachingThresholdRule {
	return &CreditUtilisationReachingThresholdRule{
		commsDataHelper: commsDataHelper,
	}
}

func (s *CreditUtilisationReachingThresholdRule) GetComms(ctx context.Context, data IActionData) (res []commsPb.CommMessage, resErr error) {
	txnData, ok := data.(*ffCommsPb.ActionData_TxnActionData)
	if !ok {
		return
	}
	if txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetTxnStatus() == accEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS &&
		txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetTxnType() == accEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT &&
		!helper.IsSmsDisabledForSmsType(commsPb.SmsType_CREDIT_CARD_LIMIT_REACHING_THRESHOLD) {

		limitInfo, err := s.commsDataHelper.GetLimitInfo(ctx, &accountingPb.GetCreditAccountLimitUtilisationRequest{
			GetBy: &accountingPb.GetCreditAccountLimitUtilisationRequest_ActorId{ActorId: txnData.GetActorId()},
		})
		if err != nil {
			logger.Error(ctx, "error while fetching limit details", zap.Error(err))
			return nil, err
		}
		utilisationRatio := float32(limitInfo.GetLimitUtilized().GetUnits()) / float32(limitInfo.GetLimitActual().GetUnits())
		// utilisation is not crossing 90%
		if utilisationRatio < 0.9 {
			return
		}
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_CreditCardLimitReachingThresholdSmsOption{
						CreditCardLimitReachingThresholdSmsOption: &commsPb.CreditCardLimitReachingThresholdSmsOption{
							SmsType: commsPb.SmsType_CREDIT_CARD_LIMIT_REACHING_THRESHOLD,
							Option: &commsPb.CreditCardLimitReachingThresholdSmsOption_CreditCardLimitReachingThresholdSmsOptionV1{
								CreditCardLimitReachingThresholdSmsOptionV1: &commsPb.CreditCardLimitReachingThresholdSmsOptionV1{
									TemplateVersion:     commsPb.TemplateVersion_VERSION_V1,
									ThresholdPercentage: 90,
									AvailableLimit:      limitInfo.GetLimitAvailable(),
								},
							},
						},
					},
				},
			},
		})
	}
	return
}

func (s *CreditUtilisationReachingThresholdRule) GetDoOnceTaskId(_ context.Context, data IActionData, medium commsPb.Medium) string {
	txnData, ok := data.(*ffCommsPb.ActionData_TxnActionData)
	if !ok {
		return ""
	}
	if txnData.TxnActionData.GetBillWindow() != nil {
		billWindowString := ffPkgHelper.GetBillingWindowAsString(txnData.TxnActionData.GetBillWindow())
		return data.GetActorId() + "_" + "CREDIT_UTILISATION_REACHING_THRESHOLD" + "_" + billWindowString + "_" + medium.String()
	}
	return ""
}
