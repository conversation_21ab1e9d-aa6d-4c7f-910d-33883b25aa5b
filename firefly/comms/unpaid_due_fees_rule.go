// nolint
package comms

import (
	"context"

	"go.uber.org/zap"

	commsPb "github.com/epifi/gamma/api/comms"
	ffPb "github.com/epifi/gamma/api/firefly"
	accEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ffAccHelper "github.com/epifi/gamma/firefly/accounting/helper"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/be-common/pkg/logger"
)

type UnpaidDuesFeesRule struct {
	commsDataHelper *ffHelper.CommsDataHelper
}

func NewUnpaidDuesFeesRule(commsDataHelper *ffHelper.CommsDataHelper) *UnpaidDuesFeesRule {
	return &UnpaidDuesFeesRule{
		commsDataHelper: commsDataHelper,
	}
}

func (s *UnpaidDuesFeesRule) GetComms(ctx context.Context, data IActionData) (res []commsPb.CommMessage, resErr error) {
	txnData, ok := data.(*ffCommsPb.ActionData_TxnActionData)
	if !ok {
		return
	}
	if txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetTxnStatus() == accEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS &&
		txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetTxnType() == accEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT &&
		ffAccHelper.IsUnpaidDuesFeeTransaction(txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction()) {
		getCcReq := &ffPb.GetCreditCardRequest{
			GetBy:            &ffPb.GetCreditCardRequest_ActorId{ActorId: txnData.TxnActionData.GetActorId()},
			SelectFieldMasks: []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO},
		}
		creditCardDetails, err := s.commsDataHelper.GetCreditCardDetails(ctx, getCcReq)
		if err != nil {
			logger.Error(ctx, "error while fetching credit card details", zap.Error(err))
			return nil, err
		}
		ccLastSixDigits, err := ffHelper.GetCreditCardLastKDigits(creditCardDetails.GetCreditCard().GetBasicInfo().GetMaskedCardNumber(), 6)
		if err != nil {
			logger.Error(ctx, "error while fetching credit card last four digits", zap.Error(err))
			return nil, err
		}
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_CreditCardUnpaidDueFeesSmsOption{
						CreditCardUnpaidDueFeesSmsOption: &commsPb.CreditCardUnpaidDueFeesSmsOption{
							SmsType: commsPb.SmsType_CREDIT_CARD_UNPAID_DUE_FEES,
							Option: &commsPb.CreditCardUnpaidDueFeesSmsOption_CreditCardUnpaidDueFeesSmsOptionV1{
								CreditCardUnpaidDueFeesSmsOptionV1: &commsPb.CreditCardFeeSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									Amount:          txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetAmount(),
									TxnTime:         txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetTxnTime(),
									CcEndingDigits:  ccLastSixDigits,
								},
							},
						},
					},
				},
			},
		})
	}
	return
}
