// nolint
package comms

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	commsPb "github.com/epifi/gamma/api/comms"
	ffPb "github.com/epifi/gamma/api/firefly"
	accountingPb "github.com/epifi/gamma/api/firefly/accounting"
	accEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffBeBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	userPb "github.com/epifi/gamma/api/user"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/be-common/pkg/datetime"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	ffPkgHelper "github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

var (
	successfulRepaymentEventType   = "Repayment Successful for"
	successfulRepaymentHeading     = "Bill paid successfully"
	successfulRepaymentDescription = "Well done, %s! You have successfully repaid the bill for your Credit Card ending with %s"
)

type SuccessfulRepaymentRule struct {
	commsDataHelper *ffHelper.CommsDataHelper
}

func NewSuccessfulRepaymentRule(commsDataHelper *ffHelper.CommsDataHelper) *SuccessfulRepaymentRule {
	return &SuccessfulRepaymentRule{
		commsDataHelper: commsDataHelper,
	}
}

func (s *SuccessfulRepaymentRule) GetComms(ctx context.Context, data IActionData) (res []commsPb.CommMessage, resErr error) {
	txnData, ok := data.(*ffCommsPb.ActionData_TxnActionData)
	if !ok {
		return
	}
	if txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetTxnStatus() == accEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS &&
		txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetTxnCategory() == accEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_DIRECT_CREDIT {

		billInfo, err := s.commsDataHelper.GetBillInfo(ctx, &ffBeBillingPb.GetCreditCardBillRequest{
			GetBy: &ffBeBillingPb.GetCreditCardBillRequest_ActorId{ActorId: txnData.GetActorId()},
		})
		// This automatically handles the case where no bill is generated by record not found error.
		if err != nil {
			logger.Error(ctx, "error fetching bill info", zap.Error(err))
			return nil, err
		}
		userDetails, err := s.commsDataHelper.GetUserDetails(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{ActorId: txnData.GetActorId()},
		})
		if err != nil {
			logger.Error(ctx, "error while fetching user details", zap.Error(err))
			return nil, err
		}
		dueInfo, err := s.commsDataHelper.GetDueInfo(ctx, &accountingPb.GetCreditAccountDueInformationRequest{
			GetBy: &accountingPb.GetCreditAccountDueInformationRequest_ActorId{ActorId: txnData.GetActorId()},
		})
		if err != nil {
			logger.Error(ctx, "error while fetching due details", zap.Error(err))
			return nil, err
		}
		compareUnpaidTotalDue, err := moneyPkg.CompareV2(dueInfo.GetUnpaidTotalDue(), moneyPkg.ZeroINR().GetPb())
		if err != nil {
			logger.Error(ctx, "error comparing unpaid due amount with zero amount", zap.Error(err))
			return nil, err
		}
		// there is still some unpaid due amount left
		if compareUnpaidTotalDue > 0 {
			return
		}
		compareTotalDue, err := moneyPkg.CompareV2(billInfo.GetCreditCardBill().GetTotalDue(), moneyPkg.ZeroINR().GetPb())
		// in case the bill did not have any due, no comms has to be sent
		if compareTotalDue <= 0 {
			return
		}
		getCcReq := &ffPb.GetCreditCardRequest{
			GetBy:            &ffPb.GetCreditCardRequest_ActorId{ActorId: txnData.TxnActionData.GetActorId()},
			SelectFieldMasks: []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO},
		}
		creditCardDetails, err := s.commsDataHelper.GetCreditCardDetails(ctx, getCcReq)
		if err != nil {
			logger.Error(ctx, "error while fetching credit card details", zap.Error(err))
			return nil, err
		}
		ccLastFourDigits, err := ffHelper.GetCreditCardLastKDigits(creditCardDetails.GetCreditCard().GetBasicInfo().GetMaskedCardNumber(), 4)
		if err != nil {
			logger.Error(ctx, "error while fetching credit card last four digits", zap.Error(err))
			return nil, err
		}

		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_CreditCardBillSuccessfulRepaymentSmsOption{
						CreditCardBillSuccessfulRepaymentSmsOption: &commsPb.CreditCardBillSuccessfulRepaymentSmsOption{
							SmsType: commsPb.SmsType_CREDIT_CARD_BILL_SUCCESSFUL_REPAYMENT,
							Option: &commsPb.CreditCardBillSuccessfulRepaymentSmsOption_CreditCardBillSuccessfulRepaymentSmsOptionV1{
								CreditCardBillSuccessfulRepaymentSmsOptionV1: &commsPb.CreditCardBillSuccessfulRepaymentSmsOptionV1{
									TemplateVersion:  commsPb.TemplateVersion_VERSION_V1,
									FirstName:        userDetails.GetUser().GetProfile().GetKycName(),
									MaskedCardNumber: ffHelper.GetCrossSuffixedCreditCardLastFourDigits(ccLastFourDigits),
									CreditCardType:   ffPkg.CreditCardType,
								},
							},
						},
					},
				},
			},
		})
		res = append(res, getSuccessfulRepaymentEmail(txnData, ccLastFourDigits, userDetails.GetUser().GetProfile().GetKycName().GetFirstName(), txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetAmount(), dueInfo))
	}
	return
}

func getSuccessfulRepaymentEmail(txnData *ffCommsPb.ActionData_TxnActionData, ccLastFourDigits string, firstName string, txnAmount *money.Money, dueInfo *accountingPb.GetCreditAccountDueInformationResponse) commsPb.CommMessage {

	commsEmailOption := &commsPb.CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV2{
		CreditCardTransactionStatusEmailOptionV2: &commsPb.CreditCardTransactionStatusEmailOptionV2{
			TemplateVersion:          commsPb.TemplateVersion_VERSION_V2,
			LastFourDigits:           ccLastFourDigits,
			CardTransactionEventType: successfulRepaymentEventType,
			BackgroundColor:          bgColorWhite,
			Heading:                  successfulRepaymentHeading,
			Description:              fmt.Sprintf(successfulRepaymentDescription, firstName, ffHelper.GetCrossSuffixedCreditCardLastFourDigits(ccLastFourDigits)),
			BoxTitle:                 "TOTAL PAID",
			BoxDescription:           moneyPkg.ToDisplayStringInIndianFormat(txnAmount, 0, true),
			IconUrl:                  greenTickIconUrl,
			BoxColorClass:            bgLightGray,
			TableRowDetails:          getTableRowsForSuccessfulRepaymentEmail(txnData, dueInfo, ccLastFourDigits),
		},
	}

	return &commsPb.SendMessageRequest_Email{
		Email: &commsPb.EmailMessage{
			FromEmailId:   "<EMAIL>",
			FromEmailName: "Fi Money",
			EmailOption: &commsPb.EmailOption{Option: &commsPb.EmailOption_CreditCardTransactionStatusEmailOption{
				CreditCardTransactionStatusEmailOption: &commsPb.CreditCardTransactionStatusEmailOption{
					EmailType: commsPb.EmailType_CREDIT_CARD_TRANSACTION_STATUS_EMAIL,
					Option:    commsEmailOption,
				}}},
		},
	}
}

func getTableRowsForSuccessfulRepaymentEmail(txnData *ffCommsPb.ActionData_TxnActionData, dueInfo *accountingPb.GetCreditAccountDueInformationResponse, ccLastFourDigits string) string {
	row1 := strings.Replace(tableRowDetailsLayout, "{#item#}", "Total due", 1)
	row1 = strings.Replace(row1, "{#value#}", moneyPkg.ToDisplayStringInIndianFormat(dueInfo.GetTotalDueAmount(), 0, true), 1)
	row2 := strings.Replace(tableRowDetailsLayout, "{#item#}", "Remaining due", 1)
	row2 = strings.Replace(row2, "{#value#}", moneyPkg.ToDisplayStringInIndianFormat(dueInfo.GetUnpaidTotalDue(), 0, true), 1)
	row3 := strings.Replace(tableRowDetailsLayout, "{#item#}", "Time", 1)
	row3 = strings.Replace(row3, "{#value#}", datetime.TimestampToString(txnData.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetTxnTime(), commsPb.TransactionTimeLayout, datetime.IST), 1)
	row4 := strings.Replace(tableRowDetailsLayout, "{#item#}", "Card Number", 1)
	row4 = strings.Replace(row4, "{#value#}", ffHelper.GetCrossSuffixedCreditCardLastFourDigits(ccLastFourDigits), 1)
	return row1 + row2 + row3 + row4
}

func (s *SuccessfulRepaymentRule) GetDoOnceTaskId(_ context.Context, data IActionData, medium commsPb.Medium) string {
	txnData, ok := data.(*ffCommsPb.ActionData_TxnActionData)
	if !ok {
		return ""
	}
	if txnData.TxnActionData.GetBillWindow() != nil {
		billWindowString := ffPkgHelper.GetBillingWindowAsString(txnData.TxnActionData.GetBillWindow())
		return data.GetActorId() + "_" + "REPAYMENT_SUCCESS" + "_" + billWindowString + "_" + medium.String()
	}
	return ""
}
