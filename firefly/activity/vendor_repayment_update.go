package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"strconv"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	ffPb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	orderPb "github.com/epifi/gamma/api/order"
	ccVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	federalEpifiId = "FDEPIFICR"
)

// UpdateVendorRepayment activity is responsible for calling the vendor repayment api once the payment is successful.
func (p *Processor) UpdateVendorRepayment(ctx context.Context, req *ffActivityPb.PaymentVendorUpdateRequest) (*ffActivityPb.PaymentVendorUpdateResponse, error) {
	res := &ffActivityPb.PaymentVendorUpdateResponse{}
	lg := activity.GetLogger(ctx)

	/***			Fetching the db entries for our card request stage and card request status/sub-status updates			*****/
	cardRequest, cardRequestStage, err := p.getCardReqAndCreateCardReqStage(ctx, req.GetRequestHeader().GetClientReqId(),
		ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CREDIT_CARD_PAYMENT_VENDOR_UPDATE,
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED,
		ffEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED, "")
	if err != nil {
		lg.Error("error in getCardReqAndCreateCardReqStage", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(err, "error in getCardReqAndCreateCardReqStage")
	}

	finishActivity, err := p.checkIfStageAlreadyTerminated(cardRequestStage)
	switch {
	case err != nil:
		return nil, err
	case finishActivity:
		return res, nil
	default:
		// continue
	}

	vendorRepaymentResponse, err := p.fetchDetailsAndMakeVendorCall(ctx, req, req.GetPaymentClientReqId(), cardRequest)
	if err != nil {
		lg.Error("error while initiating vendor repayment call", err)
		return nil, err
	}
	res.VendorTxnRefNo = strconv.Itoa(int(vendorRepaymentResponse.GetTransactionId()))
	return res, nil
}

func (p *Processor) fetchDetailsAndMakeVendorCall(ctx context.Context, req *ffActivityPb.PaymentVendorUpdateRequest, paymentRequestId string, cardRequest *ffPb.CardRequest) (*ccVgPb.RepayLoanAmountResponse, error) {

	savedCard, err := p.creditCardDao.GetById(ctx, req.GetCardId(), []ffEnumsPb.CreditCardFieldMask{
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_VENDOR,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_VENDOR_IDENTIFIER,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID,
	})
	if err != nil {

		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrPermanent, errors.Wrap(err, "No card found for given card id").Error())
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "error in fetching card for given id").Error())
	}
	creditAccount, err := p.rpcHelper.GetCreditAccountById(ctx, savedCard.GetAccountId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrPermanent, errors.Wrap(err, "No account found for given account id").Error())
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error fetching account").Error())
	}
	orderResponse, err := p.rpcHelper.GetOrdersWithTransactions(ctx, paymentRequestId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrPermanent, errors.Wrap(err, "No order/transactions found for given payment req id").Error())
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error fetching order").Error())
	}
	vendorRequest := getVendorRequest(orderResponse.GetOrderWithTransactions()[0], creditAccount, cardRequest)
	resp, err := p.rpcHelper.RepayLoanAmount(ctx, vendorRequest)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error calling vendor repayment").Error())
	}
	return resp, nil
}

func getVendorRequest(orderWithTxn *orderPb.OrderWithTransactions, creditAccount *accounting.CreditAccount, cardRequest *ffPb.CardRequest) *ccVgPb.RepayLoanAmountRequest {
	extTxnId := ""
	switch {
	case cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_AUTOMATED_REPAYMENT:
		extTxnId = orderWithTxn.GetTransactions()[0].GetReqId()
	case cardRequest.GetRequestDetails().GetPaymentRequestDetails().GetPaymentAccountType() == ffEnumsPb.PaymentAccountType_PAYMENT_ACCOUNT_TYPE_THIRD_PARTY_ACCOUNT:
		extTxnId = orderWithTxn.GetTransactions()[0].GetUtr()
	default:
		extTxnId = orderWithTxn.GetTransactions()[0].GetReqId()
	}
	return &ccVgPb.RepayLoanAmountRequest{
		Header:                 &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_M2P},
		EntityId:               creditAccount.GetReferenceId(),
		BusinessEntityId:       federalEpifiId,
		BusinessName:           federalEpifiId,
		Amount:                 orderWithTxn.GetTransactions()[0].GetAmount(),
		TransactionType:        ffEnumsPb.TransactionType_DIRECT_CREDIT,
		TransactionOrigin:      ccVgPb.TransactionOrigin_MOBILE,
		ProductId:              "GENERAL",
		ExternalTransactionId:  extTxnId,
		TransactionDescription: remark,
		OtherPartyId:           orderWithTxn.GetTransactions()[0].GetPartnerBank().String(),
		OtherPartyName:         orderWithTxn.GetTransactions()[0].GetPartnerBank().String(),
	}
}
