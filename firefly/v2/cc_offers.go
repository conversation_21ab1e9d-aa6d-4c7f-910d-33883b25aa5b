package v2

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	feBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

func (s *Service) GetCreditCardOffers(ctx context.Context, req *feBeV2Pb.GetCreditCardOffersRequest) (*feBeV2Pb.GetCreditCardOffersResponse, error) {
	res := &feBeV2Pb.GetCreditCardOffersResponse{}

	ccOffers, err := s.ccOfferDao.GetAllActiveOffersByActorId(ctx, req.GetActorId())
	switch {
	case storageV2.IsRecordNotFoundError(err):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching credit card offer", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	cardProgram := ""
	if req.GetCardProgram() != nil {
		cardProgram = ffPkg.GetCardProgramStringFromCardProgram(req.GetCardProgram())
	}
	filteredOffers := filterCreditCardOfferByVendorAndCardProgram(ccOffers, req.GetVendor(), cardProgram)
	if len(filteredOffers) == 0 {
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}
	res.Offers = append(res.Offers, filteredOffers...)
	res.Status = rpc.StatusOk()
	return res, nil
}

func filterCreditCardOfferByVendorAndCardProgram(ccOffers []*feBeV2Pb.CreditCardOffer, vendor vendorgateway.Vendor, cardProgram string) []*feBeV2Pb.CreditCardOffer {
	filteredOffers := make([]*feBeV2Pb.CreditCardOffer, 0)

	for _, offer := range ccOffers {
		if vendor != vendorgateway.Vendor_VENDOR_UNSPECIFIED && vendor != offer.GetVendor() {
			continue
		}
		if cardProgram != "" && ffPkg.GetCardProgramStringFromCardProgram(offer.GetCardProgram()) != cardProgram {
			continue
		}
		filteredOffers = append(filteredOffers, offer)
	}
	return filteredOffers
}
