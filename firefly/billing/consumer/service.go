package consumer

import (
	"context"

	"github.com/epifi/be-common/api/queue"
	ffPb "github.com/epifi/gamma/api/firefly"
	consumerPb "github.com/epifi/gamma/api/firefly/billing/consumer"
	ffBillingConsumerPb "github.com/epifi/gamma/api/firefly/billing/consumer"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type ConsumerService struct {
	ffSvcClient ffPb.FireflyClient
}

func NewBillingConsumerService(ffSvcClient ffPb.FireflyClient) *ConsumerService {
	return &ConsumerService{
		ffSvcClient: ffSvcClient,
	}
}

func (c *ConsumerService) ProcessStatementGeneratedNotification(ctx context.Context, request *consumerPb.ProcessStatementGeneratedNotificationRequest) (*consumerPb.ProcessStatementGeneratedNotificationResponse, error) {
	var (
		resp       = &ffBillingConsumerPb.ProcessStatementGeneratedNotificationResponse{}
		respHeader = &queue.ConsumerResponseHeader{}
	)
	resp.ResponseHeader = respHeader
	billGenerationRequest := &ffPb.StartBillGenerationWorkflowRequest{
		EntityId:      request.GetEntityId(),
		StatementDate: request.GetStatementDate(),
		Vendor:        request.GetVendor(),
	}
	startBillGenResponse, err := c.ffSvcClient.StartBillGenerationWorkflow(ctx, billGenerationRequest)
	if te := epifigrpc.RPCError(startBillGenResponse, err); te != nil {
		respHeader.Status = queue.MessageConsumptionStatus_TRANSIENT_FAILURE
	} else {
		respHeader.Status = queue.MessageConsumptionStatus_SUCCESS
	}
	return resp, nil
}
