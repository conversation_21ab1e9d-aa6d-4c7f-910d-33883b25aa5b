// nolint
package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	ffPb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/be-common/pkg/events"
)

type FdCreationFailureEvent struct {
	ActorId       string
	SessionId     string
	EventId       string
	ProspectId    string
	Timestamp     time.Time
	EventType     string
	EventMetaData *EventMetaData
}

func NewFdCreationFailureEvent(cardReq *ffPb.CardRequest) *FdCreationFailureEvent {
	return &FdCreationFailureEvent{
		ActorId:       cardReq.GetActorId(),
		SessionId:     cardReq.GetId(),
		EventId:       uuid.New().String(),
		Timestamp:     time.Now(),
		EventType:     events.EventTrack,
		EventMetaData: GetEventMetaData(cardReq.GetRequestDetails().GetCardProgram()),
	}
}

func (c *FdCreationFailureEvent) GetEventType() string {
	return c.EventType
}

func (c *FdCreationFailureEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *FdCreationFailureEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *FdCreationFailureEvent) GetEventId() string {
	return c.EventId
}

func (c *FdCreationFailureEvent) GetUserId() string {
	return c.ActorId
}

func (c *FdCreationFailureEvent) GetProspectId() string {
	return c.ProspectId
}

func (c *FdCreationFailureEvent) GetEventName() string {
	return EventFdCreationFailed
}
