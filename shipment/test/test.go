package test

import (
	"log"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	"github.com/epifi/be-common/pkg/logger"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/shipment/config"
	"github.com/epifi/gamma/shipment/config/genconf"
)

// InitTestServer returns static conf, dynamic conf, crdb conn, pgdb conn and teardown function
func InitTestServer() (*config.Config, *genconf.Config, *gorm.DB, *gorm.DB, func()) {
	// Init config
	conf, err := config.Load()
	if err != nil {
		log.Panic("failed to load config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	genConf, err := dynconf.LoadConfig(config.Load, genconf.NewConfig, cfg.SHIPMENT_SERVICE)
	if err != nil {
		logger.Panic("failed to load user action config", zap.Error(err))
	}

	// Init partner logger
	logger.InitPartnerLogger(&logger.LogConfig{
		MaxSize:    conf.SecureLogging.MaxSizeInMBs,
		MaxBackups: conf.SecureLogging.MaxBackups,
	})
	defer func() { _ = logger.PartnerLogger.Sync() }()

	// Init pgdb connection
	vendorDataPGDB, _, closeFn, er := pkgTestV2.PrepareRandomScopedRdsTestDb(conf.VendorDataDb, false)
	if er != nil {
		log.Panic("error making connection to test rds database", err)
	}

	return conf, genConf, nil, vendorDataPGDB, func() {
		_ = logger.Log.Sync()
		closeFn()
	}
}
