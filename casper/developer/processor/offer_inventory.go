package processor

import (
	"context"
	"encoding/json"

	"google.golang.org/protobuf/encoding/protojson"

	casperPb "github.com/epifi/gamma/api/casper"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/gamma/api/casper/developer"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/casper/dao"
	"github.com/epifi/be-common/pkg/logger"
)

type OfferInventoryProcessor struct {
	offerInventoryDao dao.OfferInventoryDao
}

func NewOfferInventoryProcessor(offerInventoryDao dao.OfferInventoryDao) *OfferInventoryProcessor {
	return &OfferInventoryProcessor{offerInventoryDao: offerInventoryDao}
}

func (r *OfferInventoryProcessor) FetchParamList(ctx context.Context, entity developer.CasperEntity) ([]*db_state.ParameterMeta, error) {

	paramList := []*db_state.ParameterMeta{
		{
			Name:            offerId,
			Label:           "Offer Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            actorId,
			Label:           "Actor Id (for getting available inventory for actor)",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (r *OfferInventoryProcessor) FetchData(ctx context.Context, entity developer.CasperEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}

	var offerid, actorid string

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case offerId:
			offerid = filter.GetStringValue()

		case actorId:
			actorid = filter.GetStringValue()

		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	var marshalledRes []byte

	// if actor id is not present, fetch using offer id only.
	if actorid == "" {
		// fetch inventory by offer id
		offerInventory, err := r.offerInventoryDao.GetOfferInventoryByOfferId(ctx, offerid)
		if err != nil {
			logger.Error(ctx, "error fetching inventory by offer id", zap.String("offerId", offerid))
			return "", errors.Wrap(err, "error fetching inventory by offer id")
		}
		marshalledRes, err = json.Marshal(offerInventory)
		if err != nil {
			logger.Error(ctx, "error marshalling offer inventory to json", zap.Any("offerInventory", offerInventory))
			return "", errors.Wrap(err, "error marshalling offer inventory to json")
		}
	} else {
		// fetch offer inventory for given actor
		offerInventory, err := r.offerInventoryDao.GetOfferInventory(ctx, &casperPb.GetOfferInventoryRequest{
			OfferId: offerid,
			ActorId: actorid,
		})
		if err != nil {
			logger.Error(ctx, "error fetching inventory by offer and actor id", zap.String("offerId", offerid), zap.String("actorId", actorid))
			return "", errors.Wrap(err, "error fetching inventory by offer and actor id")
		}
		marshalledRes, err = protojson.Marshal(offerInventory)
		if err != nil {
			logger.Error(ctx, "error marshalling offer inventory proto", zap.Any("offerInventory", offerInventory))
			return "", errors.Wrap(err, "error marshalling offer inventory proto")
		}
	}

	return string(marshalledRes), nil
}
