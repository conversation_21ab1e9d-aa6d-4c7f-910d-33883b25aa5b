package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	gormv2 "gorm.io/gorm"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	casperPb "github.com/epifi/gamma/api/casper"
	redemptionPb "github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/casper/redemption/dao/model"
)

type InHouseRedemptionDaoImpl struct {
	db *gormv2.DB
}

func NewInHouseRedemptionDaoImpl(db pkgTypes.CasperPGDB) *InHouseRedemptionDaoImpl {
	return &InHouseRedemptionDaoImpl{db: db}
}
func (i *InHouseRedemptionDaoImpl) Create(ctx context.Context, inHouseRedemption *redemptionPb.InHouseRedemption) (*redemptionPb.InHouseRedemption, error) {
	defer metric_util.TrackDuration("casper/redemption/dao", "InHouseRedemptionDaoImpl", "Create", time.Now())
	db := i.db.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	inHouseRedemptionModel, err := model.NewInHouseRedemption(inHouseRedemption)
	if err != nil {
		return nil, fmt.Errorf("error getting inHouseRedemptionModel from inHouseRedemption proto err: %w", err)
	}
	if dbErr := db.Create(inHouseRedemptionModel).Error; dbErr != nil {
		return nil, fmt.Errorf("error creating inHouseRedemption entry in db err: %w", dbErr)
	}
	inHouseRedemptionPb, getProtoErr := inHouseRedemptionModel.GetProto()
	if getProtoErr != nil {
		return nil, fmt.Errorf("error getting inHouseRedemptionPb from inHouseRedemptionModel: %w", getProtoErr)
	}
	return inHouseRedemptionPb, nil
}

func (i *InHouseRedemptionDaoImpl) GetByRefIdAndRequestSrc(ctx context.Context, refId string, requestSource casperPb.RequestSource) (*redemptionPb.InHouseRedemption, error) {
	defer metric_util.TrackDuration("casper/redemption/dao", "InHouseRedemptionDaoImpl", "GetByRefIdAndRequestSrc", time.Now())
	db := i.db.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	inHouseRedemption := &model.InHouseRedemption{}
	res := db.First(inHouseRedemption, "ref_id=? and request_source=?", refId, requestSource)
	if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch inHouseRedemption by ref id and request source, err : %w", res.Error)
	}
	inHouseRedemptionPb, err := inHouseRedemption.GetProto()
	if err != nil {
		return nil, fmt.Errorf("failed while getting inHouseRedemption proto from inHouseRedemption model, err : %w", res.Error)
	}
	return inHouseRedemptionPb, nil
}
