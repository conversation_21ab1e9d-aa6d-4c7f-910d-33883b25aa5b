package stateprocessor

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	casperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/casper/redemption"
)

type VendorRedemptionUpdateProcessor struct {
	s *CommonProcessorUtil
}

func NewVendorRedemptionUpdateProcessor(s *CommonProcessorUtil) *VendorRedemptionUpdateProcessor {
	return &VendorRedemptionUpdateProcessor{s: s}
}

// Process updates vendor redemption details after receiving a success callback from the vendor
// Updates only redemption status for now, can update redeemed offer details as well in the future
func (v *VendorRedemptionUpdateProcessor) Process(ctx context.Context, redeemedOffer *redemption.RedeemedOffer, _ *casperPb.Offer, _ *redemption.RedemptionRequest, transitionAction *redemption.TransitionAction) *StateProcessorResponse {
	logger.Info(ctx, "inside vendor redemption update processor", zap.String(logger.REDEEMED_OFFER_ID, redeemedOffer.GetId()))

	currentState := redeemedOffer.GetRedemptionState()

	switch transitionAction.GetUpdateVendorRedemptionTransitionActionPayload().GetStatus() {
	case redemption.UpdateVendorRedemptionTransitionActionPayload_SUCCESS:
		redeemedOffer.RedemptionState = redemption.OfferRedemptionState_VENDOR_REDEMPTION_SUCCESSFUL
	case redemption.UpdateVendorRedemptionTransitionActionPayload_FAILURE:
		redeemedOffer.RedemptionState = redemption.OfferRedemptionState_VENDOR_REDEMPTION_FAILED
	default:
		logger.Error(ctx, "invalid update vendor redemption transition action status", zap.String(logger.STATUS, transitionAction.GetUpdateVendorRedemptionTransitionActionPayload().GetStatus().String()), zap.String(logger.REDEEMED_OFFER_ID, redeemedOffer.GetId()))
		return NewStateProcessorResponse(false, fmt.Errorf("invalid update vendor redemption transition action status: %s", transitionAction.GetUpdateVendorRedemptionTransitionActionPayload().GetStatus().String()), currentState)
	}

	updateFieldMask := []redemption.RedeemedOfferFieldMask{redemption.RedeemedOfferFieldMask_OFFER_REDEMPTION_STATE}
	err := v.s.redeemedOfferDao.Update(ctx, redeemedOffer, updateFieldMask)
	if err != nil {
		logger.Error(ctx, "error updating the redemption state", zap.String(logger.REDEEMED_OFFER_ID, redeemedOffer.GetId()))
		return NewStateProcessorResponse(false, fmt.Errorf("error updating the redemption state"), currentState)
	}
	return NewStateProcessorResponse(true, nil, redeemedOffer.GetRedemptionState())
}
