// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/kms"
	kms2 "github.com/epifi/be-common/pkg/aws/v2/kms"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/storage/v2"
	genconf2 "github.com/epifi/be-common/quest/sdk/config/genconf"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/accrual"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/casper/external_vendor_redemption"
	"github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/api/cms"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/health_engine"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/quest/manager"
	"github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/timeline"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/offers/dreamfolks"
	"github.com/epifi/gamma/api/vendorgateway/offers/loylty"
	"github.com/epifi/gamma/api/vendorgateway/offers/poshvine"
	"github.com/epifi/gamma/api/vendorgateway/offers/qwikcilver"
	"github.com/epifi/gamma/api/vendorgateway/offers/thriwe"
	"github.com/epifi/gamma/api/vendorgateway/offers/vistara"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/casper/config"
	"github.com/epifi/gamma/casper/config/genconf"
	"github.com/epifi/gamma/casper/dao"
	"github.com/epifi/gamma/casper/developer"
	"github.com/epifi/gamma/casper/developer/processor"
	"github.com/epifi/gamma/casper/discount"
	dao2 "github.com/epifi/gamma/casper/discount/dao"
	"github.com/epifi/gamma/casper/exchanger"
	consumer2 "github.com/epifi/gamma/casper/exchanger/consumer"
	dao4 "github.com/epifi/gamma/casper/exchanger/dao"
	"github.com/epifi/gamma/casper/exchanger/redemption_statemachine"
	"github.com/epifi/gamma/casper/exchanger/redemption_statemachine/debitaccount"
	"github.com/epifi/gamma/casper/exchanger/redemption_statemachine/option_post_processor"
	"github.com/epifi/gamma/casper/exchanger/redemption_statemachine/option_post_processor/conditions_evaluator"
	"github.com/epifi/gamma/casper/exchanger/redemption_statemachine/order_fulfillment"
	"github.com/epifi/gamma/casper/exchanger/redemption_statemachine/reward_units_and_inventory_capping"
	"github.com/epifi/gamma/casper/exchanger/redemption_statemachine/rewardunitscalculator"
	stateprocessor2 "github.com/epifi/gamma/casper/exchanger/redemption_statemachine/stateprocessor"
	external_vendor_redemption2 "github.com/epifi/gamma/casper/external_vendor_redemption"
	dao5 "github.com/epifi/gamma/casper/external_vendor_redemption/dao"
	notification2 "github.com/epifi/gamma/casper/external_vendor_redemption/notification"
	"github.com/epifi/gamma/casper/external_vendor_redemption/notification/categoryhandler"
	"github.com/epifi/gamma/casper/helper"
	"github.com/epifi/gamma/casper/helper/slack_helper"
	"github.com/epifi/gamma/casper/helper/slack_helper/attachment_builder"
	"github.com/epifi/gamma/casper/jobhelper"
	offervendor2 "github.com/epifi/gamma/casper/offervendor"
	consumer4 "github.com/epifi/gamma/casper/offervendor/itc/consumer"
	consumer3 "github.com/epifi/gamma/casper/offervendor/vistara/consumer"
	redemption2 "github.com/epifi/gamma/casper/redemption"
	"github.com/epifi/gamma/casper/redemption/consumer"
	"github.com/epifi/gamma/casper/redemption/cryptor"
	dao3 "github.com/epifi/gamma/casper/redemption/dao"
	"github.com/epifi/gamma/casper/redemption/notification"
	"github.com/epifi/gamma/casper/redemption/offeraccount"
	"github.com/epifi/gamma/casper/redemption/stateprocessor"
	"github.com/epifi/gamma/casper/service"
	"github.com/epifi/gamma/casper/service/offervendor"
	types2 "github.com/epifi/gamma/casper/wire/types"
	"github.com/epifi/gamma/pkg/downtime"
	"github.com/epifi/gamma/quest/sdk/init"
	config2 "github.com/epifi/gamma/rewards/config"
	"github.com/jonboulle/clockwork"
	"github.com/slack-go/slack"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeOfferCatalogService(db types.CasperPGDB, loyltyClient loylty.LoyltyClient, segmentClient segment.SegmentationServiceClient, vendorMappingClient vendormapping.VendorMappingServiceClient, cardClient provisioning.CardProvisioningClient, usersClient user.UsersClient, onboardingClient onboarding.OnboardingClient, poshvineVgClient poshvine.PoshvineClient, fireflyClient firefly.FireflyClient, accountingClient accounting.AccountingClient, externalVendorRedemptionClient external_vendor_redemption.ExternalVendorRedemptionServiceClient, conf *config.Config, dyconf *genconf.Config, redisStore types2.CasperRedisStore, userGroupClient group.GroupClient) *service.OfferCatalogService {
	cacheConfig := CacheConfigProvider(dyconf)
	offerCatalogDaoImpl := dao.NewOfferCatalogDao(db)
	client := types2.CasperRedisStoreProvider(redisStore)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	offerCatalogCacheDao := dao.NewOfferCatalogCacheDao(offerCatalogDaoImpl, redisCacheStorage, cacheConfig)
	offerCatalogDao := dao.ProvideOfferCatalogDao(cacheConfig, offerCatalogCacheDao, offerCatalogDaoImpl)
	offervendorLoylty := offervendor.NewLoylty(loyltyClient)
	vendorFactory := offervendor.NewVendorFactory(offervendorLoylty)
	slackClient := slackClientProvider(conf)
	offerAttachmentBuilder := attachment_builder.NewOfferAttachmentBuilder()
	exchangerOfferAttachmentBuilder := attachment_builder.NewExchangerOfferAttachmentBuilder()
	attachmentBuilderFactory := attachment_builder.NewAttachmentBuilderFactory(offerAttachmentBuilder, exchangerOfferAttachmentBuilder)
	slackHelperSvc := slack_helper.NewSlackHelperSvc(slackClient, attachmentBuilderFactory, dyconf)
	offerCatalogService := service.NewOfferCatalogService(offerCatalogDao, vendorFactory, slackHelperSvc, segmentClient, vendorMappingClient, cardClient, usersClient, onboardingClient, poshvineVgClient, fireflyClient, accountingClient, externalVendorRedemptionClient, dyconf, userGroupClient)
	return offerCatalogService
}

func InitializeOfferInventoryService(db types.CasperPGDB, dyconf *genconf.Config, cacheStorage types2.CasperRedisStore) *service.OfferInventoryService {
	defaultTime := datetime.NewDefaultTime()
	offerInventoryDaoImpl := dao.NewOfferInventoryDao(db, defaultTime)
	cacheConfig := CacheConfigProvider(dyconf)
	offerCatalogDaoImpl := dao.NewOfferCatalogDao(db)
	client := types2.CasperRedisStoreProvider(cacheStorage)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	offerCatalogCacheDao := dao.NewOfferCatalogCacheDao(offerCatalogDaoImpl, redisCacheStorage, cacheConfig)
	offerCatalogDao := dao.ProvideOfferCatalogDao(cacheConfig, offerCatalogCacheDao, offerCatalogDaoImpl)
	offerInventoryService := service.NewOfferInventoryService(offerInventoryDaoImpl, offerCatalogDao, dyconf)
	return offerInventoryService
}

func InitializeOfferListingService(db types.CasperPGDB, loyltyClient loylty.LoyltyClient, redemptionClient redemption.OfferRedemptionServiceClient, actorClient actor.ActorClient, usersClient user.UsersClient, segmentClient segment.SegmentationServiceClient, userGroupClient group.GroupClient, questCacheStorage types.QuestCacheStorage, questManagerCl manager.ManagerClient, vendorMappingClient vendormapping.VendorMappingServiceClient, cardClient provisioning.CardProvisioningClient, onboardingClient onboarding.OnboardingClient, poshvineVgClient poshvine.PoshvineClient, fireflyClient firefly.FireflyClient, accountingClient accounting.AccountingClient, externalVendorRedemptionClient external_vendor_redemption.ExternalVendorRedemptionServiceClient, eventsBroker events.Broker, conf *config.Config, dyconf *genconf.Config, cacheStorage types2.CasperRedisStore) *service.OfferListingService {
	cacheConfig := CacheConfigProvider(dyconf)
	cacheCacheStorage := types.QuestCacheStorageProvider(questCacheStorage)
	offerListingDaoImpl := dao.NewOfferListingDao(db)
	offerListingCacheDao := dao.NewOfferListingCacheDao(cacheCacheStorage, offerListingDaoImpl, cacheConfig)
	offerListingDao := dao.ProvideOfferListingDao(cacheConfig, offerListingCacheDao, offerListingDaoImpl)
	offerCatalogServiceServer := offerCatalogServiceServerProvider(db, loyltyClient, segmentClient, vendorMappingClient, cardClient, usersClient, onboardingClient, poshvineVgClient, fireflyClient, accountingClient, conf, dyconf, cacheStorage, externalVendorRedemptionClient, userGroupClient)
	defaultTime := datetime.NewDefaultTime()
	offerInventoryDaoImpl := dao.NewOfferInventoryDao(db, defaultTime)
	offerCatalogDaoImpl := dao.NewOfferCatalogDao(db)
	offerCatalogCacheDao := dao.NewOfferCatalogCacheDao(offerCatalogDaoImpl, cacheCacheStorage, cacheConfig)
	offerCatalogDao := dao.ProvideOfferCatalogDao(cacheConfig, offerCatalogCacheDao, offerCatalogDaoImpl)
	offerInventoryService := service.NewOfferInventoryService(offerInventoryDaoImpl, offerCatalogDao, dyconf)
	discountDaoPgdb := dao2.NewDiscountDaoPgdb(db)
	genconfConfig := questSDKClientConfProvider(dyconf)
	client := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGroupClient, usersClient, actorClient, segmentClient, cacheCacheStorage, eventsBroker)
	offerListingService := service.NewOfferListingService(offerListingDao, offerCatalogServiceServer, offerInventoryService, discountDaoPgdb, redemptionClient, client, segmentClient)
	return offerListingService
}

func InitializeRedemptionService(db types.CasperPGDB, awsConf aws.Config, commsClient types2.CasperCommsClientWithInterceptors, accrualClient accrual.AccrualClient, vendorMappingClient vendormapping.VendorMappingServiceClient, dynamicConf *genconf.Config, loyltyClient loylty.LoyltyClient, qwikcilverVgClient qwikcilver.QwikcilverClient, dreamfolksVgClient dreamfolks.DreamfolksClient, thriweVgClient thriwe.ThriweClient, userClient user.UsersClient, actorClient actor.ActorClient, eventsBroker events.Broker, config2 *config.Config, delayPublisher types2.RetryRedemptionEventsSqsPublisher, userGroupClient group.GroupClient, vistaraClient vistara.VistaraClient, cmsServiceClient cms.CmsServiceClient, successfulOfferRedemptionEventPublisher types2.OfferRedemptionStatusUpdateEventsSnsPublisher, rewardsClient rewards.RewardsGeneratorClient, segmentClient segment.SegmentationServiceClient, cardClient provisioning.CardProvisioningClient, onboardingClient onboarding.OnboardingClient, poshvineVgClient poshvine.PoshvineClient, fireflyClient firefly.FireflyClient, accountingClient accounting.AccountingClient, externalVendorRedemptionClient external_vendor_redemption.ExternalVendorRedemptionServiceClient, cacheStorage types2.CasperRedisStore) (*redemption2.Service, error) {
	offerCatalogServiceServer := offerCatalogServiceServerProvider(db, loyltyClient, segmentClient, vendorMappingClient, cardClient, userClient, onboardingClient, poshvineVgClient, fireflyClient, accountingClient, config2, dynamicConf, cacheStorage, externalVendorRedemptionClient, userGroupClient)
	redeemedOfferDaoImpl := dao3.NewRedeemedOfferDaoImpl(db)
	redemptionRequestDaoImpl := dao3.NewRedemptionRequestDaoImpl(db)
	gormDB := types.CasperPGDBGormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	discountDaoPgdb := dao2.NewDiscountDaoPgdb(db)
	commonProcessorUtil := stateprocessor.NewCommonProcessorUtil(redeemedOfferDaoImpl, successfulOfferRedemptionEventPublisher)
	offerInventoryServiceServer := offerInventoryServiceServerProvider(db, dynamicConf, cacheStorage)
	purchaseOfferProcessor := stateprocessor.NewPurchaseOfferProcessor(commonProcessorUtil, offerInventoryServiceServer)
	returnPurchasedOfferProcessor := stateprocessor.NewReturnPurchasedOfferProcessor(commonProcessorUtil, offerInventoryServiceServer)
	fiCoinsAccount := offeraccount.NewFICoinsAccount(accrualClient)
	accountFactory := offeraccount.NewAccountFactory(fiCoinsAccount)
	debitFromAccountProcessor := stateprocessor.NewDebitFromAccountProcessor(commonProcessorUtil, accountFactory)
	vendorRedemptionFailureInternalFlowProcessor := stateprocessor.NewVendorRedemptionFailureInternalFlowProcessor(commonProcessorUtil)
	loyltyRedemptionDaoImpl := dao3.NewLoyltyRedemptionDaoImpl(db)
	userHelperService := helper.NewUserHelperService(actorClient, userClient, userGroupClient, rewardsClient)
	loyltyVendor := offervendor2.NewLoyltyVendor(loyltyClient, loyltyRedemptionDaoImpl, vendorMappingClient, userHelperService, config2)
	offlineRedemptionDaoImpl := dao3.NewOfflineRedemptionDaoImpl(db)
	offlineVendor := offervendor2.NewOfflineVendor(offlineRedemptionDaoImpl)
	qwikcilverRedemptionDaoImpl := dao3.NewQwikcilverRedemptionDaoImpl(db)
	qwikcilverVendor := offervendor2.NewQwikcilverVendor(qwikcilverVgClient, qwikcilverRedemptionDaoImpl, userClient)
	noOpVendor := offervendor2.NewNoOpVendor()
	thriweVendor := offervendor2.NewThriweVendor(thriweVgClient, vendorMappingClient, userClient, config2)
	vistaraRedemptionDaoImpl := dao3.NewVistaraRedemptionDaoImpl(db)
	vistaraVendor := offervendor2.NewVistaraVendor(vistaraRedemptionDaoImpl)
	inHouseRedemptionDaoImpl := dao3.NewInHouseRedemptionDaoImpl(db)
	inHouseVendor := offervendor2.NewInHouseVendor(inHouseRedemptionDaoImpl, cmsServiceClient)
	dreamfolksRedemptionDaoImpl := dao3.NewDreamfolksRedemptionDaoImpl(db)
	dreamfolksVendor := offervendor2.NewDreamfolksVendor(dreamfolksVgClient, userClient, dreamfolksRedemptionDaoImpl)
	vendorRedemptionDaoImpl := dao3.NewVendorRedemptionDaoImpl(db)
	itcVendor := offervendor2.NewItcVendor(vendorRedemptionDaoImpl, config2)
	vendorFactory := offervendor2.NewVendorFactory(loyltyVendor, offlineVendor, qwikcilverVendor, noOpVendor, thriweVendor, vistaraVendor, inHouseVendor, dreamfolksVendor, itcVendor)
	redeemedOfferCryptor := redeemedOfferCryptorProvider(config2, awsConf)
	kmsKeys := kmsKeysConfProvider(config2)
	redeemVendorOfferProcessor := stateprocessor.NewRedeemVendorOfferProcessor(commonProcessorUtil, vendorFactory, redeemedOfferCryptor, kmsKeys)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	iNotificationService := iNotificationServiceProvider(actorClient, commsCommsClient, config2)
	failRedemptionProcessor := stateprocessor.NewFailRedemptionProcessor(commonProcessorUtil, iNotificationService, eventsBroker)
	redemptionSuccessProcessor := stateprocessor.NewRedemptionSuccessProcessor(commonProcessorUtil, iNotificationService, eventsBroker)
	vendorRedemptionUpdateProcessor := stateprocessor.NewVendorRedemptionUpdateProcessor(commonProcessorUtil)
	validateDebitedAmountReversalProcessor := stateprocessor.NewValidateDebitedAmountReversalProcessor(commonProcessorUtil, userHelperService)
	factory := stateprocessor.NewFactory(purchaseOfferProcessor, returnPurchasedOfferProcessor, debitFromAccountProcessor, vendorRedemptionFailureInternalFlowProcessor, redeemVendorOfferProcessor, failRedemptionProcessor, redemptionSuccessProcessor, vendorRedemptionUpdateProcessor, validateDebitedAmountReversalProcessor)
	redemptionProcessor := redemption2.NewRedemptionProcessor(gormTxnExecutor, redemptionRequestDaoImpl, redeemedOfferDaoImpl, discountDaoPgdb, factory)
	cacheConfig := CacheConfigProvider(dynamicConf)
	offerCatalogDaoImpl := dao.NewOfferCatalogDao(db)
	client := types2.CasperRedisStoreProvider(cacheStorage)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	offerCatalogCacheDao := dao.NewOfferCatalogCacheDao(offerCatalogDaoImpl, redisCacheStorage, cacheConfig)
	offerCatalogDao := dao.ProvideOfferCatalogDao(cacheConfig, offerCatalogCacheDao, offerCatalogDaoImpl)
	redemptionService := redemption2.NewService(offerCatalogServiceServer, redeemedOfferDaoImpl, redemptionRequestDaoImpl, redemptionProcessor, vendorFactory, redeemedOfferCryptor, delayPublisher, vistaraClient, offerCatalogDao, dynamicConf)
	return redemptionService, nil
}

func InitializeRedemptionConsumerService(db types.CasperPGDB, awsConf aws.Config, commsClient types2.CasperCommsClientWithInterceptors, accrualClient accrual.AccrualClient, vendorMappingClient vendormapping.VendorMappingServiceClient, dynamicConf *genconf.Config, loyltyClient loylty.LoyltyClient, qwikcilverVgClient qwikcilver.QwikcilverClient, dreamfolksVgClient dreamfolks.DreamfolksClient, thriweVgClient thriwe.ThriweClient, userClient user.UsersClient, actorClient actor.ActorClient, eventsBroker events.Broker, config2 *config.Config, userGroupClient group.GroupClient, cmsServiceClient cms.CmsServiceClient, successfulOfferRedemptionEventPublisher types2.OfferRedemptionStatusUpdateEventsSnsPublisher, rewardsClient rewards.RewardsGeneratorClient, segmentClient segment.SegmentationServiceClient, cardClient provisioning.CardProvisioningClient, onboardingClient onboarding.OnboardingClient, poshvineVgClient poshvine.PoshvineClient, fireflyClient firefly.FireflyClient, accountingClient accounting.AccountingClient, externalVendorRedemptionClient external_vendor_redemption.ExternalVendorRedemptionServiceClient, cacheStorage types2.CasperRedisStore) *consumer.ConsumerService {
	offerCatalogServiceServer := offerCatalogServiceServerProvider(db, loyltyClient, segmentClient, vendorMappingClient, cardClient, userClient, onboardingClient, poshvineVgClient, fireflyClient, accountingClient, config2, dynamicConf, cacheStorage, externalVendorRedemptionClient, userGroupClient)
	redemptionRequestDaoImpl := dao3.NewRedemptionRequestDaoImpl(db)
	redeemedOfferDaoImpl := dao3.NewRedeemedOfferDaoImpl(db)
	gormDB := types.CasperPGDBGormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	discountDaoPgdb := dao2.NewDiscountDaoPgdb(db)
	commonProcessorUtil := stateprocessor.NewCommonProcessorUtil(redeemedOfferDaoImpl, successfulOfferRedemptionEventPublisher)
	offerInventoryServiceServer := offerInventoryServiceServerProvider(db, dynamicConf, cacheStorage)
	purchaseOfferProcessor := stateprocessor.NewPurchaseOfferProcessor(commonProcessorUtil, offerInventoryServiceServer)
	returnPurchasedOfferProcessor := stateprocessor.NewReturnPurchasedOfferProcessor(commonProcessorUtil, offerInventoryServiceServer)
	fiCoinsAccount := offeraccount.NewFICoinsAccount(accrualClient)
	accountFactory := offeraccount.NewAccountFactory(fiCoinsAccount)
	debitFromAccountProcessor := stateprocessor.NewDebitFromAccountProcessor(commonProcessorUtil, accountFactory)
	vendorRedemptionFailureInternalFlowProcessor := stateprocessor.NewVendorRedemptionFailureInternalFlowProcessor(commonProcessorUtil)
	loyltyRedemptionDaoImpl := dao3.NewLoyltyRedemptionDaoImpl(db)
	userHelperService := helper.NewUserHelperService(actorClient, userClient, userGroupClient, rewardsClient)
	loyltyVendor := offervendor2.NewLoyltyVendor(loyltyClient, loyltyRedemptionDaoImpl, vendorMappingClient, userHelperService, config2)
	offlineRedemptionDaoImpl := dao3.NewOfflineRedemptionDaoImpl(db)
	offlineVendor := offervendor2.NewOfflineVendor(offlineRedemptionDaoImpl)
	qwikcilverRedemptionDaoImpl := dao3.NewQwikcilverRedemptionDaoImpl(db)
	qwikcilverVendor := offervendor2.NewQwikcilverVendor(qwikcilverVgClient, qwikcilverRedemptionDaoImpl, userClient)
	noOpVendor := offervendor2.NewNoOpVendor()
	thriweVendor := offervendor2.NewThriweVendor(thriweVgClient, vendorMappingClient, userClient, config2)
	vistaraRedemptionDaoImpl := dao3.NewVistaraRedemptionDaoImpl(db)
	vistaraVendor := offervendor2.NewVistaraVendor(vistaraRedemptionDaoImpl)
	inHouseRedemptionDaoImpl := dao3.NewInHouseRedemptionDaoImpl(db)
	inHouseVendor := offervendor2.NewInHouseVendor(inHouseRedemptionDaoImpl, cmsServiceClient)
	dreamfolksRedemptionDaoImpl := dao3.NewDreamfolksRedemptionDaoImpl(db)
	dreamfolksVendor := offervendor2.NewDreamfolksVendor(dreamfolksVgClient, userClient, dreamfolksRedemptionDaoImpl)
	vendorRedemptionDaoImpl := dao3.NewVendorRedemptionDaoImpl(db)
	itcVendor := offervendor2.NewItcVendor(vendorRedemptionDaoImpl, config2)
	vendorFactory := offervendor2.NewVendorFactory(loyltyVendor, offlineVendor, qwikcilverVendor, noOpVendor, thriweVendor, vistaraVendor, inHouseVendor, dreamfolksVendor, itcVendor)
	redeemedOfferCryptor := redeemedOfferCryptorProvider(config2, awsConf)
	kmsKeys := kmsKeysConfProvider(config2)
	redeemVendorOfferProcessor := stateprocessor.NewRedeemVendorOfferProcessor(commonProcessorUtil, vendorFactory, redeemedOfferCryptor, kmsKeys)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	iNotificationService := iNotificationServiceProvider(actorClient, commsCommsClient, config2)
	failRedemptionProcessor := stateprocessor.NewFailRedemptionProcessor(commonProcessorUtil, iNotificationService, eventsBroker)
	redemptionSuccessProcessor := stateprocessor.NewRedemptionSuccessProcessor(commonProcessorUtil, iNotificationService, eventsBroker)
	vendorRedemptionUpdateProcessor := stateprocessor.NewVendorRedemptionUpdateProcessor(commonProcessorUtil)
	validateDebitedAmountReversalProcessor := stateprocessor.NewValidateDebitedAmountReversalProcessor(commonProcessorUtil, userHelperService)
	factory := stateprocessor.NewFactory(purchaseOfferProcessor, returnPurchasedOfferProcessor, debitFromAccountProcessor, vendorRedemptionFailureInternalFlowProcessor, redeemVendorOfferProcessor, failRedemptionProcessor, redemptionSuccessProcessor, vendorRedemptionUpdateProcessor, validateDebitedAmountReversalProcessor)
	redemptionProcessor := redemption2.NewRedemptionProcessor(gormTxnExecutor, redemptionRequestDaoImpl, redeemedOfferDaoImpl, discountDaoPgdb, factory)
	casperCxActivityHelperService := helper.NewCasperCxActivityHelperService(eventsBroker, userHelperService, dynamicConf)
	consumerService := consumer.NewConsumerService(offerCatalogServiceServer, redemptionRequestDaoImpl, redeemedOfferDaoImpl, redemptionProcessor, casperCxActivityHelperService)
	return consumerService
}

func InitializeRedemptionNotificationService(actorClient actor.ActorClient, commsClient comms.CommsClient, conf *config.Config) *notification.Service {
	redemptionNotificationParams := redemptionNotificationParamsProvider(conf)
	notificationService := notification.NewService(commsClient, actorClient, redemptionNotificationParams)
	return notificationService
}

func InitializeDevService(db types.CasperPGDB, loyltyVgClient loylty.LoyltyClient, qwikcilverVgClient qwikcilver.QwikcilverClient, cacheStorage types2.CasperRedisStore, dynamicConf *genconf.Config) *developer.CasperDevService {
	cacheConfig := CacheConfigProvider(dynamicConf)
	offerCatalogDaoImpl := dao.NewOfferCatalogDao(db)
	client := types2.CasperRedisStoreProvider(cacheStorage)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	offerCatalogCacheDao := dao.NewOfferCatalogCacheDao(offerCatalogDaoImpl, redisCacheStorage, cacheConfig)
	offerCatalogDao := dao.ProvideOfferCatalogDao(cacheConfig, offerCatalogCacheDao, offerCatalogDaoImpl)
	offerProcessor := processor.NewOfferProcessor(offerCatalogDao)
	defaultTime := datetime.NewDefaultTime()
	offerInventoryDaoImpl := dao.NewOfferInventoryDao(db, defaultTime)
	offerInventoryProcessor := processor.NewOfferInventoryProcessor(offerInventoryDaoImpl)
	offerListingDaoImpl := dao.NewOfferListingDao(db)
	offerListingCacheDao := dao.NewOfferListingCacheDao(redisCacheStorage, offerListingDaoImpl, cacheConfig)
	offerListingDao := dao.ProvideOfferListingDao(cacheConfig, offerListingCacheDao, offerListingDaoImpl)
	offerListingProcessor := processor.NewOfferListingProcessor(offerListingDao)
	redeemedOfferDaoImpl := dao3.NewRedeemedOfferDaoImpl(db)
	redeemedOfferProcessor := processor.NewRedeemedOfferProcessor(redeemedOfferDaoImpl)
	redemptionRequestDaoImpl := dao3.NewRedemptionRequestDaoImpl(db)
	redemptionRequestProcessor := processor.NewRedemptionRequestProcessor(redemptionRequestDaoImpl)
	loyltyRedemptionDaoImpl := dao3.NewLoyltyRedemptionDaoImpl(db)
	loyltyRedemptionProcessor := processor.NewLoyltyRedemptionProcessor(loyltyRedemptionDaoImpl)
	offlineRedemptionDaoImpl := dao3.NewOfflineRedemptionDaoImpl(db)
	offlineRedemptionProcessor := processor.NewOfflineRedemptionProcessor(offlineRedemptionDaoImpl)
	loyltyEgvOfferCatalogProcessor := processor.NewLoyltyEgvOfferCatalogProcessor(loyltyVgClient)
	loyltyEgvProductDetailProcessor := processor.NewLoyltyEgvProductDetailProcessor(loyltyVgClient)
	qwikcilverRedemptionDaoImpl := dao3.NewQwikcilverRedemptionDaoImpl(db)
	qwikcilverRedemptionProcessor := processor.NewQwikcilverRedemptionProcessor(qwikcilverRedemptionDaoImpl)
	qwikcilverCategoryDetailsProcessor := processor.NewQwikcilverCategoryDetailsProcessor(qwikcilverVgClient)
	qwikcilverProductListProcessor := processor.NewQwikcilverProductListProcessor(qwikcilverVgClient)
	qwikcilverProductDetailsProcessor := processor.NewQwikcilverProductDetailsProcessor(qwikcilverVgClient)
	pgdbExchangerOfferDao := dao4.NewPGDBExchangerOfferDao(db)
	exchangerOfferCacheDao := dao4.NewExchangerOfferCacheDao(redisCacheStorage, pgdbExchangerOfferDao, cacheConfig)
	iExchangerOfferDao := dao4.ProvideExchangerOfferDao(cacheConfig, exchangerOfferCacheDao, pgdbExchangerOfferDao)
	pgdbExchangerOfferListingDao := dao4.NewPGDBExchangerOfferListingDao(db)
	exchangerOfferListingCacheDao := dao4.NewExchangerOfferListingCacheDao(redisCacheStorage, pgdbExchangerOfferListingDao, cacheConfig)
	iExchangerOfferListingDao := dao4.ProvideExchangerOfferListingDao(cacheConfig, exchangerOfferListingCacheDao, pgdbExchangerOfferListingDao)
	offerDisplayRankProcessor := processor.NewOfferDisplayRankProcessor(offerListingDao, offerCatalogDao, iExchangerOfferDao, iExchangerOfferListingDao)
	exchangerOfferProcessor := processor.NewExchangerOfferProcessor(iExchangerOfferDao)
	pgdbExchangerOfferGroupDao := dao4.NewPGDBExchangerOfferGroupDao(db)
	exchangerOfferGroupProcessor := processor.NewExchangerOfferGroupProcessor(pgdbExchangerOfferGroupDao)
	pgdbExchangerOfferActorAttemptDao := dao4.NewPGDBExchangerOfferActorAttemptDao(db)
	exchangerOfferActorAttemptProcessor := processor.NewExchangerOfferActorAttemptProcessor(pgdbExchangerOfferActorAttemptDao)
	exchangerOfferListingProcessor := processor.NewExchangerOfferListingProcessor(iExchangerOfferListingDao)
	pgdbExchangerOfferOrderDao := dao4.NewPGDBExchangerOfferOrderDao(db)
	exchangerOfferOrderProcessor := processor.NewExchangerOfferOrderProcessor(pgdbExchangerOfferOrderDao)
	pgdbExchangerOfferInventoryDao := dao4.NewPGDBExchangerOfferInventoryDao(db)
	exchangerOfferInventoryProcessor := processor.NewExchangerOfferInventoryProcessor(pgdbExchangerOfferInventoryDao)
	pgdbeoLevelRewardUnitsUtilisationDao := dao4.NewPGDBEOLevelRewardUnitsUtilisationDao(db)
	exchangerOfferLevelRewardUnitsUtilisationProcessor := processor.NewExchangerOfferLevelRewardUnitsUtilisationProcessor(pgdbeoLevelRewardUnitsUtilisationDao)
	pgdbExchangerOfferOrderFulfillmentRequestDao := dao4.NewPGDBExchangerOfferOrderFulfillmentRequestDao(db)
	exchangerOfferOrderFulfillmentRequestProcessor := processor.NewExchangerOfferOrderFulfillmentRequestProcessor(pgdbExchangerOfferOrderFulfillmentRequestDao)
	discountDaoPgdb := dao2.NewDiscountDaoPgdb(db)
	discountProcessor := processor.NewDiscountProcessor(discountDaoPgdb)
	inHouseRedemptionDaoImpl := dao3.NewInHouseRedemptionDaoImpl(db)
	inHouseRedemptionProcessor := processor.NewInHouseRedemptionProcessor(inHouseRedemptionDaoImpl)
	gormDB := GormProvider(db)
	pgdbExternalVendorRedemptionDao := dao5.NewExternalVendorRedemptionPGDB(gormDB)
	externalVendorRedemptionProcessor := processor.NewExternalVendorRedemptionProcessor(pgdbExternalVendorRedemptionDao)
	pgdbFiStoreRedemptionDao := dao5.NewFiStoreRedemptionPGDB(gormDB)
	fiStoreRedemptionProcessor := processor.NewFiStoreRedemptionProcessor(pgdbFiStoreRedemptionDao)
	devFactory := developer.NewDevFactory(offerProcessor, offerInventoryProcessor, offerListingProcessor, redeemedOfferProcessor, redemptionRequestProcessor, loyltyRedemptionProcessor, offlineRedemptionProcessor, loyltyEgvOfferCatalogProcessor, loyltyEgvProductDetailProcessor, qwikcilverRedemptionProcessor, qwikcilverCategoryDetailsProcessor, qwikcilverProductListProcessor, qwikcilverProductDetailsProcessor, offerDisplayRankProcessor, exchangerOfferProcessor, exchangerOfferGroupProcessor, exchangerOfferActorAttemptProcessor, exchangerOfferListingProcessor, exchangerOfferOrderProcessor, exchangerOfferInventoryProcessor, exchangerOfferLevelRewardUnitsUtilisationProcessor, exchangerOfferOrderFulfillmentRequestProcessor, discountProcessor, inHouseRedemptionProcessor, externalVendorRedemptionProcessor, fiStoreRedemptionProcessor)
	casperDevService := developer.NewCasperDevService(devFactory)
	return casperDevService
}

func InitializeRedeemedOfferCryptor(kms3 *kms.Client) *cryptor.RedeemedOfferCryptorImpl {
	kmsSymmetricCryptor := kms2.NewKMSSymmetricCryptor(kms3)
	redeemedOfferCryptorImpl := cryptor.NewRedeemedOfferCryptorImpl(kmsSymmetricCryptor)
	return redeemedOfferCryptorImpl
}

func InitializeCasperJobHelperService(db types.CasperPGDB) *jobhelper.CasperJobHelperService {
	offlineRedemptionDaoImpl := dao3.NewOfflineRedemptionDaoImpl(db)
	casperJobHelperService := jobhelper.NewCasperJobHelperService(offlineRedemptionDaoImpl)
	return casperJobHelperService
}

func InitializeExchangerOfferService(db types.CasperPGDB, healthEngineClient health_engine.HealthEngineServiceClient, accrualClient accrual.AccrualClient, orderClient order.OrderServiceClient, payClient pay.PayClient, actorClient actor.ActorClient, timelineClient timeline.TimelineServiceClient, accountPiRelationClient account_pi.AccountPIRelationClient, config2 *config.Config, dynamicConf *genconf.Config, delayPublisher types2.ExchangerOrderOrchestrationEventsSqsDelayPublisher, vendorMappingClient vendormapping.VendorMappingServiceClient, notificationDelayPublisher types2.ExchangerOrderNotificationCustomDelayPublisher, publisher types2.ExchangerOrderOrchestrationEventsSqsPublisher, userGroupClient group.GroupClient, usersClient user.UsersClient, eventsBroker events.Broker, awsConf aws.Config, loyltyClient loylty.LoyltyClient, qwikcilverVgClient qwikcilver.QwikcilverClient, dreamfolksVgClient dreamfolks.DreamfolksClient, thriweVgClient thriwe.ThriweClient, cmsServiceClient cms.CmsServiceClient, successfulOfferRedemptionEventPublisher types2.OfferRedemptionStatusUpdateEventsSnsPublisher, rewardsClient rewards.RewardsGeneratorClient, segmentClient segment.SegmentationServiceClient, questCacheStorage types.QuestCacheStorage, questManagerCl manager.ManagerClient, ffBillingClient billing.BillingClient, onboardingClient onboarding.OnboardingClient, savingsClient savings.SavingsClient, operationalStatusClient operstatus.OperationalStatusServiceClient, fireflyClient firefly.FireflyClient) *exchanger.Service {
	cacheConfig := CacheConfigProvider(dynamicConf)
	cacheStorage := types.QuestCacheStorageProvider(questCacheStorage)
	pgdbExchangerOfferListingDao := dao4.NewPGDBExchangerOfferListingDao(db)
	exchangerOfferListingCacheDao := dao4.NewExchangerOfferListingCacheDao(cacheStorage, pgdbExchangerOfferListingDao, cacheConfig)
	iExchangerOfferListingDao := dao4.ProvideExchangerOfferListingDao(cacheConfig, exchangerOfferListingCacheDao, pgdbExchangerOfferListingDao)
	pgdbExchangerOfferDao := dao4.NewPGDBExchangerOfferDao(db)
	exchangerOfferCacheDao := dao4.NewExchangerOfferCacheDao(cacheStorage, pgdbExchangerOfferDao, cacheConfig)
	iExchangerOfferDao := dao4.ProvideExchangerOfferDao(cacheConfig, exchangerOfferCacheDao, pgdbExchangerOfferDao)
	pgdbExchangerOfferOrderDao := dao4.NewPGDBExchangerOfferOrderDao(db)
	pgdbExchangerOfferActorAttemptDao := dao4.NewPGDBExchangerOfferActorAttemptDao(db)
	pgdbExchangerOfferGroupDao := dao4.NewPGDBExchangerOfferGroupDao(db)
	pgdbeoGroupRewardUnitsActorUtilisationDao := dao4.NewPGDBEOGroupRewardUnitsActorUtilisationDao(db)
	pgdbExchangerOfferInventoryDao := dao4.NewPGDBExchangerOfferInventoryDao(db)
	gormDB := GormProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	fiCoinsAccountSvc := debitaccount.NewFICoinsAccountSvc(accrualClient)
	factory := debitaccount.NewFactory(fiCoinsAccountSvc)
	debitOfferPriceProcessor := stateprocessor2.NewDebitOfferPriceProcessor(factory, pgdbExchangerOfferOrderDao, eventsBroker)
	defaultRewardUnitsCalculator := rewardunitscalculator.NewDefaultRewardUnitsCalculator()
	pgdbeoLevelRewardUnitsUtilisationDao := dao4.NewPGDBEOLevelRewardUnitsUtilisationDao(db)
	pgdbeoUserLevelInventoryUnitsUtilisationDao := dao4.NewPGDBEOUserLevelInventoryUnitsUtilisationDao(db)
	rewardUnitsAndInventoryCapping := reward_units_and_inventory_capping.NewRewardUnitsAndInventoryCapping(gormTxnExecutor, pgdbeoGroupRewardUnitsActorUtilisationDao, pgdbExchangerOfferInventoryDao, pgdbeoLevelRewardUnitsUtilisationDao, pgdbeoUserLevelInventoryUnitsUtilisationDao)
	earlyUserRewardMultiplier := option_post_processor.NewEarlyUserRewardMultiplier()
	option_post_processorFactory := option_post_processor.NewFactory(earlyUserRewardMultiplier)
	conditionsEvaluator := conditions_evaluator.NewConditionsEvaluator(pgdbExchangerOfferOrderDao, dynamicConf)
	generateRewardOptionsProcessor := stateprocessor2.NewGenerateRewardOptionsProcessor(pgdbExchangerOfferOrderDao, defaultRewardUnitsCalculator, pgdbExchangerOfferGroupDao, iExchangerOfferDao, pgdbeoLevelRewardUnitsUtilisationDao, pgdbeoGroupRewardUnitsActorUtilisationDao, rewardUnitsAndInventoryCapping, gormTxnExecutor, dynamicConf, option_post_processorFactory, conditionsEvaluator, onboardingClient)
	updateRedemptionCountProcessor := stateprocessor2.NewUpdateRedemptionCountProcessor(pgdbExchangerOfferOrderDao)
	clock := clockwork.NewRealClock()
	csisDownTimeCheck := downtime.NewCsisDownTimeCheck(healthEngineClient, clock)
	pgdbExchangerOfferOrderFulfillmentRequestDao := dao4.NewPGDBExchangerOfferOrderFulfillmentRequestDao(db)
	cashRewardFulfillmentSvc := order_fulfillment.NewCashRewardFulfillmentSvc(actorClient, timelineClient, orderClient, payClient, accountPiRelationClient, csisDownTimeCheck, pgdbExchangerOfferOrderFulfillmentRequestDao, config2, dynamicConf, savingsClient)
	v := FiCoinsFulfillmentSvcOptsProvider()
	fiCoinsFulfillmentSvc := order_fulfillment.NewFiCoinsFulfillmentSvc(accrualClient, pgdbExchangerOfferOrderFulfillmentRequestDao, fireflyClient, v...)
	physicalMerchandiseFulfillmentSvc := order_fulfillment.NewPhysicalMerchandiseFulfillmentSvc()
	loyltyRedemptionDaoImpl := dao3.NewLoyltyRedemptionDaoImpl(db)
	userHelperService := helper.NewUserHelperService(actorClient, usersClient, userGroupClient, rewardsClient)
	loyltyVendor := offervendor2.NewLoyltyVendor(loyltyClient, loyltyRedemptionDaoImpl, vendorMappingClient, userHelperService, config2)
	offlineRedemptionDaoImpl := dao3.NewOfflineRedemptionDaoImpl(db)
	offlineVendor := offervendor2.NewOfflineVendor(offlineRedemptionDaoImpl)
	qwikcilverRedemptionDaoImpl := dao3.NewQwikcilverRedemptionDaoImpl(db)
	qwikcilverVendor := offervendor2.NewQwikcilverVendor(qwikcilverVgClient, qwikcilverRedemptionDaoImpl, usersClient)
	noOpVendor := offervendor2.NewNoOpVendor()
	thriweVendor := offervendor2.NewThriweVendor(thriweVgClient, vendorMappingClient, usersClient, config2)
	vistaraRedemptionDaoImpl := dao3.NewVistaraRedemptionDaoImpl(db)
	vistaraVendor := offervendor2.NewVistaraVendor(vistaraRedemptionDaoImpl)
	inHouseRedemptionDaoImpl := dao3.NewInHouseRedemptionDaoImpl(db)
	inHouseVendor := offervendor2.NewInHouseVendor(inHouseRedemptionDaoImpl, cmsServiceClient)
	dreamfolksRedemptionDaoImpl := dao3.NewDreamfolksRedemptionDaoImpl(db)
	dreamfolksVendor := offervendor2.NewDreamfolksVendor(dreamfolksVgClient, usersClient, dreamfolksRedemptionDaoImpl)
	vendorRedemptionDaoImpl := dao3.NewVendorRedemptionDaoImpl(db)
	itcVendor := offervendor2.NewItcVendor(vendorRedemptionDaoImpl, config2)
	vendorFactory := offervendor2.NewVendorFactory(loyltyVendor, offlineVendor, qwikcilverVendor, noOpVendor, thriweVendor, vistaraVendor, inHouseVendor, dreamfolksVendor, itcVendor)
	redeemedOfferCryptor := redeemedOfferCryptorProvider(config2, awsConf)
	kmsKeys := kmsKeysConfProvider(config2)
	egvFulfillmentSvc := order_fulfillment.NewEgvFulfillmentSvc(vendorFactory, pgdbExchangerOfferOrderFulfillmentRequestDao, redeemedOfferCryptor, kmsKeys)
	creditCardBillEraserRewardFulfillmentSvc := order_fulfillment.NewCreditCardBillEraserRewardFulfillmentSvc(ffBillingClient)
	order_fulfillmentFactory := order_fulfillment.NewFactory(cashRewardFulfillmentSvc, fiCoinsFulfillmentSvc, physicalMerchandiseFulfillmentSvc, egvFulfillmentSvc, creditCardBillEraserRewardFulfillmentSvc)
	fulfillClaimedRewardProcessor := stateprocessor2.NewFulfillClaimedRewardProcessor(order_fulfillmentFactory, pgdbExchangerOfferOrderDao, pgdbExchangerOfferOrderFulfillmentRequestDao, eventsBroker, kmsKeys, redeemedOfferCryptor)
	chooseRewardOptionProcessor := stateprocessor2.NewChooseRewardOptionProcessor(pgdbExchangerOfferOrderDao, eventsBroker, pgdbeoGroupRewardUnitsActorUtilisationDao, pgdbExchangerOfferGroupDao, rewardUnitsAndInventoryCapping, gormTxnExecutor)
	checkForUserInputOrInitFulfillmentProcessor := stateprocessor2.NewCheckForUserInputOrInitFulfillmentProcessor(pgdbExchangerOfferOrderDao, notificationDelayPublisher, config2)
	addUserInputProcessor := stateprocessor2.NewAddUserInputProcessor(pgdbExchangerOfferOrderDao, eventsBroker)
	validateDebitedOfferPriceReversalProcessor := stateprocessor2.NewValidateDebitedOfferPriceReversalProcessor(pgdbExchangerOfferOrderDao, userHelperService)
	decrementRedemptionCountProcessor := stateprocessor2.NewDecrementRedemptionCountProcessor(pgdbExchangerOfferOrderDao)
	stateprocessorFactory := stateprocessor2.NewFactory(debitOfferPriceProcessor, generateRewardOptionsProcessor, updateRedemptionCountProcessor, fulfillClaimedRewardProcessor, chooseRewardOptionProcessor, checkForUserInputOrInitFulfillmentProcessor, addUserInputProcessor, validateDebitedOfferPriceReversalProcessor, decrementRedemptionCountProcessor)
	orchestrator := redemption_statemachine.NewOrchestrator(iExchangerOfferDao, pgdbExchangerOfferOrderDao, pgdbExchangerOfferActorAttemptDao, gormTxnExecutor, stateprocessorFactory, successfulOfferRedemptionEventPublisher, onboardingClient)
	client := slackClientProvider(config2)
	offerAttachmentBuilder := attachment_builder.NewOfferAttachmentBuilder()
	exchangerOfferAttachmentBuilder := attachment_builder.NewExchangerOfferAttachmentBuilder()
	attachmentBuilderFactory := attachment_builder.NewAttachmentBuilderFactory(offerAttachmentBuilder, exchangerOfferAttachmentBuilder)
	slackHelperSvc := slack_helper.NewSlackHelperSvc(client, attachmentBuilderFactory, dynamicConf)
	genconfConfig := questSDKClientConfProvider(dynamicConf)
	questsdkClient := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGroupClient, usersClient, actorClient, segmentClient, cacheStorage, eventsBroker)
	casperCxActivityHelperService := helper.NewCasperCxActivityHelperService(eventsBroker, userHelperService, dynamicConf)
	exchangerService := exchanger.NewService(iExchangerOfferListingDao, iExchangerOfferDao, pgdbExchangerOfferOrderDao, pgdbExchangerOfferActorAttemptDao, pgdbExchangerOfferGroupDao, pgdbeoGroupRewardUnitsActorUtilisationDao, pgdbExchangerOfferInventoryDao, gormTxnExecutor, orchestrator, delayPublisher, publisher, userHelperService, redeemedOfferCryptor, slackHelperSvc, savingsClient, operationalStatusClient, dynamicConf, questsdkClient, segmentClient, casperCxActivityHelperService)
	return exchangerService
}

func InitializeExchangerConsumerService(db types.CasperPGDB, healthEngineClient health_engine.HealthEngineServiceClient, accrualClient accrual.AccrualClient, orderClient order.OrderServiceClient, payClient pay.PayClient, actorClient actor.ActorClient, commsClient types2.CasperCommsClientWithInterceptors, userGroupClient group.GroupClient, usersClient user.UsersClient, timelineClient timeline.TimelineServiceClient, accountPiRelationClient account_pi.AccountPIRelationClient, vendorMappingClient vendormapping.VendorMappingServiceClient, eventsBroker events.Broker, notificationDelayPublisher types2.ExchangerOrderNotificationCustomDelayPublisher, exchangerOrchDlqDelayPublisher types2.ExchangerOrderOrchestrationEventsSqsPublisherDlq, config2 *config.Config, dynamicConf *genconf.Config, awsConf aws.Config, loyltyClient loylty.LoyltyClient, qwikcilverVgClient qwikcilver.QwikcilverClient, dreamfolksVgClient dreamfolks.DreamfolksClient, thriweVgClient thriwe.ThriweClient, cmsServiceClient cms.CmsServiceClient, successfulOfferRedemptionEventPublisher types2.OfferRedemptionStatusUpdateEventsSnsPublisher, rewardsClient rewards.RewardsGeneratorClient, ffBillingClient billing.BillingClient, onboardingClient onboarding.OnboardingClient, redisStore types2.CasperRedisStore, savingsClient savings.SavingsClient, fireflyClient firefly.FireflyClient) *consumer2.Service {
	cacheConfig := CacheConfigProvider(dynamicConf)
	client := types2.CasperRedisStoreProvider(redisStore)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	pgdbExchangerOfferDao := dao4.NewPGDBExchangerOfferDao(db)
	exchangerOfferCacheDao := dao4.NewExchangerOfferCacheDao(redisCacheStorage, pgdbExchangerOfferDao, cacheConfig)
	iExchangerOfferDao := dao4.ProvideExchangerOfferDao(cacheConfig, exchangerOfferCacheDao, pgdbExchangerOfferDao)
	pgdbExchangerOfferOrderDao := dao4.NewPGDBExchangerOfferOrderDao(db)
	pgdbExchangerOfferActorAttemptDao := dao4.NewPGDBExchangerOfferActorAttemptDao(db)
	gormDB := GormProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	fiCoinsAccountSvc := debitaccount.NewFICoinsAccountSvc(accrualClient)
	factory := debitaccount.NewFactory(fiCoinsAccountSvc)
	debitOfferPriceProcessor := stateprocessor2.NewDebitOfferPriceProcessor(factory, pgdbExchangerOfferOrderDao, eventsBroker)
	defaultRewardUnitsCalculator := rewardunitscalculator.NewDefaultRewardUnitsCalculator()
	pgdbExchangerOfferGroupDao := dao4.NewPGDBExchangerOfferGroupDao(db)
	pgdbeoLevelRewardUnitsUtilisationDao := dao4.NewPGDBEOLevelRewardUnitsUtilisationDao(db)
	pgdbeoGroupRewardUnitsActorUtilisationDao := dao4.NewPGDBEOGroupRewardUnitsActorUtilisationDao(db)
	pgdbExchangerOfferInventoryDao := dao4.NewPGDBExchangerOfferInventoryDao(db)
	pgdbeoUserLevelInventoryUnitsUtilisationDao := dao4.NewPGDBEOUserLevelInventoryUnitsUtilisationDao(db)
	rewardUnitsAndInventoryCapping := reward_units_and_inventory_capping.NewRewardUnitsAndInventoryCapping(gormTxnExecutor, pgdbeoGroupRewardUnitsActorUtilisationDao, pgdbExchangerOfferInventoryDao, pgdbeoLevelRewardUnitsUtilisationDao, pgdbeoUserLevelInventoryUnitsUtilisationDao)
	earlyUserRewardMultiplier := option_post_processor.NewEarlyUserRewardMultiplier()
	option_post_processorFactory := option_post_processor.NewFactory(earlyUserRewardMultiplier)
	conditionsEvaluator := conditions_evaluator.NewConditionsEvaluator(pgdbExchangerOfferOrderDao, dynamicConf)
	generateRewardOptionsProcessor := stateprocessor2.NewGenerateRewardOptionsProcessor(pgdbExchangerOfferOrderDao, defaultRewardUnitsCalculator, pgdbExchangerOfferGroupDao, iExchangerOfferDao, pgdbeoLevelRewardUnitsUtilisationDao, pgdbeoGroupRewardUnitsActorUtilisationDao, rewardUnitsAndInventoryCapping, gormTxnExecutor, dynamicConf, option_post_processorFactory, conditionsEvaluator, onboardingClient)
	updateRedemptionCountProcessor := stateprocessor2.NewUpdateRedemptionCountProcessor(pgdbExchangerOfferOrderDao)
	clock := clockwork.NewRealClock()
	csisDownTimeCheck := downtime.NewCsisDownTimeCheck(healthEngineClient, clock)
	pgdbExchangerOfferOrderFulfillmentRequestDao := dao4.NewPGDBExchangerOfferOrderFulfillmentRequestDao(db)
	cashRewardFulfillmentSvc := order_fulfillment.NewCashRewardFulfillmentSvc(actorClient, timelineClient, orderClient, payClient, accountPiRelationClient, csisDownTimeCheck, pgdbExchangerOfferOrderFulfillmentRequestDao, config2, dynamicConf, savingsClient)
	v := FiCoinsFulfillmentSvcOptsProvider()
	fiCoinsFulfillmentSvc := order_fulfillment.NewFiCoinsFulfillmentSvc(accrualClient, pgdbExchangerOfferOrderFulfillmentRequestDao, fireflyClient, v...)
	physicalMerchandiseFulfillmentSvc := order_fulfillment.NewPhysicalMerchandiseFulfillmentSvc()
	loyltyRedemptionDaoImpl := dao3.NewLoyltyRedemptionDaoImpl(db)
	userHelperService := helper.NewUserHelperService(actorClient, usersClient, userGroupClient, rewardsClient)
	loyltyVendor := offervendor2.NewLoyltyVendor(loyltyClient, loyltyRedemptionDaoImpl, vendorMappingClient, userHelperService, config2)
	offlineRedemptionDaoImpl := dao3.NewOfflineRedemptionDaoImpl(db)
	offlineVendor := offervendor2.NewOfflineVendor(offlineRedemptionDaoImpl)
	qwikcilverRedemptionDaoImpl := dao3.NewQwikcilverRedemptionDaoImpl(db)
	qwikcilverVendor := offervendor2.NewQwikcilverVendor(qwikcilverVgClient, qwikcilverRedemptionDaoImpl, usersClient)
	noOpVendor := offervendor2.NewNoOpVendor()
	thriweVendor := offervendor2.NewThriweVendor(thriweVgClient, vendorMappingClient, usersClient, config2)
	vistaraRedemptionDaoImpl := dao3.NewVistaraRedemptionDaoImpl(db)
	vistaraVendor := offervendor2.NewVistaraVendor(vistaraRedemptionDaoImpl)
	inHouseRedemptionDaoImpl := dao3.NewInHouseRedemptionDaoImpl(db)
	inHouseVendor := offervendor2.NewInHouseVendor(inHouseRedemptionDaoImpl, cmsServiceClient)
	dreamfolksRedemptionDaoImpl := dao3.NewDreamfolksRedemptionDaoImpl(db)
	dreamfolksVendor := offervendor2.NewDreamfolksVendor(dreamfolksVgClient, usersClient, dreamfolksRedemptionDaoImpl)
	vendorRedemptionDaoImpl := dao3.NewVendorRedemptionDaoImpl(db)
	itcVendor := offervendor2.NewItcVendor(vendorRedemptionDaoImpl, config2)
	vendorFactory := offervendor2.NewVendorFactory(loyltyVendor, offlineVendor, qwikcilverVendor, noOpVendor, thriweVendor, vistaraVendor, inHouseVendor, dreamfolksVendor, itcVendor)
	redeemedOfferCryptor := redeemedOfferCryptorProvider(config2, awsConf)
	kmsKeys := kmsKeysConfProvider(config2)
	egvFulfillmentSvc := order_fulfillment.NewEgvFulfillmentSvc(vendorFactory, pgdbExchangerOfferOrderFulfillmentRequestDao, redeemedOfferCryptor, kmsKeys)
	creditCardBillEraserRewardFulfillmentSvc := order_fulfillment.NewCreditCardBillEraserRewardFulfillmentSvc(ffBillingClient)
	order_fulfillmentFactory := order_fulfillment.NewFactory(cashRewardFulfillmentSvc, fiCoinsFulfillmentSvc, physicalMerchandiseFulfillmentSvc, egvFulfillmentSvc, creditCardBillEraserRewardFulfillmentSvc)
	fulfillClaimedRewardProcessor := stateprocessor2.NewFulfillClaimedRewardProcessor(order_fulfillmentFactory, pgdbExchangerOfferOrderDao, pgdbExchangerOfferOrderFulfillmentRequestDao, eventsBroker, kmsKeys, redeemedOfferCryptor)
	chooseRewardOptionProcessor := stateprocessor2.NewChooseRewardOptionProcessor(pgdbExchangerOfferOrderDao, eventsBroker, pgdbeoGroupRewardUnitsActorUtilisationDao, pgdbExchangerOfferGroupDao, rewardUnitsAndInventoryCapping, gormTxnExecutor)
	checkForUserInputOrInitFulfillmentProcessor := stateprocessor2.NewCheckForUserInputOrInitFulfillmentProcessor(pgdbExchangerOfferOrderDao, notificationDelayPublisher, config2)
	addUserInputProcessor := stateprocessor2.NewAddUserInputProcessor(pgdbExchangerOfferOrderDao, eventsBroker)
	validateDebitedOfferPriceReversalProcessor := stateprocessor2.NewValidateDebitedOfferPriceReversalProcessor(pgdbExchangerOfferOrderDao, userHelperService)
	decrementRedemptionCountProcessor := stateprocessor2.NewDecrementRedemptionCountProcessor(pgdbExchangerOfferOrderDao)
	stateprocessorFactory := stateprocessor2.NewFactory(debitOfferPriceProcessor, generateRewardOptionsProcessor, updateRedemptionCountProcessor, fulfillClaimedRewardProcessor, chooseRewardOptionProcessor, checkForUserInputOrInitFulfillmentProcessor, addUserInputProcessor, validateDebitedOfferPriceReversalProcessor, decrementRedemptionCountProcessor)
	orchestrator := redemption_statemachine.NewOrchestrator(iExchangerOfferDao, pgdbExchangerOfferOrderDao, pgdbExchangerOfferActorAttemptDao, gormTxnExecutor, stateprocessorFactory, successfulOfferRedemptionEventPublisher, onboardingClient)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	casperCxActivityHelperService := helper.NewCasperCxActivityHelperService(eventsBroker, userHelperService, dynamicConf)
	consumerService := consumer2.NewService(iExchangerOfferDao, pgdbExchangerOfferOrderDao, orchestrator, actorClient, commsCommsClient, exchangerOrchDlqDelayPublisher, casperCxActivityHelperService)
	return consumerService
}

func InitializeDiscountsService(db types.CasperPGDB) *discount.Service {
	discountDaoPgdb := dao2.NewDiscountDaoPgdb(db)
	discountService := discount.NewDiscountsService(discountDaoPgdb)
	return discountService
}

func InitializeVistaraConsumerService(db types.CasperPGDB, vistaraVgClient vistara.VistaraClient, delayPublisher types2.RetryRedemptionEventsSqsPublisher, awsConf aws.Config, dynConf *genconf.Config) *consumer3.Service {
	redeemedOfferDaoImpl := dao3.NewRedeemedOfferDaoImpl(db)
	vistaraRedemptionDaoImpl := dao3.NewVistaraRedemptionDaoImpl(db)
	string2 := vistaraAirMilesBucketNameProvider(dynConf)
	client := s3.NewClient(awsConf, string2)
	consumerService := consumer3.NewService(redeemedOfferDaoImpl, vistaraRedemptionDaoImpl, vistaraVgClient, delayPublisher, client, dynConf)
	return consumerService
}

func InitializeItcConsumerService(db types.CasperPGDB, commsClient comms.CommsClient, delayPublisher types2.RetryRedemptionEventsSqsPublisher, awsConf aws.Config, dynConf *genconf.Config) *consumer4.Service {
	redeemedOfferDaoImpl := dao3.NewRedeemedOfferDaoImpl(db)
	vendorRedemptionDaoImpl := dao3.NewVendorRedemptionDaoImpl(db)
	string2 := itcGreenPointsBucketNameProvider(dynConf)
	client := s3.NewClient(awsConf, string2)
	consumerService := consumer4.NewService(commsClient, redeemedOfferDaoImpl, vendorRedemptionDaoImpl, delayPublisher, client, dynConf)
	return consumerService
}

func InitializeExternalVendorRedemptionService(db types.CasperPGDB, accrualClient accrual.AccrualClient, fireflyClient firefly.FireflyClient, fiStoreOrderNotificationPublisher types2.FiStoreOrderNotificationSqsPublisher, eventsBroker events.Broker, dynConf *genconf.Config) (*external_vendor_redemption2.Service, error) {
	gormDB := GormProvider(db)
	pgdbExternalVendorRedemptionDao := dao5.NewExternalVendorRedemptionPGDB(gormDB)
	pgdbFiStoreRedemptionDao := dao5.NewFiStoreRedemptionPGDB(gormDB)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	v := ExternalVendorRedemptionSvcOptsProvider()
	external_vendor_redemptionService, err := external_vendor_redemption2.NewExternalVendorRedemptionService(dynConf, pgdbExternalVendorRedemptionDao, pgdbFiStoreRedemptionDao, fiStoreOrderNotificationPublisher, gormTxnExecutor, accrualClient, eventsBroker, fireflyClient, v...)
	if err != nil {
		return nil, err
	}
	return external_vendor_redemptionService, nil
}

func InitializeNotificationConsumerService(db types.CasperPGDB, actorClient actor.ActorClient, commsClient types2.CasperCommsClientWithInterceptors, vendorMappingClient vendormapping.VendorMappingServiceClient, dynamicConf *genconf.Config) *notification2.NotificationConsumerService {
	commsCommsClient := types2.CommsClientProvider(commsClient)
	gormDB := GormProvider(db)
	pgdbFiStoreRedemptionDao := dao5.NewFiStoreRedemptionPGDB(gormDB)
	eComStoreHandler := categoryhandler.NewEComStoreHandler(dynamicConf, pgdbFiStoreRedemptionDao, vendorMappingClient)
	eComDiscountsHandler := categoryhandler.NewEComDiscountsHandler(dynamicConf, pgdbFiStoreRedemptionDao, vendorMappingClient)
	pgdbExternalVendorRedemptionDao := dao5.NewExternalVendorRedemptionPGDB(gormDB)
	helperService := categoryhandler.NewHelperService(pgdbFiStoreRedemptionDao, pgdbExternalVendorRedemptionDao)
	giftCardsHandler := categoryhandler.NewGiftCardsHandler(dynamicConf, pgdbExternalVendorRedemptionDao, pgdbFiStoreRedemptionDao, vendorMappingClient, helperService)
	milesExchangeHandler := categoryhandler.NewMilesExchangeHandler(dynamicConf, pgdbExternalVendorRedemptionDao, pgdbFiStoreRedemptionDao, vendorMappingClient, helperService)
	categoryHandlerFactory := categoryhandler.NewCategoryHandlerFactory(eComStoreHandler, eComDiscountsHandler, giftCardsHandler, milesExchangeHandler)
	notificationSvc := notification2.NewNotificationSvc(commsCommsClient, actorClient, categoryHandlerFactory)
	notificationConsumerService := notification2.NewConsumerService(notificationSvc)
	return notificationConsumerService
}

// wire.go:

func GormProvider(db types.CasperPGDB) *gorm.DB {
	return db
}

func questSDKClientConfProvider(conf *genconf.Config) *genconf2.Config {
	return conf.QuestSdk()
}

func slackClientProvider(conf *config.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config2.SlackOauthTokenKey], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config2.SlackOauthTokenKey], slack.OptionDebug(true))
	}
	return slackClient
}

func CacheConfigProvider(conf *genconf.Config) *genconf.CacheConfig {
	return conf.CacheConfig()
}

func getRedeemedOfferCryptor(conf *config.Config, kmsClient *kms.Client) cryptor.RedeemedOfferCryptor {

	env := conf.Application.Environment
	if env == cfg.DevelopmentEnv || env == cfg.TestEnv {
		return cryptor.NewMockRedeemedOfferCryptorImpl()
	}
	return InitializeRedeemedOfferCryptor(kmsClient)
}

// initializing redeemed offer cryptor
func redeemedOfferCryptorProvider(conf *config.Config, awsConf aws.Config) cryptor.RedeemedOfferCryptor {
	kmsClient := kms2.InitKMSClient(awsConf)
	return getRedeemedOfferCryptor(conf, kmsClient)
}

func iNotificationServiceProvider(actorClient actor.ActorClient, commsClient comms.CommsClient, conf *config.Config) notification.INotificationService {
	return InitializeRedemptionNotificationService(actorClient, commsClient, conf)
}

func offerCatalogServiceServerProvider(db types.CasperPGDB, loyltyClient loylty.LoyltyClient, segmentClient segment.SegmentationServiceClient, vendorMappingClient vendormapping.VendorMappingServiceClient, cardClient provisioning.CardProvisioningClient, usersClient user.UsersClient,
	onboardingClient onboarding.OnboardingClient, poshvineVgClient poshvine.PoshvineClient, fireflyClient firefly.FireflyClient, accountingClient accounting.AccountingClient, conf *config.Config, dyconf *genconf.Config, redisStore types2.CasperRedisStore,
	externalVendorRedemptionClient external_vendor_redemption.ExternalVendorRedemptionServiceClient, userGroupClient group.GroupClient) casper.OfferCatalogServiceServer {
	return InitializeOfferCatalogService(db, loyltyClient, segmentClient, vendorMappingClient, cardClient, usersClient, onboardingClient, poshvineVgClient, fireflyClient, accountingClient, externalVendorRedemptionClient, conf, dyconf, redisStore, userGroupClient)
}

func offerInventoryServiceServerProvider(db types.CasperPGDB, dyconf *genconf.Config, redisStore types2.CasperRedisStore) casper.OfferInventoryServiceServer {
	return InitializeOfferInventoryService(db, dyconf, redisStore)
}

func kmsKeysConfProvider(conf *config.Config) *config.KmsKeys {
	return conf.KmsKeys
}

func redemptionNotificationParamsProvider(conf *config.Config) *config.RedemptionNotificationParams {
	return conf.RedemptionNotificationParams
}

func vistaraAirMilesBucketNameProvider(dynConf *genconf.Config) string {
	return dynConf.VistaraVendorConfig().S3BucketName
}

func itcGreenPointsBucketNameProvider(dynConf *genconf.Config) string {
	return dynConf.ItcVendorConfig().GreenPointsTransferCsvFileS3BucketName
}

func FiCoinsFulfillmentSvcOptsProvider() []order_fulfillment.Opt {
	return nil
}

func ExternalVendorRedemptionSvcOptsProvider() []external_vendor_redemption2.Opt {
	return nil
}
