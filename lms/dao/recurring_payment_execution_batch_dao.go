package dao

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/google/uuid"
	"gorm.io/gorm"

	lmspb "github.com/epifi/gringott/api/stockguardian/lms"
	"github.com/epifi/gringott/lms/dao/model"
)

type RecurringPaymentExecutionBatchDaoImpl struct {
	db types.StockGuardianPGDB
}

func NewRecurringPaymentExecutionBatchDaoImpl(db types.StockGuardianPGDB) *RecurringPaymentExecutionBatchDaoImpl {
	return &RecurringPaymentExecutionBatchDaoImpl{db: db}
}

var _ RecurringPaymentExecutionBatchDao = (*RecurringPaymentExecutionBatchDaoImpl)(nil)

func (r *RecurringPaymentExecutionBatchDaoImpl) Create(ctx context.Context, recurringPaymentExecutionBatch *lmspb.RecurringPaymentExecutionBatch) (*lmspb.RecurringPaymentExecutionBatch, error) {
	dbConn := gormctxv2.FromContextOrDefault(ctx, r.db)
	rpBatch := model.NewRecurringPaymentExecutionBatchFromProto(uuid.NewString(), recurringPaymentExecutionBatch)
	if err := dbConn.Create(rpBatch).Error; err != nil {
		return nil, fmt.Errorf("error while creating recurring payment batch: %w", err)
	}
	return rpBatch.ToProto(), nil
}

func (r *RecurringPaymentExecutionBatchDaoImpl) GetById(ctx context.Context, id string) (*lmspb.RecurringPaymentExecutionBatch, error) {
	dbConn := gormctxv2.FromContextOrDefault(ctx, r.db)
	var rpBatch model.RecurringPaymentExecutionBatch
	if err := dbConn.Where("id = ?", id).Take(&rpBatch).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("error while fetching recurring payment batch: %w", err)
	}
	return rpBatch.ToProto(), nil
}
