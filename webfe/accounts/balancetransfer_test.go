package accounts

import (
	"context"
	"reflect"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/savings/extacct"
	userPb "github.com/epifi/gamma/api/user"
	webAccountsPb "github.com/epifi/gamma/api/webfe/accounts"
	pkgSavings "github.com/epifi/gamma/pkg/savings"

	"github.com/stretchr/testify/assert"
)

func TestService_shareAlternateAccountForBalanceTransfer(t *testing.T) {
	ctx := context.Background()
	actorId = "actor_id"
	reqH := &header.RequestHeader{
		Auth: &header.AuthHeader{
			ActorId: actorId,
		},
	}
	ifsc := "FDRL12345F"
	accountNumber := "1234"
	accountHolder := "name"

	tests := []struct {
		name    string
		req     *webAccountsPb.ShareAlternateAccountForBalanceTransferRequest
		mocks   func(m *mockedDependencies)
		want    *webAccountsPb.ShareAlternateAccountForBalanceTransferResponse
		wantErr error
	}{
		{
			name: "successfully shared alternate account",
			req: &webAccountsPb.ShareAlternateAccountForBalanceTransferRequest{
				Req:               reqH,
				Ifsc:              ifsc,
				AccountNumber:     accountNumber,
				AccountHolderName: accountHolder,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
					ActorId: actorId,
				}).Return(&extacct.GetBankAccountsResponse{
					Status:                     rpc.StatusOk(),
					NameMatchRetriesLeft:       5,
					AccVerificationRetriesLeft: 3,
					BankAccountVerifications: []*extacct.BankAccountVerification{
						{
							Id:            "1",
							ActorId:       actorId,
							AccountNumber: "4321",
							Ifsc:          "HDFC12345H",
							NameAtBank:    "not name",
							OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
							FailureReason: extacct.FailureReason_FAILURE_REASON_NAME_AT_BANK_MISMATCH,
						},
					},
				}, nil)
				m.extAcctClient.EXPECT().AddBankAccount(ctx, &extacct.AddBankAccountRequest{
					ActorId:       actorId,
					Ifsc:          ifsc,
					AccountNumber: accountNumber,
					UserGivenName: accountHolder,
					Caller: &extacct.Caller{
						Source: extacct.Source_SOURCE_WEB,
					},
				}).Return(&extacct.AddBankAccountResponse{
					Status:        rpc.StatusOk(),
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_SUCCESS,
				}, nil)
				m.eventLogger.EXPECT().LogUserAlternateAccountDetailsVerificationServer(ctx, actorId, extacct.OverallStatus_OVERALL_STATUS_SUCCESS, extacct.FailureReason_FAILURE_REASON_UNSPECIFIED)
			},
			want: &webAccountsPb.ShareAlternateAccountForBalanceTransferResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: pkgSavings.AcctClosureTransferInitScreen(ctx, userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED, &extacct.BankAccount{
					Ifsc:          ifsc,
					AccountNumber: accountNumber,
					Name:          accountHolder,
				}),
			},
			wantErr: nil,
		},
		{
			name: "validation failed due to invalid account number",
			req: &webAccountsPb.ShareAlternateAccountForBalanceTransferRequest{
				Req:               reqH,
				Ifsc:              ifsc,
				AccountNumber:     accountNumber,
				AccountHolderName: accountHolder,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
					ActorId: actorId,
				}).Return(&extacct.GetBankAccountsResponse{
					Status:                     rpc.StatusOk(),
					NameMatchRetriesLeft:       5,
					AccVerificationRetriesLeft: 3,
					BankAccountVerifications: []*extacct.BankAccountVerification{
						{
							Id:            "1",
							ActorId:       actorId,
							AccountNumber: "4321",
							Ifsc:          "HDFC12345H",
							NameAtBank:    "not name",
							OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
							FailureReason: extacct.FailureReason_FAILURE_REASON_NAME_AT_BANK_MISMATCH,
						},
					},
				}, nil)
				m.extAcctClient.EXPECT().AddBankAccount(ctx, &extacct.AddBankAccountRequest{
					ActorId:       actorId,
					Ifsc:          ifsc,
					AccountNumber: accountNumber,
					UserGivenName: accountHolder,
					Caller: &extacct.Caller{
						Source: extacct.Source_SOURCE_WEB,
					},
				}).Return(&extacct.AddBankAccountResponse{
					Status:        rpc.StatusOk(),
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
					FailureReason: extacct.FailureReason_FAILURE_REASON_INVALID_ACCOUNT_NUMBER,
				}, nil)
				m.eventLogger.EXPECT().LogUserAlternateAccountDetailsVerificationServer(ctx, actorId, extacct.OverallStatus_OVERALL_STATUS_FAILURE, extacct.FailureReason_FAILURE_REASON_INVALID_ACCOUNT_NUMBER)
			},
			want: &webAccountsPb.ShareAlternateAccountForBalanceTransferResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(998, "show form errors", ""),
				},
				FormErrors: []*webAccountsPb.ShareAlternateAccountForBalanceTransferResponse_FormError{
					{
						Field:   webAccountsPb.BankAccountFormField_BANK_ACCOUNT_FORM_FIELD_ACCOUNT_NUMBER,
						Message: "This account number is invalid. You have 2 more tries left.",
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "shared details validation in progress",
			req: &webAccountsPb.ShareAlternateAccountForBalanceTransferRequest{
				Req:               reqH,
				Ifsc:              ifsc,
				AccountNumber:     accountNumber,
				AccountHolderName: accountHolder,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
					ActorId: actorId,
				}).Return(&extacct.GetBankAccountsResponse{
					Status:                     rpc.StatusOk(),
					NameMatchRetriesLeft:       5,
					AccVerificationRetriesLeft: 3,
				}, nil)
				m.extAcctClient.EXPECT().AddBankAccount(ctx, &extacct.AddBankAccountRequest{
					ActorId:       actorId,
					Ifsc:          ifsc,
					AccountNumber: accountNumber,
					UserGivenName: accountHolder,
					Caller: &extacct.Caller{
						Source: extacct.Source_SOURCE_WEB,
					},
				}).Return(&extacct.AddBankAccountResponse{
					Status:        rpc.StatusAlreadyExists(),
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
					FailureReason: extacct.FailureReason_FAILURE_REASON_INVALID_ACCOUNT_NUMBER,
				}, nil)
			},
			want: &webAccountsPb.ShareAlternateAccountForBalanceTransferResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(998, "show form errors", ""),
				},
				FormErrors: []*webAccountsPb.ShareAlternateAccountForBalanceTransferResponse_FormError{
					accountValidationInProgressFormError,
				},
			},
			wantErr: nil,
		},
		{
			name: "encountered internal error while adding external account",
			req: &webAccountsPb.ShareAlternateAccountForBalanceTransferRequest{
				Req:               reqH,
				Ifsc:              ifsc,
				AccountNumber:     accountNumber,
				AccountHolderName: accountHolder,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
					ActorId: actorId,
				}).Return(&extacct.GetBankAccountsResponse{
					Status:                     rpc.StatusOk(),
					NameMatchRetriesLeft:       5,
					AccVerificationRetriesLeft: 3,
				}, nil)
				m.extAcctClient.EXPECT().AddBankAccount(ctx, &extacct.AddBankAccountRequest{
					ActorId:       actorId,
					Ifsc:          ifsc,
					AccountNumber: accountNumber,
					UserGivenName: accountHolder,
					Caller: &extacct.Caller{
						Source: extacct.Source_SOURCE_WEB,
					},
				}).Return(&extacct.AddBankAccountResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &webAccountsPb.ShareAlternateAccountForBalanceTransferResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(998, "show form errors", ""),
				},
				FormErrors: []*webAccountsPb.ShareAlternateAccountForBalanceTransferResponse_FormError{
					genericFailureFormError,
				},
			},
			wantErr: nil,
		},
		{
			name: "validation failed with given details already",
			req: &webAccountsPb.ShareAlternateAccountForBalanceTransferRequest{
				Req:               reqH,
				Ifsc:              "HDFC12345H",
				AccountNumber:     "4321",
				AccountHolderName: accountHolder,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
					ActorId: actorId,
				}).Return(&extacct.GetBankAccountsResponse{
					Status:                     rpc.StatusOk(),
					NameMatchRetriesLeft:       5,
					AccVerificationRetriesLeft: 3,
					BankAccountVerifications: []*extacct.BankAccountVerification{
						{
							Id:            "1",
							ActorId:       actorId,
							AccountNumber: "4321",
							Ifsc:          "HDFC12345H",
							NameAtBank:    "not name",
							OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
							FailureReason: extacct.FailureReason_FAILURE_REASON_INVALID_ACCOUNT_NUMBER,
						},
					},
				}, nil)
			},
			want: &webAccountsPb.ShareAlternateAccountForBalanceTransferResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(998, "show form errors", ""),
				},
				FormErrors: []*webAccountsPb.ShareAlternateAccountForBalanceTransferResponse_FormError{
					{
						Field:   webAccountsPb.BankAccountFormField_BANK_ACCOUNT_FORM_FIELD_ACCOUNT_NUMBER,
						Message: "This account number is invalid. You have 3 more tries left.",
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "retry limit reached",
			req: &webAccountsPb.ShareAlternateAccountForBalanceTransferRequest{
				Req:               reqH,
				Ifsc:              ifsc,
				AccountNumber:     accountNumber,
				AccountHolderName: accountHolder,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
					ActorId: actorId,
				}).Return(&extacct.GetBankAccountsResponse{
					Status:                     rpc.StatusOk(),
					NameMatchRetriesLeft:       0,
					AccVerificationRetriesLeft: 0,
					BankAccountVerifications: []*extacct.BankAccountVerification{
						{
							Id:            "1",
							ActorId:       actorId,
							AccountNumber: "4321",
							Ifsc:          "HDFC12345H",
							NameAtBank:    "not name",
							OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
							FailureReason: extacct.FailureReason_FAILURE_REASON_NAME_AT_BANK_MISMATCH,
						},
					},
				}, nil)
			},
			want: &webAccountsPb.ShareAlternateAccountForBalanceTransferResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(998, "show form errors", ""),
				},
				FormErrors: []*webAccountsPb.ShareAlternateAccountForBalanceTransferResponse_FormError{
					{
						Field:   webAccountsPb.BankAccountFormField_BANK_ACCOUNT_FORM_FIELD_BELOW_TITLE,
						Message: "You have reached the max retries for sharing your alternate account details. Please reach out to our customer care for further help",
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "alternate details already shared",
			req: &webAccountsPb.ShareAlternateAccountForBalanceTransferRequest{
				Req:               reqH,
				Ifsc:              ifsc,
				AccountNumber:     accountNumber,
				AccountHolderName: accountHolder,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
					ActorId: actorId,
				}).Return(&extacct.GetBankAccountsResponse{
					Status: rpc.StatusOk(),
					BankAccounts: []*extacct.BankAccount{
						{
							Ifsc:          ifsc,
							AccountNumber: accountNumber,
							Name:          accountHolder,
						},
					},
				}, nil)
			},
			want: &webAccountsPb.ShareAlternateAccountForBalanceTransferResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: pkgSavings.AcctClosureTransferInitScreen(ctx, userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED, &extacct.BankAccount{
					Ifsc:          ifsc,
					AccountNumber: accountNumber,
					Name:          accountHolder,
				}),
			},
			wantErr: nil,
		},
		{
			name: "internal error while getting bank accounts",
			req: &webAccountsPb.ShareAlternateAccountForBalanceTransferRequest{
				Req:               reqH,
				Ifsc:              ifsc,
				AccountNumber:     accountNumber,
				AccountHolderName: accountHolder,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
					ActorId: actorId,
				}).Return(&extacct.GetBankAccountsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &webAccountsPb.ShareAlternateAccountForBalanceTransferResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(998, "show form errors", ""),
				},
				FormErrors: []*webAccountsPb.ShareAlternateAccountForBalanceTransferResponse_FormError{
					genericFailureFormError,
				},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := newServiceWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mocks)
			}
			got, err := svc.shareAlternateAccountForBalanceTransfer(ctx, tt.req)
			assert.Equal(t, tt.wantErr, err)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("shareAlternateAccountForBalanceTransfer() got = %v, want %v", got, tt.want)
			}
		})
	}
}
