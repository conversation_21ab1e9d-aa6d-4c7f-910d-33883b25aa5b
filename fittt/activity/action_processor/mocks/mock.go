// Code generated by MockGen. DO NOT EDIT.
// Source: processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	rms "github.com/epifi/gamma/api/typesv2/rms"
	gomock "github.com/golang/mock/gomock"
)

// MockIActionProcessor is a mock of IActionProcessor interface.
type MockIActionProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockIActionProcessorMockRecorder
}

// MockIActionProcessorMockRecorder is the mock recorder for MockIActionProcessor.
type MockIActionProcessorMockRecorder struct {
	mock *MockIActionProcessor
}

// NewMockIActionProcessor creates a new mock instance.
func NewMockIActionProcessor(ctrl *gomock.Controller) *MockIActionProcessor {
	mock := &MockIActionProcessor{ctrl: ctrl}
	mock.recorder = &MockIActionProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIActionProcessor) EXPECT() *MockIActionProcessorMockRecorder {
	return m.recorder
}

// InitiateAction mocks base method.
func (m *MockIActionProcessor) InitiateAction(ctx context.Context, actionInfo *rms.RmsActionInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateAction", ctx, actionInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitiateAction indicates an expected call of InitiateAction.
func (mr *MockIActionProcessorMockRecorder) InitiateAction(ctx, actionInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateAction", reflect.TypeOf((*MockIActionProcessor)(nil).InitiateAction), ctx, actionInfo)
}

// TrackAction mocks base method.
func (m *MockIActionProcessor) TrackAction(ctx context.Context, actionInfo *rms.RmsActionInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TrackAction", ctx, actionInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// TrackAction indicates an expected call of TrackAction.
func (mr *MockIActionProcessorMockRecorder) TrackAction(ctx, actionInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TrackAction", reflect.TypeOf((*MockIActionProcessor)(nil).TrackAction), ctx, actionInfo)
}
