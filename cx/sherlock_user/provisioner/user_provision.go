package provisioner

import (
	"context"

	sherlockUserPb "github.com/epifi/gamma/api/cx/sherlock_user"
	"github.com/epifi/gamma/cx/sherlock_user/dao"
)

type IUserProvisioner interface {
	CreateUser(ctx context.Context, request *sherlockUserPb.CreateSherlockUserRequest) *sherlockUserPb.ProvisioningStatus
	UpdateUser(ctx context.Context, sherlockUserInfo *sherlockUserPb.SherlockUserInfo, updateFieldMask []sherlockUserPb.SherlockUserFieldMask) *sherlockUserPb.ProvisioningStatus
}

type IUserFetcher interface {
	GetUser(ctx context.Context, email string) (*sherlockUserPb.SherlockUserInfo, error)
	GetUsers(ctx context.Context, pageSize int, pageToken *dao.PageToken, filters *sherlockUserPb.GetAllSherlockUsersRequest_Filters) ([]*sherlockUserPb.SherlockUserInfo, error)
	GetUserBySearchId(ctx context.Context, searchId *sherlockUserPb.SearchId) (*sherlockUserPb.SherlockUserInfo, error)
}
