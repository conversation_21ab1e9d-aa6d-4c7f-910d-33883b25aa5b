package consumer

import (
	"context"

	consumerPb "github.com/epifi/gamma/api/cx/consumer"
)

type TicketEventProcessor interface {
	ProcessTicket(ctx context.Context, req *consumerPb.ProcessTicketEventRequest) (*consumerPb.ProcessTicketEventResponse, error)
}

type ContactEventProcessor interface {
	ProcessContact(ctx context.Context, req *consumerPb.ProcessContactEventRequest) (*consumerPb.ProcessContactEventResponse, error)
}

type UpdateTicketEventProcessor interface {
	ProcessUpdateTicketEvent(ctx context.Context, req *consumerPb.UpdateTicketEventRequest) (*consumerPb.UpdateTicketEventResponse, error)
}

type CreateTicketEventProcessor interface {
	ProcessCreateTicketEvent(ctx context.Context, req *consumerPb.CreateTicketEventRequest) (*consumerPb.CreateTicketEventResponse, error)
}

type EscalationProcessor interface {
	ProcessEscalationEvent(ctx context.Context, req *consumerPb.ProcessFederalEscalationEventRequest) (*consumerPb.ProcessFederalEscalationEventResponse, error)
}

type EscalationCreationProcessor interface {
	ProcessEscalationCreationEvent(ctx context.Context, req *consumerPb.ProcessFederalEscalationCreationEventRequest) (*consumerPb.ProcessFederalEscalationCreationEventResponse, error)
}
