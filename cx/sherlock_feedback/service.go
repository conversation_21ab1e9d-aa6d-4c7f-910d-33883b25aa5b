package sherlock_feedback

import (
	"context"
	"errors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_feedback"
	"github.com/epifi/gamma/cx/config"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/sherlock_feedback/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"go.uber.org/zap"
)

type Service struct {
	config                     *config.Config
	sherlockFeedbackDetailsDao dao.ISherlockFeedbackDetailsDao
}

func NewSherlockFeedbackService(config *config.Config, sherlockFeedbackDetailsDao dao.ISherlockFeedbackDetailsDao) *Service {
	return &Service{
		config:                     config,
		sherlockFeedbackDetailsDao: sherlockFeedbackDetailsDao,
	}
}

var _ sbPb.SherlockFeedbackServer = &Service{}

func (s Service) SubmitFeedback(ctx context.Context, req *sbPb.SubmitFeedbackRequest) (*sbPb.SubmitFeedbackResponse, error) {
	req.GetSherlockFeedbackDetails().AgentEmail = req.GetHeader().GetAgentEmail()
	_, createErr := s.sherlockFeedbackDetailsDao.Create(ctx, req.GetSherlockFeedbackDetails())
	if createErr != nil {
		cxLogger.Error(ctx, "error while creating sherlock feedback details", zap.Error(createErr),
			zap.Any(logger.AGENT_EMAIL, req.GetHeader().GetAgentEmail()),
			zap.Any("identifierType", req.GetSherlockFeedbackDetails().GetFeedbackIdentifierType()),
			zap.Any("feedbackCategory", req.GetSherlockFeedbackDetails().GetFeedbackCategory()),
			zap.Any("identifierValue", req.GetSherlockFeedbackDetails().GetFeedbackIdentifierValue()),
		)
		return &sbPb.SubmitFeedbackResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &sbPb.SubmitFeedbackResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s Service) GetFeedback(ctx context.Context, req *sbPb.GetFeedbackRequest) (*sbPb.GetFeedbackResponse, error) {
	// check for valid page token for pagination
	pageToken, tokenErr := pagination.GetPageToken(req.GetPageContextRequest())
	if tokenErr != nil {
		cxLogger.Error(ctx, "invalid page token", zap.Error(tokenErr), zap.Any(logger.AGENT_EMAIL, req.GetHeader().GetAgentEmail()))
		return &sbPb.GetFeedbackResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid page token"),
		}, nil
	}
	sherlockFeedbackList, pageCtxResp, err := s.sherlockFeedbackDetailsDao.GetAllFeedbacks(ctx, pageToken, s.config.SherlockFeedbackDetailsConfig.PageSize, req.GetFilters())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &sbPb.GetFeedbackResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		cxLogger.Error(ctx, "failed to get feedbacks", zap.Error(err))
		return &sbPb.GetFeedbackResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &sbPb.GetFeedbackResponse{
		Status:                  rpcPb.StatusOk(),
		SherlockFeedbackDetails: sherlockFeedbackList,
		PageContextResponse:     pageCtxResp,
	}, nil
}
