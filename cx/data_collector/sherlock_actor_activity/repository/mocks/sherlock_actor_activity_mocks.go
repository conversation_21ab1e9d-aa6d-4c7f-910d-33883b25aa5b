// Code generated by MockGen. DO NOT EDIT.
// Source: sherlock_actor_activity_repo_interface.go

// Package repository is a generated GoMock package.
package repository

import (
	context "context"
	reflect "reflect"

	actor_activity "github.com/epifi/gamma/api/actor_activity"
	gomock "github.com/golang/mock/gomock"
)

// MockSherlockActorActivityRepository is a mock of SherlockActorActivityRepository interface.
type MockSherlockActorActivityRepository struct {
	ctrl     *gomock.Controller
	recorder *MockSherlockActorActivityRepositoryMockRecorder
}

// MockSherlockActorActivityRepositoryMockRecorder is the mock recorder for MockSherlockActorActivityRepository.
type MockSherlockActorActivityRepositoryMockRecorder struct {
	mock *MockSherlockActorActivityRepository
}

// NewMockSherlockActorActivityRepository creates a new mock instance.
func NewMockSherlockActorActivityRepository(ctrl *gomock.Controller) *MockSherlockActorActivityRepository {
	mock := &MockSherlockActorActivityRepository{ctrl: ctrl}
	mock.recorder = &MockSherlockActorActivityRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSherlockActorActivityRepository) EXPECT() *MockSherlockActorActivityRepositoryMockRecorder {
	return m.recorder
}

// GetActivity mocks base method.
func (m *MockSherlockActorActivityRepository) GetActivity(ctx context.Context, activityFilter *actor_activity.ActivityFilter) ([]*actor_activity.Activity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActivity", ctx, activityFilter)
	ret0, _ := ret[0].([]*actor_activity.Activity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActivity indicates an expected call of GetActivity.
func (mr *MockSherlockActorActivityRepositoryMockRecorder) GetActivity(ctx, activityFilter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActivity", reflect.TypeOf((*MockSherlockActorActivityRepository)(nil).GetActivity), ctx, activityFilter)
}
