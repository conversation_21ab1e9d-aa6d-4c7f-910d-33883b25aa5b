package salarydataops

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/test"
)

var gConf *genconf.Config
var conf *config.Config

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, gConf, _, teardown = test.InitTestServer(false)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
