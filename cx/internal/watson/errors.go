package watson

import "github.com/pkg/errors"

var (
	ErrConfigNotFound            = errors.New("error config not found")
	ErrInvalidArguments          = errors.New("error invalid arguments passed")
	ErrUnhandled                 = errors.New("unhandled error")
	ErrRateLimitExceeded         = errors.New("error rate-limit exceeded")
	ErrClientNotRegistered       = errors.New("error client not registered to watson info collector factory")
	ErrFailedToCallClient        = errors.New("error while calling client info collector")
	ErrFailedToInitiateWorkflow  = errors.New("error while initiating workflow")
	ErrFailedToSignalWorkflow    = errors.New("error while sending signal to workflow")
	ErrProcessingNotEnabled      = errors.New("error processing not enabled for given incident category")
	ErrInvalidIncidentCategoryId = errors.New("error given incident category id is invalid")
)
