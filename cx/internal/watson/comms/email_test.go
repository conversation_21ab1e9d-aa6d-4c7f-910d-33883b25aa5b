package comms

import (
	"context"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/comms/mocks"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"

	"github.com/pkg/errors"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
)

var (
	incidentId		= uuid.NewString()
	validIncidentCategoryId	= "PRODUCT_CATEGORY_TRANSACTIONS::PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP::SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT"
	incidentCategoryId1	= "PRODUCT_CATEGORY_ACCOUNTS::PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY::SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND"
	actorId1		= "actorId-1"
	clientRequestId		= uuid.NewString()
	mockErr			= errors.New("mock error")
	ticketId		= int64(1234)

	incident1	= &watsonPb.Incident{
		Id:			incidentId,
		ActorId:		actorId1,
		ClientRequestId:	clientRequestId,
		IncidentCategoryId:	validIncidentCategoryId,
		IncidentData:		&watsonPb.IncidentData{},
		Client:			types.ServiceName_USER_SERVICE,
	}
	incident2	= &watsonPb.Incident{
		Id:			incidentId,
		ActorId:		actorId1,
		ClientRequestId:	clientRequestId,
		IncidentCategoryId:	incidentCategoryId1,
		IncidentData:		&watsonPb.IncidentData{},
		Client:			types.ServiceName_USER_SERVICE,
	}
	incidentTicketDetails1	= &watsonPb.IncidentTicketDetail{
		IncidentId:	incidentId,
		TicketId:	ticketId,
		TicketStatus:	0,
		ResolutionMode:	0,
		CreatedAt:	nil,
		UpdatedAt:	nil,
	}
	commsMsgId	= "123"
	user1		= &userPb.User{}
)

func TestEmailProcessor_SendCommsToUser(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockCommsClient := mocks.NewMockCommsClient(ctr)

	type args struct {
		ctx			context.Context
		incident		*watsonPb.Incident
		user			*userPb.User
		incidentTicketDetail	*watsonPb.IncidentTicketDetail
		commsType		watsonPb.CommsType
	}
	tests := []struct {
		mocks	[]interface{}
		name	string
		args	args
		want	string
		wantErr	bool
	}{
		{
			name:	"error: invalid comms type",
			args: args{
				ctx:			context.Background(),
				incident:		incident1,
				incidentTicketDetail:	incidentTicketDetails1,
				user:			user1,
			},
			want:		"",
			wantErr:	true,
		},
		{
			name:	"error: while sending comms",
			args: args{
				ctx:			context.Background(),
				incident:		incident1,
				incidentTicketDetail:	incidentTicketDetails1,
				user:			user1,
				commsType:		watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
			},
			mocks: []interface{}{
				mockCommsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).Return(nil, mockErr),
			},
			want:		"",
			wantErr:	true,
		},
		{
			name:	"success without ticket",
			args: args{
				ctx:		context.Background(),
				incident:	incident1,
				user:		user1,
				commsType:	watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
			},
			mocks: []interface{}{
				mockCommsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).
					Return(&commsPb.SendMessageResponse{
						Status:		rpc.StatusOk(),
						MessageId:	commsMsgId,
					}, nil),
			},
			want:		commsMsgId,
			wantErr:	false,
		},
		{
			name:	"success",
			args: args{
				ctx:			context.Background(),
				incident:		incident1,
				incidentTicketDetail:	incidentTicketDetails1,
				user:			user1,
				commsType:		watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
			},
			mocks: []interface{}{
				mockCommsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).
					Return(&commsPb.SendMessageResponse{
						Status:		rpc.StatusOk(),
						MessageId:	commsMsgId,
					}, nil),
			},
			want:		commsMsgId,
			wantErr:	false,
		},
		{
			name:	"success: min-kyc expiry balance refund",
			args: args{
				ctx:			context.Background(),
				incident:		incident2,
				incidentTicketDetail:	incidentTicketDetails1,
				user:			user1,
				commsType:		watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
			},
			mocks: []interface{}{
				mockCommsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).
					Return(&commsPb.SendMessageResponse{
						Status:		rpc.StatusOk(),
						MessageId:	commsMsgId,
					}, nil),
			},
			want:		commsMsgId,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		emailProcessor := NewEmailProcessor(mockCommsClient, SendCommsTS.conf)
		t.Run(tt.name, func(t *testing.T) {
			got, err := emailProcessor.SendCommsToUser(tt.args.ctx, tt.args.incident, tt.args.incidentTicketDetail, tt.args.user, tt.args.commsType)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendCommsToUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("SendCommsToUser() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEmailProcessor_SendCustomCommsToUser(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockCommsClient := mocks.NewMockCommsClient(ctr)

	type args struct {
		ctx		context.Context
		incident	*watsonPb.Incident
		user		*userPb.User
		commsDetail	*watsonPb.CommsDetail
	}
	tests := []struct {
		mocks	[]interface{}
		name	string
		args	args
		want	string
		wantErr	bool
	}{
		{
			name:	"error: email message is empty",
			args: args{
				ctx:		context.Background(),
				incident:	incident1,
				commsDetail:	&watsonPb.CommsDetail{},
				user:		user1,
			},
			want:		"",
			wantErr:	true,
		},
		{
			name:	"error: while sending comms",
			args: args{
				ctx:		context.Background(),
				incident:	incident1,
				commsDetail:	&watsonPb.CommsDetail{Detail: &watsonPb.CommsDetail_Email{Email: &commsPb.EmailMessage{}}},
				user:		user1,
			},
			mocks: []interface{}{
				mockCommsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).Return(nil, mockErr),
			},
			want:		"",
			wantErr:	true,
		},
		{
			name:	"success",
			args: args{
				ctx:		context.Background(),
				incident:	incident1,
				commsDetail:	&watsonPb.CommsDetail{Detail: &watsonPb.CommsDetail_Email{Email: &commsPb.EmailMessage{}}},
				user:		user1,
			},
			mocks: []interface{}{
				mockCommsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).
					Return(&commsPb.SendMessageResponse{
						Status:		rpc.StatusOk(),
						MessageId:	commsMsgId,
					}, nil),
			},
			want:		commsMsgId,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		emailProcessor := NewEmailProcessor(mockCommsClient, SendCommsTS.conf)
		t.Run(tt.name, func(t *testing.T) {
			got, err := emailProcessor.SendCustomCommsToUser(tt.args.ctx, tt.args.incident, tt.args.commsDetail, tt.args.user)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendCustomCommsToUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("SendCustomCommsToUser() got = %v, want %v", got, tt.want)
			}
		})
	}
}
