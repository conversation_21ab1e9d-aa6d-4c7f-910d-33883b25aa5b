package activity_helper

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	icPb "github.com/epifi/gamma/api/cx/issue_category"
	"github.com/epifi/gamma/api/cx/issue_config"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	userPb "github.com/epifi/gamma/api/user"
	workerConfig "github.com/epifi/gamma/cx/config/worker"
	"github.com/epifi/gamma/cx/internal/watson"
	"github.com/epifi/gamma/cx/issue_category/manager"
	"github.com/epifi/gamma/cx/issue_config/dao"
)

// ActivityHelperV2 implements WatsonActivityHelper taking into account that
// issueCategoryId is uuid which represents (L1, L2, L3) in issue_categories table
type ActivityHelperV2 struct {
	// embedding older helper, to use un-changed methods and dependencies
	*WatsonActivityHelper
	issueConfigDao dao.IssueConfigDao
	issueCategory  manager.IssueCategoryManager
	ticketClient   ticketPb.TicketClient
}

func NewActivityHelperV2(helperV1 *WatsonActivityHelper, issueConfigDao dao.IssueConfigDao, issueCategory manager.IssueCategoryManager) *ActivityHelperV2 {
	return &ActivityHelperV2{
		WatsonActivityHelper: helperV1,
		issueConfigDao:       issueConfigDao,
		issueCategory:        issueCategory,
	}
}

var _ IWatsonActivityHelper = &ActivityHelperV2{}

func (a *ActivityHelperV2) GetWatsonIncidentCategoryConfig(ctx context.Context, incidentCategoryId string) (*workerConfig.IncidentCategoryDetailsConfig, error) {
	config, err := a.issueConfigDao.Get(ctx, incidentCategoryId, issue_config.ConfigType_CONFIG_TYPE_WATSON)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(watson.ErrConfigNotFound, err.Error())
		}
		return nil, errors.Wrap(err, "error while fetching config from DB")
	}
	watsonConfig := config.GetConfigPayload().GetWatsonConfig()
	incidentCategoryConfig := &workerConfig.IncidentCategoryDetailsConfig{
		IsTicketCreationEnabled:    watsonConfig.GetIsTicketCreationEnabled().ToBool(),
		IsCommsEnabled:             watsonConfig.GetIsCommsEnabled().ToBool(),
		TicketStatusCommsConfigMap: watsonConfig.GetTicketStatusCommsConfigMap(),
	}
	// no need to handle conversion errors or validating if field is present as that is already done at the time of insertion
	if incidentCategoryConfig.IsTicketCreationEnabled {
		ticketDelay, conversionErr := time.ParseDuration(watsonConfig.GetTicketCreationDelay())
		if conversionErr != nil {
			return nil, errors.Wrap(conversionErr, "error while converting ticket creation delay to duration")
		}
		incidentCategoryConfig.TicketCreationDelay = ticketDelay
	}
	if incidentCategoryConfig.IsCommsEnabled {
		commsDelay, conversionErr := time.ParseDuration(watsonConfig.GetCommsDelay())
		if conversionErr != nil {
			return nil, errors.Wrap(conversionErr, "error while converting comms delay to duration")
		}
		incidentCategoryConfig.CommsDelay = commsDelay
	}
	if watsonConfig.GetAutoClosurePeriod() != "" {
		autoClosurePeriod, conversionErr := time.ParseDuration(watsonConfig.GetAutoClosurePeriod())
		if conversionErr != nil {
			return nil, errors.Wrap(conversionErr, "failed to convert auto-closure period to duration")
		}
		incidentCategoryConfig.AutoClosurePeriod = autoClosurePeriod
	}
	return incidentCategoryConfig, nil
}

func (a *ActivityHelperV2) GetTicketForAction(ctx context.Context, incident *watsonPb.Incident, actionType watsonPb.GetTicketDetailsActionType) (*ticketPb.Ticket, error) {
	// Get any additional details that client wants to populate in ticket
	clientTicketDetails, err := a.getTicketDetailsFromClient(ctx, incident, actionType)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching ticket details from client")
	}

	switch actionType {
	case watsonPb.GetTicketDetailsActionType_GET_TICKET_DETAILS_ACTION_TYPE_CREATE_TICKET_FOR_INCIDENT:
		return a.getTicketForIncidentCreation(ctx, incident, clientTicketDetails)
	case watsonPb.GetTicketDetailsActionType_GET_TICKET_DETAILS_ACTION_TYPE_UPDATE_TICKET_FOR_INCIDENT_RESOLUTION:
		return a.getTicketForIncidentResolution(ctx, clientTicketDetails), nil
	default:
		return nil, errors.Wrap(watson.ErrInvalidArguments, "action type is invalid while fetching ticket details")
	}
}

//nolint:dupl
func (a *ActivityHelperV2) getTicketForIncidentCreation(ctx context.Context, incident *watsonPb.Incident, clientTicketDetails *watsonPb.TicketDetails) (*ticketPb.Ticket, error) {
	// Fetch user details for whom the ticket is to be created
	userResp, userErr := a.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: incident.GetActorId(),
		},
	})
	if te := epifigrpc.RPCError(userResp, userErr); te != nil {
		return nil, errors.Wrap(te, "error while fetching user profile from user service")
	}
	ticket, err := a.collateTicketDetails(ctx, incident, clientTicketDetails, userResp.GetUser())
	if err != nil {
		return nil, errors.Wrap(err, "error while populating details in ticket")
	}
	return ticket, nil
}

func (a *ActivityHelperV2) collateTicketDetails(ctx context.Context, incident *watsonPb.Incident, ticketDetails *watsonPb.TicketDetails, user *userPb.User) (*ticketPb.Ticket, error) {
	issueCategory, err := a.issueCategory.GetValueById(ctx, incident.GetIncidentCategoryId())
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching issue category from id")
	}
	ticketSubject := a.getFormattedTicketSubject(issueCategory)

	ticketDescription := fmt.Sprintf(a.conf.WatsonConfig.TicketDescription, incident.GetIdentifiedAt().AsTime().
		In(datetime.IST).Format(time.RFC1123))
	// if client wants to populate description, append it to a new line
	if ticketDetails.GetDescription() != "" {
		ticketDescription += fmt.Sprintf("<br>%s", ticketDetails.GetDescription())
	}

	ticketPriority := ticketDetails.GetPriority()
	// priority is mandatory field while ticket creation, make it low if client doesn't pass it
	if ticketPriority == ticketPb.Priority_PRIORITY_UNSPECIFIED {
		ticketPriority = ticketPb.Priority_PRIORITY_LOW
	}

	return &ticketPb.Ticket{
		Subject:         ticketSubject,
		Description:     ticketDescription,
		Email:           user.GetProfile().GetEmail(),
		Priority:        ticketPriority,
		Status:          ticketPb.Status_STATUS_OPEN,
		Tags:            a.conf.WatsonConfig.TicketTags,
		Type:            a.conf.WatsonConfig.TicketType,
		IssueCategoryId: incident.GetIncidentCategoryId(),
		CustomFields:    ticketDetails.GetCustomTicketFields(),
	}, nil
}

func (a *ActivityHelperV2) getFormattedTicketSubject(category *icPb.IssueCategory) string {
	// we are populating ticket subject with L1, L2, and L3 values
	// here '|' is used as delimiter between each one of those
	// Ex. ticket title will look like: [Auto ID]: L1 | L2 | L3
	issueCategoryString := category.GetProductCategory()
	if category.GetProductCategoryDetails() != "" {
		issueCategoryString += " | " + category.GetProductCategoryDetails()
	}
	if category.GetSubCategory() != "" {
		issueCategoryString += " | " + category.GetSubCategory()
	}
	ticketSubject := fmt.Sprintf(a.conf.WatsonConfig.TicketTitle, issueCategoryString)
	return ticketSubject
}
