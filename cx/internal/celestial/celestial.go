package celestial

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	celestialPb "github.com/epifi/be-common/api/celestial"
	celestialConsumerPb "github.com/epifi/be-common/api/celestial/consumer"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	cxTypes "github.com/epifi/gamma/cx/wire/types"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type Processor struct {
	celestialClient         celestialPb.CelestialClient
	signalWorkflowPublisher cxTypes.CelestialSignalWorkflowPublisher
}

func NewCelestialProcessor(celestialClient celestialPb.CelestialClient, signalWorkflowPublisher cxTypes.CelestialSignalWorkflowPublisher) *Processor {
	return &Processor{
		celestialClient:         celestialClient,
		signalWorkflowPublisher: signalWorkflowPublisher,
	}
}

func (p *Processor) SignalWorkflow(ctx context.Context, clientReqID string, client workflowPb.Client, signalID string, payload []byte) error {
	_, err := p.signalWorkflowPublisher.Publish(ctx, &celestialConsumerPb.SignalWorkflowRequest{
		Identifier: &celestialConsumerPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     clientReqID,
				Client: client,
			},
		},
		SignalId: signalID,
		Payload:  payload,
	})
	if err != nil {
		return fmt.Errorf("error publishing signal workflow request %w", err)
	}
	return nil
}

func (p *Processor) GetWorkflowByClientRequestId(ctx context.Context, clientRequestId string, client workflowPb.Client) (*celestialPb.WorkflowRequest, error) {
	resp, err := p.celestialClient.GetWorkflowStatus(ctx, &celestialPb.GetWorkflowStatusRequest{
		Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
			ClientRequestId: &celestialPb.ClientReqId{
				Id:     clientRequestId,
				Client: client,
			},
		},
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error while fetching workflow request for given client request id %v %w", err, rpcPb.StatusAsError(rpcPb.StatusInternal()))
	case resp.GetStatus().IsSuccess():
		return resp.GetWorkflowRequest(), nil
	case resp.GetStatus().IsRecordNotFound():
		return nil, fmt.Errorf("workflow request not found for given client request id %w", rpcPb.StatusAsError(rpcPb.StatusRecordNotFound()))
	default:
		return nil, fmt.Errorf("non success status while fetching workflow request %v %w", resp.GetStatus().String(), rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
}

// InitiateWorkflow creates a workflow request of given type and payload.
func (p *Processor) InitiateWorkflow(ctx context.Context, clientReqId *celestialPb.ClientReqId, actorId string,
	payload []byte, workflowType workflowPb.Type, version workflowPb.Version) error {
	initiateWorkflowRes, err := p.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		ActorId:     actorId,
		Version:     version,
		Type:        workflowType,
		Payload:     payload,
		ClientReqId: clientReqId,
	})

	if te := epifigrpc.RPCError(initiateWorkflowRes, err); te != nil {
		if initiateWorkflowRes.GetStatus().IsAlreadyExists() {
			return errors.Wrap(epifierrors.ErrAlreadyExists, "workflow already exists")
		}
		return fmt.Errorf("error while initiating workflow for client req id %s, %w", clientReqId.GetId(), te)
	}
	return nil
}
