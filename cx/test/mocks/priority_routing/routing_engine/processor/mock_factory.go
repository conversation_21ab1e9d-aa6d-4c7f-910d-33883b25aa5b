// Code generated by MockGen. DO NOT EDIT.
// Source: priority_routing/routing_engine/processor/factory.go

// Package mock_processor is a generated GoMock package.
package mock_processor

import (
	reflect "reflect"

	priority_routing "github.com/epifi/gamma/api/cx/priority_routing"
	processor "github.com/epifi/gamma/cx/priority_routing/routing_engine/processor"
	gomock "github.com/golang/mock/gomock"
)

// MockIFactory is a mock of IFactory interface.
type MockIFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIFactoryMockRecorder
}

// MockIFactoryMockRecorder is the mock recorder for MockIFactory.
type MockIFactoryMockRecorder struct {
	mock *MockIFactory
}

// NewMockIFactory creates a new mock instance.
func NewMockIFactory(ctrl *gomock.Controller) *MockIFactory {
	mock := &MockIFactory{ctrl: ctrl}
	mock.recorder = &MockIFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIFactory) EXPECT() *MockIFactoryMockRecorder {
	return m.recorder
}

// GetRuleProcessor mocks base method.
func (m *MockIFactory) GetRuleProcessor(arg0 priority_routing.UserCategory) (processor.RuleProcessor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRuleProcessor", arg0)
	ret0, _ := ret[0].(processor.RuleProcessor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRuleProcessor indicates an expected call of GetRuleProcessor.
func (mr *MockIFactoryMockRecorder) GetRuleProcessor(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRuleProcessor", reflect.TypeOf((*MockIFactory)(nil).GetRuleProcessor), arg0)
}
