// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mock_dao is a generated GoMock package.
package mock_dao

import (
	context "context"
	reflect "reflect"

	rpc "github.com/epifi/be-common/api/rpc"
	watson "github.com/epifi/gamma/api/cx/watson"
	types "github.com/epifi/gamma/api/typesv2"
	pagination "github.com/epifi/be-common/pkg/pagination"
	gomock "github.com/golang/mock/gomock"
)

// MockIIncidentDao is a mock of IIncidentDao interface.
type MockIIncidentDao struct {
	ctrl     *gomock.Controller
	recorder *MockIIncidentDaoMockRecorder
}

// MockIIncidentDaoMockRecorder is the mock recorder for MockIIncidentDao.
type MockIIncidentDaoMockRecorder struct {
	mock *MockIIncidentDao
}

// NewMockIIncidentDao creates a new mock instance.
func NewMockIIncidentDao(ctrl *gomock.Controller) *MockIIncidentDao {
	mock := &MockIIncidentDao{ctrl: ctrl}
	mock.recorder = &MockIIncidentDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIIncidentDao) EXPECT() *MockIIncidentDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIIncidentDao) Create(ctx context.Context, req *watson.Incident) (*watson.Incident, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, req)
	ret0, _ := ret[0].(*watson.Incident)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockIIncidentDaoMockRecorder) Create(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIIncidentDao)(nil).Create), ctx, req)
}

// Get mocks base method.
func (m *MockIIncidentDao) Get(ctx context.Context, incidentId string) (*watson.Incident, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, incidentId)
	ret0, _ := ret[0].(*watson.Incident)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockIIncidentDaoMockRecorder) Get(ctx, incidentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockIIncidentDao)(nil).Get), ctx, incidentId)
}

// GetAllByFilters mocks base method.
func (m *MockIIncidentDao) GetAllByFilters(ctx context.Context, filters *watson.IncidentFilter, pageToken *pagination.PageToken, limit int) ([]*watson.Incident, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllByFilters", ctx, filters, pageToken, limit)
	ret0, _ := ret[0].([]*watson.Incident)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAllByFilters indicates an expected call of GetAllByFilters.
func (mr *MockIIncidentDaoMockRecorder) GetAllByFilters(ctx, filters, pageToken, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllByFilters", reflect.TypeOf((*MockIIncidentDao)(nil).GetAllByFilters), ctx, filters, pageToken, limit)
}

// GetByActorClientAndIncidentCategory mocks base method.
func (m *MockIIncidentDao) GetByActorClientAndIncidentCategory(ctx context.Context, actorId, clientRequestId, incidentCategoryId string, client types.ServiceName) (*watson.Incident, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorClientAndIncidentCategory", ctx, actorId, clientRequestId, incidentCategoryId, client)
	ret0, _ := ret[0].(*watson.Incident)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorClientAndIncidentCategory indicates an expected call of GetByActorClientAndIncidentCategory.
func (mr *MockIIncidentDaoMockRecorder) GetByActorClientAndIncidentCategory(ctx, actorId, clientRequestId, incidentCategoryId, client interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorClientAndIncidentCategory", reflect.TypeOf((*MockIIncidentDao)(nil).GetByActorClientAndIncidentCategory), ctx, actorId, clientRequestId, incidentCategoryId, client)
}

// Update mocks base method.
func (m *MockIIncidentDao) Update(ctx context.Context, req *watson.Incident, updateMask []watson.IncidentMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, req, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockIIncidentDaoMockRecorder) Update(ctx, req, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockIIncidentDao)(nil).Update), ctx, req, updateMask)
}

// MockIIncidentTicketDetailDao is a mock of IIncidentTicketDetailDao interface.
type MockIIncidentTicketDetailDao struct {
	ctrl     *gomock.Controller
	recorder *MockIIncidentTicketDetailDaoMockRecorder
}

// MockIIncidentTicketDetailDaoMockRecorder is the mock recorder for MockIIncidentTicketDetailDao.
type MockIIncidentTicketDetailDaoMockRecorder struct {
	mock *MockIIncidentTicketDetailDao
}

// NewMockIIncidentTicketDetailDao creates a new mock instance.
func NewMockIIncidentTicketDetailDao(ctrl *gomock.Controller) *MockIIncidentTicketDetailDao {
	mock := &MockIIncidentTicketDetailDao{ctrl: ctrl}
	mock.recorder = &MockIIncidentTicketDetailDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIIncidentTicketDetailDao) EXPECT() *MockIIncidentTicketDetailDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIIncidentTicketDetailDao) Create(ctx context.Context, req *watson.IncidentTicketDetail) (*watson.IncidentTicketDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, req)
	ret0, _ := ret[0].(*watson.IncidentTicketDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockIIncidentTicketDetailDaoMockRecorder) Create(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIIncidentTicketDetailDao)(nil).Create), ctx, req)
}

// GetByIncidentId mocks base method.
func (m *MockIIncidentTicketDetailDao) GetByIncidentId(ctx context.Context, incidentId string) (*watson.IncidentTicketDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIncidentId", ctx, incidentId)
	ret0, _ := ret[0].(*watson.IncidentTicketDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIncidentId indicates an expected call of GetByIncidentId.
func (mr *MockIIncidentTicketDetailDaoMockRecorder) GetByIncidentId(ctx, incidentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIncidentId", reflect.TypeOf((*MockIIncidentTicketDetailDao)(nil).GetByIncidentId), ctx, incidentId)
}

// GetByIncidentIds mocks base method.
func (m *MockIIncidentTicketDetailDao) GetByIncidentIds(ctx context.Context, incidentId []string) ([]*watson.IncidentTicketDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIncidentIds", ctx, incidentId)
	ret0, _ := ret[0].([]*watson.IncidentTicketDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIncidentIds indicates an expected call of GetByIncidentIds.
func (mr *MockIIncidentTicketDetailDaoMockRecorder) GetByIncidentIds(ctx, incidentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIncidentIds", reflect.TypeOf((*MockIIncidentTicketDetailDao)(nil).GetByIncidentIds), ctx, incidentId)
}

// GetByTicketId mocks base method.
func (m *MockIIncidentTicketDetailDao) GetByTicketId(ctx context.Context, ticketId int) (*watson.IncidentTicketDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTicketId", ctx, ticketId)
	ret0, _ := ret[0].(*watson.IncidentTicketDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTicketId indicates an expected call of GetByTicketId.
func (mr *MockIIncidentTicketDetailDaoMockRecorder) GetByTicketId(ctx, ticketId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTicketId", reflect.TypeOf((*MockIIncidentTicketDetailDao)(nil).GetByTicketId), ctx, ticketId)
}

// UpdateByIncidentId mocks base method.
func (m *MockIIncidentTicketDetailDao) UpdateByIncidentId(ctx context.Context, req *watson.IncidentTicketDetail, updateMask []watson.TicketDetailMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByIncidentId", ctx, req, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateByIncidentId indicates an expected call of UpdateByIncidentId.
func (mr *MockIIncidentTicketDetailDaoMockRecorder) UpdateByIncidentId(ctx, req, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByIncidentId", reflect.TypeOf((*MockIIncidentTicketDetailDao)(nil).UpdateByIncidentId), ctx, req, updateMask)
}

// UpdateByTicketId mocks base method.
func (m *MockIIncidentTicketDetailDao) UpdateByTicketId(ctx context.Context, req *watson.IncidentTicketDetail, updateMask []watson.TicketDetailMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByTicketId", ctx, req, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateByTicketId indicates an expected call of UpdateByTicketId.
func (mr *MockIIncidentTicketDetailDaoMockRecorder) UpdateByTicketId(ctx, req, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByTicketId", reflect.TypeOf((*MockIIncidentTicketDetailDao)(nil).UpdateByTicketId), ctx, req, updateMask)
}

// MockIIncidentCommsDetailDao is a mock of IIncidentCommsDetailDao interface.
type MockIIncidentCommsDetailDao struct {
	ctrl     *gomock.Controller
	recorder *MockIIncidentCommsDetailDaoMockRecorder
}

// MockIIncidentCommsDetailDaoMockRecorder is the mock recorder for MockIIncidentCommsDetailDao.
type MockIIncidentCommsDetailDaoMockRecorder struct {
	mock *MockIIncidentCommsDetailDao
}

// NewMockIIncidentCommsDetailDao creates a new mock instance.
func NewMockIIncidentCommsDetailDao(ctrl *gomock.Controller) *MockIIncidentCommsDetailDao {
	mock := &MockIIncidentCommsDetailDao{ctrl: ctrl}
	mock.recorder = &MockIIncidentCommsDetailDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIIncidentCommsDetailDao) EXPECT() *MockIIncidentCommsDetailDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIIncidentCommsDetailDao) Create(ctx context.Context, req *watson.IncidentCommsDetail) (*watson.IncidentCommsDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, req)
	ret0, _ := ret[0].(*watson.IncidentCommsDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockIIncidentCommsDetailDaoMockRecorder) Create(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIIncidentCommsDetailDao)(nil).Create), ctx, req)
}

// GetByCommsMsgId mocks base method.
func (m *MockIIncidentCommsDetailDao) GetByCommsMsgId(ctx context.Context, commsMsgId string) ([]*watson.IncidentCommsDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCommsMsgId", ctx, commsMsgId)
	ret0, _ := ret[0].([]*watson.IncidentCommsDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCommsMsgId indicates an expected call of GetByCommsMsgId.
func (mr *MockIIncidentCommsDetailDaoMockRecorder) GetByCommsMsgId(ctx, commsMsgId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCommsMsgId", reflect.TypeOf((*MockIIncidentCommsDetailDao)(nil).GetByCommsMsgId), ctx, commsMsgId)
}

// GetByIncidentId mocks base method.
func (m *MockIIncidentCommsDetailDao) GetByIncidentId(ctx context.Context, incidentId string) ([]*watson.IncidentCommsDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIncidentId", ctx, incidentId)
	ret0, _ := ret[0].([]*watson.IncidentCommsDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIncidentId indicates an expected call of GetByIncidentId.
func (mr *MockIIncidentCommsDetailDaoMockRecorder) GetByIncidentId(ctx, incidentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIncidentId", reflect.TypeOf((*MockIIncidentCommsDetailDao)(nil).GetByIncidentId), ctx, incidentId)
}
