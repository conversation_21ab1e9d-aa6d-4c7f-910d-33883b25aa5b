package actions

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/risk/case_management/mocks"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	mock_annotations "github.com/epifi/gamma/cx/test/mocks/risk_ops/review/annotations"
	mockComments "github.com/epifi/gamma/cx/test/mocks/risk_ops/review/comments"
	"github.com/epifi/be-common/pkg/epifierrors"
)

type InvestigateLEAActorMockedDependencies struct {
	caseManagementClient	*mocks.MockCaseManagementClient
	commentsHelper		*mockComments.MockICommentsHelper
	annotationsHelper	*mock_annotations.MockIAnnotationsHelper
}

func NewInvestigateLEAActorWithMockedDependencies(t *testing.T) (*InvestigateLEAActor, *InvestigateLEAActorMockedDependencies, func()) {
	ctr := gomock.NewController(t)
	mockedDependencies := &InvestigateLEAActorMockedDependencies{
		caseManagementClient:	mocks.NewMockCaseManagementClient(ctr),
		commentsHelper:		mockComments.NewMockICommentsHelper(ctr),
		annotationsHelper:	mock_annotations.NewMockIAnnotationsHelper(ctr),
	}
	return NewInvestigateLEAActor(mockedDependencies.caseManagementClient, mockedDependencies.commentsHelper,
			mockedDependencies.annotationsHelper), mockedDependencies, func() {
			ctr.Finish()
		}
}

var (
	testInvestivateLEAActorActionParameters	= []*dsPb.Filter{
		{
			ParameterName:	profileIssuesName,
			Value: &dsPb.Filter_MultiSelectDropdownFilter{
				MultiSelectDropdownFilter: &dsPb.MultiSelectDropdownFilter{
					DropdownValues: []string{"annotationValue1", "annotationValue2"},
				},
			},
		},
		{
			ParameterName:	estimatedMOsName,
			Value: &dsPb.Filter_MultiSelectDropdownFilter{
				MultiSelectDropdownFilter: &dsPb.MultiSelectDropdownFilter{
					DropdownValues: []string{"annotationValue3", "annotationValue4"},
				},
			},
		},
		{
			ParameterName:	investigationNotes,
			Value: &dsPb.Filter_StringValue{
				StringValue: "test investigation notes",
			},
		},
	}
	testAllowedAnnotations	= []*reviewPb.AllowedAnnotation{
		{Id: "id1", Value: "annotationValue1"},
		{Id: "id2", Value: "annotationValue2"},
		{Id: "id1", Value: "annotationValue3"},
		{Id: "id2", Value: "annotationValue4"},
	}
)

func TestInvestigateLEAActor_Perform(t *testing.T) {
	t.Parallel()
	resp, _ := protojson.Marshal(&case_management.PerformActionResponse{Status: rpc.StatusOk()})
	ctx := context.Background()
	type args struct {
		actionInput		*ActionInput
		actionParameters	[]*dsPb.Filter
	}
	tests := []struct {
		name	string
		args	args
		mocks	func(mocks *InvestigateLEAActorMockedDependencies, args args)
		want	[]*actionPb.ActionResponse
		wantErr	bool
	}{
		{
			name:		"failed to parse annotation action parameters",
			wantErr:	true,
			args: args{
				actionParameters: testInvestivateLEAActorActionParameters,
			},
			mocks: func(mocks *InvestigateLEAActorMockedDependencies, args args) {
				mocks.annotationsHelper.EXPECT().ParseActionParameters(ctx, testInvestivateLEAActorActionParameters,
					actionTypeAnnotationFormElementsMap[reviewPb.ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR]).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
		},
		{
			name:		"failed to create comment for investigation notes",
			wantErr:	true,
			args: args{
				actionParameters:	testInvestivateLEAActorActionParameters,
				actionInput:		actionInput,
			},
			mocks: func(mocks *InvestigateLEAActorMockedDependencies, args args) {
				mocks.annotationsHelper.EXPECT().ParseActionParameters(ctx, testInvestivateLEAActorActionParameters,
					actionTypeAnnotationFormElementsMap[reviewPb.ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR]).
					Return(testAllowedAnnotations, nil)
				mocks.commentsHelper.EXPECT().AddCommentForCase(ctx, actionInput.GetCaseDetails().GetId(),
					reviewPb.CaseCommentType_CASE_COMMENT_TYPE_INVESTIGATION_NOTES, testInvestivateLEAActorActionParameters[2].GetStringValue(),
					actionInput.GetHeader().GetAgentEmail()).Return(epifierrors.ErrInvalidArgument)
			},
		},
		{
			name:		"failed to create annotations",
			wantErr:	true,
			args: args{
				actionParameters:	testInvestivateLEAActorActionParameters,
				actionInput:		actionInput,
			},
			mocks: func(mocks *InvestigateLEAActorMockedDependencies, args args) {
				mocks.annotationsHelper.EXPECT().ParseActionParameters(ctx, testInvestivateLEAActorActionParameters,
					actionTypeAnnotationFormElementsMap[reviewPb.ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR]).
					Return(testAllowedAnnotations, nil)
				mocks.commentsHelper.EXPECT().AddCommentForCase(ctx, actionInput.GetCaseDetails().GetId(),
					reviewPb.CaseCommentType_CASE_COMMENT_TYPE_INVESTIGATION_NOTES, testInvestivateLEAActorActionParameters[2].GetStringValue(),
					actionInput.GetHeader().GetAgentEmail()).Return(nil)
				mocks.annotationsHelper.EXPECT().CreateAnnotations(ctx, testAllowedAnnotations, actionInput.GetCaseDetails(),
					actionInput.GetHeader().GetAgentEmail()).Return(epifierrors.ErrInvalidArgument)
			},
		},
		{
			name:		"success",
			wantErr:	false,
			args: args{
				actionParameters:	testInvestivateLEAActorActionParameters,
				actionInput:		actionInput,
			},
			mocks: func(mocks *InvestigateLEAActorMockedDependencies, args args) {
				mocks.annotationsHelper.EXPECT().ParseActionParameters(ctx, testInvestivateLEAActorActionParameters,
					actionTypeAnnotationFormElementsMap[reviewPb.ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR]).
					Return(testAllowedAnnotations, nil)
				mocks.commentsHelper.EXPECT().AddCommentForCase(ctx, actionInput.GetCaseDetails().GetId(),
					reviewPb.CaseCommentType_CASE_COMMENT_TYPE_INVESTIGATION_NOTES, testInvestivateLEAActorActionParameters[2].GetStringValue(),
					actionInput.GetHeader().GetAgentEmail()).Return(nil)
				mocks.annotationsHelper.EXPECT().CreateAnnotations(ctx, testAllowedAnnotations, actionInput.GetCaseDetails(),
					actionInput.GetHeader().GetAgentEmail()).Return(nil)
				mocks.caseManagementClient.EXPECT().PerformAction(ctx, &case_management.PerformActionRequest{
					CaseId:		actionInput.GetCaseDetails().GetId(),
					ActionType:	reviewPb.ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR,
					AnalystEmail:	actionInput.GetHeader().GetAgentEmail(),
					Source:		reviewPb.ActionSource_ACTION_SOURCE_REVIEW_FLOW,
				}).Return(&case_management.PerformActionResponse{Status: rpc.StatusOk()}, nil)
			},
			want: []*actionPb.ActionResponse{
				{
					ActionResponseType:	actionPb.ActionResponseType_JSON,
					ActionResponse: &actionPb.ActionResponse_JsonResp{
						JsonResp: string(resp),
					},
				},
			},
		},
		{
			name:		"failed to perform action",
			wantErr:	true,
			args: args{
				actionParameters:	testInvestivateLEAActorActionParameters,
				actionInput:		actionInput,
			},
			mocks: func(mocks *InvestigateLEAActorMockedDependencies, args args) {
				mocks.annotationsHelper.EXPECT().ParseActionParameters(ctx, testInvestivateLEAActorActionParameters,
					actionTypeAnnotationFormElementsMap[reviewPb.ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR]).
					Return(testAllowedAnnotations, nil)
				mocks.commentsHelper.EXPECT().AddCommentForCase(ctx, actionInput.GetCaseDetails().GetId(),
					reviewPb.CaseCommentType_CASE_COMMENT_TYPE_INVESTIGATION_NOTES, testInvestivateLEAActorActionParameters[2].GetStringValue(),
					actionInput.GetHeader().GetAgentEmail()).Return(nil)
				mocks.annotationsHelper.EXPECT().CreateAnnotations(ctx, testAllowedAnnotations, actionInput.GetCaseDetails(),
					actionInput.GetHeader().GetAgentEmail()).Return(nil)
				mocks.caseManagementClient.EXPECT().PerformAction(ctx, &case_management.PerformActionRequest{
					CaseId:		actionInput.GetCaseDetails().GetId(),
					ActionType:	reviewPb.ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR,
					AnalystEmail:	actionInput.GetHeader().GetAgentEmail(),
					Source:		reviewPb.ActionSource_ACTION_SOURCE_REVIEW_FLOW,
				}).Return(&case_management.PerformActionResponse{Status: rpc.StatusInternal()}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDependencies, assertTest := NewInvestigateLEAActorWithMockedDependencies(t)
			if tt.mocks != nil {
				tt.mocks(mockedDependencies, tt.args)
			}
			got, err := s.Perform(ctx, tt.args.actionInput, tt.args.actionParameters)
			if (err != nil) != tt.wantErr {
				t.Errorf("Perform() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Perform() got = %v, want %v", got, tt.want)
			}
			assertTest()
		})
	}
}

func TestInvestigateLEAActor_FetchFormElements(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	type args struct {
		actionInput *ActionInput
	}
	tests := []struct {
		name	string
		mocks	func(mocks *InvestigateLEAActorMockedDependencies, args args)
		args	args
		want	[]*dsPb.ParameterMeta
		wantErr	bool
	}{
		{
			name:		"failed to fetch annotations form elements",
			wantErr:	true,
			mocks: func(mocks *InvestigateLEAActorMockedDependencies, args args) {
				mocks.annotationsHelper.EXPECT().GetFormElements(ctx,
					actionTypeAnnotationFormElementsMap[reviewPb.ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR]).
					Return(nil, epifierrors.ErrPermanent)
			},
		},
		{
			name:		"success",
			wantErr:	false,
			mocks: func(mocks *InvestigateLEAActorMockedDependencies, args args) {
				mocks.annotationsHelper.EXPECT().GetFormElements(ctx,
					actionTypeAnnotationFormElementsMap[reviewPb.ActionType_ACTION_TYPE_INVESTIGATE_LEA_ACTOR]).
					Return([]*dsPb.ParameterMeta{
						{
							Name:			"testName",
							Label:			"testLabel",
							ParameterOption:	dsPb.ParameterOption_MANDATORY,
							Options:		[]string{"annotation1", "annotation2"},
						},
					}, nil)
			},
			want: []*dsPb.ParameterMeta{
				{
					Name:			"testName",
					Label:			"testLabel",
					ParameterOption:	dsPb.ParameterOption_MANDATORY,
					Options:		[]string{"annotation1", "annotation2"},
				},
				{
					Name:			investigationNotes,
					Label:			"Investigation Notes",
					Type:			dsPb.ParameterDataType_MULTILINE_TEXT,
					ParameterOption:	dsPb.ParameterOption_MANDATORY,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDependencies, assertTest := NewInvestigateLEAActorWithMockedDependencies(t)
			if tt.mocks != nil {
				tt.mocks(mockedDependencies, tt.args)
			}
			got, err := s.FetchFormElements(ctx, tt.args.actionInput)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchFormElements() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FetchFormElements() got = %v, want %v", got, tt.want)
			}
			assertTest()
		})
	}
}
