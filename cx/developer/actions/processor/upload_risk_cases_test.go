//nolint:all
package processor

import (
	"bytes"
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/api/rpc"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	cmMocks "github.com/epifi/gamma/api/risk/case_management/mocks"
)

type mockedDependencies struct {
	mockCaseManagementClient *cmMocks.MockCaseManagementClient
}

type svcDependencies struct {
	caseManagementClient caseManagementPb.CaseManagementClient
}

func newMocks(t *testing.T) (*svcDependencies, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)
	mockCaseManagementClient := cmMocks.NewMockCaseManagementClient(ctr)

	md := &mockedDependencies{
		mockCaseManagementClient: mockCaseManagementClient,
	}
	svc := &svcDependencies{
		caseManagementClient: mockCaseManagementClient,
	}
	return svc, md,
		func() {
			ctr.Finish()
		}
}

func TestUploadRiskCases_validateRules(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx		context.Context
		externalIds	[]string
	}
	type mockListRules struct {
		isEnable	bool
		req		*caseManagementPb.ListRulesRequest
		res		*caseManagementPb.ListRulesResponse
		err		error
	}
	tests := []struct {
		name		string
		args		args
		want		[]string
		listRules	mockListRules
		wantErr		bool
	}{
		{
			name:	"successful verification",
			args: args{
				ctx:		context.Background(),
				externalIds:	[]string{"test-1", "test-2"},
			},
			want:	nil,
			listRules: mockListRules{
				isEnable:	true,
				req: &caseManagementPb.ListRulesRequest{
					ExternalIds: []string{"test-1", "test-2"},
				},
				res: &caseManagementPb.ListRulesResponse{
					Status:	rpc.StatusOk(),
					Rules: []*caseManagementPb.Rule{
						{
							Id:		"test-1",
							ExternalId:	"test-1",
						},
						{
							Id:		"test-2",
							ExternalId:	"test-2",
						},
					},
				},
				err:	nil,
			},
			wantErr:	false,
		},
		{
			name:	"listAPI returns record not found",
			args: args{
				ctx:		context.Background(),
				externalIds:	[]string{"test-1", "test-2"},
			},
			want:	[]string{"test-1", "test-2"},
			listRules: mockListRules{
				isEnable:	true,
				req: &caseManagementPb.ListRulesRequest{
					ExternalIds: []string{"test-1", "test-2"},
				},
				res: &caseManagementPb.ListRulesResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err:	nil,
			},
			wantErr:	false,
		},
		{
			name:	"validation failed",
			args: args{
				ctx:		context.Background(),
				externalIds:	[]string{"test-1", "test-2"},
			},
			want:	[]string{"test-2"},
			listRules: mockListRules{
				isEnable:	true,
				req: &caseManagementPb.ListRulesRequest{
					ExternalIds: []string{"test-1", "test-2"},
				},
				res: &caseManagementPb.ListRulesResponse{
					Status:	rpc.StatusOk(),
					Rules: []*caseManagementPb.Rule{
						{
							Id:		"test-1",
							ExternalId:	"test-1",
						},
					},
				},
				err:	nil,
			},
			wantErr:	false,
		},
		{
			name:	"internal error",
			args: args{
				ctx:		context.Background(),
				externalIds:	[]string{"test-1", "test-2"},
			},
			want:	nil,
			listRules: mockListRules{
				isEnable:	true,
				req: &caseManagementPb.ListRulesRequest{
					ExternalIds: []string{"test-1", "test-2"},
				},
				res: &caseManagementPb.ListRulesResponse{
					Status: rpc.StatusUnauthenticated(),
				},
				err:	nil,
			},
			wantErr:	true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, mockedDeps, assertTest := newMocks(t)

			if tt.listRules.isEnable {
				mockedDeps.mockCaseManagementClient.EXPECT().ListRules(gomock.Any(), tt.listRules.req).Return(tt.listRules.res, tt.listRules.err)
			}

			u := &UploadRiskCases{
				caseManagementClient: p.caseManagementClient,
			}
			got, err := u.validateRules(tt.args.ctx, tt.args.externalIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessPayload() error = %v, wantErr %v", err, tt.wantErr)
			}
			assert.Equalf(t, tt.want, got, "validateRules(%v, %v)", tt.args.ctx, tt.args.externalIds)

			assertTest()
		})
	}
}

func TestUploadRiskCases_validateParams(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx		context.Context
		fileContent	[]byte
		payloadType	caseManagementPb.PayloadType
		batchId		string
	}
	tests := []struct {
		name	string
		args	args
		want	[]string
		wantErr	bool
	}{
		{
			name:	"success",
			args: args{
				ctx:		context.Background(),
				payloadType:	caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS,
				fileContent:	[]byte("ActorId,ExternalRuleId\nabcd,xyz\nabcde,wxyz"),
				batchId:	"batch-1",
			},
			want:		[]string{"xyz", "wxyz"},
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			u := &UploadRiskCases{}
			got, err := u.fetchExternalIdsFromFile(bytes.NewReader(tt.args.fileContent))
			if (err != nil) != tt.wantErr {
				t.Errorf("fetchExternalIdsFromFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fetchExternalIdsFromFile() got = %v, want %v", got, tt.want)
			}
		})
	}
}
