//nolint:all
package processor

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"

	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/frontend/deeplink"
	fcm "github.com/epifi/gamma/api/frontend/fcm"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	JSON = "JSON"
)

type DeepLinkBase64Encoder struct {
}

func NewDeepLinkBase64Encoder() *DeepLinkBase64Encoder {
	return &DeepLinkBase64Encoder{}
}
func (c *DeepLinkBase64Encoder) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	return []*db_state.ParameterMeta{
		{
			Name:            JSON,
			Label:           "FCM  Notification Json",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}, nil
}

type Base64EncoderResponse struct {
	Base64DeepLink        string
	Base64FmcNotification string
}

func (c *DeepLinkBase64Encoder) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	logger.Info(ctx, "executing dev action to DeepLink to base64 encode")
	fcmJson, err := c.GetParameterValues(ctx, filters)
	if err != nil {
		return "", err
	}
	fcmDeepLink := &fcm.Notification{}
	err = protojson.Unmarshal([]byte(fcmJson), fcmDeepLink)
	if err != nil {
		logger.Error(ctx, "error while unmarshal the json", zap.Error(err))
		return "", err
	}
	var deepLink *deeplink.Deeplink
	switch {
	case fcmDeepLink.GetFullscreenTemplate() != nil:
		deepLink = fcmDeepLink.GetFullscreenTemplate().GetCommonTemplateFields().GetDeeplink()
	case fcmDeepLink.GetSystemTrayTemplate() != nil:
		deepLink = fcmDeepLink.GetSystemTrayTemplate().GetCommonTemplateFields().GetDeeplink()
	case fcmDeepLink.GetBackgroundTemplate() != nil:
		deepLink = fcmDeepLink.GetBackgroundTemplate().GetCommonTemplateFields().GetDeeplink()
	case fcmDeepLink.GetInAppFullScreenTemplate() != nil:
		deepLink = fcmDeepLink.GetInAppFullScreenTemplate().GetCta().GetDeeplink()
	case fcmDeepLink.GetInAppTemplate() != nil:
		deepLink = fcmDeepLink.GetInAppTemplate().GetCommonTemplateFields().GetDeeplink()
	}

	marshalledDeepLink, err := proto.Marshal(deepLink)
	if err != nil {
		logger.Error(ctx, "error while marshal the deeplink", zap.Error(err))
		return "", err
	}
	marshalledFcmNotification, err := proto.Marshal(fcmDeepLink)
	if err != nil {
		logger.Error(ctx, "error while marshal the fcm notification", zap.Error(err))
		return "", err
	}
	base64DeepLink := base64.StdEncoding.EncodeToString(marshalledDeepLink)
	base64FcmNotificaion := base64.StdEncoding.EncodeToString(marshalledFcmNotification)
	response := Base64EncoderResponse{
		Base64DeepLink:        base64DeepLink,
		Base64FmcNotification: base64FcmNotificaion,
	}
	// returned marshalled response
	marshalledRes, err := json.Marshal(response)
	if err != nil {
		logger.Error(ctx, "error marshalling Base64EncoderResponse", zap.Error(err))
		return "", nil
	}
	return string(marshalledRes), nil
}
func (c *DeepLinkBase64Encoder) GetParameterValues(ctx context.Context, filters []*dsPb.Filter) (string, error) {
	var fcmJson string
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case JSON:
			fcmJson = filter.GetStringValue()
			if fcmJson == "" {
				logger.Error(ctx, fmt.Sprintf("invalid json: %s", filter.GetStringValue()))
				return "", status.Error(codes.InvalidArgument, fmt.Sprintf("invalid Json: %s", filter.GetStringValue()))
			}
		default:
			return "", status.Error(codes.InvalidArgument, fmt.Sprintf("invalid Json: %s", filter.GetStringValue()))
		}
	}
	return fcmJson, nil
}
