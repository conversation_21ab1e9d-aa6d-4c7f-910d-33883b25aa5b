// nolint: dupl
//
//nolint:all
package processor

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/be-common/pkg/logger"
)

type DeleteExchangerOfferListing struct {
	exchangerOfferSvcClient exchangerPb.ExchangerOfferServiceClient
}

func NewDeleteExchangerOfferListing(exchangerOfferSvcClient exchangerPb.ExchangerOfferServiceClient) *DeleteExchangerOfferListing {
	return &DeleteExchangerOfferListing{exchangerOfferSvcClient: exchangerOfferSvcClient}
}

func (c *DeleteExchangerOfferListing) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*db_state.ParameterMeta, error) {

	return []*db_state.ParameterMeta{
		{
			Name:            offerListingId,
			Label:           "Offer Listing Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}, nil
}

func (c *DeleteExchangerOfferListing) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*db_state.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	deleteOfferListingReq := &exchangerPb.DeleteExchangerOfferListingRequest{}
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case offerListingId:
			deleteOfferListingReq.OfferListingId = filter.GetStringValue()

		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	// delete offer listing
	resp, err := c.exchangerOfferSvcClient.DeleteExchangerOfferListing(ctx, deleteOfferListingReq)
	if err != nil {
		// if grpc error occurred return the error
		return "", err
	}

	// returned marshalled response
	marshalledRes, err := protojson.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "error marshalling DeleteExchangerOfferListing response", zap.Error(err))
		return "", nil
	}
	return string(marshalledRes), nil
}
