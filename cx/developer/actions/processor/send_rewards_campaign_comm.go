//nolint:all
package processor

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/rewards/campaigncomm"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	commType = "comm_type"
)

type SendRewardsCampaignComm struct {
	rewardsCampaignCommClient campaigncomm.RewardsCampaignCommClient
}

func NewSendRewardsCampaignComm(rewardsCampaignCommClient campaigncomm.RewardsCampaignCommClient) *SendRewardsCampaignComm {
	return &SendRewardsCampaignComm{rewardsCampaignCommClient: rewardsCampaignCommClient}
}

func (c *SendRewardsCampaignComm) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*db_state.ParameterMeta, error) {
	supportedCommTypes := []string{campaigncomm.CommType_REWARD_ON_FIRST_FUND_ADDITION.String(), campaigncomm.CommType_REWARD_ON_SALARY_DEPOSIT_V1.String()}

	return []*db_state.ParameterMeta{
		{
			Name:            commType,
			Label:           "Comm Type",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         supportedCommTypes,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}, nil
}

func (c *SendRewardsCampaignComm) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*db_state.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	sendCommRequest := &campaigncomm.SendCommRequest{}
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case commType:
			sendCommRequest.CommType = campaigncomm.CommType(campaigncomm.CommType_value[filter.GetDropdownValue()])

		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	// send campaign comm
	resp, err := c.rewardsCampaignCommClient.SendComm(ctx, sendCommRequest)
	if err != nil {
		// if grpc error occurred return the error
		return "", err
	}

	// returned marshalled response
	marshalledRes, err := protojson.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "error marshalling SendComm response", zap.Error(err))
		// since action has been done do not return error
		return "{\"error\": \"error in marshal response \"}", nil
	}
	return string(marshalledRes), nil
}
