startDelaySeconds: 0
ssl: false
lowercaseOutputName: true
lowercaseOutputLabelNames: true
rules:
  - pattern : "kafka.connect<type=connect-worker-metrics>([^:]+):"
    name: "kafka_connect_connect_worker_metrics_$1"
  - pattern : "kafka.connect<type=connect-metrics, client-id=([^:]+)><>([^:]+)"
    name: "kafka_connect_connect_metrics_$2"
    labels:
      client: "$1"
  - pattern: "debezium.([^:]+)<type=connector-metrics, context=([^,]+), server=([^,]+), key=([^>]+)><>RowsScanned"
    name: "debezium_metrics_rows_scanned"
    labels:
      plugin: "$1"
      name: "$3"
      context: "$2"
      table: "$4"
  - pattern: "debezium.([^:]+)<type=connector-metrics, context=([^,]+), server=([^>]+)>([^:]+)"
    name: "debezium_metrics_$4"
    labels:
      plugin: "$1"
      name: "$3"
      context: "$2"
  - pattern: 'kafka.connect<type=connector-task-metrics, connector=(.+), task=(.+)><>status: (.+)'
    name: "kafka_connect_task_status"
    value: 1
    labels:
      connector: "$1"
      task: "$2"
      status: "$3"
