package onemoney

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/heartbeat"
	vgAaPb "github.com/epifi/gamma/api/vendorgateway/aa"
	"github.com/epifi/gamma/heartbeat/config"
	"github.com/epifi/gamma/heartbeat/datastore"
	"github.com/epifi/gamma/heartbeat/trigger"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

var OneMoneyTriggerWireSet = wire.NewSet(NewOneMoneyTrigger)

type OneMoneyTrigger struct {
	conf       *config.HeartbeatTriggerConfig
	vgAaClient vgAaPb.AccountAggregatorClient
	datastore  datastore.HeartbeatStore
}

func NewOneMoneyTrigger(conf *config.Config, vgAaClient vgAaPb.AccountAggregatorClient, redisClient *redis.Client) *OneMoneyTrigger {
	// initialize redis data store
	redisDataStore := datastore.NewHeartbeatRedisStoreSvc(redisClient)
	return &OneMoneyTrigger{
		vgAaClient: vgAaClient,
		datastore:  redisDataStore,
		conf:       conf.OneMoneyTriggerConfig,
	}
}

var _ trigger.Trigger = &OneMoneyTrigger{}

func (o *OneMoneyTrigger) Execute(ctx context.Context) (*heartbeat.HeartbeatStat, error) {
	// call vg api and get response
	resp, respErr := o.vgAaClient.GetHeartbeatStatus(ctx, &vgAaPb.GetHeartbeatStatusRequest{
		Header:   &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ONE_MONEY},
		AaEntity: vgAaPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
	})
	if rpcErr := epifigrpc.RPCError(resp, respErr); rpcErr != nil {
		return nil, errors.Wrap(rpcErr, "error executing trigger - vg api GetHeartbeatStatus failure")
	}
	var hStatus heartbeat.HeartbeatStatus
	if resp.GetAaStatus() == vgAaPb.GetHeartbeatStatusResponse_UP {
		hStatus = heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_UP
	} else {
		hStatus = heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_DOWN
	}

	return &heartbeat.HeartbeatStat{
		HeartbeatStatus:    hStatus,
		HeartbeatTimestamp: timestamppb.New(time.Now()),
	}, nil
}

func (o *OneMoneyTrigger) GetConfig() *config.HeartbeatTriggerConfig {
	return o.conf
}

func (o *OneMoneyTrigger) GetTriggerName() heartbeat.HeartbeatTrigger {
	return heartbeat.HeartbeatTrigger_HEARTBEAT_TRIGGER_VENDOR_ONE_MONEY
}

func (o *OneMoneyTrigger) GetDataStore() datastore.HeartbeatStore {
	return o.datastore
}
