package reminder_builder

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/epifi/gamma/budgeting/reminder/events"

	"github.com/epifi/gamma/budgeting/reminder/dao"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	actorPb "github.com/epifi/gamma/api/actor"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	reminderPb "github.com/epifi/gamma/api/budgeting/reminder"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/frontend/budgeting/reminder/meta"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	managerPb "github.com/epifi/gamma/api/rms/manager"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/budgeting/reminder/utils"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
)

type CategoryReminder struct {
	config            *reminderPb.CategoryReminderConfig
	txnAggregates     txnAggregatesPb.TxnAggregatesClient
	actorClient       actorPb.ActorClient
	savingsClient     savingsClientPb.SavingsClient
	ruleManagerClient managerPb.RuleManagerClient
	reminderLogDao    dao.ReminderLogDao
}

func (c *CategoryReminder) BuildReminderConditionCorrectScreen(ctx context.Context, request *BuildReminderConditionCorrectScreenRequest) (*BuildReminderConditionCorrectScreenResponse, error) {
	var screen *reminderPb.PostReminderScreen
	accountId, err := utils.GetAccountIdForUser(ctx, request.ActorId, c.actorClient, c.savingsClient)
	if err != nil {
		return nil, errors.Wrapf(err, "error in getting accountId")
	}
	_, _, startOfThisMonthTime, currentTime := utils.GetCurrentAndLastMonthDates(time.Now().In(datetime.IST))
	categoriesAgg, err := utils.GetCategoryAggregate(ctx, c.txnAggregates, request.ActorId, timestamp.New(startOfThisMonthTime), timestamp.New(currentTime), accountId)
	if err != nil {
		return nil, errors.Wrapf(err, "error in getting category agg")
	}
	configuredCategory := request.RuleParams.GetRuleParamValues()[utils.ConfiguredCategory].GetStrVal()
	configuredAmount := request.RuleParams.GetRuleParamValues()[utils.ConfiguredAmount].GetMoneyVal().GetUnits()
	for _, eachCatAgg := range categoriesAgg {
		if strings.EqualFold(eachCatAgg.GetCategory(), configuredCategory) && configuredAmount <= eachCatAgg.GetSumAmount().GetUnits() {
			screen = &reminderPb.PostReminderScreen{
				ScreenType: reminderPb.PostReminderScreenType_EXCEED_REMINDER,
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: ExceedSpendsTitle,
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
					},
				},
				Rows: []*reminderPb.PostReminderScreenRows{
					{
						Row: &reminderPb.PostReminderScreenRows_TextRow{
							TextRow: &commontypes.Text{
								DisplayValue: &commontypes.Text_Html{
									Html: fmt.Sprintf("<font color='#646464'>You’ve already spent %s on %s in %s,<br> which is more than your reminder of %s.</font>", money.ToDisplayStringInIndianFormat(eachCatAgg.GetSumAmount().GetBeMoney(), 0, true),
										utils.DisplayCategoryToDisplayName[strings.ToUpper(configuredCategory)],
										time.Now().In(datetime.IST).Month().String(),
										money.ToDisplayStringInIndianFormat(request.RuleParams.GetRuleParamValues()[utils.ConfiguredAmount].GetMoneyVal().GetBeMoney(), 0, true)),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
								},
							},
						},
					},
				},
				PrimaryCta: &reminderPb.PostReminderScreenCta{
					CtaText: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "Done",
						},
						FontColor: "#FFFFFF",
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
						},
					},
					BgColor: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_BlockColour{
							BlockColour: "#00B899",
						},
					},
				},
				SecondaryCta: &reminderPb.PostReminderScreenCta{
					CtaText: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "Edit Amount",
						},
						FontColor: "#00B899",
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
						},
					},
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_REMINDERS_CONFIG_SCREEN,
						ScreenOptions: &deeplinkPb.Deeplink_RemindersConfigScreenOptions{
							RemindersConfigScreenOptions: &deeplinkPb.RemindersConfigScreenOptions{
								ConfigType:   meta.ReminderConfigType_CONFIG_UPDATE,
								ReminderType: meta.ReminderType_CATEGORY_SPENDS,
								ConfigFlow: &deeplinkPb.RemindersConfigScreenOptions_UpdateConfig{
									UpdateConfig: &deeplinkPb.UpdateReminderConfig{
										ReminderSubscriptionId: request.SubscriptionId,
									},
								},
							},
						},
					},
					BgColor: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_BlockColour{
							BlockColour: "#F7F9FA",
						},
					},
				},
				PrimaryImg: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/quick-link-icons/reminder_done.png",
				},
			}
			return &BuildReminderConditionCorrectScreenResponse{
				Screen: screen,
			}, nil
		}
	}
	return nil, nil
}

func NewCategoryReminder(config *reminderPb.CategoryReminderConfig, txnAggregates txnAggregatesPb.TxnAggregatesClient, actorClient actorPb.ActorClient,
	savingsClient savingsClientPb.SavingsClient, ruleManagerClient managerPb.RuleManagerClient, reminderLogDao dao.ReminderLogDao) *CategoryReminder {
	return &CategoryReminder{
		config:            config,
		txnAggregates:     txnAggregates,
		actorClient:       actorClient,
		savingsClient:     savingsClient,
		ruleManagerClient: ruleManagerClient,
		reminderLogDao:    reminderLogDao,
	}
}

func (c *CategoryReminder) BuildParams(request *BuildParamsRequest) (*managerPb.RuleParamValues, error) {
	ruleParamValues := &managerPb.RuleParamValues{}
	ruleParamMap := make(map[string]*managerPb.Value)
	money := c.config.GetAmount()

	ruleParamMap[utils.ConfiguredDuration] = &managerPb.Value{
		Value: &managerPb.Value_TimePeriod{
			TimePeriod: managerPb.TimePeriod_MONTH,
		},
	}
	ruleParamMap[utils.ConfiguredAmount] = &managerPb.Value{
		Value: &managerPb.Value_MoneyVal{
			MoneyVal: money,
		},
	}

	ruleParamMap[utils.ConfiguredCategory] = &managerPb.Value{
		Value: &managerPb.Value_StrVal{
			StrVal: strings.ToLower(c.config.GetCategory().String()),
		},
	}
	ruleParamMap[utils.CommsMedium] = &managerPb.Value{
		Value: &managerPb.Value_CommsMediums{
			CommsMediums: &managerPb.CommsMedium{
				MediumList: request.CommMediums,
			},
		},
	}
	ruleParamValues.RuleParamValues = ruleParamMap
	return ruleParamValues, nil
}

func (c *CategoryReminder) DecorateReminder(ctx context.Context, request *DecorateReminderRequest) (*DecorateReminderResponse, error) {
	var (
		widgets   []*reminderPb.ReminderSubscriptionWidget
		aggAmount string
	)
	catToAggThisMonth, _, err := c.GetCategoriesAggregate(ctx, request.ActorId)
	if err != nil {
		logger.Error(ctx, "error while getting categories aggregate", zap.Error(err))
	}
	for _, sub := range request.ReminderSubs {
		var deeplinkReminderStatus meta.ReminderStatus
		if sub.GetState() == managerPb.RuleSubscriptionState_ACTIVE {
			deeplinkReminderStatus = meta.ReminderStatus_PAUSED
		} else if sub.GetState() == managerPb.RuleSubscriptionState_INACTIVE {
			deeplinkReminderStatus = meta.ReminderStatus_ACTIVE
		}

		now := time.Now().In(datetime.IST)
		month, year := now.Month(), now.Year()
		reminderLog, err := c.reminderLogDao.Get(ctx, sub.GetId(), int32(year), int32(month))
		if err != nil {
			logger.Error(ctx, "error while fetching from reminder_logs_per_month db", zap.String("reminder_subscription_id", sub.GetId()))
		}
		topSectionSubHeading := constructTopSectionSubHeading(reminderLog.GetCount(), err != nil)
		ruleParams := sub.GetRuleParamValues().GetRuleParamValues()
		amount := ruleParams[utils.ConfiguredAmount]
		category := ruleParams[utils.ConfiguredCategory]
		cleanCategory := utils.DisplayCategoryToDisplayName[strings.ToUpper(category.GetStrVal())]
		categoryAgg := catToAggThisMonth[strings.ToUpper(category.GetStrVal())].GetSumAmount().GetBeMoney()
		if categoryAgg == nil {
			aggAmount = fmt.Sprintf(utils.CategoryReminderRightText, "0")
		} else {
			aggAmount = fmt.Sprintf(utils.CategoryReminderRightText, money.ToDisplayStringInIndianFormat(catToAggThisMonth[strings.ToUpper(category.GetStrVal())].GetSumAmount().GetBeMoney(), 0, false))
		}
		createWidgetParams := &CreateReminderSubscriptionWidgetParams{
			SubscriptionId:         sub.GetId(),
			ReminderType:           reminderPb.ReminderType_CATEGORY_SPENDS,
			TopSectionPrimaryImg:   fmt.Sprintf(utils.CategoryImageTmpl, utils.DisplayCatToFileName[categorizerPb.DisplayCategory(categorizerPb.DisplayCategory_value[strings.ToUpper(category.GetStrVal())])]),
			TopSectionWidgetTitle:  utils.CategoryReminderTitle,
			TopSectionHeading:      fmt.Sprintf(utils.CategoryReminderHeading, money.ToDisplayStringInIndianFormat(amount.GetMoneyVal().GetBeMoney(), 0, false), cleanCategory),
			TopSectionSubHeading:   topSectionSubHeading,
			BottomSectionLeftText:  utils.CategoryReminderLeftText,
			BottomSectionRightText: aggAmount,
			TopSectionRightCtaDeeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_REMINDERS_CONFIG_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_RemindersConfigScreenOptions{
					RemindersConfigScreenOptions: &deeplinkPb.RemindersConfigScreenOptions{
						ConfigType:   meta.ReminderConfigType_CONFIG_UPDATE,
						ReminderType: meta.ReminderType_CATEGORY_SPENDS,
						ConfigFlow: &deeplinkPb.RemindersConfigScreenOptions_UpdateConfig{
							UpdateConfig: &deeplinkPb.UpdateReminderConfig{
								ReminderSubscriptionId: sub.GetId(),
							},
						},
						Source: events.ReminderSourceMyReminders,
					},
				},
			},
			BottomSectionCtaDeeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_REMINDERS_UPDATE_STATE,
				ScreenOptions: &deeplinkPb.Deeplink_UpdateReminderStateOptions{
					UpdateReminderStateOptions: &deeplinkPb.RemindersUpdateStateOptions{
						ReminderSubscriptionId: sub.GetId(),
						ReminderStatus:         deeplinkReminderStatus,
					},
				},
			},
			CategoryConfig: &reminderPb.ReminderSubscription_CategoryConfig{
				CategoryConfig: &reminderPb.CategoryReminderConfig{
					Amount: &types.Money{
						CurrencyCode: "INR",
						Units:        amount.GetMoneyVal().GetUnits(),
					},
					Category: categorizerPb.DisplayCategory(categorizerPb.DisplayCategory_value[strings.ToUpper(category.GetStrVal())]),
				},
			},
			ReminderStatus: utils.RuleStatusToReminderStatusMap[sub.GetState()],
		}
		widgets = append(widgets, CreateReminderSubscriptionWidget(createWidgetParams))
	}
	return &DecorateReminderResponse{
		Widgets: widgets,
	}, nil
}

func constructTopSectionSubHeading(triggeredCount int32, isErrResp bool) string {
	if isErrResp {
		return ""
	}
	heading := "NOT YET INITIATED THIS MONTH"
	if triggeredCount == 1 {
		heading = fmt.Sprintf("INITIATED %d TIME THIS MONTH", triggeredCount)
	}
	if triggeredCount > 1 {
		heading = fmt.Sprintf("INITIATED %d TIMES THIS MONTH", triggeredCount)
	}
	return heading
}

func (c *CategoryReminder) GetCategoriesAggregate(ctx context.Context, actorId string) (map[string]*txnAggregatesPb.Aggregate, map[string]*txnAggregatesPb.Aggregate, error) {
	var (
		wg                                         sync.WaitGroup
		catAggThisMonth, catAggLastMonth           []*txnAggregatesPb.Aggregate
		catAggThisMonthError, catAggLastMonthError error
	)
	catToAggThisMonth := make(map[string]*txnAggregatesPb.Aggregate)
	catToAggLastMonth := make(map[string]*txnAggregatesPb.Aggregate)
	accountId, err := utils.GetAccountIdForUser(ctx, actorId, c.actorClient, c.savingsClient)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "error in getting accountId")
	}
	wg.Add(2)
	startOfLastMonthTime, endofLastMonthTime, startOfThisMonthTime, currentTime := utils.GetCurrentAndLastMonthDates(time.Now().In(datetime.IST))
	goroutine.Run(ctx, utils.GoroutineTimeoutDuration, func(ctx context.Context) {
		defer wg.Done()
		catAggThisMonth, catAggThisMonthError = utils.GetCategoryAggregate(ctx, c.txnAggregates, actorId, timestamp.New(startOfThisMonthTime), timestamp.New(currentTime), accountId)
		if catAggThisMonthError != nil {
			logger.Error(ctx, "error in getting category agg for this month", zap.Error(catAggThisMonthError))
		}
	})
	goroutine.Run(ctx, utils.GoroutineTimeoutDuration, func(ctx context.Context) {
		defer wg.Done()
		catAggLastMonth, catAggLastMonthError = utils.GetCategoryAggregate(ctx, c.txnAggregates, actorId, timestamp.New(startOfLastMonthTime), timestamp.New(endofLastMonthTime), accountId)
		if catAggLastMonthError != nil {
			logger.Error(ctx, "error in getting category agg for last month", zap.Error(catAggLastMonthError))
		}
	})
	wg.Wait()
	if catAggThisMonthError != nil && catAggLastMonthError != nil {
		return nil, nil, errors.New("error in getting txnaggregate")
	}
	for _, eachCatAgg := range catAggThisMonth {
		catToAggThisMonth[eachCatAgg.Category] = eachCatAgg
	}
	for _, eachCatAgg := range catAggLastMonth {
		catToAggLastMonth[eachCatAgg.Category] = eachCatAgg
	}
	return catToAggThisMonth, catToAggLastMonth, nil
}

func (c *CategoryReminder) GetReminderConfig(ctx context.Context, request *GetReminderConfigRequest) (*GetReminderConfigResponse, error) {
	var (
		categoryDeeplink *deeplinkPb.Deeplink
		categoryEditIcon string
		avgAgg           int64
		highLightedText  *reminderPb.HighlightedText
	)
	categoriesAgg, err := utils.GetRecommendedCategoryReminder(ctx, c.actorClient, c.savingsClient, c.txnAggregates, request.ActorId, 100)
	if err != nil {
		return nil, errors.Wrapf(err, "error in getting category Aggregate")
	}
	if c.config.GetAmount() == nil || c.config.GetCategory() == categorizerPb.DisplayCategory_DISPLAY_CATEGORY_UNSPECIFIED {
		defaultConfig, err := c.getDefaultCategoryConfig(ctx, request.ActorId)
		if err != nil {
			return nil, errors.Wrapf(err, "error in getting default config")
		}
		c.config = defaultConfig
	}

	for _, eachCatAgg := range categoriesAgg {
		if strings.EqualFold(eachCatAgg.DisplayCategory, c.config.GetCategory().String()) {
			avgAgg = eachCatAgg.Amount
		}
	}
	if !request.IsUpdateRequest {
		categoryDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REMINDERS_SUPPORTED_TOPICS_LIST_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_RemindersSupportedTopicsListScreenOptions{
				RemindersSupportedTopicsListScreenOptions: &deeplinkPb.RemindersSupportedTopicsListScreenOptions{
					ReminderType: []meta.ReminderType{
						meta.ReminderType_CATEGORY_SPENDS,
					},
				},
			},
		}
		categoryEditIcon = "https://epifi-icons.pointz.in/quick-link-icons/reminder_edit_mode.png"
	}
	if avgAgg != 0 {
		highLightedText = &reminderPb.HighlightedText{
			Content: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: fmt.Sprintf("<font color='#333333'>You spend <font color='##555555'>₹%d</font> on avg per month</font>", avgAgg),
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_4,
				},
			},
			BgColor: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#F4E7BF",
				},
			},
		}
	}
	return &GetReminderConfigResponse{
		Widgets: []*reminderPb.ReminderFormWidget{
			{
				Widget: &reminderPb.ReminderFormWidget_ReminderFormInput{
					ReminderFormInput: &reminderPb.ReminderFormInput{
						HighlightedText: highLightedText,
						Rows: []*reminderPb.ReminderFormInputRow{
							{
								Sections: []*reminderPb.ReminderFormInputRowSection{
									{
										Content: &reminderPb.ReminderFormInputRowSection_TextContent{
											TextContent: &commontypes.Text{
												DisplayValue: &commontypes.Text_PlainString{
													PlainString: "Remind me when I spend	",
												},
												FontColor: "#333333",
												FontStyle: &commontypes.Text_StandardFontStyle{
													StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
												},
											},
										},
									},
								},
							},
							{
								Sections: []*reminderPb.ReminderFormInputRowSection{
									{
										Content: &reminderPb.ReminderFormInputRowSection_TextContent{
											TextContent: &commontypes.Text{
												DisplayValue: &commontypes.Text_PlainString{
													PlainString: "more than",
												},
												FontColor: "#333333",
												FontStyle: &commontypes.Text_StandardFontStyle{
													StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
												},
											},
										},
									},
									{
										Content: &reminderPb.ReminderFormInputRowSection_InputField{
											InputField: &reminderPb.ReminderFormInputRowSectionField{
												IconTextComponent: &ui.IconTextComponent{
													Texts: []*commontypes.Text{
														{
															DisplayValue: &commontypes.Text_PlainString{
																PlainString: money.ToDisplayStringInIndianFormat(c.config.GetAmount().GetBeMoney(), 0, true),
															},
															FontColor: "#333333",
															FontStyle: &commontypes.Text_StandardFontStyle{
																StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
															},
														},
													},
													RightIcon: &commontypes.Image{
														ImageType: commontypes.ImageType_PNG,
														ImageUrl:  "https://epifi-icons.pointz.in/quick-link-icons/reminder_edit_mode.png",
														Width:     24,
														Height:    24,
													},
													RightImgTxtPadding: 8,
													ContainerProperties: &ui.IconTextComponent_ContainerProperties{
														BgColor:       "#ECEEF0",
														CornerRadius:  20,
														Height:        24,
														LeftPadding:   12,
														RightPadding:  8,
														TopPadding:    10,
														BottomPadding: 10,
													},
												},
												FieldType: reminderPb.ReminderSubscriptionFieldMask_MASK_AMOUNT,
												FieldVal: &reminderPb.ReminderFormInputRowSectionField_Amount{
													Amount: int32(c.config.GetAmount().GetBeMoney().GetUnits()),
												},
											},
										},
									},
								},
							},
							{
								Sections: []*reminderPb.ReminderFormInputRowSection{
									{
										Content: &reminderPb.ReminderFormInputRowSection_TextContent{
											TextContent: &commontypes.Text{
												DisplayValue: &commontypes.Text_PlainString{
													PlainString: "on",
												},
												FontColor: "#333333",
												FontStyle: &commontypes.Text_StandardFontStyle{
													StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
												},
											},
										},
									},
									{
										Content: &reminderPb.ReminderFormInputRowSection_InputField{
											InputField: &reminderPb.ReminderFormInputRowSectionField{
												IconTextComponent: &ui.IconTextComponent{
													Texts: []*commontypes.Text{
														{
															DisplayValue: &commontypes.Text_PlainString{
																PlainString: utils.DisplayCategoryToDisplayName[c.config.GetCategory().String()],
															},
															FontColor: "#333333",
															FontStyle: &commontypes.Text_StandardFontStyle{
																StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
															},
														},
													},
													RightIcon: &commontypes.Image{
														ImageType: commontypes.ImageType_PNG,
														ImageUrl:  categoryEditIcon,
														Width:     24,
														Height:    24,
													},
													RightImgTxtPadding: 8,
													ContainerProperties: &ui.IconTextComponent_ContainerProperties{
														BgColor:       "#ECEEF0",
														CornerRadius:  20,
														Height:        24,
														LeftPadding:   12,
														RightPadding:  8,
														TopPadding:    10,
														BottomPadding: 10,
													},
													Deeplink: categoryDeeplink,
												},
												FieldType: reminderPb.ReminderSubscriptionFieldMask_MASK_CATEGORY,
												FieldVal: &reminderPb.ReminderFormInputRowSectionField_DisplayCategory{
													DisplayCategory: c.config.GetCategory().String(),
												},
											},
										},
									},
									{
										Content: &reminderPb.ReminderFormInputRowSection_TextContent{
											TextContent: &commontypes.Text{
												DisplayValue: &commontypes.Text_PlainString{
													PlainString: "in a month",
												},
												FontColor: "#333333",
												FontStyle: &commontypes.Text_StandardFontStyle{
													StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
												},
											},
										},
									},
								},
							},
							{
								Sections: []*reminderPb.ReminderFormInputRowSection{
									{
										Content: &reminderPb.ReminderFormInputRowSection_TextContent{
											TextContent: &commontypes.Text{
												DisplayValue: &commontypes.Text_PlainString{
													PlainString: "from Federal Savings account through Fi",
												},
												FontColor: "#333333",
												FontStyle: &commontypes.Text_StandardFontStyle{
													StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			{
				Widget: buildNotificationWidget(&BuildNotificationWidgetReq{
					notificationSelected: request.IsNotificationSelected,
					smsSelected:          request.IsSmsSelected,
					whatsappSelected:     request.IsWhatsappSelected,
					emailSelected:        request.IsEmailSelected,
					reminderType:         reminderPb.ReminderType_CATEGORY_SPENDS,
				}),
			},
		},
	}, nil
}

func (c *CategoryReminder) getDefaultCategoryConfig(ctx context.Context, actorId string) (*reminderPb.CategoryReminderConfig, error) {
	fromTime := timestamp.New(time.Now().In(datetime.IST).AddDate(0, -3, 0))
	toTime := timestamp.New(time.Now().In(datetime.IST))
	accountId, err := utils.GetAccountIdForUser(ctx, actorId, c.actorClient, c.savingsClient)
	if err != nil {
		return nil, errors.Wrapf(err, "error in getting accountId")
	}
	categoriesAgg, err := utils.GetCategoryAggregate(ctx, c.txnAggregates, actorId, fromTime, toTime, accountId)
	if err != nil {
		return nil, errors.Wrapf(err, "error in getting category agg")
	}
	if len(categoriesAgg) == 0 {
		return &reminderPb.CategoryReminderConfig{
			Category: categorizerPb.DisplayCategory_SHOPPING,
			Amount: &types.Money{
				CurrencyCode: "INR",
				Units:        1000,
			},
		}, nil
	}
	categoryAgg := categoriesAgg[0]
	return &reminderPb.CategoryReminderConfig{
		Category: categorizerPb.DisplayCategory(categorizerPb.DisplayCategory_value[categoryAgg.GetCategory()]),
		Amount:   categoryAgg.GetSumAmount(),
	}, nil
}

func (c *CategoryReminder) GetReminderDetailByParams(ctx context.Context, request *GetReminderDetailByParamsRequest) (*GetReminderDetailByParamsResponse, error) {
	var (
		categoryRuleId string
		resp           GetReminderDetailByParamsResponse
		subId          string
	)
	category := c.config.GetCategory()
	rulesDetail, err := utils.GetRulesDetailForReminder(ctx, c.ruleManagerClient)
	if err != nil {
		return nil, errors.Wrapf(err, "error in getting rule detail")
	}
	for _, eachRule := range rulesDetail {
		if eachRule.GetRuleTypeForSpecialHandling() == managerPb.RuleTypeForSpecialHandling_RULE_TYPE_CATEGORY_SPENDS_REMINDER {
			categoryRuleId = eachRule.GetId()
			break
		}
	}
	if categoryRuleId == "" {
		return nil, errors.New("rule not found")
	}
	ruleSubsDetail, err := c.ruleManagerClient.GetSubscriptionsByActorForRules(ctx, &managerPb.GetSubscriptionsByActorForRulesRequest{
		ActorId: request.ActorId,
		RuleIds: []string{
			categoryRuleId,
		},
		States: []managerPb.RuleSubscriptionState{
			managerPb.RuleSubscriptionState_ACTIVE, managerPb.RuleSubscriptionState_INACTIVE,
		},
	})
	if grpcErr := epifigrpc.RPCError(ruleSubsDetail, err); grpcErr != nil {
		return nil, fmt.Errorf("error in getting subscriptions detail for rule %w", grpcErr)
	}
	if len(ruleSubsDetail.GetRuleSubscriptions()) == 0 {
		return &resp, nil
	}
	subsDetail := ruleSubsDetail.GetRuleSubscriptions()[categoryRuleId].GetRuleSubscriptions()
	for _, eachSub := range subsDetail {
		ruleParamsValue := eachSub.GetRuleParamValues().GetRuleParamValues()
		configuredCategory := ruleParamsValue[utils.ConfiguredCategory].GetStrVal()
		if strings.ToLower(configuredCategory) == strings.ToLower(category.String()) {
			subId = eachSub.GetId()
			break
		}
	}
	resp.ReminderSubscriptionId = subId
	return &resp, nil
}

func (c *CategoryReminder) BuildReminderTopics(ctx context.Context, request *BuildReminderTopicsRequest) (*GetReminderTopicsResponse, error) {
	var (
		topics               []*reminderPb.ReminderTopic
		calculatedCategories []categorizerPb.DisplayCategory
	)
	topCategoriesAgg, err := utils.GetRecommendedCategoryReminder(ctx, c.actorClient, c.savingsClient, c.txnAggregates, request.ActorId, 10)
	if err != nil {
		return nil, errors.Wrapf(err, "error in getting top recommended categories")
	}
	for _, eachCategory := range topCategoriesAgg {
		if len(topics) == 3 {
			break
		}
		displayCategory := categorizerPb.DisplayCategory(categorizerPb.DisplayCategory_value[eachCategory.DisplayCategory])
		if utils.IsCategorySupported[displayCategory] == false {
			continue
		}
		topic := buildTopicForUserTransactCategory(eachCategory)
		topics = append(topics, topic)
	}
	if len(topics) < 3 {
		remainingSupportedCategories := utils.GetRemainingSupportedCategories(calculatedCategories)
		for _, eachCategory := range remainingSupportedCategories {
			if len(topics) == 3 {
				break
			}
			topic := buildTopicForCategory(eachCategory)
			topics = append(topics, topic)
		}
	}
	topics = append(topics, buildAllCategoriesTopic(len(utils.IsCategorySupported)-len(topics)))
	return &GetReminderTopicsResponse{
		Topics: topics,
	}, nil
}

func buildAllCategoriesTopic(remainingCategoryCount int) *reminderPb.ReminderTopic {
	return &reminderPb.ReminderTopic{
		Heading: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: fmt.Sprintf("%d other categories", remainingCategoryCount),
			},
			FontColor: "#333333",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
			},
		},
		PrimaryImg: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  "https://epifi-icons.pointz.in/quick-link-icons/reminder_all_categories.png",
			Width:     52,
			Height:    24,
		},
		Type: reminderPb.ReminderTopicType_CATEGORY,
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REMINDERS_SUPPORTED_TOPICS_LIST_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_RemindersSupportedTopicsListScreenOptions{
				RemindersSupportedTopicsListScreenOptions: &deeplinkPb.RemindersSupportedTopicsListScreenOptions{
					ReminderType: []meta.ReminderType{
						meta.ReminderType_CATEGORY_SPENDS,
					},
					SupportedTopicProvenance: meta.SupportedTopicProvenance_OVER_SPENDS,
				},
			},
		},
	}
}

func buildTopicForCategory(category categorizerPb.DisplayCategory) *reminderPb.ReminderTopic {
	imgURL := fmt.Sprintf(utils.CategoryImageTmpl, utils.DisplayCatToFileName[category])
	topic := &reminderPb.ReminderTopic{
		Type: reminderPb.ReminderTopicType_CATEGORY,
		Heading: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: utils.DisplayCategoryToDisplayName[strings.ToUpper(category.String())],
			},
			FontColor: "#333333",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
		},
		PrimaryImg: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  imgURL,
			Width:     28,
			Height:    28,
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REMINDERS_CONFIG_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_RemindersConfigScreenOptions{
				RemindersConfigScreenOptions: &deeplinkPb.RemindersConfigScreenOptions{
					ConfigType:   meta.ReminderConfigType_CONFIG_CREATE,
					ReminderType: meta.ReminderType_CATEGORY_SPENDS,
					ConfigFlow: &deeplinkPb.RemindersConfigScreenOptions_CreateConfig{
						CreateConfig: &deeplinkPb.CreateReminderConfig{
							Type: meta.ReminderType_CATEGORY_SPENDS,
							CreateReminderConfigParams: &deeplinkPb.CreateReminderConfig_CategoryConfig{
								CategoryConfig: &deeplinkPb.CategoryReminderConfig{
									Amount: &types.Money{
										CurrencyCode: "INR",
										Units:        1200,
									},
									Category: &deeplinkPb.CategoryDetail{
										DisplayCategory: category.String(),
									},
								},
							},
						},
					},
				},
			},
		},
	}
	return topic
}

func buildTopicForUserTransactCategory(categoryAgg *utils.CategoryDetail) *reminderPb.ReminderTopic {
	imgURL := fmt.Sprintf(utils.CategoryImageTmpl, utils.DisplayCatToFileName[categorizerPb.DisplayCategory(categorizerPb.DisplayCategory_value[categoryAgg.DisplayCategory])])
	topic := &reminderPb.ReminderTopic{
		Type: reminderPb.ReminderTopicType_CATEGORY,
		Heading: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: utils.DisplayCategoryToDisplayName[strings.ToUpper(categoryAgg.DisplayCategory)],
			},
			FontColor: "#333333",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
		},
		PrimaryImg: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  imgURL,
			Width:     20,
			Height:    20,
		},
		SubHeading: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: fmt.Sprintf("AVG ₹%d", categoryAgg.Amount),
			},
			FontColor: "#A4A4A4",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		},
		Topic: &reminderPb.ReminderTopic_TopicCategory{
			TopicCategory: &reminderPb.ReminderTopicCategory{
				DisplayCategory: categorizerPb.DisplayCategory(categorizerPb.DisplayCategory_value[categoryAgg.DisplayCategory]),
				Amount: &types.Money{
					Units: categoryAgg.Amount,
				},
			},
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REMINDERS_CONFIG_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_RemindersConfigScreenOptions{
				RemindersConfigScreenOptions: &deeplinkPb.RemindersConfigScreenOptions{
					ConfigType:   meta.ReminderConfigType_CONFIG_CREATE,
					ReminderType: meta.ReminderType_CATEGORY_SPENDS,
					ConfigFlow: &deeplinkPb.RemindersConfigScreenOptions_CreateConfig{
						CreateConfig: &deeplinkPb.CreateReminderConfig{
							Type: meta.ReminderType_CATEGORY_SPENDS,
							CreateReminderConfigParams: &deeplinkPb.CreateReminderConfig_CategoryConfig{
								CategoryConfig: &deeplinkPb.CategoryReminderConfig{
									Amount: &types.Money{
										CurrencyCode: "INR",
										Units:        categoryAgg.Amount,
									},
									Category: &deeplinkPb.CategoryDetail{
										DisplayCategory: categoryAgg.DisplayCategory,
									},
								},
							},
						},
					},
				},
			},
		},
	}
	return topic
}
