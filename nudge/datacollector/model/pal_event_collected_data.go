package model

import (
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	nudgePb "github.com/epifi/gamma/api/nudge"
	nudgeDataCollectorPb "github.com/epifi/gamma/api/nudge/datacollector"
	palEventsPb "github.com/epifi/gamma/api/preapprovedloan"
)

type PALCollectedData struct {
	*palEventsPb.PALEvent
}

var _ CollectedData = &PALCollectedData{}

func (p *PALCollectedData) GetId() string {
	return p.GetActorId()
}

func (p *PALCollectedData) GetEventDataType() nudgePb.NudgeEventDataType {
	return nudgePb.NudgeEventDataType_PAL_EVENT
}

func (p *PALCollectedData) GetEntryEventId() string {
	return p.GetId()
}

func (p *PALCollectedData) GetActionTime() (*timestampPb.Timestamp, error) {
	return p.GetActionTimestamp(), nil
}

func (p *PALCollectedData) GetProtoCollectedData() (*nudgeDataCollectorPb.CollectedData, error) {
	return &nudgeDataCollectorPb.CollectedData{
		Id:            p.GetId(),
		ActorId:       p.GetActorId(),
		EntryEventId:  p.GetEntryEventId(),
		ActionTime:    p.GetActionTimestamp(),
		EventDataType: p.GetEventDataType(),
		EventData: &nudgeDataCollectorPb.CollectedData_PalEvent{
			PalEvent: p.PALEvent,
		},
	}, nil
}
