package generator

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/nudge/helper"

	nudgePb "github.com/epifi/gamma/api/nudge"
	nudgeDataCollectorPb "github.com/epifi/gamma/api/nudge/datacollector"
	"github.com/epifi/gamma/nudge/ruleengine/fact/common"
	"github.com/epifi/gamma/nudge/ruleengine/fact/debit_card"
	factHelper "github.com/epifi/gamma/nudge/ruleengine/fact/helper"
)

type DebitCardUpdateFactGenerator struct {
	factHelperSvc factHelper.IHelperService
	userHelperSvc helper.IUserHelperService
}

func NewDebitCardUpdateFactGenerator(factHelperSvc factHelper.IHelperService, userHelperSvc helper.IUserHelperService) *DebitCardUpdateFactGenerator {
	return &DebitCardUpdateFactGenerator{
		factHelperSvc: factHelperSvc,
		userHelperSvc: userHelperSvc,
	}
}

func (d *DebitCardUpdateFactGenerator) GenerateFacts(ctx context.Context, collectedData *nudgeDataCollectorPb.CollectedData, nudge *nudgePb.Nudge) ([]common.IFact, error) {
	if nudge.GetExitEvent() != nudgePb.NudgeEventDataType_DEBIT_CARD_UPDATE_EVENT {
		return nil, fmt.Errorf("invalid exit event [%v] found instead of debit card update event", nudge.GetExitEvent())
	}

	entryEventIds, err := d.factHelperSvc.GetEntryEventIds(ctx, nudge, collectedData)
	if err != nil {
		return nil, fmt.Errorf("error while fetching entry event ids: %w", err)
	}

	facts := make([]common.IFact, 0)
	for _, entryEventId := range entryEventIds {
		facts = append(facts, &debit_card.DebitCardUpdateFact{
			CommonFact: &common.CommonFact{
				Ctx:               ctx,
				RefId:             collectedData.GetId(),
				ActorId:           collectedData.GetActorId(),
				ActionTime:        collectedData.GetActionTime(),
				ActionType:        collectedData.GetEventDataType().String(),
				Nudge:             nudge,
				EntryEventId:      entryEventId,
				UserHelperService: d.userHelperSvc,
			},
			DebitCardUpdateEvent: collectedData.GetDebitCardUpdateEvent(),
		})
	}
	return facts, nil
}
