package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"
	"time"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	celestialPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/celestial/workflow"
	activityPb "github.com/epifi/gamma/api/pay/activity/internationalfundtransfer"
	iftVg "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	"github.com/epifi/be-common/pkg/datetime"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	"github.com/epifi/be-common/pkg/money"
	testPkg "github.com/epifi/be-common/pkg/test"
)

var (
	sampleWfClientReqId = &workflow.ClientReqId{
		Id:     "sample-client-req-id",
		Client: workflow.Client_PAY,
	}
	sampleWfReqId    = "sample-wf-req-id"
	sampleCustomerId = "sample-customer-id"
	sampleActorId    = "sample-actor-id"
	sampleTxnDate    = timestamp.New(time.Date(2022, 12, 20, 0, 0, 0, 0, datetime.IST))
)

func TestProcessor_ReportTcsCharges(t *testing.T) {
	todaysDate := datetime.TimestampToDateInLoc(timestamp.Now(), datetime.IST)
	tests := []struct {
		name       string
		req        *activityPb.ReportTcsChargesRequest
		setupMocks func(md *mockedDependencies)
		assertErr  func(err error) bool
	}{
		{
			name: "successful activity execution",
			req: &activityPb.ReportTcsChargesRequest{
				RequestHeader:  &celestialPb.RequestHeader{ClientReqId: sampleWfClientReqId.GetId()},
				WfReqId:        sampleWfReqId,
				TxnAmountInInr: money.AmountINR(100).GetPb(),
				TxnAmountInUsd: &moneyPb.Money{
					CurrencyCode: money.USDCurrencyCode,
					Units:        80,
				},
				ActorId: sampleActorId,
			},
			setupMocks: func(md *mockedDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(),
					&bankCustPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{ActorId: sampleActorId},
					},
				).Return(&bankCustPb.GetBankCustomerResponse{
					Status:       rpc.StatusOk(),
					BankCustomer: &bankCustPb.BankCustomer{VendorCustomerId: sampleCustomerId},
				}, nil)
				md.mockIftVgClient.EXPECT().CalculateTcs(gomock.Any(), testPkg.NewProtoArgMatcher(&iftVg.CalculateTcsRequest{
					Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					CustomerId:  sampleCustomerId,
					AmountInInr: money.AmountINR(100).GetPb(),
					AmountInForeignCurrency: &moneyPb.Money{
						CurrencyCode: money.USDCurrencyCode,
						Units:        80,
					},
					ClientReqId: sampleWfClientReqId.GetId(),
					// TODO(Brijesh): Test can fail in the rare scenario when it is executed at midnight. Fix by mocking time.
					TransactionDate: todaysDate,
					RequestType:     iftVg.TCSRequestType_TCS_REQUEST_TYPE_CALCULATE_AND_REPORT,
				})).Return(&iftVg.CalculateTcsResponse{
					Status:              rpc.StatusOk(),
					TcsAmount:           money.AmountINR(20).GetPb(),
					TcsApplicableAmount: money.AmountINR(100).GetPb(),
				}, nil)
			},
			assertErr: func(err error) bool {
				if err == nil {
					return true
				}
				return false
			},
		},
		{
			name: "successful activity execution with already exists response",
			req: &activityPb.ReportTcsChargesRequest{
				RequestHeader:  &celestialPb.RequestHeader{ClientReqId: sampleWfClientReqId.GetId()},
				WfReqId:        sampleWfReqId,
				TxnAmountInInr: money.AmountINR(100).GetPb(),
				TxnAmountInUsd: &moneyPb.Money{
					CurrencyCode: money.USDCurrencyCode,
					Units:        80,
				},
				ActorId: sampleActorId,
			},
			setupMocks: func(md *mockedDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(),
					&bankCustPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{ActorId: sampleActorId},
					},
				).Return(&bankCustPb.GetBankCustomerResponse{
					Status:       rpc.StatusOk(),
					BankCustomer: &bankCustPb.BankCustomer{VendorCustomerId: sampleCustomerId},
				}, nil)
				md.mockIftVgClient.EXPECT().CalculateTcs(gomock.Any(), testPkg.NewProtoArgMatcher(&iftVg.CalculateTcsRequest{
					Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					CustomerId:  sampleCustomerId,
					AmountInInr: money.AmountINR(100).GetPb(),
					AmountInForeignCurrency: &moneyPb.Money{
						CurrencyCode: money.USDCurrencyCode,
						Units:        80,
					},
					ClientReqId:     sampleWfClientReqId.GetId(),
					TransactionDate: todaysDate,
					RequestType:     iftVg.TCSRequestType_TCS_REQUEST_TYPE_CALCULATE_AND_REPORT,
				})).Return(&iftVg.CalculateTcsResponse{
					Status: rpc.StatusAlreadyExists(),
				}, nil)
			},
			assertErr: func(err error) bool {
				if err == nil {
					return true
				}
				return false
			},
		},
		{
			name: "error when unable to get data for TCS",
			req: &activityPb.ReportTcsChargesRequest{
				RequestHeader:  &celestialPb.RequestHeader{ClientReqId: sampleWfClientReqId.GetId()},
				WfReqId:        sampleWfReqId,
				TxnAmountInInr: money.AmountINR(100).GetPb(),
				TxnAmountInUsd: &moneyPb.Money{
					CurrencyCode: money.USDCurrencyCode,
					Units:        80,
				},
				ActorId: sampleActorId,
			},
			setupMocks: func(md *mockedDependencies) {
				md.mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(),
					&bankCustPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{ActorId: sampleActorId},
					},
				).Return(&bankCustPb.GetBankCustomerResponse{Status: rpc.StatusInternal()}, nil)
			},
			assertErr: func(err error) bool {
				if err == nil {
					return false
				}
				return true
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md := newProcessorWithMocks(t)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)
			tt.setupMocks(md)
			if _, err := env.ExecuteActivity(payNs.ReportTcsCharges, tt.req); !tt.assertErr(err) {
				t.Errorf("ReportTcsCharges() error = %v", err)
			}
		})
	}
}
