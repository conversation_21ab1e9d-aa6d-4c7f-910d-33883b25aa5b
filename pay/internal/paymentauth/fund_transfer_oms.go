package paymentauth

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	orderPb "github.com/epifi/gamma/api/order"
)

type FundTransferOmsProcessor struct {
	orderClient       orderPb.OrderServiceClient
	paymentValidation *PaymentValidation
	*BaseVendorAuthorisation
}

func NewFundTransferOmsProcessor(orderClient orderPb.OrderServiceClient,
	paymentValidation *PaymentValidation,
	vendorAuthorisation *BaseVendorAuthorisation) *FundTransferOmsProcessor {
	return &FundTransferOmsProcessor{
		orderClient:             orderClient,
		paymentValidation:       paymentValidation,
		BaseVendorAuthorisation: vendorAuthorisation,
	}
}

func (p *FundTransferOmsProcessor) Validate(ctx context.Context, currentActorId string, order *orderPb.Order, device *commontypes.Device, clientReqId *workflowPb.ClientReqId, ownership commontypes.Ownership) error {
	return p.paymentValidation.validateOrder(ctx, currentActorId, order.GetId(), device)
}

func (p *FundTransferOmsProcessor) PostAuthSignal(ctx context.Context, clientReqId *workflowPb.ClientReqId, payload proto.Message,
	ownership commontypes.Ownership) error {
	initiateOrderRes, err := p.orderClient.InitiateOrderProcessing(ctx, &orderPb.InitiateOrderProcessingRequest{
		Identifier: &orderPb.InitiateOrderProcessingRequest_ClientReqId{
			ClientReqId: clientReqId.GetId(),
		},
	})
	switch {
	case err != nil:
		return fmt.Errorf("error in initiating order processing %v %w", err, rpc.StatusAsError(rpc.StatusInternal()))
	// if order is already processed then we do not need to do anything
	case !initiateOrderRes.GetStatus().IsSuccess() && !initiateOrderRes.GetStatus().IsAlreadyProcessed():
		return fmt.Errorf("error in initiating order processing %v %w",
			initiateOrderRes.GetStatus().String(), rpc.StatusAsError(rpc.StatusInternal()))
	default:
		return nil
	}
}
