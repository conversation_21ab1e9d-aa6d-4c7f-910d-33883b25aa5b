package federal

import (
	"context"
	"reflect"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator/content_generator"
	genConf "github.com/epifi/gamma/pay/config/server/genconf"
	iftErrors "github.com/epifi/gamma/pay/internationalfundtransfer/errors"
	iftFileGenUtils "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/utils"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
)

const (
	PRECISION = 2
)

type InwardFundTransferFileContent struct {
	conf      *genConf.Config
	iftClient iftPb.InternationalFundTransferClient
}

func NewInwardFundTransferFileContent(conf *genConf.Config, iftClient iftPb.InternationalFundTransferClient) *InwardFundTransferFileContent {
	return &InwardFundTransferFileContent{
		conf:      conf,
		iftClient: iftClient,
	}
}

func (s *InwardFundTransferFileContent) GetFileContent(ctx context.Context, fgReq *content_generator.FileContentGenerationRequest) (*content_generator.FileContentGenerationResponse, error) {
	var inwardFundTransferFileModels []*InwardFundTransferFileModel
	var totalRecords int
	var entityIdsToUpdate []string
	var err error
	inwardFundTransferFileModels, entityIdsToUpdate, totalRecords, err = s.fetchInwardFundTransferModelData(ctx, fgReq.ClientRequestId)
	if err != nil {
		logger.Error(ctx, "error generating inward fund transfer file models", zap.Error(err))
		return nil, err
	}
	return &content_generator.FileContentGenerationResponse{
		TotalRecords:  int32(totalRecords),
		FileContent:   s.createFileContent(ctx, inwardFundTransferFileModels),
		UsedEntityIds: entityIdsToUpdate,
	}, nil
}

func (s *InwardFundTransferFileContent) fetchInwardFundTransferModelData(ctx context.Context, clientRequestId string) ([]*InwardFundTransferFileModel, []string, int, error) {
	var (
		inwardFundTransferFileModels []*InwardFundTransferFileModel
		entityIdsToUpdate            []string
		skippedWfIds                 []string
	)
	resp, err := s.iftClient.GetInwardFundTransferData(ctx, &iftPb.GetInwardFundTransferRequest{ClientRequestId: clientRequestId})
	if respErr := epifigrpc.RPCError(resp, err); respErr != nil {
		return nil, nil, 0, errors.Wrap(respErr, "error while fetching inward fund transfer data from international fund transfer service")
	}
	i := 0
	for wfId, mapValue := range resp.WfIdDataMap {
		inwardFundTransferFileModel, err := s.getInwardFundTransferModelFields(ctx, i, mapValue)
		if err != nil {
			logger.Error(ctx, "error filling refund transfer model fields", zap.Error(err))
			skippedWfIds = append(skippedWfIds, wfId)
			continue
		}
		inwardFundTransferFileModels = append(inwardFundTransferFileModels, inwardFundTransferFileModel)
		entityIdsToUpdate = append(entityIdsToUpdate, wfId)
		i++
	}
	if len(skippedWfIds) > 0 {
		logger.Error(ctx, "skipped workflow id in inward transfer file", zap.Error(iftErrors.OrdersSkippedFromInwardFundTransferFileErr), zap.Strings(logger.WORKFLOW_REQ_IDS, skippedWfIds))
	}
	return inwardFundTransferFileModels, entityIdsToUpdate, len(inwardFundTransferFileModels), nil
}

func (s *InwardFundTransferFileContent) getInwardFundTransferModelFields(ctx context.Context, srNo int, inwardFundTransferRespModel *iftPb.InwardFundTransferDataModel) (*InwardFundTransferFileModel, error) {
	logger.Debug(ctx, "received inward transfer model from iftClient", zap.Any("inward transfer response model", inwardFundTransferRespModel))
	inwardFundTransferModel := NewInwardFundTransferFileModel()
	inwardFundTransferModel.SrNo = strconv.Itoa(srNo)
	inwardFundTransferModel.AccountNumber = inwardFundTransferRespModel.GetActorAccountNumber()
	inwardFundTransferModel.SolId = inwardFundTransferRespModel.GetSolId()
	inwardFundTransferModel.CreditOrDebit = inwardFundTransferRespModel.GetCreditOrDebit()
	amtString, err := money.ToString(inwardFundTransferRespModel.GetAmount(), PRECISION)
	if err != nil {
		return nil, err
	}
	inwardFundTransferModel.Amount = amtString
	inwardFundTransferModel.Remarks = inwardFundTransferRespModel.GetRemarks()
	return inwardFundTransferModel, nil
}

func (s *InwardFundTransferFileContent) createFileContent(ctx context.Context, inwardFundTransferFileModels []*InwardFundTransferFileModel) string {
	var sb strings.Builder
	for _, swiftTransferFileTxn := range inwardFundTransferFileModels {
		value := reflect.ValueOf(swiftTransferFileTxn).Elem()
		for i := 0; i < value.NumField(); i++ {
			sb.WriteString(value.Field(i).String())
			if i < value.NumField()-1 {
				sb.WriteString(iftFileGenUtils.CommaDelimiter)
			}
		}
		sb.WriteString(iftFileGenUtils.UnixNewLine)
	}
	logger.Debug(ctx, "inward fund transfer file content", zap.String("file data", sb.String()))
	return sb.String()
}

func (s *InwardFundTransferFileContent) UpdateEntityStatus(ctx context.Context, entityIds []string, filePath string) error {
	// hit the service api's from which the data for the corresponding file is fetched and
	// update the status of all the entity ids fetched in above GenerateFileContent call to another state
	// so that those entity ids aren't picked again while generating another file
	logger.Debug(ctx, "updating entity ids status", zap.Strings(logger.ENTITY_ID, entityIds))
	resp, err := s.iftClient.UpdateWorkflows(ctx, &iftPb.UpdateWorkflowsRequest{
		WorkflowIds: entityIds,
		Stage:       iftPb.UpdateWorkflowStage_UPDATE_WORKFLOW_STAGE_INWARD_FUND_TRANSFER_FILE_GENERATED,
	})
	if respErr := epifigrpc.RPCError(resp, err); respErr != nil {
		return errors.Wrap(respErr, "error while updating inward fund transfer entities for international fund transfer service")
	}
	return nil
}
