package model

import (
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/order/dao/model"
)

type Order struct {
	// Using embedding to make sure changes in the order model in the order path is in
	// alignment with model.
	model.Order
}

func NewOrder(protoOrder *orderPb.Order) *Order {
	modelOrder := model.NewOrder(protoOrder)
	return &Order{Order: *modelOrder}
}

type OrderWithTxn struct {
	model.OrderWithTxn
}

func GetOrdersWithTxnsProtoListFromOWTModel(orderWithTxns []*OrderWithTxn) []*orderPb.OrderWithTransactions {
	var res []*orderPb.OrderWithTransactions

	for _, owt := range orderWithTxns {
		protoOrder, protoTxn := owt.GetProto()
		protoOwt := &orderPb.OrderWithTransactions{
			Order:        protoOrder,
			Transactions: []*paymentPb.Transaction{protoTxn},
		}
		res = append(res, protoOwt)
	}

	return res
}

func GetOrdersWithTxnsProtoListFromOrderModel(orders []*Order) []*orderPb.OrderWithTransactions {
	var orderWithTxns []*orderPb.OrderWithTransactions
	for _, order := range orders {
		orderWithTxns = append(orderWithTxns, order.GetOrderWithTxnsProto())
	}
	return orderWithTxns
}
