package ckyc

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	ckycPb "github.com/epifi/gringott/api/stockguardian/ckyc"
)

func (s *Service) GetS3PreSignedURL(ctx context.Context, req *ckycPb.GetS3PreSignedURLRequest) (*ckycPb.GetS3PreSignedURLResponse, error) {
	logger.Debug(ctx, fmt.Sprintf("GetS3PreSignedURL request map : %+v", req.GetFileNameS3URLMap()))
	if len(req.GetFileNameS3URLMap()) == 0 {
		logger.Error(ctx, "empty file name to s3 url map")
		return &ckycPb.GetS3PreSignedURLResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("empty file name to s3 url map"),
		}, nil
	}

	inMap := req.GetFileNameS3URLMap()

	for k, v := range inMap {
		url, err := s.s3Client.GetPreSignedUrl(ctx, v, 15*time.Minute)
		if err != nil {
			logger.Error(ctx, "error while getting pre signed url", zap.Error(err))
			return &ckycPb.GetS3PreSignedURLResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}

		inMap[k] = url
	}
	logger.Debug(ctx, fmt.Sprintf("GetS3PreSignedURL ouput request map : %+v", inMap))
	return &ckycPb.GetS3PreSignedURLResponse{
		Status:                    rpc.StatusOk(),
		FileNameS3PreSignedURLMap: inMap,
	}, nil
}
