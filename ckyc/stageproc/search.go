package stageproc

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gringott/api/stockguardian/ckyc"
	vgCkycPb "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/ckyc"
	"github.com/epifi/gringott/ckyc/config"
	"github.com/epifi/gringott/ckyc/dao"
	"github.com/epifi/gringott/ckyc/helper"

	"go.uber.org/zap"
)

type SearchStage struct {
	vgCkycClient vgCkycPb.CkycClient
	ckycDao      dao.CkycSummaryDao
	conf         *config.Config
}

func NewSearchStage(vgCkycClient vgCkycPb.CkycClient, ckycDao dao.CkycSummaryDao, conf *config.Config) *SearchStage {
	return &SearchStage{
		vgCkycClient: vgCkycClient,
		ckycDao:      ckycDao,
		conf:         conf,
	}
}

var _ StageProcessor = (*SearchStage)(nil)

func (s *SearchStage) Process(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	searchResp, errSearch := s.vgCkycClient.SearchCKYC(ctx, &vgCkycPb.SearchCKYCRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: vendorgateway.Vendor_CKYC,
		},
		Pan:   req.CkycSumm.GetSensitiveClientRequestOptions().GetPan(),
		ReqId: helper.GenerateCkycReqId(),
	})
	if rpcErr := epifigrpc.RPCError(searchResp, errSearch); rpcErr != nil {
		if rpc.StatusFromError(rpcErr).IsRecordNotFound() {
			logger.Info(ctx, "ckyc search not found for user")
			return &StageProcessorResponse{
				StageStatus: ckyc.Status_STATUS_FAILED,
			}, nil
		}
		logger.Error(ctx, "failure in ckyc search rpc", zap.Error(rpcErr))
		return nil, rpcErr
	}

	if req.CkycSumm.GetSearchMetadata() == nil {
		req.CkycSumm.SearchMetadata = &ckyc.SearchMetadata{}
	}
	req.CkycSumm.SearchMetadata.CkycNo = searchResp.GetData().GetCkycNumber()
	req.CkycSumm.SearchMetadata.CkycReferenceId = searchResp.GetData().GetCkycReferenceId()
	req.CkycSumm.SearchMetadata.UserImage = searchResp.GetData().GetPhoto()
	if errUpdate := s.ckycDao.Update(ctx, req.CkycSumm, []ckyc.CkycSummaryFieldMask{ckyc.CkycSummaryFieldMask_CKYC_SUMMARY_FIELD_MASK_SEARCH_METADATA}); errUpdate != nil {
		logger.Error(ctx, "failure in updating ckyc summary", zap.Error(errUpdate))
		return nil, errUpdate
	}

	return &StageProcessorResponse{
		StageStatus: ckyc.Status_STATUS_SUCCESS,
	}, nil
}
