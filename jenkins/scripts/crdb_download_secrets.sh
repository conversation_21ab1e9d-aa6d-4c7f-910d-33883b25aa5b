#!/bin/bash

ENV=$1
USER=$2

echo "Cleaning secrets destination"
rm -rf temp_secrets
mkdir temp_secrets
cd temp_secrets

echo "Generating file ca.key"
aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/ca.key | jq -r '.SecretString | fromjson | ."ca.key"' | tr ' ' '\n' | sed -z 's/BEGIN\nRSA\nPRIVATE\nKEY/BEGIN RSA PRIVATE KEY/g' | sed -z 's/END\nRSA\nPRIVATE\nKEY/END RSA PRIVATE KEY/g' > ca.key

echo "Generating file ca.crt"
aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/ca.crt | jq -r '.SecretString' | tr ' ' '\n' | sed -z 's/BEGIN\nCERTIFICATE/BEGIN CERTIFICATE/g' | sed -z 's/END\nCERTIFICATE/END CERTIFICATE/g' > ca.crt

echo "Generating file client.$USER.crt from ${ENV}/cockroach/client.root"
aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/client.root | jq -r '.SecretString | fromjson | ."cert"' | base64 --decode > client.$USER.crt

echo "Generating file client.$USER.key from ${ENV}/cockroach/client.root"
aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/client.root | jq -r '.SecretString | fromjson | ."key"' | base64 --decode > client.$USER.key
sudo chmod 600 *

echo "Secrets stored at $(pwd)"
