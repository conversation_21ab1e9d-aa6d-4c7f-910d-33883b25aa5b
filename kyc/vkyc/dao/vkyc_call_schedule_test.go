package dao

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"testing"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

type VKYCCallScheduleDaoTestSuite struct {
	db        *gorm.DB
	vkycCSDao VKYCCallScheduleDao
}

var (
	vkycCSDao            VKYCCallScheduleDaoTestSuite
	vkycCallScheduleFix1 = &vkycPb.VKYCCallSchedule{
		Id:      "f0973e81-e7d7-4bef-9a8f-0a18926cd9ff",
		RefId:   "afabe43b-df01-4025-88eb-81edb921e8e3",
		AgentId: "agent-id-1",
		Vendor:  commonvgpb.Vendor_KARZA,
	}
	vkycCallScheduleFix2 = []*vkycPb.VKYCCallSchedule{
		{
			Id:      "a5004f18-5d52-4991-82a9-2a1e3010e990",
			RefId:   "238d8ca5-da6f-44dd-848a-fd7430c4a5d6",
			AgentId: "agent-id-1",
		}, {
			Id:      "f0973e81-e7d7-4bef-9a8f-0a18926cd9ff",
			RefId:   "afabe43b-df01-4025-88eb-81edb921e8e3",
			AgentId: "agent-id-1",
		},
	}
)

func TestVKYCCallSchedule_GetById(t *testing.T) {
	type fields struct {
		DB *gorm.DB
	}
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *vkycPb.VKYCCallSchedule
		wantErr bool
	}{
		{
			name: "id not passed",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx: context.Background(),
				id:  "",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "invalid id passed",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx: context.Background(),
				id:  "12345",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "get fixture successfully",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx: context.Background(),
				id:  "f0973e81-e7d7-4bef-9a8f-0a18926cd9ff",
			},
			want:    vkycCallScheduleFix1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			pkgTest.PrepareScopedDatabase(t, vksts.conf.EpifiDb().GetName(), vksts.db, AffectedTestTables)
			V := &VKYCCallScheduleCRDB{
				DB: tt.fields.DB,
			}
			got, err := V.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !areVkycCallScheduleProtoEqual(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func areVkycCallScheduleProtoEqual(p1 *vkycPb.VKYCCallSchedule, p2 *vkycPb.VKYCCallSchedule) bool {
	if p1.GetId() == p2.GetId() && p1.GetAgentId() == p2.GetAgentId() && p1.GetVendor() == p2.GetVendor() &&
		p1.GetStatus() == p2.GetStatus() && p1.GetSubStatus() == p2.GetSubStatus() && p1.GetRefId() == p2.GetRefId() {
		return true
	}
	return false
}

func TestVKYCCallSchedule_Create(t *testing.T) {
	type fields struct {
		DB *gorm.DB
	}
	type args struct {
		ctx              context.Context
		vkycCallSchedule *vkycPb.VKYCCallSchedule
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *vkycPb.VKYCCallSchedule
		wantErr bool
	}{
		{
			name: "create vkyc call schedule request success",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx: context.Background(),
				vkycCallSchedule: &vkycPb.VKYCCallSchedule{
					RefId:     "bfc05f22-2722-4057-bd10-aa0351f591cd",
					StartTime: timestamppb.Now(),
					EndTime:   timestamppb.Now(),
					AgentId:   "test-agent-create-id-1",
					Vendor:    commonvgpb.Vendor_KARZA,
					CreatedAt: nil,
					UpdatedAt: nil,
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "create vkyc call schedule request failure due to mandatory params missing",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx:              context.Background(),
				vkycCallSchedule: &vkycPb.VKYCCallSchedule{},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			pkgTest.PrepareScopedDatabase(t, vksts.conf.EpifiDb().GetName(), vksts.db, AffectedTestTables)
			V := &VKYCCallScheduleCRDB{
				DB: tt.fields.DB,
			}
			_, err := V.Create(tt.args.ctx, tt.args.vkycCallSchedule)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVKYCCallSchedule_UpdateById(t *testing.T) {
	type fields struct {
		DB *gorm.DB
	}
	type args struct {
		ctx                        context.Context
		vkycCallSchedule           *vkycPb.VKYCCallSchedule
		vkycCallScheduleFieldMasks []vkycPb.VKYCCallScheduleFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "empty primary key",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx: context.Background(),
				vkycCallSchedule: &vkycPb.VKYCCallSchedule{
					RefId:   "238d8ca5-da6f-44dd-848a-fd7430c4a5d6",
					AgentId: "test-agent-2",
				},
			},
			wantErr: true,
		},
		{
			name: "empty update mask",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx: context.Background(),
				vkycCallSchedule: &vkycPb.VKYCCallSchedule{
					Id:      "a5004f18-5d52-4991-82a9-2a1e3010e990",
					RefId:   "238d8ca5-da6f-44dd-848a-fd7430c4a5d6",
					AgentId: "test-agent-2",
				},
			},
			wantErr: true,
		},
		{
			name: "update agent id successfully",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx: context.Background(),
				vkycCallSchedule: &vkycPb.VKYCCallSchedule{
					Id:      "a5004f18-5d52-4991-82a9-2a1e3010e990",
					RefId:   "238d8ca5-da6f-44dd-848a-fd7430c4a5d6",
					AgentId: "test-agent-2",
				},
				vkycCallScheduleFieldMasks: []vkycPb.VKYCCallScheduleFieldMask{vkycPb.VKYCCallScheduleFieldMask_VKYC_CALL_SCHEDULE_FIELD_MASK_AGENT_ID},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			pkgTest.PrepareScopedDatabase(t, vksts.conf.EpifiDb().GetName(), vksts.db, AffectedTestTables)
			V := &VKYCCallScheduleCRDB{
				DB: tt.fields.DB,
			}
			err := V.UpdateById(tt.args.ctx, tt.args.vkycCallSchedule, tt.args.vkycCallScheduleFieldMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestVKYCCallScheduleCRDB_GetByRefId(t *testing.T) {
	type fields struct {
		DB *gorm.DB
	}
	type args struct {
		ctx   context.Context
		refId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *vkycPb.VKYCCallSchedule
		wantErr bool
	}{
		{
			name: "id not passed",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx:   context.Background(),
				refId: "",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "invalid id passed",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx:   context.Background(),
				refId: "12345",
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "get fixture successfully",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx:   context.Background(),
				refId: vkycKarzaCallInfoId,
			},
			want:    vkycCallScheduleFix1,
			wantErr: false,
		},
	}
	pkgTest.PrepareScopedDatabase(t, vksts.conf.EpifiDb().GetName(), vksts.db, AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			V := &VKYCCallScheduleCRDB{
				DB: tt.fields.DB,
			}
			got, err := V.GetByRefId(tt.args.ctx, tt.args.refId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByRefId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !areVkycCallScheduleProtoEqual(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVKYCCallScheduleCRDB_GetCallSchedulesByAgentId(t *testing.T) {
	type fields struct {
		DB *gorm.DB
	}
	type args struct {
		ctx     context.Context
		agentId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*vkycPb.VKYCCallSchedule
		wantErr bool
	}{
		{
			name: "schedules fetched successfully",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx:     context.Background(),
				agentId: "agent-id-1",
			},
			want:    vkycCallScheduleFix2,
			wantErr: false,
		},
		{
			name: "no records found",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx:     context.Background(),
				agentId: "agent-id-2",
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "agentId not passed",
			fields: fields{
				DB: vkycCSDao.db,
			},
			args: args{
				ctx:     context.Background(),
				agentId: "",
			},
			want:    nil,
			wantErr: true,
		},
	}
	pkgTest.PrepareScopedDatabase(t, vksts.conf.EpifiDb().GetName(), vksts.db, AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			V := &VKYCCallScheduleCRDB{
				DB: tt.fields.DB,
			}
			got, err := V.GetCallSchedulesByAgentId(tt.args.ctx, tt.args.agentId)
			if tt.name == "no records found" {
				fmt.Println("itshere", got, err)
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAgentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !isProtosEqual(got, tt.want) {
				t.Errorf("GetCallsByVkycAttemptId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isProtosEqual(got []*vkycPb.VKYCCallSchedule, want []*vkycPb.VKYCCallSchedule) bool {
	for i := 0; i < len(want); i++ {
		if got[i].GetId() != want[i].GetId() || got[i].GetRefId() != want[i].GetRefId() || got[i].GetAgentId() != want[i].GetAgentId() {
			return false
		}
	}
	return true
}
