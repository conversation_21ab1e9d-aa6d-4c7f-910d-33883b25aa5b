package stateprocessor

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/kyc/config/genconf"
	vkycDao "github.com/epifi/gamma/kyc/vkyc/dao"
	"github.com/epifi/gamma/kyc/vkyc/metrics"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
)

type PanEntry struct {
	conf           *genconf.Config
	bankCustClient bankcust.BankCustomerServiceClient
	idGen          idgen.IdGenerator
	vkycSummaryDao vkycDao.VKYCSummaryDao
	vendor         commonvgpb.Vendor
	abReleaseEval  *release.ABEvaluator[string]
	panClient      pan.PanClient
}

func NewPanEntryState(conf *genconf.Config, bcClient bankcust.BankCustomerServiceClient, idGen idgen.IdGenerator,
	vkycSummaryDao vkycDao.VKYCSummaryDao, vendor commonvgpb.Vendor, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient,
	panClient pan.PanClient) *PanEntry {
	return &PanEntry{
		bankCustClient: bcClient, conf: conf,
		idGen: idGen, vendor: vendor,
		vkycSummaryDao: vkycSummaryDao, panClient: panClient,
		abReleaseEval: GetABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, conf.VKYC().ABFeatureReleaseConfig(), func(str string) string { return str }),
	}
}

func (s *PanEntry) StateProc(ctx context.Context, req *StateProcRequest) (*StateProcResponse, error) {
	if req.GetReq().GetClientLastState() == vkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_ENTRY {
		return nil, SkipStateError
	}
	metrics.RecordVKYCStateActivity(ctx, vkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_ENTRY, metrics.StateStarted, req.GetReq().GetEntryPoint())
	vkycSummary, err := getOrCreateSummary(ctx, req.GetReq().GetActorId(), s.vkycSummaryDao, s.vendor)
	if err != nil {
		return nil, err
	}
	isShowPanCapture, err := showPanImageCaptureScreen(ctx, req.GetReq().GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, vkycSummary, s.conf, s.abReleaseEval, s.bankCustClient, s.panClient)
	if err != nil {
		return nil, err
	}
	if !isShowPanCapture {
		logger.Info(ctx, "pan entry state is not enabled")
		return nil, SkipStateError
	}
	clientReqId, errReq := s.createAndUpdateClientReqId(ctx, vkycSummary)
	if errReq != nil {
		return nil, errReq
	}
	capturePanImageDeeplink := getPANInstructionsScreen(ctx, clientReqId, req.GetReq().GetEntryPoint(), s.conf.VKYC().InstructionPageSkipOptionTime())
	return &StateProcResponse{
		NextAction: capturePanImageDeeplink,
	}, nil
}

func (s *PanEntry) createAndUpdateClientReqId(ctx context.Context, vkycSummary *vkycPb.VKYCSummary) (string, error) {
	clientReqId := s.idGen.GetInAlphaNumeric(idgen.VKYCPanCapture)
	if vkycSummary.GetMetadata() == nil {
		vkycSummary.Metadata = &vkycPb.SummaryMetadata{}
	}
	vkycSummary.GetMetadata().ScannedPanReqId = clientReqId
	err := s.vkycSummaryDao.UpdateById(ctx, vkycSummary, []vkycPb.VKYCSummaryFieldMask{vkycPb.VKYCSummaryFieldMask_VKYC_SUMMARY_FIELD_MASK_METADATA})
	if err != nil {
		logger.Error(ctx, "error in updating VKYC summary", zap.Error(err))
		return "", err
	}
	return clientReqId, nil
}
