package events

// event name constants
const (
	EventStartedKYCServer     = "StartedKYCServer"
	EventCompletedKYCServer   = "CompletedKYCServer"
	EventStartedCKYCServer    = "StartedCKYCServer"
	EventCKYCSearch           = "CKYCSearch"
	EventCKYCDownload         = "CKYCDownload"
	EventUNNameCheck          = "UNNameCheck"
	EventCompletedCKYCServer  = "CompletedCKYCServer"
	EventStartedEKYCServer    = "StartedEKYCServer"
	EventEKYCDataUploadServer = "EKYCDataUploadServer"
	EventCompletedEKYCServer  = "CompletedEKYCServer"
	EventNameMatchCheck       = "NameMatchCheck"
	EventMobileNoMatch        = "MobileNoMatch"
	EventIDProofCheck         = "IDProofCheck"
	ServiceName               = "kyc"
)

const (
	Success                                = "success"
	Failure                                = "failure"
	InvalidKYCSearchTransitionStateError   = "ckyc search invalid transition, failing permanently"
	InvalidKYCDownloadTransitionStateError = "invalid transition ckyc download"
	CKYCSearchNotFoundError                = "ckyc search not found, failing permanently"
	CKYCSearchLSOError                     = "user has LSO ckyc number via search, failing permanently"
	FailVgUnnamecheck                      = "error in calling vg unnamecheck"
	InvalidKYCType                         = "invalid kyc type"
	SourceEKYC                             = "ekyc"
	SourceCKYC                             = "ckyc"
	NameMatchError                         = "name match failed"
	MobileMatchError                       = "mobile match failed"
)
