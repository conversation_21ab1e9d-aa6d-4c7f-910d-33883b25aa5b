// nolint:depguard,funlen
package service

import (
	"reflect"
	"testing"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"google.golang.org/protobuf/types/known/timestamppb"

	searchPb "github.com/epifi/gamma/api/search"
	dao_query "github.com/epifi/gamma/search/dao/query"
)

func TestGetFormattedTime(t *testing.T) {
	tests := []struct {
		name     string
		ts       *timestamp.Timestamp
		expected string
		wantErr  bool
	}{
		{
			name:     "nil timestamp",
			ts:       nil,
			expected: "",
			wantErr:  false,
		},
		{
			name:     "valid timestamp",
			ts:       &timestamp.Timestamp{Seconds: 1617196800},
			expected: "2021-03-31 13:20:00",
			wantErr:  false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got, err := getFormattedTime(tc.ts)
			if (err != nil) != tc.wantErr {
				t.Fatalf("getFormattedTime() error = %v, wantErr %v", err, tc.wantErr)
			}
			if got != tc.expected {
				t.<PERSON><PERSON><PERSON>("getFormattedTime() = %v, want %v", got, tc.expected)
			}
		})
	}
}

func Test_buildQueryParamsFitAggr(t *testing.T) {
	testCases := []struct {
		name          string
		input         *searchPb.GetTxnAggregateFitRequest
		expectedResp  *dao_query.FitAggrQueryParams
		expectedError bool
	}{
		{
			name: "Success case",
			input: &searchPb.GetTxnAggregateFitRequest{
				ActorId: "actor-id",
				SubscriptionIds: []string{
					"s1", "s2", "s3",
				},
				RuleId:        "rule-id",
				ExecutionTags: []string{"e1"},
				FromTime:      timestamppb.New(time.Date(1997, 9, 17, 20, 4, 26, 0, time.FixedZone("EST", -18000))),
				ToTime:        timestamppb.New(time.Date(1999, 9, 17, 20, 4, 26, 0, time.FixedZone("EST", -18000))),
			},
			expectedResp: &dao_query.FitAggrQueryParams{
				ActorId: "actor-id",
				SubscriptionId: []string{
					"s1", "s2", "s3",
				},
				RuleId:        "rule-id",
				ExecutionTags: []string{"e1"},
				FromTime:      "1997-09-18 01:04:26",
				ToTime:        "1999-09-18 01:04:26",
			},
			expectedError: false,
		},
		{
			name: "Success case - nil timestamp",
			input: &searchPb.GetTxnAggregateFitRequest{
				ActorId: "actor-id",
				SubscriptionIds: []string{
					"s1", "s2", "s3",
				},
				RuleId:        "rule-id",
				ExecutionTags: []string{"e1"},
				FromTime:      nil,
				ToTime:        timestamppb.New(time.Date(1999, 9, 17, 15, 4, 5, 0, time.FixedZone("EST", -18000))),
			},
			expectedResp: &dao_query.FitAggrQueryParams{
				ActorId: "actor-id",
				SubscriptionId: []string{
					"s1", "s2", "s3",
				},
				RuleId:        "rule-id",
				ExecutionTags: []string{"e1"},
				FromTime:      "",
				ToTime:        "1999-09-17 20:04:05",
			},
			expectedError: false,
		},
	}
	for _, tt := range testCases {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			gotResp, gotErr := buildQueryParamsFitAggr(tt.input)
			if !reflect.DeepEqual(tt.expectedResp, gotResp) {
				t.Errorf("Expected %v, Got %v", tt.expectedResp, gotResp)
			}
			if (tt.expectedError && gotErr == nil) || (!tt.expectedError && gotErr != nil) {
				t.Errorf("expected and got error are not same")
			}
		})
	}
}
