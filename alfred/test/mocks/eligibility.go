// Code generated by MockGen. DO NOT EDIT.
// Source: alfred/procfactory/eligibility.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	procfactory "github.com/epifi/gamma/alfred/procfactory"
	gomock "github.com/golang/mock/gomock"
)

// MockRequestEligibilityChecker is a mock of RequestEligibilityChecker interface.
type MockRequestEligibilityChecker struct {
	ctrl     *gomock.Controller
	recorder *MockRequestEligibilityCheckerMockRecorder
}

// MockRequestEligibilityCheckerMockRecorder is the mock recorder for MockRequestEligibilityChecker.
type MockRequestEligibilityCheckerMockRecorder struct {
	mock *MockRequestEligibilityChecker
}

// NewMockRequestEligibilityChecker creates a new mock instance.
func NewMockRequestEligibilityChecker(ctrl *gomock.Controller) *MockRequestEligibilityChecker {
	mock := &MockRequestEligibilityChecker{ctrl: ctrl}
	mock.recorder = &MockRequestEligibilityCheckerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRequestEligibilityChecker) EXPECT() *MockRequestEligibilityCheckerMockRecorder {
	return m.recorder
}

// IsActorEligible mocks base method.
func (m *MockRequestEligibilityChecker) IsActorEligible(ctx context.Context, request *procfactory.IsActorEligibleRequest) (*procfactory.IsActorEligibleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsActorEligible", ctx, request)
	ret0, _ := ret[0].(*procfactory.IsActorEligibleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsActorEligible indicates an expected call of IsActorEligible.
func (mr *MockRequestEligibilityCheckerMockRecorder) IsActorEligible(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsActorEligible", reflect.TypeOf((*MockRequestEligibilityChecker)(nil).IsActorEligible), ctx, request)
}

// MockEligibilityChecker is a mock of EligibilityChecker interface.
type MockEligibilityChecker struct {
	ctrl     *gomock.Controller
	recorder *MockEligibilityCheckerMockRecorder
}

// MockEligibilityCheckerMockRecorder is the mock recorder for MockEligibilityChecker.
type MockEligibilityCheckerMockRecorder struct {
	mock *MockEligibilityChecker
}

// NewMockEligibilityChecker creates a new mock instance.
func NewMockEligibilityChecker(ctrl *gomock.Controller) *MockEligibilityChecker {
	mock := &MockEligibilityChecker{ctrl: ctrl}
	mock.recorder = &MockEligibilityCheckerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEligibilityChecker) EXPECT() *MockEligibilityCheckerMockRecorder {
	return m.recorder
}

// IsEligible mocks base method.
func (m *MockEligibilityChecker) IsEligible(ctx context.Context, request *procfactory.IsEligibleRequest) (*procfactory.IsEligibleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsEligible", ctx, request)
	ret0, _ := ret[0].(*procfactory.IsEligibleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsEligible indicates an expected call of IsEligible.
func (mr *MockEligibilityCheckerMockRecorder) IsEligible(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsEligible", reflect.TypeOf((*MockEligibilityChecker)(nil).IsEligible), ctx, request)
}
