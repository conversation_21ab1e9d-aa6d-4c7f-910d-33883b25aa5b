package model

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"database/sql"
	"time"

	"github.com/epifi/gamma/api/aml"
	"github.com/epifi/be-common/pkg/nulltypes"

	"google.golang.org/protobuf/types/known/timestamppb"
)

type AmlScreeningAttempt struct {
	Id                       string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	ActorId                  string
	ClientRequestId          string
	Product                  aml.AmlProduct
	Vendor                   commonvgpb.Vendor
	CustomerDetails          *aml.CustomerDetails
	Status                   aml.AmlScreeningStatus
	Result                   aml.AmlMatch
	RejectionMessage         nulltypes.NullString
	RejectionCode            aml.RejectionCode
	LastScreeningAttemptedAt sql.NullTime
	CreatedAt                time.Time
	UpdatedAt                time.Time
}

func (AmlScreeningAttempt) TableName() string {
	return "aml_screening_attempts"
}

func NewScreeningAttemptModel(attempt *aml.ScreeningAttempt) *AmlScreeningAttempt {
	ret := &AmlScreeningAttempt{
		Id:               attempt.GetId(),
		ActorId:          attempt.GetActorId(),
		ClientRequestId:  attempt.GetClientRequestId(),
		Product:          attempt.GetProduct(),
		Vendor:           attempt.GetVendor(),
		CustomerDetails:  attempt.GetCustomerDetails(),
		Status:           attempt.GetStatus(),
		Result:           attempt.GetResult(),
		RejectionMessage: nulltypes.NewNullString(attempt.GetRejectionMessage()),
		RejectionCode:    attempt.GetRejectionCode(),
	}
	if attempt.GetLastScreeningAttemptedAt().IsValid() {
		ret.LastScreeningAttemptedAt = sql.NullTime{
			Time:  attempt.GetLastScreeningAttemptedAt().AsTime(),
			Valid: true,
		}
	}
	return ret
}

func (s *AmlScreeningAttempt) GetProto() *aml.ScreeningAttempt {
	return &aml.ScreeningAttempt{
		Id:                       s.Id,
		ActorId:                  s.ActorId,
		ClientRequestId:          s.ClientRequestId,
		Product:                  s.Product,
		Vendor:                   s.Vendor,
		CustomerDetails:          s.CustomerDetails,
		Status:                   s.Status,
		Result:                   s.Result,
		RejectionMessage:         s.RejectionMessage.GetValue(),
		RejectionCode:            s.RejectionCode,
		LastScreeningAttemptedAt: timestamppb.New(s.LastScreeningAttemptedAt.Time),
		CreatedAt:                timestamppb.New(s.CreatedAt),
		UpdatedAt:                timestamppb.New(s.UpdatedAt),
	}
}
