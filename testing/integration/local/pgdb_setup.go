package local

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"

	"github.com/epifi/gamma/testing/integration/config"

	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTestv2 "github.com/epifi/be-common/pkg/test/v2"
)

// preparePGDBTestDb creates DB, populates fixtures if required and returns connection with teardown function
func preparePGDBTestDb(conf *config.Config, dbConf *cfg.DB) *gormv2.DB {
	if !isDbConnRequired(conf.Flags, dbConf) {
		return nil
	}
	if usePreloadedDataFromImage(conf.UsePreloadedData) {
		return restorePGDBFromImage(dbConf)
	}
	if usePreloadedData(conf.UsePreloadedData) {
		return restorePGDBFromS3(dbConf)
	}
	if conf.Flags.SkipOneTimeSetup {
		return truncatePGDBandPopulateWithFixtures(dbConf)
	}
	return createPGDBWithFixtures(dbConf)
}

func truncatePGDBandPopulateWithFixtures(dbConf *cfg.DB) *gormv2.DB {
	dbConn, err := storagev2.NewGormDB(dbConf)
	if err != nil {
		logger.ErrorNoCtx("failed to connect to default postgres db", zap.Error(err))
	}
	pkgTestv2.TruncateAndPopulateRdsFixtures(pkgTestv2.NewZapLogger(logger.Log), dbConn, dbConf.Name, nil)
	return dbConn
}

func createPGDBWithFixtures(dbConf *cfg.DB) *gormv2.DB {
	dbConn, err := storagev2.NewDefaultPostgresConn(dbConf)
	if err != nil {
		logger.ErrorNoCtx("failed to connect to default postgres db", zap.Error(err))
	}
	err = dbConn.Exec("SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE pg_stat_activity.datname =" + "'" + dbConf.GetName() + "'  AND pid <> pg_backend_pid()").Error
	if err != nil {
		logger.ErrorNoCtx("Error while closing db conn:", zap.Error(err))
	}
	sqlDB, _ := dbConn.DB()
	err = sqlDB.Close()
	if err != nil {
		logger.ErrorNoCtx("failed to close db conn", zap.Error(err))
	}

	dbConn, err = pkgTestv2.PrepareRdsTestDB(dbConf)
	if err != nil {
		logger.Panic(fmt.Sprintf("failed to connect to %v db", dbConf.Name), zap.Error(err))
	}
	pkgTestv2.PopulateRdsFixtures(pkgTestv2.NewZapLogger(logger.Log), dbConn, dbConf.GetName())
	return dbConn
}

func restorePGDBFromS3(dbConf *cfg.DB) *gormv2.DB {
	sqlFilePath := filepath.Join(getPgdbBackupPath(), fmt.Sprintf("%v.sql", dbConf.Name))
	if _, err := os.Stat(sqlFilePath); os.IsNotExist(err) {
		logger.InfoNoCtx(fmt.Sprintf("no backup exists for DB %v", dbConf.Name))
		// default handling if backup does not exist
		dbConn, dbErr := pkgTestv2.PrepareRdsTestDB(dbConf)
		if dbErr != nil {
			logger.Panic(fmt.Sprintf("failed to connect to %v db", dbConf.Name), zap.Error(dbErr))
		}
		pkgTestv2.PopulateRdsFixtures(pkgTestv2.NewZapLogger(logger.Log), dbConn, dbConf.GetName())
		return dbConn
	}
	return applyPGDBBackup(dbConf, sqlFilePath)
}

func applyPGDBBackup(dbConf *cfg.DB, sqlFilePath string) *gormv2.DB {
	dbConn, dbErr := pkgTestv2.PrepareRdsTestDB(dbConf)
	if dbErr != nil {
		logger.Panic(fmt.Sprintf("failed to connect to %v db", dbConf.Name), zap.Error(dbErr))
	}
	//nolint:gosec
	if _, err := exec.Command("bash", "-c", fmt.Sprintf("psql -U %v -h %v -p %v -d %v < %v", dbConf.Username, dbConf.Host, dbConf.Port, dbConf.Name, sqlFilePath)).Output(); err != nil {
		errExit := err.(*exec.ExitError) //nolint:erro
		logger.Panic(fmt.Sprintf("error in restoring PGDB backup for table %v", dbConf.Name), zap.String(logger.ERROR_REASON, string(errExit.Stderr)))
	}
	logger.InfoNoCtx("PGDB tables restored from S3")
	return dbConn
}

func restorePGDBFromImage(dbConf *cfg.DB) *gormv2.DB {
	dbConn, err := storagev2.NewDefaultPostgresConn(dbConf)
	if err != nil {
		logger.Panic(fmt.Sprintf("failed to connect to %v db", dbConf.Name), zap.Error(err))
	}
	// DB already exists with preloaded data
	if pkgTestv2.CheckPgDbExists(dbConn, dbConf.GetName()) {
		logger.InfoNoCtx(fmt.Sprintf("DB already exists %v", dbConf.GetName()))
		dbConn, err = storagev2.NewGormDB(dbConf)
		if err != nil {
			logger.Panic(fmt.Sprintf("failed to connect to %v db", dbConf.Name), zap.Error(err))
		}
		// todo(saiteja) assert if DBs are populated with data dump
		return dbConn
	}
	return createPGDBWithFixtures(dbConf)
}

func getPgdbBackupPath() string {
	dbBackup := getDbBackupDirectoryPath()
	return filepath.Join(dbBackup, "pgdb-backup")
}
