package main

// nolint: goimports
import (
	"context"
	"flag"
	"fmt"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/scripts/risk_send_lea_comms/config"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	"go.uber.org/zap"

	operstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	commspb "github.com/epifi/gamma/api/comms"
	enumsPb "github.com/epifi/gamma/api/risk/enums"
	leaPb "github.com/epifi/gamma/api/risk/lea"
	savingspb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/risk/accountstatus"
	"github.com/epifi/gamma/risk/dao"
	"github.com/epifi/gamma/risk/essential"
	leaComm "github.com/epifi/gamma/risk/lea/unified_lea/comms"
)

var (
	actorIdsString = flag.String("actorIdsString", "", "comma seperated list of actor ids")
)

func main() {
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	ctx, cancelFn := context.WithTimeout(context.Background(), 1000*time.Minute)
	defer cancelFn()

	fRMPGDB, err := storage2.NewGormDB(conf.FRMPgdb)
	if err != nil {
		logger.Panic("failed to establish database conn", zap.Error(err), zap.Any("db_config", "FRMPGDB"))
	}

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actor.NewActorClient(actorConn)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)
	savingsClient := savingspb.NewSavingsClient(savingsConn)

	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	commsClient := commspb.NewCommsClient(commsConn)

	accountsConn := epifigrpc.NewConnByService(cfg.ACCOUNTS_SERVICE)
	defer epifigrpc.CloseConn(accountsConn)
	operationalStatusServiceClient := operstatuspb.NewOperationalStatusServiceClient(accountsConn)

	userriskConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.USER_RISK_SERVER)
	defer epifigrpc.CloseConn(userriskConn)
	leaClient := leaPb.NewLeaClient(userriskConn)

	unifiedLeaComplaintPGDB := dao.NewUnifiedLeaComplaintPGDB(fRMPGDB)
	operationalStatusFetcher := accountstatus.NewOperationalStatusFetcher(operationalStatusServiceClient, savingsClient)
	unifiedLeaComplaintConfig := conf.UnifiedLeaComplaintConfig
	actorManagerImpl := essential.NewActorManagerImpl(actorClient)
	refineImpl := leaComm.NewRefineImpl(unifiedLeaComplaintPGDB, operationalStatusFetcher, unifiedLeaComplaintConfig, actorManagerImpl, savingsClient)
	collateCommsImpl := leaComm.NewCollateComms(actorClient, unifiedLeaComplaintConfig)

	actorIds := strings.Split(*actorIdsString, ",")
	logger.Info(ctx, "ActorIds", zap.Any("ActorIds", actorIds))

	successCount, failureCount, alreadyInUnfreeze := 0, 0, 0
	failedActorsList := []string{}
	for indx, actorId := range actorIds {

		if indx%10 == 0 {
			logger.Info(ctx, "Starting processing for indx", zap.Int("indx", indx))
			time.Sleep(time.Second)
		}

		complaint, err := getLayer3ComplaintByActorId(ctx, actorId, leaClient)
		if err != nil {
			failureCount++
			failedActorsList = append(failedActorsList, actorId)
			logger.Error(ctx, "error in getting layer 3 complaint", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			continue
		}
		if complaint == nil {
			alreadyInUnfreeze++
			continue
		}

		handlingParam, err := refineImpl.ValidateAndGetHandler(ctx, complaint)
		if err != nil {
			failureCount++
			failedActorsList = append(failedActorsList, actorId)
			logger.Error(ctx, "error in ValidateAndGetHandler call", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			continue
		}

		comm, err := collateCommsImpl.GetUnifiedComms(ctx, handlingParam, false)
		if err != nil {
			failureCount++
			failedActorsList = append(failedActorsList, actorId)
			logger.Error(ctx, "error in GetUnifiedComms call", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			continue
		}

		sendMessageBatchRes, err := commsClient.SendMessageBatch(ctx, &commspb.SendMessageBatchRequest{
			Type:              commspb.QoS_GUARANTEED,
			CommunicationList: comm,
			UserIdentifier:    &commspb.SendMessageBatchRequest_UserId{UserId: handlingParam.GetEntityId()},
		})
		if rpcErr := epifigrpc.RPCError(sendMessageBatchRes, err); rpcErr != nil {
			failureCount++
			failedActorsList = append(failedActorsList, actorId)
			logger.Error(ctx, "error in SendMessageBatch call", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			continue
		}
		successCount++

	}

	logger.Info(ctx, "List of failed actors", zap.Any("failedActorsList", failedActorsList))

	logger.Info(ctx, "Completed processing all actors", zap.Int("TotalCount", len(actorIds)), zap.Int("successCount", successCount),
		zap.Int("alreadyInUnfreezeCount", alreadyInUnfreeze), zap.Int("failureCount", failureCount))
}

func getLayer3ComplaintByActorId(ctx context.Context, actorId string, leaClient leaPb.LeaClient) (*leaPb.UnifiedLeaComplaint, error) {
	getUnifiedLeaComplaintRes, err := leaClient.GetUnifiedLeaComplaint(ctx, &leaPb.GetUnifiedLeaComplaintRequest{
		Identifier: &leaPb.GetUnifiedLeaComplaintRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(getUnifiedLeaComplaintRes, err); rpcErr != nil {
		return nil, fmt.Errorf("error in getting LEA complaint for actorId: %v, err: %w", actorId, rpcErr)
	}

	var latestLayer3Complaint *leaPb.UnifiedLeaComplaint
	for _, unifiedLeaComplaint := range getUnifiedLeaComplaintRes.GetUnifiedLeaComplaints() {
		// if the latest entry if UNFROZEN, then we don't need to send comm to this user
		if unifiedLeaComplaint.GetOperationalStatus() == enumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_UNFROZEN {
			logger.Info(ctx, "Found UNFREEZE entry in Unified LEA complaints", zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, nil
		}
		if unifiedLeaComplaint.GetLayerNumber().GetValid() && unifiedLeaComplaint.GetLayerNumber().GetValue() == 3 {
			latestLayer3Complaint = unifiedLeaComplaint
			break
		}
	}

	if latestLayer3Complaint == nil {
		return nil, epifierrors.ErrRecordNotFound
	}
	return latestLayer3Complaint, nil
}
