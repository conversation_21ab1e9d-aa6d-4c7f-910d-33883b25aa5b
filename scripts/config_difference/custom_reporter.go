package main

import (
	"fmt"
	"strings"

	"github.com/google/go-cmp/cmp"
)

// DiffReporter is a simple custom reporter that only records differences
// detected during comparison.
type DiffReporter struct {
	Env1   string
	Env2   string
	path   cmp.Path
	diffs  []string
	equals []string
}

func (r *DiffReporter) PushStep(ps cmp.PathStep) {
	r.path = append(r.path, ps)
}

func (r *DiffReporter) Report(rs cmp.Result) {
	if !rs.Equal() {
		vx, vy := r.path.Last().Values()
		var x, y any
		if vx.IsValid() {
			x = vx
		} else {
			x = "value does not exist"
		}
		if vy.IsValid() {
			y = vy
		} else {
			y = "value does not exist"
		}
		r.diffs = append(r.diffs, fmt.Sprintf("%#v:\n\t%v: %+v\n\t%v: %+v\n", r.path, r.Env1, x, r.Env2, y))

	}
}

func (r *DiffReporter) PopStep() {
	r.path = r.path[:len(r.path)-1]
}

func (r *DiffReporter) String() string {
	return strings.Join(r.diffs, "\n")
}
