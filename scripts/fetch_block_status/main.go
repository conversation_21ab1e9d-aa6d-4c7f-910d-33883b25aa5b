package main

import (
	"context"
	"flag"
	"fmt"
	"strings"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	beSavingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	actorIds = flag.String("actorIds", "", "List of actor Id's to be seperated by commas ,")

	// clients
	savingsClient        beSavingsPb.SavingsClient
	actorClient          actorPb.ActorClient
	accountBalanceClient accountBalancePb.BalanceClient
)

// nolint:funlen
func main() {
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient = actorPb.NewActorClient(actorConn)

	savingsClient = beSavingsPb.NewSavingsClient(savingsConn)
	ctx := context.Background()

	actorIdsSplit := strings.Split(*actorIds, ",")
	for i, value := range actorIdsSplit {
		actorIdsSplit[i] = strings.TrimSpace(value)
	}
	if len(actorIdsSplit) == 0 {
		logger.Fatal("Actor Id's length is 0")
	}

	if len(actorIdsSplit) > 2001 {
		logger.Fatal("Actor Id's length exceeds 2000")
	}

	var failedActorIds []string
	var accountDoesNotExistActorIds []string
	var statusString []string

	for _, actorId := range actorIdsSplit {
		actorResp, actorErr := actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
		if actorErr = epifigrpc.RPCError(actorResp, actorErr); actorErr != nil {
			logger.Error(ctx, "error fetching actor by id")
			failedActorIds = append(failedActorIds, actorId)
			continue
		}
		// Get savings account by user id
		savingsAccResp, savingsAccErr := savingsClient.GetAccount(ctx, &beSavingsPb.GetAccountRequest{
			Identifier: &beSavingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: actorResp.GetActor().GetEntityId()}})
		if savingsAccErr != nil || savingsAccResp == nil || savingsAccResp.GetAccount() == nil {
			logger.Error(ctx, "error fetching savings account by user id")
			failedActorIds = append(failedActorIds, actorId)
			continue
		}
		// Get Balance by savings account id
		accBalResp, accBalErr := accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
			Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{Id: savingsAccResp.GetAccount().GetId()},
			ActorId:    actorId,
		})
		if accBalResp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "account deactivated from bank end")
			accountDoesNotExistActorIds = append(accountDoesNotExistActorIds, actorId)
			continue
		}
		if accBalErr = epifigrpc.RPCError(accBalResp, accBalErr); accBalErr != nil {
			logger.Error(ctx, "error fetching account balance by savings id and actor id")
			failedActorIds = append(failedActorIds, actorId)
			continue
		}
		statusString = append(statusString, actorId+"   freeze raw code: "+accBalResp.GetFreezeRawCode()+"   reason: "+accBalResp.GetFreezeReason())
	}

	fmt.Println("actor status output")
	fmt.Println("--------------------")
	fmt.Println()
	for _, i2 := range statusString {
		fmt.Println(i2)
	}
	fmt.Println()
	fmt.Println("--------------------")
	logger.InfoNoCtx(fmt.Sprintf("Process failed for actor id: %v ", failedActorIds))
	fmt.Println("--------------------")
	logger.InfoNoCtx(fmt.Sprintf("Account not present for: %v ", accountDoesNotExistActorIds))
}
