package job

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/dao"
	dlProvider "github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/scripts/pal/helper"
	"github.com/epifi/gamma/scripts/pal/job/vendors"
)

type CancelLeadJob struct {
	llPalVgClient     llVgPb.LiquiloansClient
	loanApplicantDao  dao.LoanApplicantDao
	s3Client          s3.S3Client
	helper            *helper.Helper
	loanRequests      dao.LoanRequestsDao
	loanStepExecution dao.LoanStepExecutionsDao
	dl                *dlProvider.Provider
	palClient         palPb.PreApprovedLoanClient
}

func NewCancelLeadJob(
	llPalVgClient llVgPb.LiquiloansClient,
	loanApplicantDao dao.LoanApplicantDao,
	s3Client s3.S3Client,
	helper *helper.Helper,
	loanRequests dao.LoanRequestsDao,
	loanStepExecution dao.LoanStepExecutionsDao,
	dl *dlProvider.Provider,
	palClient palPb.PreApprovedLoanClient,
) *CancelLeadJob {
	return &CancelLeadJob{
		llPalVgClient:     llPalVgClient,
		loanApplicantDao:  loanApplicantDao,
		s3Client:          s3Client,
		helper:            helper,
		loanRequests:      loanRequests,
		loanStepExecution: loanStepExecution,
		dl:                dl,
		palClient:         palClient,
	}
}

type CancelLeadJobArgs struct {
	FilePath string `json:"filePath"`
}

func (c *CancelLeadJob) GetArgs() interface{} {
	return &CancelLeadJobArgs{}
}

// JobName : "CANCEL_LEAD"
// jobArguments : `{"filePath":"cancel_lead/cancel_lead.csv"}`

// CSV file should have ApplicantId, ApplicationId and LoanProgram from second line without any space, seperated by commas

// nolint:funlen
func (c *CancelLeadJob) Run(ctx context.Context, args ...interface{}) error {
	// todo(divas) need to revert these changes
	ctx = epificontext.WithOwnership(ctx, vendors.GetPalOwnership(palPb.Vendor_FEDERAL))

	offerRes, err := c.palClient.GetOfferDetails(ctx, &palPb.GetOfferDetailsRequest{
		ActorId: "AC220813L57yIpZrQKOUCPhW4o7tEA==",
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      palPb.Vendor_FEDERAL,
		},
	})
	if te := epifigrpc.RPCError(offerRes, err); te != nil {
		return errors.Wrap(te, "error in GetInstantLoanOffer from VG")
	}
	fmt.Println(offerRes.GetOfferInfo())
	// actorIds := []string{"ACzor46l/rRau9NMnCR091Pw231121==",
	//	"ACQ7zIedMQSdSGknehUD4uXg231123==",
	//	"ACtXeIeuQFT92kz+XuTmjIxQ231122==",
	//	"ACwX7mQkpfTCqjLlfwIHGCjw231123==",
	//	"ACd+HhQv/MQP2qTTakbINpDw231122==",
	//	"ACdwnjM/uGSkOQbSf9HGOMvg231123==",
	//	"ACei+GTg+QR+6vzNg6wdSaFA231124==",
	//	"ACbrd3ZRI0TE297mfQeIppQw231118==",
	//	"ACijAnbtkcTPeSZaNzv8i9oA231119==",
	//	"AC+FeLHHhXRvKmW2doRDC+UQ231122==",
	//	"AC13XpaR59QzinR/Lp7y+FbQ231124==",
	//	"ACSKfgH/ftTNito6v1BJ2ADg231121==",
	//	"ACd3cwAs9bSyaIdk9TLfdsCA231122==",
	//	"ACJJe8HTqRSs+DtJm3zWd5UA231124==",
	//	"ACtfnb+5jMRN2Mew+/DTfzdQ231119==",
	//	"ACywmMSvZrSqeBQnD4LgVjbw231122==",
	//	"ACtV46Gm2LTg+lqxDdqL8ajw231117==",
	//	"ACymgwM6ZlQ+6JnzBMwAlpMw231122==",
	//	"AC+yX2elE3TjCHT3z/xDdkBw231123==",
	//	"AC2o2P1MM2RzKfwBeKKUwRgw231121==",
	//	"AC0ux86I1uQhGOTx3m6kznow231122=="}
	//
	// for _, actorId := range actorIds {
	//	lr, lrErr := c.loanRequests.GetByActorIdTypesStatusAndLoanProgram(ctx, actorId, []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION}, []palPb.LoanRequestStatus{palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING}, []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND}, nil)
	//	if lrErr != nil {
	//		logger.Error(ctx, fmt.Sprintf("failed to get pending lr for actor: %v", actorId))
	//		continue
	//	}
	//	lse, lseErr := c.loanStepExecution.GetByRefIdAndFlowAndName(ctx, lr[0].GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS)
	//	if lseErr != nil {
	//		logger.Error(ctx, fmt.Sprintf("failed to get banking lse for lr: %v", lr[0].GetId()))
	//		continue
	//	}
	//	if lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED && lr[0].GetNextAction().GetScreen() != deeplink.Screen_PL_BANKING_DETAILS_SCREEN {
	//		dl, dlErr := c.dl.GetBankingDetailsScreenDeeplink(&pal_enums.LoanHeader{
	//			LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND,
	//			Vendor:      palFeEnumsPb.Vendor_LIQUILOANS,
	//		}, lr[0].GetId())
	//		if dlErr != nil {
	//			logger.Error(ctx, fmt.Sprintf("failed to set next action for lr: %v", lr[0].GetId()))
	//			continue
	//		}
	//
	//		lr[0].NextAction = dl
	//		updateErr := c.loanRequests.Update(ctx, lr[0], []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION})
	//		if updateErr != nil {
	//			logger.Error(ctx, fmt.Sprintf("failed to update lr next action for: %v", lr[0].GetId()))
	//			continue
	//		}
	//		fmt.Println("successfully updated lr next action for: ", actorId)
	//	} else {
	//		fmt.Println("skipped updating lr next action for: ", actorId)
	//	}
	//
	// }
	// return nil

	// csvRows, err := c.helper.ReadCsvFromS3CancelLead(ctx, args[0].(*CancelLeadJobArgs).FilePath)
	// if err != nil {
	//	return errors.Wrap(err, "error in readCsvFromS3")
	// }
	// c.cancelLead(ctx, csvRows)
	return nil
}

func (c *CancelLeadJob) cancelLead(ctx context.Context, csvRows []*helper.CancelLeadCsvRow) {
	for _, row := range csvRows {
		lp := helper.GetLoanProgramFromString(row.LoanProgram)
		cancelLeadRes, err := c.llPalVgClient.CancelLead(ctx, &llVgPb.CancelLeadRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
			ApplicationId: row.ApplicationId,
			ApplicantId:   row.ApplicantId,
			LoanProgram:   c.helper.ConvertToVGLoanProgram(lp),
			SchemeVersion: llVgPb.SchemeVersion_SCHEME_VERSION_V1,
		})
		if te := epifigrpc.RPCError(cancelLeadRes, err); te != nil {
			logger.Error(ctx, "failed in cancel lead vg call", zap.Error(te))
			continue
		}
	}
}
