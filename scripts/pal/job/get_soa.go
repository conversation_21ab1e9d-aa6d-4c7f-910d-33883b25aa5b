package job

import (
	"context"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/be-common/pkg/logger"

	vgLiquiloans "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	"github.com/epifi/gamma/pkg/loans"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/scripts/pal/helper"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type GetSoaStatementJob struct {
	loanRequestDao dao.LoanRequestsDao
	helper         *helper.Helper
	llVgClient     vgLiquiloans.LiquiloansClient
}

type GetSoaStatementJobArgs struct {
	LoanId string `json:"loanId"`
}

func NewGetSoaStatementJob(loanRequestDao dao.LoanRequestsDao, helper *helper.Helper, llVgClient vgLiquiloans.LiquiloansClient) *GetSoaStatementJob {
	return &GetSoaStatementJob{
		loanRequestDao: loanRequestDao,
		helper:         helper,
		llVgClient:     llVgClient,
	}
}

func (p *GetSoaStatementJob) GetArgs() interface{} {
	return &GetSoaStatementJobArgs{}
}

func (p *GetSoaStatementJob) Run(ctx context.Context, args ...interface{}) error {
	if len(args) == 0 {
		return errors.New("args not passed")
	}
	jobArgs, ok := args[0].(*GetSoaStatementJobArgs)
	if !ok {
		return errors.New("invalid args passed")
	}

	loanId := jobArgs.LoanId
	if loanId == "" {
		return errors.New("loanId is a required argument")
	}

	logger.Info(ctx, "fetching loan request details", zap.String("loanId", loanId))
	loanRequest, err := p.loanRequestDao.GetByVendorReqId(ctx, loanId)
	if err != nil {
		return errors.Wrap(err, "failed to get loan request")
	}

	logger.Info(ctx, "fetching soa from ll vendor", zap.String("LoanId", loanRequest.GetVendorRequestId()))

	req := &vgLiquiloans.GetApplicationSoaRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
		ApplicationId: loanRequest.GetVendorRequestId(),
		LoanProgram:   p.helper.ConvertToVGLoanProgram(loanRequest.GetLoanProgram()),
		SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[loanRequest.GetDetails().GetProgramVersion()],
	}

	resp, err := p.llVgClient.GetApplicationSoa(ctx, req)
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error fetching SOA from vendor", zap.Error(te))
		return errors.Wrap(te, "failed to fetch SOA from vendor")
	}

	if resp.GetData() == nil || resp.GetData().GetLink() == "" {
		logger.Error(ctx, "invalid SOA response from vendorgateway", zap.Error(err), zap.Any("response", resp))
		return errors.New("received empty link from vendor for SOA")
	}

	logger.Info(ctx, "successfully retrieved SOA link", zap.String("loanId", loanId), zap.String("link", resp.GetData().GetLink()))

	return nil
}
