package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"

	"github.com/epifi/gamma/comms/config"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/gamma/api/vendorgateway/sms"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

const toPhoneNumber = ""
const vendor = commonvgpb.Vendor_KALEYRA

type SMSResult struct {
	Template       string
	Vendor         commonvgpb.Vendor
	Account        sms.SmsVendorAccountType
	MessageId      string
	Status         sms.SMSStatus
	DetailedStatus string
}

var result []*SMSResult

// script to verify if a particular template is whitelisted on vendor's end
// script goes through list of sms templates from comms config and sens sms to phone number mentioned in `toPhoneNumber` string constant
// account selection is done on basis of account mentioned in config
// vendor can be switched / modified through `vendor` constant
// once the script execution is completed, a detailed template level report is generated containing template name, vendor, account, msg id, status
// to identify which template got delivered or failed
// for current testing of KALEYRA we are using GetSMSStatus API to check delivery of SMS
// TOOO: figure out way for read from ACL callbacks

func main() {

	// Get environment
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %v", err))
	}

	// Load configuration
	conf, err := config.Load()
	if err != nil {
		panic(err)
	}

	// Setup logger
	logger.Init(envName)
	defer func() { _ = logger.Log.Sync() }()

	vgServiceConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgServiceConn)
	smsClient := sms.NewSMSClient(vgServiceConn)
	smsTemplates := conf.SmsTemplates

	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))

	logger.Info(ctx, fmt.Sprintf("total number of templates %d", len(smsTemplates)))

	for templateName, versions := range smsTemplates {
		for _, template := range versions {
			msg := template.Template
			msg = strings.Replace(msg, "{#", "", -1)
			msg = strings.Replace(msg, "#}", "", -1)
			val, ok := sms.SmsVendorAccountType_value[strings.ToUpper(template.Account)]
			var accountType sms.SmsVendorAccountType
			if ok {
				accountType = sms.SmsVendorAccountType(val)
			}
			if strings.EqualFold(strings.ToLower(templateName), "onboarding_otp") {
				newMsg := strings.ReplaceAll(msg, "android_client_signature", "vEESbjXDMpQ")
				newMsg = strings.ReplaceAll(newMsg, "otp", "111000")
				sendSms(newMsg, vendor, smsClient, accountType, templateName)
			} else {
				sendSms(msg, vendor, smsClient, accountType, templateName)
			}
		}
	}

	time.Sleep(2 * time.Minute)

	for _, resultItem := range result {

		resp, err := smsClient.GetSMSStatus(context.Background(), &sms.GetSMSStatusRequest{
			Header:               &commonvgpb.RequestHeader{Vendor: resultItem.Vendor},
			MessageId:            resultItem.MessageId,
			SmsVendorAccountType: resultItem.Account,
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			logger.Panic("error in get sms", zap.Error(te))
		}
		resultItem.Status = resp.GetSmsStatus()
		resultItem.DetailedStatus = resp.GetDetailedStatus()
		time.Sleep(5 * time.Second)
	}

	for _, resultItem := range result {
		logger.Info(ctx, fmt.Sprintf("template: %s, vendor: %s, account: %s, messageId: %s, status: %s, detailed status: %s",
			resultItem.Template, resultItem.Vendor.String(), resultItem.Account.String(), resultItem.MessageId, resultItem.Status.String(), resultItem.DetailedStatus))
	}

	logger.Info(ctx, fmt.Sprintf("total templates verified %d", len(result)))
}

func sendSms(msg string, vendor commonvgpb.Vendor, client sms.SMSClient, accType sms.SmsVendorAccountType, tempHead string) {
	resp, err := client.SendSMS(context.Background(), &sms.SendSMSRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendor,
		},
		PhoneNumber:          toPhoneNumber,
		Message:              msg,
		SmsVendorAccountType: accType,
	})

	templateResult := &SMSResult{
		Template: strings.ToUpper(tempHead),
		Vendor:   vendor,
		Account:  accType,
	}

	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Panic("error in sens sms", zap.Error(te))
	}

	templateResult.MessageId = resp.GetMessageId()
	templateResult.Status = resp.GetSmsStatus()
	result = append(result, templateResult)

	time.Sleep(2 * time.Second)
}
