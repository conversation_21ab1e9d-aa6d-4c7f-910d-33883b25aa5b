package main

import (
	"context"
	"flag"
	"fmt"
	"path/filepath"
	"sort"
	"strconv"
	"time"

	"github.com/google/go-cmp/cmp"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"gorm.io/gorm"

	"github.com/pkg/errors"

	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/scripts/validate_vendor_request_crdb_s3/config"
	"github.com/epifi/gamma/wealthonboarding/dao/model"
)

var (
	dbName            string
	tableName         string
	startTimeStr      string
	endTimeStr        string
	crdbSelectResults vendorReqSlice
	s3ReadResults     vendorReqSlice
)

type vendorReqSlice []*model.VendorRequest

func (v vendorReqSlice) Len() int {
	return len(v)
}

func (v vendorReqSlice) Less(i, j int) bool {
	return v[i].Id < v[j].Id
}

func (v vendorReqSlice) Swap(i, j int) {
	v[i], v[j] = v[j], v[i]
}

func main() {
	flag.StringVar(&dbName, "db", "", "Name of the database to filter table configuration")
	flag.StringVar(&tableName, "table", "", "Name of the table to validate data")
	flag.StringVar(&startTimeStr, "startTime", "", "date from which data need to be validated in 2022-12-05T05:00:00Z format. use time rounded to hour")
	flag.StringVar(&endTimeStr, "endTime", "", "date till which data need to be validated in 2022-12-05T05:00:00Z format. use time rounded to hour")
	flag.Parse()

	// Load configuration
	conf, err := config.Load()
	if err != nil {
		panic("failed to load config. error: " + err.Error())
	}
	// Setup logger
	logger.Init(conf.Application.Environment)
	startTime, err := time.Parse(time.RFC3339, startTimeStr)
	if err != nil {
		logger.Panic("failed to parse start time", zap.Error(err))
	}
	endTime, err := time.Parse(time.RFC3339, endTimeStr)
	if err != nil {
		logger.Panic("failed to parse end time", zap.Error(err))
	}

	defer func() { _ = logger.Log.Sync() }()

	table, err := findAndValidateDBConf(conf, dbName, tableName)
	if err != nil {
		logger.Panic("failed to find and validate db config", zap.Error(err))
	}
	ctx, cancelFn := context.WithTimeout(context.Background(), table.MaxRunDuration)
	defer cancelFn()

	sess, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		logger.Panic("failed to create new aws session", zap.Error(err))
	}
	// Set up s3 client
	s3Client := s3.NewClient(sess, conf.S3Conf.Bucket)

	err = validateData(ctx, table, s3Client, conf, startTime, endTime)
	if err != nil {
		logger.Panic("error while validating data", zap.Error(err))
	}
}

func validateData(ctx context.Context, table *config.Table, s3Client *s3.Client, conf *config.Config, startTime, endTime time.Time) error {
	dbConf := getDBConf(table.DBName, conf)
	// Connect to CRDB
	db, err := storagev2.NewCRDBWithConfig(dbConf, false)
	if err != nil {
		return fmt.Errorf("failed to create gorm DB connection. %w", err)
	}
	logger.InfoNoCtx(fmt.Sprintf("Successfully connected to DB: %s on %s:%d",
		dbConf.GetName(), dbConf.Host, dbConf.Port),
		zap.String("service", conf.Application.Name))
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get sql DB from gorm DB. %w", err)
	}
	defer func() { _ = sqlDB.Close() }()

	db = db.WithContext(ctx)

	err = getDataFromDB(db, table, startTime, endTime)
	if err != nil {
		return err
	}
	err = getDataFromS3(s3Client, conf, startTime, endTime)
	if err != nil {
		return err
	}
	err = compareResults(crdbSelectResults, s3ReadResults)
	if err != nil {
		return err
	}
	return nil
}

func getDataFromDB(db *gorm.DB, table *config.Table, startTime, endTime time.Time) error {
	var (
		isEmpty   bool
		moveAfter string
		err       error
	)
	moveAfter = "00000000-0000-0000-0000-000000000000" // lexicographically small uuid
	for i := uint32(0); i < table.MaxBatchIter; i++ {
		isEmpty, moveAfter, err = getRowsWithSelector(db, table, startTime, endTime, moveAfter)
		if err != nil {
			return fmt.Errorf("failed to get row with selector for iteration %d, %w", i, err)
		}
		if isEmpty {
			logger.InfoNoCtx("No more rows to select in the table")
			break
		}
		logger.InfoNoCtx(fmt.Sprintf("Successfully got rows in iteration %d, next iteration will get data after %s is %s", i, table.OffsetColumnName, moveAfter))
		time.Sleep(table.BatchDelay)
	}
	return nil
}

func getRowsWithSelector(db *gorm.DB, table *config.Table, startTime, endTime time.Time, moveAfter string) (isEmpty bool, nextOffsetValue string, err error) {
	selectQuery := fmt.Sprintf("select * from %s where %s in (select %s from %s where %s >= '%s' and %s < '%s') and %s > '%s' order by %s limit %d", table.TableName, table.OffsetColumnName, table.OffsetColumnName, table.TableName, table.DateTimeColumnName, startTime.Format(time.RFC3339), table.DateTimeColumnName, endTime.Format(time.RFC3339), table.OffsetColumnName, moveAfter, table.OffsetColumnName, table.BatchSize)
	var queryResults vendorReqSlice
	err = db.Raw(selectQuery).Scan(&queryResults).Error
	if err != nil {
		logger.ErrorNoCtx(fmt.Sprintf("failed to read the rows starting from offset value %s = %s", table.OffsetColumnName, moveAfter), zap.Error(err))
		return false, nextOffsetValue, err
	}
	if len(queryResults) == 0 {
		return true, nextOffsetValue, nil
	}
	nextOffsetValue = queryResults[len(queryResults)-1].Id
	crdbSelectResults = append(crdbSelectResults, queryResults...)
	queryResults = nil
	return false, nextOffsetValue, nil
}

func getDataFromS3(s3Client *s3.Client, conf *config.Config, startTime, endTime time.Time) error {
	var allFilePaths []string
	for t := startTime; t.Before(endTime); t = t.Add(time.Hour) {
		s3Path := filepath.Join(conf.Application.Environment, "data", "vendor", "vendor_request", strconv.Itoa(t.Year()), intToStringWith2Digits(int(t.Month())), intToStringWith2Digits(t.Day()), intToStringWith2Digits(t.Hour()))
		logger.InfoNoCtx("Listing file paths from s3 in current directory: ", zap.String("Directory Path", s3Path))
		filePaths, err := s3Client.LS(s3Path)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("failed to list files from s3 in the path %s", s3Path))
		}
		logger.InfoNoCtx("number of s3 files in current path is", zap.Int("count", len(filePaths)))
		allFilePaths = append(allFilePaths, filePaths...)
	}

	for _, filePath := range allFilePaths {
		logger.InfoNoCtx("Reading files from file path", zap.String("File Path", filePath))
		vendorReqBytes, err := s3Client.Read(filePath)
		logger.InfoNoCtx("Successfully read file from s3")
		if err != nil {
			return errors.Wrap(err, "error reading file from s3 in the file path")
		}
		indexArr, err := getJsonElementIndex(vendorReqBytes)
		if err != nil {
			return errors.Wrap(err, "error while getting index of json elements")
		}
		for _, index := range indexArr {
			readResult := &woPb.VendorRequest{}
			unmarshalErr := protojson.Unmarshal(vendorReqBytes[index.start:index.end+1], readResult)
			if unmarshalErr != nil {
				return errors.Wrap(err, "error while unmarshalling json bytes")
			}
			readResultModel := model.NewVendorRequest(readResult)
			s3ReadResults = append(s3ReadResults, readResultModel)
		}
	}
	return nil
}

func intToStringWith2Digits(val int) string {
	return fmt.Sprintf("%02v", val)
}

type jsonElementIndex struct {
	start int
	end   int
}

func getJsonElementIndex(file []byte) ([]jsonElementIndex, error) {
	var indexArr []jsonElementIndex
	level := 0
	curr := jsonElementIndex{}
	for i, char := range file {
		if string(char) == "{" {
			if level == 0 {
				curr.start = i
			}
			level++
		} else if string(char) == "}" {
			level--
			if level == 0 {
				curr.end = i
				indexArr = append(indexArr, curr)
				curr = jsonElementIndex{}
			}
		}
	}
	if level != 0 {
		return nil, fmt.Errorf("failed to get Json element Index. Got level %d when 0 is expected after parsing the whole file", level)
	}
	return indexArr, nil
}

func compareResults(crdbSelectResults, s3ReadResults vendorReqSlice) error {
	if len(crdbSelectResults) != len(s3ReadResults) {
		return errors.Errorf("Unequal number of events in crdb and s3. Number of events in crdb = %d, s3 = %d", len(crdbSelectResults), len(s3ReadResults))
	}
	logger.InfoNoCtx("Equal number of events in crdb and s3.", zap.Int("Number of events in crdb", len(crdbSelectResults)), zap.Int("Number of events in s3", len(s3ReadResults)))
	n := len(crdbSelectResults)

	sort.Sort(crdbSelectResults)
	sort.Sort(s3ReadResults)

	for i := 0; i < n; i++ {
		logger.InfoNoCtx(fmt.Sprintf("Validating row %d with Id in crdb = %s, s3 = %s", i, crdbSelectResults[i].Id, s3ReadResults[i].Id))

		crdbSelectResults[i].CreatedAt = time.Time{}
		crdbSelectResults[i].UpdatedAt = time.Time{}
		crdbSelectResults[i].DeletedAt = nil
		cmpRes := cmp.Diff(crdbSelectResults[i], s3ReadResults[i])
		if cmpRes != "" {
			return errors.New(fmt.Sprintf("Validation unsuccessful for iteration %d, difference in crdb and s3 is %s", i, cmpRes))
		}
		logger.InfoNoCtx(fmt.Sprintf("Validation successful for iteration %d", i))
	}
	return nil
}

func findAndValidateDBConf(conf *config.Config, dbName, tableName string) (*config.Table, error) {
	for _, table := range conf.Tables {
		if !(table.DBName == dbName && table.TableName == tableName) {
			continue
		}
		// Note: limits for the below validations are chosen based on experiences with bulk
		// deletion jobs. Violating these constraints can lead to Production cluster outage as happened in the past.
		// These values should be changed by experimenting with gradual increase of limits in production/equivalent cluster.
		if table.BatchDelay < (20 * time.Second) {
			err := fmt.Errorf("batch move delay for table %q cannot be less than 20 seconds", table.TableName)
			return nil, err
		}
		if table.BatchSize > (10000) {
			err := fmt.Errorf("conf MoveBatchSize for table %q cannot exceed 10000. Given: %d", table.DBName+"."+table.TableName,
				table.BatchSize)
			return nil, err
		}
		if table.OffsetColumnName == "" {
			err := fmt.Errorf("conf OffsetColumnName for table %s cannot be empty", table.DBName)
			return nil, err
		}

		return table, nil
	}
	return nil, fmt.Errorf("configuration for %q is not found", dbName+"."+tableName)
}

func getDBConf(dbName string, conf *config.Config) *cfg.DB {
	var dbConf *cfg.DB
	switch dbName {
	case "epifi_wealth":
		dbConf = conf.EpifiWealthDb
	case "validate_crdb_s3":
		dbConf = conf.TestDb
	default:
		logger.Panic("Invalid db name", zap.String("db", dbName))
	}
	return dbConf
}
