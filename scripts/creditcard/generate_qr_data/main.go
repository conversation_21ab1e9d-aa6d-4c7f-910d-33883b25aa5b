package main

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"flag"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/crypto/aes"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/scripts/creditcard/generate_qr_data/config"
)

var (
	plainText = flag.String("plainText", "",
		"plain text for physical card activation for which encrypted dats is to be generated")
)

func main() {
	flag.Parse()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	conf, err1 := config.Load()
	if err1 != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err1))
		return
	}

	aesCbcCryptor := aes.NewAesCbcCryptor(conf.Secrets.Ids[config.ClientEncryptionKey])
	iv, err := hex.DecodeString(conf.Secrets.Ids[config.ClientEncryptionInitialisationVector])
	if err != nil {
		logger.Panic("Failed to decode hex encoded initialisation vector", zap.Error(err))
	}
	sha256Hash := sha256.Sum256([]byte(*plainText))

	cipherText, err5 := aesCbcCryptor.Encrypt(context.Background(), sha256Hash[:], string(iv))
	if err5 != nil {
		logger.ErrorNoCtx("Failed to encrypt qr data", zap.Error(err5))
		return
	}

	// base64 encoding of encrypted data
	base64EncodedData := base64.StdEncoding.EncodeToString(cipherText)

	fmt.Printf("encyrpted data %s\n", base64EncodedData)

}
