package main

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	rewardsPb "github.com/epifi/gamma/api/rewards"

	"github.com/epifi/gamma/rewards/generator/dao/model"
)

const (
	dateLayout                      = "2006-01-02"
	defaultPageSize                 = 100
	defaultMaxAllowedPaginatedCalls = 500
	defaultNextPageFetchWaitTime    = 200 * time.Millisecond
)

var (
	rewardStatusesForProcessingExpiry = []rewardsPb.RewardStatus{rewardsPb.RewardStatus_CREATED, rewardsPb.RewardStatus_LOCKED}
)

type JobUpdateRewardStatusToExpiry struct {
	rewardDBConn *gorm.DB
}

func (j *JobUpdateRewardStatusToExpiry) DoJob(ctx context.Context, req *JobRequest) error {
	fromTime, toTime, maxAllowedPaginatedCalls, nextPageFetchWaitTime, err := j.parseJobArgs(req)
	if err != nil {
		return err
	}

	var (
		nextPageFetchFromTime            = fromTime
		nextPageFetchTillTime            = toTime
		totalExpiredRewardsFetched       = 0
		totalExpiredRewardsStatusUpdated = 0
		errorCount                       = 0
	)

	for range maxAllowedPaginatedCalls {
		newNextPageFetchFromTime, rewardsFetched, rewardsUpdated, shouldBreak, hasError := j.processRewardPage(ctx, nextPageFetchFromTime, nextPageFetchTillTime)
		if hasError {
			errorCount++
		}

		totalExpiredRewardsFetched += rewardsFetched
		totalExpiredRewardsStatusUpdated += rewardsUpdated
		nextPageFetchFromTime = newNextPageFetchFromTime

		if shouldBreak {
			logger.Info(ctx, "no more expired rewards left to process, breaking the loop")
			break
		}
		// waiting for sometime before fetching the next page to avoid db load
		time.Sleep(nextPageFetchWaitTime)
	}

	logger.Info(ctx, "script completed", zap.Int("totalExpiredRewardsFetched", totalExpiredRewardsFetched), zap.Int("totalExpiredRewardsStatusUpdated", totalExpiredRewardsStatusUpdated), zap.Time("rewardsExpiredTill", nextPageFetchFromTime), zap.Int("totalFailedPages", errorCount))

	if errorCount > 0 {
		return fmt.Errorf("failed to process %d pages", errorCount)
	}

	return nil
}

func (j *JobUpdateRewardStatusToExpiry) processRewardPage(ctx context.Context, nextPageFetchFromTime, nextPageFetchTillTime time.Time) (time.Time, int, int, bool, bool) {
	// clone the context to long-running db queries and ensure it's cancelled
	clonedCtx, cancel := context.WithDeadline(ctx, time.Now().Add(30*time.Second))
	defer cancel()

	// get expired rewards in a paginated fashion
	expiredRewardsPaginated, err := j.fetchExpiredRewardsPaginated(clonedCtx, nextPageFetchFromTime, nextPageFetchTillTime, defaultPageSize)
	if err != nil {
		logger.Error(clonedCtx, "error fetching expired rewards, continuing to next iteration", zap.Error(err))
		return nextPageFetchFromTime, 0, 0, false, true
	}
	if len(expiredRewardsPaginated) == 0 {
		logger.Info(clonedCtx, "no more expired rewards rewards left")
		return nextPageFetchFromTime, 0, 0, true, false
	}
	rewardsFetched := len(expiredRewardsPaginated)

	expiredRewardIdsPaginated := lo.Map(expiredRewardsPaginated, func(reward *model.Reward, _ int) string {
		return *reward.Id
	})

	rewardsUpdated, err := updateRewardStatus(clonedCtx, j.rewardDBConn, expiredRewardIdsPaginated)
	if err != nil {
		logger.Error(clonedCtx, "error processing reward", zap.Error(err))
		return nextPageFetchFromTime, rewardsFetched, 0, false, true
	}
	logger.Info(clonedCtx, "reward status updated successfully", zap.Int("totalExpiredRewardsStatusUpdated", rewardsUpdated))

	// if all rewards for current page are processed,
	// then nextPageFetchFromTime will be the last processed reward's created_at,
	// so that we fetch next page from the next reward
	newNextPageFetchFromTime := expiredRewardsPaginated[len(expiredRewardsPaginated)-1].CreatedAt
	return newNextPageFetchFromTime, rewardsFetched, rewardsUpdated, false, false
}

func (j *JobUpdateRewardStatusToExpiry) parseJobArgs(req *JobRequest) (fromTime, toTime time.Time, maxAllowedPaginatedCalls int, nextPageFetchWaitTime time.Duration, err error) {
	maxAllowedPaginatedCalls = defaultMaxAllowedPaginatedCalls
	nextPageFetchWaitTime = defaultNextPageFetchWaitTime

	timeArgs := strings.Split(req.Args1, "#")
	if len(timeArgs) != 2 {
		err = fmt.Errorf("invalid time arguments, expected fromTime#toTime, got %s", req.Args1)
		return
	}

	fromTime, err = time.Parse(dateLayout, timeArgs[0])
	if err != nil {
		err = fmt.Errorf("failed to parse fromTime to given layout: %w", err)
		return
	}

	toTime, err = time.Parse(dateLayout, timeArgs[1])
	if err != nil {
		err = fmt.Errorf("failed to parse toTime to given layout: %w", err)
		return
	}

	if req.Args2 != "" {
		configArgs := strings.Split(req.Args2, "#")
		if len(configArgs) == 2 {
			if parsedCalls, e := strconv.Atoi(configArgs[0]); e == nil {
				maxAllowedPaginatedCalls = parsedCalls
			}
			if parsedWaitTime, e := strconv.Atoi(configArgs[1]); e == nil {
				nextPageFetchWaitTime = time.Duration(parsedWaitTime) * time.Millisecond
			}
		}
	}
	return
}

func (j *JobUpdateRewardStatusToExpiry) fetchExpiredRewardsPaginated(ctx context.Context, fromTime time.Time, uptoTime time.Time, limit int) ([]*model.Reward, error) {
	if fromTime.IsZero() || uptoTime.IsZero() || fromTime.After(uptoTime) || limit > 500 {
		return nil, epifierrors.ErrInvalidArgument
	}
	db := gormctxv2.FromContextOrDefault(ctx, j.rewardDBConn)

	var rewardsList []*model.Reward
	if err := db.Model(&model.Reward{}).Where("status in (?) AND created_at >= ? AND created_at <= ? AND expires_at is not null AND expires_at <= ? AND deleted_at is null", rewardStatusesForProcessingExpiry, fromTime, uptoTime, time.Now()).Order("created_at asc").Limit(limit).Find(&rewardsList).Error; err != nil {
		return nil, fmt.Errorf("error fetching failed processing rewards from db, err : %w", err)
	}
	return rewardsList, nil
}

// updateRewardStatus updates the status of rewards to expired
func updateRewardStatus(ctx context.Context, dbConn *gorm.DB, rewardIds []string) (int, error) {
	db := gormctxv2.FromContextOrDefault(ctx, dbConn)

	// Update status for rewards to expired
	query := `
		UPDATE rewards
		SET status = ?
		WHERE id in (?)
	`

	result := db.Exec(query, rewardsPb.RewardStatus_EXPIRED, rewardIds)

	if result.Error != nil {
		return 0, result.Error
	}

	return int(result.RowsAffected), nil
}
