package main

import (
	"context"
	"flag"
	"fmt"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/bndr/gojenkins"
	"github.com/slack-go/slack"
	"go.uber.org/zap"
)

const (
	enableSlackNotifications = true
)

type parentJob []string

func (i *parentJob) String() string {
	return ""
}

func (i *parentJob) Set(value string) error {
	*i = append(*i, value)
	return nil
}

type JenkinsJobNotifier struct {
	jenkinsUrl      string
	jenkinsUsername string
	jenkinsAPIToken string
	jenkinsServer   *gojenkins.Jenkins
	slackClient     *slack.Client
	slackChannelID  string
}

func NewJenkinsJobNotifier(jenkinsUrl string, username string, apiToken string, oauthToken string, channelID string) *JenkinsJobNotifier {
	return &JenkinsJobNotifier{jenkinsUrl: jenkinsUrl, jenkinsUsername: username, jenkinsAPIToken: apiToken, slackClient: slack.New(oauthToken, slack.OptionDebug(true)), slackChannelID: channelID}
}

func getJenkinsServer(ctx context.Context, jenkinsConfig *JenkinsJobNotifier) error {
	var err error
	if jenkinsConfig.jenkinsServer, err = gojenkins.CreateJenkins(nil, jenkinsConfig.jenkinsUrl, jenkinsConfig.jenkinsUsername, jenkinsConfig.jenkinsAPIToken).Init(ctx); err != nil {
		return err
	}
	return nil
}

func (j *JenkinsJobNotifier) GetJob(ctx context.Context, jobName string, parentJob parentJob) *gojenkins.Job {
	job, err := j.jenkinsServer.GetJob(ctx, jobName, parentJob...)
	if err != nil {
		logger.Error(ctx, "could not fetch job", zap.Error(err))
		return nil
	}
	return job
}

func (j *JenkinsJobNotifier) GetBuildStatusByOffset(ctx context.Context, job *gojenkins.Job, numOfBuild int, offset int) []bool {
	builds := job.GetDetails().Builds
	limit := offset + numOfBuild
	if limit > len(builds) {
		logger.Error(ctx, "limit greater than total builds tracked",
			zap.Int("total builds", len(builds)),
			zap.Int("limit", limit))
		return nil
	}
	var buildStatus []bool
	for i := offset; i < limit; i++ {
		if status, err := job.GetBuild(ctx, builds[i].Number); err != nil {
			logger.Error(ctx, "error in fetching build", zap.Error(err))
			return nil
		} else {
			buildStatus = append(buildStatus, status.IsGood(ctx))
		}
	}
	return buildStatus
}

func triggerSlackNotification(status []bool) bool {
	response := false
	for _, buildStatus := range status {
		response = response || buildStatus
	}
	return !response
}

var parent parentJob

func main() {
	ctx := context.Background()
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "error fetching environment details", zap.Error(err))
		return
	}
	logger.Init(env)
	flag.Var(&parent, "parentJob", "list of parent jobs, example usage: -parentJob=V3_Deployment -parentJob=staging jobName=user")
	jobName := flag.String("job", "", "job to check the build status")
	offset := flag.Int("offset", 0, "starting offset for counting the jobs")
	numOfBuilds := flag.Int("n", 1, "number of builds to check")
	jenkinsUsername := flag.String("username", "", "jenkins username")
	jenkinsApiToken := flag.String("apitoken", "", "jenkins api token")
	slackOauthToken := flag.String("oauth", "", "slack oauth token")
	slackChannelID := flag.String("channelid", "", "slack channel id to post alerts in")
	flag.Parse()
	jobNotifier := NewJenkinsJobNotifier("https://jenkins-deploy.pointz.in/", *jenkinsUsername, *jenkinsApiToken, *slackOauthToken, *slackChannelID)
	if err := getJenkinsServer(ctx, jobNotifier); err != nil {
		logger.Error(ctx, "error while getting jobNotifier jenkinsServer", zap.Error(err))
		return
	}
	jenkinsJob := jobNotifier.GetJob(ctx, *jobName, parent)
	if jenkinsJob == nil {
		return
	}
	buildStatuses := jobNotifier.GetBuildStatusByOffset(ctx, jenkinsJob, *numOfBuilds, *offset)
	if buildStatuses == nil {
		return
	}
	if enableSlackNotifications && triggerSlackNotification(buildStatuses) {
		if lastFailedBuild, err := jenkinsJob.GetLastFailedBuild(ctx); err != nil {
			logger.Error(ctx, "error fetching last failing build", zap.Error(err))
			return
		} else {
			slackAttachment := slack.Attachment{
				Pretext: fmt.Sprintf("Last %d builds of %s failed from offset %d", *numOfBuilds, jenkinsJob.GetName(), *offset),
				Text:    "URL of the last failing build",
				Fields: []slack.AttachmentField{
					{
						Title: "URL",
						Value: lastFailedBuild.GetUrl(),
					},
				},
			}
			_, timestamp, err := jobNotifier.slackClient.PostMessage(jobNotifier.slackChannelID, slack.MsgOptionAttachments(slackAttachment))
			if err != nil {
				logger.Error(ctx, "error posting message on slack", zap.Error(err))
				return
			}
			logger.Info(ctx, "slack message posted", zap.String("timestamp", timestamp))
		}
	}
}
