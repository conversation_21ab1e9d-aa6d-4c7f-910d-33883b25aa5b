package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
	wealthConf "github.com/epifi/gamma/wealthonboarding/config"
)

type Config struct {
	Application *application
	RMSDb       *cfg.DB
	RMSSecrets  *cfg.Secrets
	AWS         *cfg.AWS
	EpifiDb     *cfg.DB
	SimulatorDb *cfg.DB
	S3Conf      wealthConf.S3Conf
}

type application struct {
	Environment string
	Name        string
}

var (
	_, b, _, _ = runtime.Caller(0)

	once   sync.Once
	config *Config
	err    error
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, "fetch_inactive_sips")
	if err != nil {
		return nil, fmt.Errorf("failed to load  config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to  unmarshal config: %w", err)
	}

	rmsKeyToIdMap := cfg.AddPgdbSslCertSecretIds(conf.RMSDb, conf.RMSSecrets.Ids)
	rmsKeyToSecret, err := cfg.LoadSecrets(rmsKeyToIdMap, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}
	keyToSecret, err := cfg.LoadAllSecrets(conf.EpifiDb, nil, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}
	err = updateDefaultConfig(conf.RMSDb, conf.Application.Environment, rmsKeyToSecret, conf.RMSSecrets)
	if err != nil {
		return nil, err
	}

	updateDefaultConfigepifi(conf.EpifiDb, keyToSecret)

	return conf, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(db *cfg.DB, env string, keyToSecret map[string]string, secrets *cfg.Secrets) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(db, dbServerEndpoint)

	cfg.UpdatePGDBSecretValues(db, secrets, keyToSecret)

	if env == cfg.TestEnv {
		// use default credentials for DB in test env
		cfg.UpdateDbUsernamePasswordInConfig(db, secrets.Ids["DbCredentials"])
		return nil
	}
	if _, ok := keyToSecret["DbCredentials"]; !ok {
		return fmt.Errorf("DBCredentials not fetched from secrets manager")
	}
	// update db credentials
	cfg.UpdateDbUsernamePasswordInConfig(db, keyToSecret["DbCredentials"])
	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println("config path: ", configPath)
	return configPath
}

func updateDefaultConfigepifi(c *cfg.DB, keyToSecret map[string]string) {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c, dbServerEndpoint)
	cfg.UpdateSecretValues(c, nil, keyToSecret)
}
