package main

import (
	"context"
	"fmt"

	"google.golang.org/grpc/metadata"

	"github.com/epifi/gamma/api/cx/dispute/job"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func main() {
	// Get environment
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}

	// Setup logger
	logger.Init(envName)
	defer func() { _ = logger.Log.Sync() }()
	cxConn := epifigrpc.NewConnByService(cfg.CX_SERVICE)
	defer epifigrpc.CloseConn(cxConn)
	disputeJobClient := job.NewDisputeProcessingJobClient(cxConn)
	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))
	resp, err := disputeJobClient.ProcessDisputesJob(ctx, &empty.Empty{})
	if resp != nil && resp.IsSuccess() {
		logger.Info(ctx, "Dispute processing job success", zap.Any("response", resp))
	} else {
		logger.Fatal("Dispute processing job failed", zap.Any("response", resp), zap.Error(err))
	}
}
