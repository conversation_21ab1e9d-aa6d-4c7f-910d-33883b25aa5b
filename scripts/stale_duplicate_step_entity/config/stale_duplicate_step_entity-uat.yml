Application:
  Environment: "uat"

Aws:
  Region: "ap-south-1"

EpifiWealthDb:
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  StatementTimeout: 2m
  SSLClientCert: "uat/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

S3Conf:
  Bucket: "epifi-uat-wealth-onboarding"
