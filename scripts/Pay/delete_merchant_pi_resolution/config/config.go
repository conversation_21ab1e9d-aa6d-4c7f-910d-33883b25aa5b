package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	_, b, _, _ = runtime.Caller(0)

	once   sync.Once
	config *Config
	err    error
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "delete_merchant_pi_resolution")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	_, dbConfigErr := cfg.LoadSecretsAndPrepareDBConfig(nil, conf.Application.Environment, conf.Aws.Region, conf.MerchantDb)
	if dbConfigErr != nil {
		return nil, dbConfigErr
	}
	return conf, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

type Config struct {
	Application        *application
	MerchantDb         *cfg.DB
	Aws                *cfg.AWS
	MerchantRedis      *cfg.RedisOptions
	ConsulData         *consulData
	MerchantPiIdPrefix string
}

type application struct {
	Environment string
	Name        string
}

type consulData struct {
	// key which is used for dynamic config to check if we need to pause the script or not
	Key string
	// address of the consul endpoint
	Address string
	Scheme  string
}
