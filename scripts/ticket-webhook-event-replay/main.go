package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"path/filepath"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/gocarina/gocsv"
	"go.uber.org/zap"
)

type TicketCSV struct {
	TicketID     int64  `csv:"Ticket ID"`
	Source       string `csv:"Source"`
	Phone        string `csv:"Phone"`
	ActualUserId string `csv:"Actual User ID"`
	Email        string `csv:"Email"`
}

type TicketEvent struct {
	FreshdeskWebhook *FreshdeskWebhook `json:"freshdesk_webhook"`
}

type FreshdeskWebhook struct {
	TicketId           int64  `json:"ticket_id"`
	TicketContactPhone string `json:"ticket_contact_phone"`
	TicketContactEmail string `json:"ticket_contact_email"`
	TicketSource       string `json:"ticket_source"`
	ActualUserId       string `json:"ticket_cf_actual_user_id"`
}

func main() {
	logger.Init("development")
	defer func() { _ = logger.Log.Sync() }()
	PublishEventsForTickets()
}

const (
	SherlockSourceChatUrl = "https://sherlock.epifi.in/external-api/v1/web-hooks/chat-ticket-create"
	SherlockSourceUrl     = "https://sherlock.epifi.in/external-api/v1/web-hooks/source-ticket-create"
	ApiKey                = ""
)

// nolint:funlen
func PublishEventsForTickets() {
	fileName := "/Users/<USER>/go/src/github.com/epifi/gamma/scripts/ticket-webhook-event-replay/ticket.csv"

	logger.InfoNoCtx(fmt.Sprintf("running tickets csv for file: %s", fileName))

	ticketFile, openErr := os.OpenFile(filepath.Clean(fileName), os.O_RDWR|os.O_CREATE, os.ModePerm)

	if openErr != nil {
		logger.ErrorNoCtx("openErr", zap.Error(openErr))
	}

	var tickets []*TicketCSV

	if loadErr := gocsv.UnmarshalFile(ticketFile, &tickets); loadErr != nil {
		return
	}

	if closeErr := ticketFile.Close(); closeErr != nil {
		return
	}
	for i, ticket := range tickets {
		ticketEvent := &TicketEvent{
			FreshdeskWebhook: &FreshdeskWebhook{
				TicketId:     ticket.TicketID,
				TicketSource: ticket.Source,
			},
		}
		sherlockUrl := SherlockSourceUrl
		switch ticket.Source {
		case "Phone", "Portal":
			ticketEvent.FreshdeskWebhook.TicketContactPhone = ticket.Phone
		case "Email":
			ticketEvent.FreshdeskWebhook.TicketContactEmail = ticket.Email
		case "Chat":
			ticketEvent.FreshdeskWebhook.ActualUserId = ticket.ActualUserId
			sherlockUrl = SherlockSourceChatUrl
		}
		body, err := json.Marshal(ticketEvent)
		if err != nil {
			logger.InfoNoCtx("invalid body", zap.Error(err), zap.Int64(logger.TICKET_ID, ticket.TicketID))
			continue
		}
		client := &http.Client{}
		// nolint:noctx
		ticketRequest, err := http.NewRequest(http.MethodPost, sherlockUrl, bytes.NewReader(body))
		if err != nil {
			logger.ErrorNoCtx("error while creating webhook request", zap.Error(err))
		}
		ticketRequest.Header.Add("Content-Type", "application/json")
		ticketRequest.Header.Add("app-source", "freshdesk")
		ticketRequest.SetBasicAuth(ApiKey, "")
		ticketResponse, err := client.Do(ticketRequest)
		if err != nil {
			logger.InfoNoCtx("failed to trigger webhook event", zap.Error(err), zap.Int64(logger.TICKET_ID, ticket.TicketID))
			continue
		}
		defer func() {
			err = ticketResponse.Body.Close()
			if err != nil {
				logger.ErrorNoCtx("error while closing response body", zap.Error(err))
			}
		}()
		if ticketResponse.StatusCode != http.StatusOK {
			logger.InfoNoCtx("error while replaying webhook event", zap.Error(err), zap.Int64(logger.TICKET_ID, ticket.TicketID))
			respBody, _ := ioutil.ReadAll(ticketResponse.Body)
			logger.InfoNoCtx("resp", zap.Int("status", ticketResponse.StatusCode), zap.ByteString("resp", respBody))
			continue
		}
		logger.InfoNoCtx(fmt.Sprintf("successfully triggered webhook event %d", i), zap.Int64(logger.TICKET_ID, ticket.TicketID))
	}

}
