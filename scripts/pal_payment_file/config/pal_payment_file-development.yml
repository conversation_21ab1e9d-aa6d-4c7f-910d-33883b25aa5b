Application:
  Environment: "development"
  Name: "pal_payment_file"

PgDbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_federal"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_liquiloans"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_idfc"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

AWS:
  Region: "ap-south-1"
  S3:
    PreapprovedLoanBucketName: "epifi-preapprovedloan"


Email:
  FromEmail: "<EMAIL>"
  ToEmail: "<EMAIL>"
  CcEmails: ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

RedisStore:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: ""
    DB: 0
