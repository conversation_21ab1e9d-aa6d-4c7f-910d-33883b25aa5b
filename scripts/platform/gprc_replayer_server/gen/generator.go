package main

import (
	"flag"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/samber/lo"

	toolspkg "github.com/epifi/be-common/tools/pkg"

	"github.com/epifi/be-common/tools/pkg/importer"
)

// nolint: funlen
func main() {
	apiDir := flag.String("api-dir", "./../../../api", "the directory to search for proto files")
	apiServerDir := flag.String("api-server-dir", "./../../../api/frontend", "the directory to search for gRPC services")
	outDir := flag.String("out-dir", ".", "the directory to search for proto files")
	flag.Parse()
	log.SetFlags(log.Lshortfile | log.LstdFlags)
	unimplementedServerRegex := regexp.MustCompile(`Unimplemented(.*Server) struct {`)
	importBuilder := importer.NewBuilder()
	importBuilder.AddImport("explorer", "github.com/epifi/gamma/api/pkg/cfg/explorer")
	g := &Generator{Imports: importBuilder}
	const gammaRepo = "github.com/epifi/gamma"
	err := filepath.Walk(*apiServerDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() || !strings.HasSuffix(path, "_grpc.pb.go") {
			return nil
		}
		// nolint: gosec
		content, err := ioutil.ReadFile(path)
		if err != nil {
			return err
		}
		path, err = filepath.Abs(path)
		if err != nil {
			return err
		}
		// nolint: gocritic
		path = path[strings.Index(path, gammaRepo):]
		pkgDir := filepath.Dir(path)
		res := unimplementedServerRegex.FindAllStringSubmatch(string(content), -1)
		for _, r := range res {

			serverName := r[1]
			alias := importBuilder.AddImport(filepath.Base(pkgDir), pkgDir)
			g.Services = append(g.Services, &Service{
				Alias: alias,
				Name:  serverName,
			})
		}
		return nil
	})
	if err != nil {
		log.Fatal(err)
	}
	var protoPkgs []string
	err = filepath.Walk(*apiDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() || !strings.HasSuffix(path, ".pb.go") {
			return nil
		}
		path, err = filepath.Abs(path)
		if err != nil {
			return err
		}
		// nolint: gocritic
		path = path[strings.Index(path, gammaRepo):]
		pkgDir := filepath.Dir(path)
		_, _, exist := lo.FindIndexOf(protoPkgs, func(item string) bool {
			return item == pkgDir
		})
		if !exist {
			protoPkgs = append(protoPkgs, pkgDir)
		}
		return nil
	})
	if err != nil {
		log.Fatal(err)
	}
	g.DummyImports = protoPkgs
	err = toolspkg.GenerateGoFile(filepath.Join(*outDir, "register.gen.go"), g, "gen", template)
	if err != nil {
		log.Fatal(err)
	}
}

type Generator struct {
	Services     []*Service
	Imports      *importer.Builder
	DummyImports []string
}
type Service struct {
	Alias string
	Name  string
}

const template = `
//nolint: goimports
package main

import (
	"google.golang.org/grpc"
	{{range $pkg, $alias := .Imports.ImportPkgToAliasMap}}
	{{$alias}} "{{$pkg}}"{{end}}
	{{- range $pkg := .DummyImports}}
	_ "{{$pkg}}"{{end}}
)

//nolint: funlen
//go:generate go run ./gen/generator.go
func registerGRPCServices(s *grpc.Server) {
	explorer.RegisterConfigExplorerServer(s, &explorer.UnimplementedConfigExplorerServer{})
	{{- range .Services}}
	{{.Alias}}.Register{{.Name}}(s, &{{.Alias}}.Unimplemented{{.Name}}{})
	{{- end}}
}
`
