DROP INDEX IF EXISTS row_id_by_table_name_idx;
DROP INDEX IF EXISTS change_feeds_updated_at_idx;
DROP TABLE IF EXISTS change_feeds;
CREATE TABLE IF NOT EXISTS history_tables (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	row_identifier UUID NOT NULL,
	updated_column STRING NOT NULL,
	table_name STRING NOT NULL,
	changelog JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (table_name ASC, row_identifier ASC, updated_column ASC, created_at),
	UNIQUE INDEX id_key (id ASC)
);
CREATE INDEX IF NOT EXISTS row_id_by_table_name_idx ON history_tables(row_identifier, table_name);
