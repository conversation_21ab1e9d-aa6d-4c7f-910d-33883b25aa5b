-- firing this query in prod since the CX team want to know the reason why the cards were blocked for the given user
-- for more context : https://epifi.slack.com/archives/C01FL3043AP/p1665902691136239


select reason, provenance, updated_at
from card_block_details
where card_id = '0e87af2b-635b-4132-995d-2a999b61ad2f';

select reason, provenance, updated_at
from card_block_details
where card_id = '1034a15c-7cae-4abe-b67d-7f13253369c9';
