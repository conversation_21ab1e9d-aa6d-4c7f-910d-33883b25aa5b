--- Due to the issue where loec sub status was getting set to APPROVED_BY_FI_PRE_BRE for
--- sending event lead to wrong state management.
--- Now, Taking out all these users for starting the journey again
update loan_offer_eligibility_criteria
set sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED',
	updated_at = NOW()
WHERE updated_at > '2025-06-09'
  AND sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE'
  AND status != 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED';
