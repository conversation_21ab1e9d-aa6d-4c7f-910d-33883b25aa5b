UPDATE
	rules
SET
	condition = '{"condition": "paymentAmount.Units >= 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}',
	actions = '{"actionArr": [{"type": "DEPOSIT", "data": {"expressions": [{"varName": "depositAmount", "expression": "calculateChangeAmount(paymentAmount, configuredRoundAmount)"}]}}]}'
WHERE
	id IN ('f49d1c45-708f-49c7-988e-131050dbfe88', '78d18f6c-03bf-499a-aed7-f49eef104ba8');
