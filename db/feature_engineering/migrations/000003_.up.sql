CREATE TABLE IF NOT EXISTS credit_reports_raw
(
	id                 VARCHAR     NOT NULL PRIMARY KEY,
	actor_id           VARCHAR     NOT NULL,
	vendor             VARCHAR     NOT NULL,
	raw_report         BYTEA       NULL,
	consent_valid_till TIMESTAMPTZ NOT NULL,
	created_at         TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at         TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at_unix    BIGINT      NOT NULL DEFAULT 0
);
CREATE INDEX IF NOT EXISTS credit_reports_raw_actor_id ON credit_reports_raw (actor_id);
CREATE INDEX IF NOT EXISTS credit_reports_raw_consent_valid_till_deleted_at ON credit_reports_raw (consent_valid_till, deleted_at_unix);
CREATE INDEX IF NOT EXISTS credit_reports_raw_updated_at ON credit_reports_raw (updated_at);
