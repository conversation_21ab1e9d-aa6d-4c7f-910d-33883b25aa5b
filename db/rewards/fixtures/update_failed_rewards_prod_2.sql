-- SD rewards moved to permanent failure state due permanent failure at upstream service.
-- Deleting failed processing requests and updating the state of these rewards to 'PROCESSING_PENDING'.
-- We need to re-trigger the processing for this reward through a sherlock dev action after running this fixture.
-- https://monorail.pointz.in/p/fi-app/issues/detail?id=10044
delete from processing_requests where id = 'de4fdf0a-0283-4b93-ace0-1e3ebd3f432f';
update rewards set processing_ref = '', status = 'PROCESSING_PENDING' where id = 'RW210604en8TeDXsRiu8BhsKKWs0Mw==' AND status = 'PROCESSING_FAILED';
