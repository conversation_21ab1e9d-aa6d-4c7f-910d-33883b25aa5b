package workflow_test

import (
	"log"
	"os"
	"testing"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifitemporal"
	epifitemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"
	payWorkerConfig "github.com/epifi/gamma/pay/config/worker"
)

var wts epifitemporalTest.WorkflowTestSuite

func TestMain(m *testing.M) {
	// Initialize logger with test environment
	logger.Init(cfg.TestEnv)

	// Load pay worker config to get workflow and activity parameters
	conf, err := payWorkerConfig.Load()
	if err != nil {
		log.Fatal("failed to load pay worker config", err)
	}

	// Initialize workflow parameters from config
	err = epifitemporal.InitWorkflowParams(conf.WorkflowParamsList.GetWorkflowParamsMap())
	if err != nil {
		logger.Panic("failed to load workflow params", zap.Error(err))
	}

	// Initialize activity parameters from config
	err = epifitemporal.InitDefaultActivityParams(conf.DefaultActivityParamsList.GetActivityParamsMap())
	if err != nil {
		logger.Panic("failed to load default activity params", zap.Error(err))
	}

	// Set up temporal test suite
	wts.SetLogger(epifitemporalLogging.NewZapAdapter(logger.Log))

	// Run tests
	exitCode := m.Run()

	// Cleanup
	_ = logger.Log.Sync()

	os.Exit(exitCode)
}
