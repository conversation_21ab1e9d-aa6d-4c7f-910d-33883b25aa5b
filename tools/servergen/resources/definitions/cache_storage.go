package definitions

import (
	"errors"
	"strings"

	"github.com/epifi/be-common/tools/servergen/resources"
	typeinfo "github.com/epifi/be-common/tools/servergen/type"
)

var cacheStoragePkgPath = "github.com/epifi/be-common/pkg/cache"

const (
	CacheStorageResourceName        resources.ResourceName = "CacheStorage"
	RueidisCacheStorageResourceName resources.ResourceName = "RueidisCacheStorage"
)

type CacheStorage struct {
}

func NewCacheStorage() *CacheStorage { return &CacheStorage{} }
func (r *CacheStorage) Name() resources.ResourceName {
	return CacheStorageResourceName
}
func (r *CacheStorage) IsServerResource() bool { return true }
func (r *CacheStorage) MatchType(serverResourceFactory resources.ServerFactory, serviceResourceFactory resources.ServiceFactory, t *typeinfo.Type) (matched bool, resource *resources.ResourceConf, err error) {
	if isCacheStorage(t) {
		// Since, the tool uses the type name to map the configuration to init publisher, type shouldn't be used directly.
		return false, nil, errors.New("expecting CacheStorage suffix in the type name. Define a type alias for original type and use it. ex: type AuthCacheStorage cache.CacheStorage")
	}
	return
}
func (r *CacheStorage) MatchUnderlyingType(serverResourceFactory resources.ServerFactory, serviceResourceFactory resources.ServiceFactory, definedType, underlyingType *typeinfo.Type) (matched bool, resource *resources.ResourceConf, err error) {
	if !isCacheStorage(underlyingType) {
		return false, nil, nil
	}
	name := definedType.Name
	conf, err := r.getCacheStorageConf(serverResourceFactory, name)
	if err != nil {
		return false, nil, err
	}
	return true, conf, nil
}

func (r *CacheStorage) getCacheStorageConf(serverResourceFactory resources.ServerFactory, name string) (*resources.ResourceConf, error) {
	switch {
	case strings.HasSuffix(name, string(RueidisCacheStorageResourceName)):
		redisClientName := strings.TrimSuffix(name, string(RueidisCacheStorageResourceName)) + "RueidisRedisStore"
		redisClientConf := newRedisClientConf(serverResourceFactory, RueidisRedisStoreResourceName, redisClientName)
		return newCacheStorageConf(serverResourceFactory, RueidisCacheStorageResourceName, name, redisClientConf), nil

	case strings.HasSuffix(name, string(CacheStorageResourceName)):
		redisClientName := strings.TrimSuffix(name, string(CacheStorageResourceName)) + "RedisStore"
		redisClientConf := newRedisClientConf(serverResourceFactory, RedisStoreResourceName, redisClientName)
		return newCacheStorageConf(serverResourceFactory, CacheStorageResourceName, name, redisClientConf), nil

	default:
		return nil, errors.New("expecting CacheStorage or RueidisCacheStorage suffix in the type name. Define a type alias for original type and use it. ex: type AuthCacheStorage cache.CacheStorage")
	}
}

func newRedisClientConf(serverResourceFactory resources.ServerFactory, resourceName resources.ResourceName, name string) *resources.ResourceConf {
	varName := serverResourceFactory.GetVariableGenerator().GetUniqueVar(name)
	return serverResourceFactory.AddResourceConf(&resources.ResourceConf{
		ResourceName: resourceName,
		Var:          varName,
		Key:          name,
		CustomConf: &RedisOption{
			ConfigField: name,
			Var:         varName,
		},
	})
}

func newCacheStorageConf(serverResourceFactory resources.ServerFactory, resourceName resources.ResourceName, name string, redisClientConf *resources.ResourceConf) *resources.ResourceConf {
	varName := serverResourceFactory.GetVariableGenerator().GetUniqueVar(name)
	return &resources.ResourceConf{
		ResourceName: resourceName,
		Var:          varName,
		Key:          name,
		CustomConf: &Config{
			ConfigField:    redisClientConf.Key,
			RedisClientVar: redisClientConf.CustomConf.(*RedisOption).Var,
		},
	}
}

type Config struct {
	ConfigField    string
	RedisClientVar string
}

func isCacheStorage(typ *typeinfo.Type) bool {
	return !typ.IsPointer && typ.Pkg == cacheStoragePkgPath &&
		typ.Name == "CacheStorage"
}
