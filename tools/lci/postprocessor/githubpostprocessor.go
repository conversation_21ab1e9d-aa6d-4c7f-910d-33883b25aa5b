package postprocessor

import (
	"fmt"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/lci/internal/ghclient"
	"github.com/epifi/be-common/tools/lci/pkg/cmdlogger"
	"github.com/epifi/be-common/tools/lci/projectinfo"
	"github.com/epifi/be-common/tools/pkg/cmdrunner"
)

const RunWorkflowsLabel = "RunWorkflows"

type githubPostProcessor struct {
	cmdExec  cmdrunner.CommandRunner
	ghClient *ghclient.GhClient
}

func NewGithubPostProcessor() PostProcessor {
	return &githubPostProcessor{}
}

func (g *githubPostProcessor) GetName() string {
	return "GithubPostProcessor"
}

func (g *githubPostProcessor) RunAlways() bool {
	return false
}

func (g *githubPostProcessor) Execute(p *projectinfo.ProjectInfo) error {
	cmdRunner, err := cmdrunner.NewCmdRunner(p.<PERSON>o<PERSON>, cmdrunner.WithLogger(cmdlogger.Logger{}))
	if err != nil {
		return errors.Wrap(err, "error while trying to create cmd runner")
	}
	defer func() { _ = cmdRunner.Close() }()
	g.cmdExec = cmdRunner

	githubClient, ghErr := ghclient.NewGhClient(cmdRunner)
	if ghErr != nil {
		return errors.Wrap(ghErr, "error initialising github client")
	}
	g.ghClient = githubClient

	// create status for GitHub UI
	_, cmdErr := g.cmdExec.ExecuteCommand(fmt.Sprintf("gh api --method POST -H \"Accept: application/vnd.github+json\" -H \"X-GitHub-Api-Version: 2022-11-28\"  /repos/epifi/%s/statuses/%s -f state='success' -f description='LCI Stamped' -f context='Local CI Stamped'", p.RepoName, p.PrData.LatestCommit))
	if cmdErr != nil {
		return errors.Wrap(cmdErr, "error marking commit status on github")
	}

	// apply run workflows label automatically
	shouldApplyLabel, labelCheckErr := g.shouldApplyRunWorkflowsLabel(p)
	if labelCheckErr != nil {
		return labelCheckErr
	}
	if shouldApplyLabel {
		labelApplyErr := g.ghClient.ReapplyLabel(p.UserInput.PrNumber, RunWorkflowsLabel, p.RepoName)
		if labelApplyErr != nil {
			return errors.Wrap(labelApplyErr, fmt.Sprintf("error reapplying %s label on PR", RunWorkflowsLabel))
		}
	}
	return nil
}

func (g *githubPostProcessor) shouldApplyRunWorkflowsLabel(p *projectinfo.ProjectInfo) (bool, error) {
	// if running inside github, don't apply label
	if p.UserInput.GhActionMode {
		return false, nil
	}

	// check for any failure
	for _, v := range p.CheckerStatus {
		if !v {
			logger.InfoNoCtx("Skipping auto label applying due to some checks failing locally")
			return false, nil
		}
	}

	// check for any workflows still pending
	if len(p.ExcludedAffectedWorkflows) != 0 {
		logger.InfoNoCtx("Skipping auto label applying due to some workflows pending to be run")
		return false, nil
	}

	// check for approval and no changes requested
	prReviews, prReviewErr := g.ghClient.GetPrReviewInfo(p.UserInput.PrNumber, p.RepoName)
	if prReviewErr != nil {
		return false, errors.Wrap(prReviewErr, "error getting pr review info on pr")
	}

	atleastOneApproved := false
	noChangesRequested := true
	for _, review := range prReviews {
		if review.GetState() == "APPROVED" {
			atleastOneApproved = true
		}
		if review.GetState() == "CHANGES_REQUESTED" {
			noChangesRequested = false
		}
	}
	if !(atleastOneApproved && noChangesRequested) {
		logger.InfoNoCtx("Skipping auto label applying due to missing approval or PR in changes requested state")
		return false, nil
	}

	return true, nil
}
