//nolint:dupl
package sms_rules

import (
	"github.com/samber/lo"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	commsPb "github.com/epifi/gamma/api/comms"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/order/notification/comms"
)

type ChequeCreditSmsRule struct {
}

func NewChequeCreditSmsRule() *ChequeCreditSmsRule {
	return &ChequeCreditSmsRule{}
}

func (d *ChequeCreditSmsRule) IsSmsApplicable(owt *orderPb.OrderWithTransactions, args ...interface{}) bool {
	if len(owt.GetTransactions()) == 0 {
		return false
	}
	return owt.GetTransactions()[0].GetPartnerRefIdDebit() == "" && lo.Contains[orderPb.OrderTag](owt.GetOrder().GetTags(), orderPb.OrderTag_CHEQUE)
}

func (d *ChequeCreditSmsRule) GetSmsType() commsPb.SmsType {
	return commsPb.SmsType_CHEQUE_CREDIT_PROCESSING_FINT
}

func (d *ChequeCreditSmsRule) GetSmsQos() commsPb.QoS {
	return commsPb.QoS_BEST_EFFORT
}

func (d *ChequeCreditSmsRule) GetSmsOption(owt *orderPb.OrderWithTransactions, args ...string) *commsPb.SmsOption {
	amount := &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        0,
		Nanos:        0,
	}
	if len(owt.GetTransactions()) != 0 {
		amount = owt.GetTransactions()[0].GetAmount()
	}
	return &commsPb.SmsOption{
		Option: &commsPb.SmsOption_ChequeCreditProcessingFint{
			ChequeCreditProcessingFint: &commsPb.ChequeCreditProcessingFint{
				SmsType: commsPb.SmsType_CHEQUE_CREDIT_PROCESSING_FINT,
				Option: &commsPb.ChequeCreditProcessingFint_ChequeCreditProcessingFintSmsOptionV1{
					ChequeCreditProcessingFintSmsOptionV1: &commsPb.ChequeCreditProcessingFintSmsOptionV1{
						TemplateVersion:   commsPb.TemplateVersion_VERSION_V1,
						TransactionAmount: amount,
					},
				},
			},
		},
	}
}

var _ comms.SmsRule = &ChequeCreditSmsRule{}
