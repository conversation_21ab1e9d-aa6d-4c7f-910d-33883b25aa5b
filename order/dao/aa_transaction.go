package dao

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	"golang.org/x/exp/maps"
	gormv2 "gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/plugin/dbresolver"

	errorsPkg "github.com/pkg/errors"

	rpcPb "github.com/epifi/be-common/api/rpc"

	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	aaPb "github.com/epifi/gamma/api/order/aa"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/dao/model"
)

var (
	aaTransactionImmutableColumnNameMap = map[aaPb.AATransactionFieldMask]string{
		aaPb.AATransactionFieldMask_EXECUTED_AT: "executed_at",
		aaPb.AATransactionFieldMask_CREATED_AT:  "created_at",
		aaPb.AATransactionFieldMask_DELETED_AT:  "deleted_at",
		aaPb.AATransactionFieldMask_REF_ID:      "partner_ref_id",
		aaPb.AATransactionFieldMask_AMOUNT:      "amount",
		aaPb.AATransactionFieldMask_ID:          "id",
		aaPb.AATransactionFieldMask_AA_TXN_ID:   "aa_txn_id",
		aaPb.AATransactionFieldMask_BANK:        "bank",
	}

	aaTransactionMutableColumnNameMap = map[aaPb.AATransactionFieldMask]string{
		aaPb.AATransactionFieldMask_PI_FROM:          "pi_from",
		aaPb.AATransactionFieldMask_PI_TO:            "pi_to",
		aaPb.AATransactionFieldMask_UTR:              "utr",
		aaPb.AATransactionFieldMask_PROTOCOL:         "payment_protocol",
		aaPb.AATransactionFieldMask_TRANSACTION_TYPE: "transaction_type",
		aaPb.AATransactionFieldMask_PAYLOAD:          "payload",
		aaPb.AATransactionFieldMask_REMARKS:          "remarks",
		aaPb.AATransactionFieldMask_TEMPLATE_ID:      "template_id",
		aaPb.AATransactionFieldMask_UPDATED_AT:       "updated_at",
	}

	aaTransactionColumnNameMap = go_utils.MergeMaps(aaTransactionImmutableColumnNameMap, aaTransactionMutableColumnNameMap)

	sortOrderToSqlCommand = map[commontypes.SortOrder]string{
		commontypes.SortOrder_SORT_ORDER_ASCENDING:  "ASC",
		commontypes.SortOrder_SORT_ORDER_DESCENDING: "DESC",
	}
)

func AATransactionDaoProvider(featureFlags *genconf.FeatureFlags, crdbDao *AATransactionDaoCRDB, pgdbDao *AATransactionDaoPgdb) AATransactionDao {
	if featureFlags.EnablePGDBDaoForAA() {
		return pgdbDao
	}

	return crdbDao
}

// Deprecated: all new implementations for the table must be added as part of AATransactionDaoPgdb which is PGDB
// compliant implementation.
type AATransactionDaoCRDB struct {
	db    *gormv2.DB
	idGen idgen.IdGenerator
}

// Ensure  AATransactionDaoCRDB implements AATransactionDao at compile time
var _ AATransactionDao = &AATransactionDaoCRDB{}

// NewAATransactionDao Factory method for creating an instance of AATransaction dao. This method will be used by the injector
// when providing the dependencies at initialization time.
// Deprecated: in favour of NewAATransactionDaoPgdb
func NewAATransactionDao(db cmdTypes.EpifiWealthCRDB, idGen idgen.IdGenerator) *AATransactionDaoCRDB {
	return &AATransactionDaoCRDB{db: db, idGen: idGen}
}

// getAllSelectColumnsForAATransaction converts field mask to string slice with column name corresponding to field name enums
func getAllSelectColumnsForAATransaction() []string {
	var selectColumns []string
	for _, columnName := range transactionColumnNameMap {
		selectColumns = append(selectColumns, columnName)
	}

	sort.Strings(selectColumns)
	return selectColumns
}

// getSelectColumnsForAATransaction converts field mask to string slice with column name corresponding to field name enums
func getSelectColumnsForAATransaction(fieldMasks []aaPb.AATransactionFieldMask) []string {
	var selectColumns []string

	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, aaTransactionColumnNameMap[field])
	}
	return funk.UniqString(selectColumns)
}

// Create inserts entry into DB
func (a *AATransactionDaoCRDB) Create(ctx context.Context, txn *aaPb.Transaction) (*aaPb.Transaction, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "Create", time.Now())

	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	txn.Remarks = sanitiseRemarks(txn.GetRemarks())
	id, err := a.idGen.Get(idgen.AATransaction)
	if err != nil {
		return nil, fmt.Errorf("transaction id generation failed: %w", err)
	}

	modelTrans := model.NewModelAATransaction(txn)
	modelTrans.ID = id

	if err = db.Create(modelTrans).Error; err != nil {
		logger.Error(ctx, "error in creating aa_transaction", zap.Error(err))
		if storagev2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, err
	}

	logger.Debug(ctx, "aa transaction entry created!",
		zap.String("id", modelTrans.ID))
	return modelTrans.ToProto(), nil
}

// BatchUpsert Creates or Updates entry into DB. Operations are batched by the batchSize provided in input
func (a *AATransactionDaoCRDB) BatchUpsert(ctx context.Context, txnList []*aaPb.Transaction, batchSize int) error {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "BatchUpsert", time.Now())

	if batchSize <= 0 {
		return errors.New("Batch size cannot be zero/negative")
	}
	if len(txnList) == 0 {
		return errors.New("Empty request sent for upsert")
	}
	var models []*model.AaTransaction
	for _, txn := range txnList {
		model := model.NewModelAATransaction(txn)
		if model.ID == "" {
			id, err := a.idGen.Get(idgen.AATransaction)
			if err != nil {
				return errorsPkg.Wrap(err, fmt.Sprintf("Id generation for Enriched transaction failed. Aa Transaction Id : %s", txn.GetAaTxnId()))
			}
			model.ID = id
		}
		models = append(models, model)
	}
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	err := db.Transaction(func(tx *gormv2.DB) error {
		if err := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: aaTransactionColumnNameMap[aaPb.AATransactionFieldMask_ID]}},
			DoUpdates: clause.AssignmentColumns(maps.Values(aaTransactionMutableColumnNameMap)),
		}).CreateInBatches(models, batchSize).Error; err != nil {
			tx.Rollback()
			return errorsPkg.Wrap(err, "Failed to Batch Upsert Enriched Transactions")
		}
		return nil
	})

	return err
}

// BatchCreate Creates entries into DB. Operations are batched by the batchSize provided in input
func (a *AATransactionDaoCRDB) BatchCreate(ctx context.Context, txnList []*aaPb.Transaction, batchSize int) ([]*aaPb.Transaction, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "BatchCreate", time.Now())

	if batchSize <= 0 {
		return nil, errors.New("Batch size cannot be zero/negative")
	}
	if len(txnList) == 0 {
		return nil, errors.New("Empty request sent for batch create")
	}
	var models []*model.AaTransaction
	var enrichedTransactionList []*aaPb.Transaction
	for _, txn := range txnList {
		model := model.NewModelAATransaction(txn)
		id, err := a.idGen.Get(idgen.AATransaction)
		if err != nil {
			return nil, errorsPkg.Wrap(err, fmt.Sprintf("Id generation for Enriched transaction failed. Aa Transaction Id : %s", txn.GetAaTxnId()))
		}
		model.ID = id
		models = append(models, model)
		enrichedTransactionList = append(enrichedTransactionList, model.ToProto())
	}
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	err := db.Transaction(func(tx *gormv2.DB) error {
		if err := tx.CreateInBatches(models, batchSize).Error; err != nil {
			tx.Rollback()
			return errorsPkg.Wrap(err, "Failed to Batch Create Enriched Transactions")
		}
		return nil
	})

	if err != nil {
		return nil, err
	}
	return enrichedTransactionList, err
}

// GetById fetches DB record belonging to given transaction id. Returns error if record not found.
// nolint: dupl
func (a *AATransactionDaoCRDB) GetById(ctx context.Context, txnId string) (*aaPb.Transaction, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	modelTrans := &model.AaTransaction{}
	if res := db.Where("id = ?", txnId).First(modelTrans); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) || res.RowsAffected == 0 {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for transaction txnId: %s: %w", txnId, res.Error)
	}

	return modelTrans.ToProto(), nil
}

// DeleteByUtr soft delete DB record belonging to given transaction id. Returns error if record not found or no row updated.
func (a *AATransactionDaoCRDB) DeleteByUtr(ctx context.Context, utr string) error {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "DeleteByUtr", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	modelTrans := &model.AaTransaction{}
	if res := db.Where("utr = ?", utr).Delete(modelTrans); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) || res.RowsAffected == 0 {
			return epifierrors.ErrRecordNotFound
		}
		return fmt.Errorf("failed to soft delete record for transaction utr: %s: %w", utr, res.Error)
	}
	return nil
}

// GetTransactionByAATxnId returns aa transaction for the given aa transaction id
// nolint: dupl
func (a *AATransactionDaoCRDB) GetTransactionByAATxnId(ctx context.Context, aaTxnId string) (*aaPb.Transaction, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetTransactionByAATxnId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	modelTrans := &model.AaTransaction{}
	if res := db.Where("aa_txn_id = ?", aaTxnId).First(modelTrans); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) || res.RowsAffected == 0 {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for transaction aaTxnId: %s: %w", aaTxnId, res.Error)
	}

	return modelTrans.ToProto(), nil
}

// GetTransactionsByAATxnIds returns aa transactions bulk for the given aa transaction ids
// nolint: dupl
func (a *AATransactionDaoCRDB) GetTransactionsByAATxnIds(ctx context.Context, aaTxnIds []string, options ...storagev2.FilterOption) ([]*aaPb.Transaction, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetTransactionsByAATxnIds", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	for _, opt := range options {
		db = opt.ApplyInGorm(db)
	}
	var modelTrans []*model.AaTransaction

	if err := db.Where("aa_txn_id in (?)", aaTxnIds).Find(&modelTrans).Error; err != nil {
		return nil, fmt.Errorf("failed to read data from DB: %w", err)
	}

	if len(modelTrans) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	var protoRes []*aaPb.Transaction
	for _, dbModel := range modelTrans {
		protoRes = append(protoRes, dbModel.ToProto())
	}

	return protoRes, nil
}

// DeleteByTxnIds deletes txns for given txn Id
func (a *AATransactionDaoCRDB) DeleteByTxnIds(ctx context.Context, txnIds []string) error {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "DeleteByTxnIds", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	limit := len(txnIds)
	err := db.Unscoped().Where("id in (?)", txnIds).Delete([]*model.AaTransaction{}).Order("id DESC").Limit(limit).Error
	if err != nil {
		return fmt.Errorf("error deleting aa txns: %w", err)
	}
	return nil
}

// nolint: dupl
func (a *AATransactionDaoCRDB) GetTxnCountsForPiIds(ctx context.Context, piIds []string, accountId string) (map[string]int32, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetTxnCountsForPiIds", time.Now())
	var (
		queryResultsPiFrom []*model.PiIdToTxnCount
		queryResultsPiTo   []*model.PiIdToTxnCount
		queryParams        []interface{}
	)
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	// filter by account Id if accountId is not empty
	var accountIdFilterString string
	if accountId != "" {
		accountIdFilterString = "computed_account_id=? AND "
		queryParams = append(queryParams, accountId)
	}

	queryParams = append(queryParams, piIds)
	queryPiFrom := "select pi_from as pi_id, count(*) as txn_count from aa_enriched_transactions where " + accountIdFilterString + "pi_from in (?) GROUP BY pi_from"
	err := db.Raw(queryPiFrom, queryParams...).Scan(&queryResultsPiFrom).Error
	if err != nil {
		return nil, fmt.Errorf("error fetching txn count for pi ids: %w", err)
	}

	queryPiTo := "select pi_to as pi_id, count(*) as txn_count from aa_enriched_transactions where " + accountIdFilterString + "pi_to in (?) GROUP BY pi_to"
	err = db.Raw(queryPiTo, queryParams...).Scan(&queryResultsPiTo).Error
	if err != nil {
		return nil, fmt.Errorf("error fetching txn count for pi ids: %w", err)
	}

	return getPiIdToTxnCountMap(queryResultsPiFrom, queryResultsPiTo, piIds), nil
}

// GetTransactionsCount gets the total number of transactions for given actorId based on various filters
// startTime and endTime are optional, but both startTime and endTime cannot be nil
// optional: otherPiFilter filters the other piIds involved in the transaction
// optional: transactionType filters the transaction based on credit,debit etc.
// optional: paymentProtocol filters the transaction based on payment protocol like NEFT,RTGS etc
func (a *AATransactionDaoCRDB) GetTransactionsCount(ctx context.Context, currentActorPiIds []string, otherPiFilter []string,
	transactionType payment.AccountingEntryType, startTime, endTime time.Time,
	paymentProtocol []payment.PaymentProtocol) (int64, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetTransactionsCount", time.Now())
	var (
		queryPlaceholder     []interface{}
		timeQuery            string
		piQuery              string
		paymentProtocolQuery string
		count                int64
	)

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	if len(currentActorPiIds) == 0 {
		return 0, fmt.Errorf("list of piIds can't be empty: %w", epifierrors.ErrInvalidArgument)
	}

	if startTime.IsZero() && endTime.IsZero() {
		return 0, fmt.Errorf("both start and end time can't be zero: %w", epifierrors.ErrInvalidArgument)
	}

	piQuery, queryPlaceholder = getPiQueryForAggregateMethod(transactionType, otherPiFilter, queryPlaceholder, currentActorPiIds)

	timeQuery, queryPlaceholder = getTimeQueryForAggregateMethod(startTime, endTime, timeQuery, queryPlaceholder)

	// evaluate payment protocol filter
	if len(paymentProtocol) != 0 {
		paymentProtocolQuery = "AND payment_protocol IN (?) "
		queryPlaceholder = append(queryPlaceholder, paymentProtocol)
	}

	indexHint, indexErr := getIndexHintFromTxnTypeForGettingTxnCount(transactionType)
	if indexErr != nil {
		return 0, fmt.Errorf("error determining index hint: %w", indexErr)
	}

	query := "SELECT COUNT(*) FROM aa_enriched_transactions" + indexHint + " WHERE " +
		piQuery + timeQuery + paymentProtocolQuery

	if err := db.Debug().Raw(query, queryPlaceholder...).Scan(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count transactions: %w", err)
	}

	return count, nil
}

func getIndexHintFromTxnTypeForGettingTxnCount(transactionType payment.AccountingEntryType) (string, error) {
	switch transactionType {
	case payment.AccountingEntryType_DEBIT:
		return "@{FORCE_INDEX=aa_transactions_pi_from_computed_account_id_idx}", nil
	case payment.AccountingEntryType_CREDIT:
		return "@{FORCE_INDEX=aa_transactions_pi_to_computed_account_id_idx}", nil
	default:
		return "", errors.New("index can't be determined for txn type other than debit and credit")
	}
}
func getTimeQueryForAggregateMethod(startTime time.Time, endTime time.Time, timeQuery string, queryPlaceholder []interface{}) (string, []interface{}) {
	switch {
	case !startTime.IsZero() && !endTime.IsZero():
		timeQuery = "AND (COALESCE(executed_at,updated_at) >= ? ::TIMESTAMPTZ AND  COALESCE(executed_at,updated_at) <= ? ::TIMESTAMPTZ )"
		queryPlaceholder = append(queryPlaceholder, startTime, endTime)
	case !startTime.IsZero():
		timeQuery = "AND (COALESCE(executed_at,updated_at) >= ? ::TIMESTAMPTZ)"
		queryPlaceholder = append(queryPlaceholder, startTime)
	case !endTime.IsZero():
		timeQuery = "AND (COALESCE(executed_at,updated_at) <= ? ::TIMESTAMPTZ )"
		queryPlaceholder = append(queryPlaceholder, endTime)
	}
	return timeQuery, queryPlaceholder
}

// nolint: dupl
func getPiQueryForAggregateMethod(transactionType payment.AccountingEntryType, otherPiFilter []string, queryPlaceholder []interface{}, piIds []string) (string, []interface{}) {
	var piQuery string
	switch transactionType {
	case payment.AccountingEntryType_DEBIT:
		if len(otherPiFilter) == 0 {
			piQuery = "(pi_from in (?) AND transaction_type = 'DEBIT')"
			queryPlaceholder = append(queryPlaceholder, piIds)
		} else {
			piQuery = "(pi_from in (?) AND pi_to in (?) AND transaction_type = 'DEBIT')"
			queryPlaceholder = append(queryPlaceholder, piIds, otherPiFilter)
		}
	case payment.AccountingEntryType_CREDIT:
		if len(otherPiFilter) == 0 {
			piQuery = "(pi_to in (?) AND transaction_type = 'CREDIT')"
			queryPlaceholder = append(queryPlaceholder, piIds)
		} else {
			piQuery = "(pi_to in (?) AND pi_from in (?) AND transaction_type = 'CREDIT')"
			queryPlaceholder = append(queryPlaceholder, piIds, otherPiFilter)
		}
	default:
		if len(otherPiFilter) == 0 {
			piQuery = "(" + "(pi_from in (?) AND transaction_type = 'DEBIT')" + " OR " + "(pi_to in (?) AND transaction_type = 'CREDIT'))"
			queryPlaceholder = append(queryPlaceholder, piIds, piIds)
		} else {
			piQuery = "((pi_from in (?) AND pi_to in (?) AND transaction_type = 'DEBIT')" + " OR " +
				"(pi_to in (?) AND pi_from in (?) AND transaction_type = 'CREDIT'))"
			queryPlaceholder = append(queryPlaceholder, piIds, otherPiFilter, piIds, otherPiFilter)
		}
	}
	return piQuery, queryPlaceholder
}

func getPiIdToTxnCountMap(piFromToTxnCount, piToTxnCount []*model.PiIdToTxnCount, piIds []string) map[string]int32 {
	result := make(map[string]int32)
	for _, val := range piIds {
		result[val] = 0
	}
	for _, val := range piFromToTxnCount {
		result[val.PiId] += val.TxnCount
	}
	for _, val := range piToTxnCount {
		result[val.PiId] += val.TxnCount
	}

	return result
}

// nolint: dupl
func (a *AATransactionDaoCRDB) GetTxnCountsForActorIds(ctx context.Context, actorIds []string) (map[string]int32, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetTxnCountsForActorIds", time.Now())
	var (
		queryResultsActorFrom []*model.ActorIdToTxnCount
		queryResultsActorTo   []*model.ActorIdToTxnCount
	)
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	queryActorFrom := "select computed_from_actor_id as actor_id, count(*) as txn_count from aa_enriched_transactions where computed_from_actor_id in (?) GROUP BY computed_from_actor_id"
	err := db.Raw(queryActorFrom, actorIds).Scan(&queryResultsActorFrom).Error
	if err != nil {
		return nil, fmt.Errorf("error fetching txn count for actor ids: %w", err)
	}

	queryActorTo := "select computed_to_actor_id as actor_id, count(*) as txn_count from aa_enriched_transactions where computed_to_actor_id in (?) GROUP BY computed_to_actor_id"
	err = db.Raw(queryActorTo, actorIds).Scan(&queryResultsActorTo).Error
	if err != nil {
		return nil, fmt.Errorf("error fetching txn count for actor ids: %w", err)
	}

	return getActorIdToTxnCountMap(queryResultsActorFrom, queryResultsActorTo, actorIds), nil
}

func getActorIdToTxnCountMap(actorFromToTxnCount, actorToTxnCount []*model.ActorIdToTxnCount, actorIds []string) map[string]int32 {
	result := make(map[string]int32)
	for _, val := range actorIds {
		result[val] = 0
	}
	for _, val := range actorFromToTxnCount {
		result[val.ActorId] += val.TxnCount
	}
	for _, val := range actorToTxnCount {
		result[val.ActorId] += val.TxnCount
	}

	return result
}

func (a *AATransactionDaoCRDB) GetByAccountID(ctx context.Context, accountID string, options ...storagev2.FilterOption) ([]*aaPb.Transaction, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetByAccountID", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	if accountID == "" {
		return nil, fmt.Errorf("accountID can't be empty: %w", epifierrors.ErrInvalidArgument)
	}

	for _, opt := range options {
		db = opt.ApplyInGorm(db)
	}

	var dbRes []*model.AaTransaction
	if err := db.Where("computed_account_id = ?", accountID).Find(&dbRes).Error; err != nil {
		return nil, fmt.Errorf("failed to read data from DB: %w", err)
	}

	if len(dbRes) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	var protoRes []*aaPb.Transaction
	for _, dbModel := range dbRes {
		protoRes = append(protoRes, dbModel.ToProto())
	}

	return protoRes, nil
}

// GetByUtr fetches DB record belonging to given transaction utr. Returns error if record not found.
// nolint: dupl
func (a *AATransactionDaoCRDB) GetByUtr(ctx context.Context, utr string) ([]*aaPb.Transaction, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetByUtr", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	if utr == "" {
		return nil, fmt.Errorf("utr can't be empty: %w", epifierrors.ErrInvalidArgument)
	}

	var (
		aaTransModelResp []*model.AaTransaction
		aaTransProtoResp []*aaPb.Transaction
	)
	if err := db.Where("utr = ?", utr).Find(&aaTransModelResp).Error; err != nil {
		return nil, fmt.Errorf("failed to read record for transaction utr: %s: %w", utr, err)
	}

	if len(aaTransModelResp) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	for _, transModel := range aaTransModelResp {
		aaTransProtoResp = append(aaTransProtoResp, transModel.ToProto())
	}

	return aaTransProtoResp, nil
}

func (a *AATransactionDaoCRDB) GetTransactionCountForAccountId(ctx context.Context, accountId string) (uint32, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetTransactionCountForAccountId", time.Now())

	logger.Panic("GetTransactionCountForAccountId not implemented for CRDB")
	return 0, fmt.Errorf("Unimplemented")
}

func (a *AATransactionDaoCRDB) GetPaginatedEnrichedTxsByAccId(ctx context.Context, accountId string, filters *aaPb.EnrichedTransactionFilters, pageToken *pagination.PageToken, pageSize uint32, sortOrder commontypes.SortOrder) ([]*aaPb.Transaction, *rpcPb.PageContextResponse, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoCRDB", "GetPaginatedEnrichedTxsByAccId", time.Now())

	logger.Panic("GetPaginatedEnrichedTxsByAccId not implemented for CRDB")
	return nil, nil, fmt.Errorf("Unimplemented")
}

type AATransactionDaoPgdb struct {
	*AATransactionDaoCRDB
}

// Ensure  AATransactionDaoPgdb implements AATransactionDao at compile time
var _ AATransactionDao = &AATransactionDaoPgdb{}

// NewAATransactionDaoPgdb is factory method for creating an instance of AATransaction dao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewAATransactionDaoPgdb(conf *genconf.Config, db cmdTypes.EpifiWealthCRDB, idGen idgen.IdGenerator) *AATransactionDaoPgdb {
	var gormDb *gormv2.DB = db

	if conf.AaParams().PgdbConnAlias() != "" {
		gormDb = gormDb.Clauses(dbresolver.Use(conf.AaParams().PgdbConnAlias()))
	}

	return &AATransactionDaoPgdb{AATransactionDaoCRDB: NewAATransactionDao(gormDb, idGen)}
}

// GetTransactionsCount gets the total number of transactions for given actorId based on various filters
// startTime and endTime are optional, but both startTime and endTime cannot be nil
// optional: otherPiFilter filters the other piIds involved in the transaction
// optional: transactionType filters the transaction based on credit,debit etc.
// optional: paymentProtocol filters the transaction based on payment protocol like NEFT,RTGS etc
func (a *AATransactionDaoPgdb) GetTransactionsCount(ctx context.Context, currentActorPiIds []string, otherPiFilter []string,
	transactionType payment.AccountingEntryType, startTime, endTime time.Time,
	paymentProtocol []payment.PaymentProtocol) (int64, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoPgdb", "GetTransactionsCount", time.Now())
	var (
		queryPlaceholder     []interface{}
		timeQuery            string
		piQuery              string
		paymentProtocolQuery string
		count                int64
	)

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	if len(currentActorPiIds) == 0 {
		return 0, fmt.Errorf("list of piIds can't be empty: %w", epifierrors.ErrInvalidArgument)
	}

	if startTime.IsZero() && endTime.IsZero() {
		return 0, fmt.Errorf("both start and end time can't be zero: %w", epifierrors.ErrInvalidArgument)
	}

	piQuery, queryPlaceholder = getPiQueryForAggregateMethod(transactionType, otherPiFilter, queryPlaceholder, currentActorPiIds)

	timeQuery, queryPlaceholder = getTimeQueryForAggregateMethod(startTime, endTime, timeQuery, queryPlaceholder)

	// evaluate payment protocol filter
	if len(paymentProtocol) != 0 {
		paymentProtocolQuery = "AND payment_protocol IN (?) "
		queryPlaceholder = append(queryPlaceholder, paymentProtocol)
	}

	query := "SELECT COUNT(*) FROM aa_enriched_transactions WHERE " +
		piQuery + timeQuery + paymentProtocolQuery

	if err := db.Debug().Raw(query, queryPlaceholder...).Scan(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count transactions: %w", err)
	}

	return count, nil
}

func (a *AATransactionDaoPgdb) GetTransactionCountForAccountId(ctx context.Context, accountId string) (uint32, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoPgdb", "GetTransactionCountForAccountId", time.Now())

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	if accountId == "" {
		return 0, fmt.Errorf("accountId cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}

	var count int64
	if err := db.Model(&model.AaTransaction{}).Where("computed_account_id = ?", accountId).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return uint32(count), nil
}

func (a *AATransactionDaoPgdb) GetPaginatedEnrichedTxsByAccId(ctx context.Context, accountId string, filters *aaPb.EnrichedTransactionFilters, pageToken *pagination.PageToken, pageSize uint32, sortOrder commontypes.SortOrder) ([]*aaPb.Transaction, *rpcPb.PageContextResponse, error) {
	defer metric_util.TrackDuration("order/dao", "AATransactionDaoPgdb", "GetPaginatedEnrichedTxsByAccId", time.Now())

	if sortOrder == commontypes.SortOrder_SORT_ORDER_UNSPECIFIED {
		return nil, nil, errors.New("Invalid sort order passed to get Enriched Transaction By AccountId")
	}

	if accountId == "" {
		return nil, nil, errors.New("account id is mandatory to fetch transactions")
	}

	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	db = addEnrichedTransactionFilters(db, accountId, filters)
	if pageToken != nil {
		db = appendConditionForPageToken(db, sortOrder, pageToken)
	} else {
		db = db.Order("executed_at " + sortOrderToSqlCommand[sortOrder])
	}
	// fetch pageSize + 1 extra row to compute next page availability.
	db = db.Limit(int(pageSize + 1))

	var enrichedTxnModelList []*model.AaTransaction
	res := db.Find(&enrichedTxnModelList)

	if res.Error != nil {
		return nil, nil, errorsPkg.Wrap(res.Error, "Error finding transactions from db by AccountId")
	}
	if len(enrichedTxnModelList) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}

	// when it is a reverse page token, enrichedTxnModelList needs to be reversed
	if pageToken != nil && pageToken.IsReverse {
		n := len(enrichedTxnModelList)
		for i, j := 0, n-1; i < j; i, j = i+1, j-1 {
			enrichedTxnModelList[i], enrichedTxnModelList[j] = enrichedTxnModelList[j], enrichedTxnModelList[i]
		}
	}

	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, int(pageSize),
		model.AaTransactionModelRows(enrichedTxnModelList))
	if err != nil {
		return nil, nil, errorsPkg.Wrap(err, "Error getting NewPageCtxResp")
	}

	enrichedTxnModelList = rows.(model.AaTransactionModelRows)
	enrichedTxnProtoList := make([]*aaPb.Transaction, 0, len(enrichedTxnModelList))
	for _, enrichedTxn := range enrichedTxnModelList {
		enrichedTxnProtoList = append(enrichedTxnProtoList, enrichedTxn.ToProto())
	}

	return enrichedTxnProtoList, pageCtxResp, nil
}

func appendConditionForPageToken(db *gormv2.DB, sortOrder commontypes.SortOrder, pageToken *pagination.PageToken) *gormv2.DB {
	if sortOrder == commontypes.SortOrder_SORT_ORDER_ASCENDING {
		if pageToken.IsReverse {
			db = db.Where("executed_at <= ?", pageToken.Timestamp.AsTime()).
				Order("executed_at DESC")
		} else {
			db = db.Where("executed_at >= ?", pageToken.Timestamp.AsTime()).
				Order("executed_at ASC")
		}
	} else {
		if pageToken.IsReverse {
			db = db.Where("executed_at >= ?", pageToken.Timestamp.AsTime()).
				Order("executed_at ASC")
		} else {
			db = db.Where("executed_at <= ?", pageToken.Timestamp.AsTime()).
				Order("executed_at DESC")
		}
	}
	db = db.Offset(int(pageToken.Offset))
	return db
}

func addEnrichedTransactionFilters(db *gormv2.DB, accountId string, filter *aaPb.EnrichedTransactionFilters) *gormv2.DB {
	db = db.Where("computed_account_id = ?", accountId)
	if filter != nil {
		if filter.GetExecutedAfter() != nil {
			executedAfterTime := filter.GetExecutedAfter().AsTime()
			db = db.Where("executed_at >= ?", executedAfterTime)
		}
	}
	return db
}
