package consumer

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	paymentPb "github.com/epifi/gamma/api/order/payment"

	timelinePb "github.com/epifi/gamma/api/timeline"

	"go.uber.org/zap"

	caExternalPb "github.com/epifi/gamma/api/connected_account/external"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	piPb "github.com/epifi/gamma/api/paymentinstrument"

	"github.com/epifi/be-common/pkg/logger"

	rpcPb "github.com/epifi/be-common/api/rpc"
)

// ParseAndEnrichAATxnForRecon parses and enriches AA Transaction for reconcilation. This RPC is to be used only by AA Team for reconciliation
// nolint: funlen
func (s *Service) ParseAndEnrichAATxnForRecon(ctx context.Context, req *caExternalPb.TransactionEvent) (*aaOrderPb.ParseAndEnrichAATxnForReconResponse, error) {
	var (
		aaTxnId string
		err     error
	)

	logger.Debug(ctx, "processing aa txn event", zap.String(logger.ACCOUNT_ID, req.GetAccountId()))

	parsedTxn, err := s.parserProcessor.ParseAATxn(ctx, req)
	if err != nil {
		logger.Error(ctx, "error parsing aa transaction", zap.String(logger.AA_TXN_ID, aaTxnId), zap.String(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		return &aaOrderPb.ParseAndEnrichAATxnForReconResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("Failed to Parse AA Transaction"),
		}, nil
	}

	// resolve internal actor pi details
	internalPiId, internalActorName, err := s.aaProcessor.ResolveInternalActorPiDetailsForAATxn(ctx, parsedTxn.GetActorId(),
		parsedTxn.GetConnectedAccountInfo(),
		parsedTxn.GetConnectedAccountId(),
	)
	if err != nil {
		logger.Error(ctx, "error fetching internal actor pi details", zap.Error(err), zap.String(logger.AA_TXN_ID, aaTxnId))
		return &aaOrderPb.ParseAndEnrichAATxnForReconResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error fetching internal actor pi details"),
		}, nil
	}

	// resolve other actor pi details
	otherPiId, otherActorName, _, err := s.piProcessor.ResolveOtherActorPiDetails(ctx, parsedTxn.GetParsedParticulars(), nil, parsedTxn.GetActorId(), piPb.Ownership_EPIFI_WEALTH, false)
	if err != nil {
		logger.Error(ctx, "error fetching other pi details from parsed particulars", zap.Error(err), zap.String(logger.AA_TXN_ID, aaTxnId))
		return &aaOrderPb.ParseAndEnrichAATxnForReconResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error fetching other pi details from parsed particulars"),
		}, nil
	}

	// Step 3: get other actor-id from actor-service and create timeline
	fromActorId, fromPiId, toActorId, toPiId, err := s.timelineProcessor.ResolveTimeline(ctx, parsedTxn.GetActorId(), internalPiId, internalActorName,
		otherPiId, otherActorName, parsedTxn.GetTxnType() == paymentPb.AccountingEntryType_CREDIT, timelinePb.Ownership_EPIFI_WEALTH)
	if err != nil {
		logger.Error(ctx, "error in  resolve other actor and creating timeline between actors", zap.Error(err), zap.String(logger.AA_TXN_ID, aaTxnId))
		return &aaOrderPb.ParseAndEnrichAATxnForReconResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in  resolve other actor and creating timeline between actors"),
		}, nil
	}

	enrichedTxn := createEnrichedTransaction(parsedTxn, fromActorId, fromPiId, toActorId, toPiId)
	return &aaOrderPb.ParseAndEnrichAATxnForReconResponse{
		Status:              rpc.StatusOk(),
		EnrichedTransaction: enrichedTxn,
	}, nil
}

func createEnrichedTransaction(
	parsedTxn *aaOrderPb.ParsedTransaction,
	fromActorId, fromPiId, toActorId, toPiId string,
) *aaOrderPb.Transaction {

	txnPayload := &aaOrderPb.TransactionPayload{
		CurrentBalance: parsedTxn.GetCurrentBalance(),
		FromActorId:    fromActorId,
		ToActorId:      toActorId,
		AccountId:      parsedTxn.GetConnectedAccountId(),
		Particulars:    parsedTxn.GetNarration(),
		ValueDate:      parsedTxn.GetValueDate(),
		Category:       parsedTxn.GetTransactionCategory(),
		City:           parsedTxn.GetCity(),
		Keyword:        parsedTxn.GetKeyword(),
	}

	txn := &aaOrderPb.Transaction{
		PiFrom:          fromPiId,
		PiTo:            toPiId,
		Utr:             parsedTxn.GetUtr(),
		Amount:          parsedTxn.GetAmount(),
		PaymentProtocol: parsedTxn.GetPaymentProtocol(),
		ExecutedAt:      parsedTxn.GetTransactionTimestamp(),
		PartnerRefId:    parsedTxn.GetExternalTransactionId(),
		Remarks:         parsedTxn.GetTransactionRemark(),
		Payload:         txnPayload,
		TransactionType: parsedTxn.GetTxnType(),
		Bank:            parsedTxn.GetBank(),
		AaTxnId:         parsedTxn.GetTransactionId(),
		TemplateId:      parsedTxn.GetTemplateId(),
	}
	return txn
}
