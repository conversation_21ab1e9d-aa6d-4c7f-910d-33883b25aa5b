// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/queue/delay_publisher.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	queue "github.com/epifi/be-common/pkg/queue"
	gomock "github.com/golang/mock/gomock"
	proto "google.golang.org/protobuf/proto"
)

// MockDelayPublisher is a mock of DelayPublisher interface
type MockDelayPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockDelayPublisherMockRecorder
}

// MockDelayPublisherMockRecorder is the mock recorder for MockDelayPublisher
type MockDelayPublisherMockRecorder struct {
	mock *MockDelayPublisher
}

// NewMockDelayPublisher creates a new mock instance
func NewMockDelayPublisher(ctrl *gomock.Controller) *MockDelayPublisher {
	mock := &MockDelayPublisher{ctrl: ctrl}
	mock.recorder = &MockDelayPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockDelayPublisher) EXPECT() *MockDelayPublisherMockRecorder {
	return m.recorder
}

// PublishWithDelay mocks base method
func (m *MockDelayPublisher) PublishWithDelay(ctx context.Context, msg proto.Message, delay time.Duration) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishWithDelay", ctx, msg, delay)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishWithDelay indicates an expected call of PublishWithDelay
func (mr *MockDelayPublisherMockRecorder) PublishWithDelay(ctx, msg, delay interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishWithDelay", reflect.TypeOf((*MockDelayPublisher)(nil).PublishWithDelay), ctx, msg, delay)
}

// Marshal mocks base method
func (m *MockDelayPublisher) Marshal(arg0 queue.PublishMessage) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Marshal", arg0)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Marshal indicates an expected call of Marshal
func (mr *MockDelayPublisherMockRecorder) Marshal(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Marshal", reflect.TypeOf((*MockDelayPublisher)(nil).Marshal), arg0)
}
