{"name": "MyClass", "type": "record", "namespace": "com.acme.avro", "fields": [{"name": "txn_id", "type": "string"}, {"name": "vendor_ref_id", "type": "string"}, {"name": "outlet_id", "type": "string"}, {"name": "card_name", "type": "string"}, {"name": "customer_name", "type": "string"}, {"name": "store_name", "type": "string"}, {"name": "store_type", "type": "string"}, {"name": "store_city", "type": "string"}, {"name": "txn_amount", "type": "string"}, {"name": "txn_currency", "type": "string"}, {"name": "txn_time", "type": "string"}, {"name": "guest_free_visit_count", "type": "string"}, {"name": "guest_paid_visit_count", "type": "string"}, {"name": "member_free_visit_count", "type": "string"}, {"name": "member_paid_visit_count", "type": "string"}, {"name": "txn_date", "type": "string"}]}