select actor_id, max(fed_aa_loan_approved_offer_category ) fed_aa_loan_approved_offer_category
from (
select a.actor_id
,case when datediff(day, b.first_offer_date, current_date()) <= 30 then 1 else 0 end as fta
,case
        when fta = 1 then 'fed_aa_fta'
        when fta = 0 then 'fed_aa_pa' end fed_aa_loan_approved_offer_category
-- taking all the current approved offers
from (
select lo.actor_id, lo.loan_program,lo.id, lo.created_at_ist
from EPIFI_DATALAKE_FEDERAL.LOANS_FEDERAL.loan_offer_eligibility_criteria lo
where 1=1
and lo.expired_at is null
and lo.loan_program = 'LOAN_PROGRAM_REAL_TIME_DISTRIBUTION' and lo.sub_status='LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_PRE_QUALIFIED_BY_VENDOR'
qualify row_number() over (partition by actor_id,loan_program order by created_at_ist desc) = 1
) a
-- join min offer created at date for fta calculation
left join (
select actor_id ,loan_program ,min(created_at_ist) as first_offer_date
from EPIFI_DATALAKE_FEDERAL.LOANS_FEDERAL.loan_offer_eligibility_criteria lo
where 1=1
and loan_program = 'LOAN_PROGRAM_REAL_TIME_DISTRIBUTION' and lo.sub_status='LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_PRE_QUALIFIED_BY_VENDOR'
group by 1,2
) b on a.actor_id = b.actor_id and a.loan_program = b.loan_program
)
group by 1