select a.ACTOR_ID,
       nvl(b.CC_ACTIVE_FLAG, 0) as bureau_cc_active_flag,
       nvl(b.CC_EVER_FLAG, 0) as bureau_cc_ever_flag,
       nvl(b.CC_COUNT_ACTIVE_TRADES, 0) as bureau_cc_active_count,
       nvl(b.CC_COUNT_TRADES_EVER, 0) as bureau_cc_ever_count,
       case when ACCOUNT_EXPIRED_FLAG = 0
            and FRAUD_FLAGGED_USERS = 0 and LEA_FLAGGED_USERS = 0
            and CC_CREATED = 0
            and CC_EVER_ELIGIBLE = 0
            and c.actor_id is null-- high risk
            and d.actor_id is null
       then 1 else 0 end as simplifi_eligible_flag
        -- ,

        -- case
        --     when
        --         f.ACTOR_ID is null and
        --          c.actor_id is null and
        --          h.ACTOR_ID is null and
        --          i.ACTOR_ID is null and
        --          b.ntc = 1
        --         then a.ACTOR_ID
        -- end as ETB_BRE_RANDOM_NTC,

        -- case
        --     when
        --         f.ACTOR_ID is null and
        --          c.actor_id is null and
        --          h.ACTOR_ID is null and
        --          i.ACTOR_ID is null and
        --          b.bureau_score > 700
        --         then a.ACTOR_ID
        -- end as ETB_BRE_RANDOM_ETC

    from (
        select actor_id
        from (
                select
                    a.id as actor_id
                from EPIFI_DATALAKE_FEDERAL.FEDERAL_EPIFI.savings_accounts sa
                inner join EPIFI_DATALAKE_TECH.ACTOR.actors a
                on sa.primary_account_holder = a.entity_id
                where state = 'CREATED' and a.id is not null
            union all
                SELECT
                  actor_id
                FROM EPIFI_DATALAKE_TECH.TECH_EPIFI.onboarding_details
                WHERE parse_json(fi_lite_details):isEnabled::VARCHAR = 'TRUE'
        )
        group by 1
    ) a -- add base table
    LEFT JOIN EPIFI_DATAMART_FEDERAL.CREDIT_CARD.PROFILE_VAR_V1 b on a.actor_id = b.actor_id
    left join (select ACTOR_ID,
                  case when prob_default_bucket = 'e. HIGH_RISK' then 0 else 1 end as pd_model_high_risk_exclude,
                  case
                      when (prob_default_bucket = 'c. MEDIUM_RISK_1' or prob_default_bucket = 'd. MEDIUM_RISK_2') and
                           bureau_thickness = 'a. Thin' then 0
                      else 1 end                                                 as thin_file_exclude,
                  case
                      when is_account_expired = 0
                          and fraud_risk_status = 'ELIGIBLE'
                          and (AGE <= 61 and AGE >= 21)
                          and pd_model_high_risk_exclude = 1
                          and thin_file_exclude = 1
                          then 1
                      ELSE 0 end as secured_cc_eligible_exclude
           from EPIFI_DATAMART_tech.credit_risk.lender_policy_waterfall
--            group by 1,2,3
        ) c on a.ACTOR_ID = c.actor_id  and secured_cc_eligible_exclude = 0
    left join (
        select
            actor_id
        from EPIFI_DATAMART_tech.credit_risk.lender_policy_waterfall
        where fed_pa_cc_today_eligible_flag_lp1=1
        group by 1
    ) d on a.actor_id = d.actor_id
    left join (
            select ACTOR_ID
            from EPIFI_DATAMART_tech.CREDIT_RISK.LENDER_POLICY_WATERFALL
            where FED_PA_CC_TODAY_ELIGIBLE_FLAG_LP1 = 1
                or FED_PA_CC_TODAY_REJECTION_REASON_LP1 = 's. Excluding Cases with Other than Epifi Relationship '
            group by 1
    ) e on a.actor_id = e.actor_id
    left join (
            select ACTOR_ID
            from epifi_datamart_frm.user_risk.risk_indicators_vpt
            where (RISK_STATUS <> 'ELIGIBLE')
            group by 1
    ) f on a.ACTOR_ID = f.ACTOR_ID -- User risk
    left join (select ACTOR_ID
            from EPIFI_DATALAKE_FEDERAL.CREDIT_CARD_PGDB.CREDIT_CARDS
            group by 1
    ) h on a.ACTOR_ID = h.ACTOR_ID -- card created
    left join (select ACTOR_ID
               from EPIFI_DATALAKE_FEDERAL.CREDIT_CARD_PGDB.CC_OFFERS
               where VALID_TILL >= current_date and DEACTIVATED_AT is null
                and (CARD_PROGRAM is null or CARD_PROGRAM not ilike '%secured%')
               group by 1
    ) i on a.ACTOR_ID = i.ACTOR_ID;
